# React Frontend Environment Variables (.env.example)
# Copy this file to .env to configure your local React frontend development
# For backend Django variables, see backend/.env.example

# API Configuration
# For local development, use localhost:8000
# For production, use your deployed backend URL
REACT_APP_API_URL=http://localhost:8000

# ESLint Suppression (Create React App)
DISABLE_ESLINT_PLUGIN=true
GENERATE_SOURCEMAP=true
SKIP_PREFLIGHT_CHECK=true

# IPinfo API Configuration (for geolocation services)
# Get your free API key from https://ipinfo.io/
# Used by src/services/geolocationService.js
REACT_APP_IPINFO_API_KEY=your_ipinfo_api_key_here

# AI API Configuration (for AI features)
# Get your API keys from respective providers
# Used by src/services/aiService.js and other AI services
REACT_APP_OPENAI_API_KEY=your_openai_api_key_here
REACT_APP_GROQ_API_KEY=your_groq_api_key_here
REACT_APP_AI_BASE_URL=https://api.openai.com/v1
REACT_APP_AI_MODEL=gpt-3.5-turbo
