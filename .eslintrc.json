{"env": {"browser": true, "es2021": true, "jest": true}, "extends": ["airbnb", "airbnb/hooks", "plugin:react/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react"], "rules": {"jsx-a11y/label-has-associated-control": "off", "react/jsx-one-expression-per-line": "off", "max-len": "off", "react/jsx-filename-extension": "off", "react/react-in-jsx-scope": "off", "comma-dangle": "off", "react/function-component-definition": "off", "no-unused-vars": "off", "react/no-array-index-key": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "react/jsx-boolean-value": "off", "arrow-parens": "off", "no-trailing-spaces": "off", "object-curly-newline": "off", "react/no-unescaped-entities": "off", "react/button-has-type": "off", "react/require-default-props": "off", "react/prop-types": "off", "no-console": "off", "import/no-extraneous-dependencies": "off", "react/jsx-props-no-spreading": "off", "no-shadow": "off", "consistent-return": "off", "no-param-reassign": "off", "prefer-destructuring": "off", "react/no-unstable-nested-components": "off", "implicit-arrow-linebreak": "off", "operator-linebreak": "off", "function-paren-newline": "off", "react-hooks/exhaustive-deps": "off", "prefer-const": "off", "eqeqeq": "off", "radix": "off", "no-plusplus": "off", "no-continue": "off", "padded-blocks": "off", "no-use-before-define": "off", "object-shorthand": "off", "no-restricted-syntax": "off", "no-nested-ternary": "off", "indent": "off", "react/self-closing-comp": "off", "react/jsx-closing-tag-location": "off", "no-confusing-arrow": "off", "react/jsx-curly-newline": "off", "quotes": "off", "no-unneeded-ternary": "off", "react/jsx-indent": "off"}}