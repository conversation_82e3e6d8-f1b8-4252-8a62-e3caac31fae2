name: SalonGenz CI

on:
  push:
    branches: [ main, master, develop, socials_feed ]
  pull_request:
    branches: [ main, master, develop, socials_feed ]

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      # ESLint disabled during development - uncomment when ready for production
      # - name: Run ESLint
      #   run: npm run lint

      - name: Run Prettier check
        run: npx prettier --check "src/**/*.{js,jsx,json,css,md}"

      - name: Run tests
        run: npm test -- --watchAll=false

      # Optional: Build step to ensure production build works
      - name: Build project
        run: npm run build
