# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.js

# testing
/coverage

# production
build/
dist/

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python virtual environment
salonvenv/
venv

# Python
__pycache__/
*.pyc
*.pyo
*.pyd

# Django
*.sqlite3
*.log

# Environment Variables
# Ignore React frontend env (root directory)
.env
.env.local
.env.development
.env.test
.env.production

# Ignore Django backend env (backend directory)
backend/.env
backend/.env.local
backend/.env.development
backend/.env.test
backend/.env.production

# Allow .env.example files for documentation
!.env.example
!backend/.env.example

# VSCode
.vscode/

# OS
Thumbs.db

# AI Engine
ai_engine/cache/
ai_engine/logs/

# Misc
*.bak
*.swp
*.swo

# Ignore test outputs
test_ai_integration.py

# Django/SQLite DB
backend/db.sqlite3

# Personal notes and documentation
.me
*.me
