# SalonGenz API Documentation

## Overview
This document outlines all the API endpoints for the SalonGenz booking and payment system.

## Base URL
```
http://localhost:8000/api/
```

## Authentication
Most endpoints require authentication using JWT tokens:
```
Authorization: Bearer <your_jwt_token>
```

## Booking Endpoints

### 1. Create Booking
**POST** `/bookings/`

Creates a new booking with payment information.

**Request Body:**
```json
{
  "user_id": "string",
  "salon_id": "integer",
  "service_id": "integer",
  "staff_id": "integer (optional)",
  "appointment_date": "YYYY-MM-DD",
  "appointment_time": "HH:MM",
  "customer_name": "string",
  "customer_email": "string",
  "customer_phone": "string",
  "payment_method": "string",
  "special_requests": "string (optional)",
  "service_price": "decimal",
  "platform_fee": "decimal",
  "total_amount": "decimal",
  "pricing_breakdown": "object (optional)"
}
```

**Response:**
```json
{
  "id": "integer",
  "user_id": "string",
  "salon_id": "integer",
  "service_id": "integer",
  "staff_id": "integer",
  "appointment_date": "YYYY-MM-DD",
  "appointment_time": "HH:MM",
  "customer_name": "string",
  "customer_email": "string",
  "customer_phone": "string",
  "payment_method": "string",
  "status": "pending",
  "service_price": "decimal",
  "platform_fee": "decimal",
  "total_amount": "decimal",
  "pricing_breakdown": "object"
}
```

### 2. Get Booking Details
**GET** `/bookings/{id}/`

Retrieves booking details by ID.

**Response:**
```json
{
  "id": "integer",
  "salon_name": "string",
  "service_name": "string",
  "staff_name": "string",
  "appointment_date": "YYYY-MM-DD",
  "appointment_time": "HH:MM",
  "status": "string",
  "payment_status": "string",
  "total_amount": "decimal",
  "transaction_id": "string"
}
```

### 3. Update Booking Status
**PATCH** `/bookings/{booking_id}/status/`

Updates the status of a booking.

**Request Body:**
```json
{
  "status": "pending|processing|confirmed|cancelled|completed|failed"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Booking status updated to confirmed",
  "booking_id": "integer",
  "status": "string"
}
```

### 4. Get User Bookings
**GET** `/user/bookings/?user_id={user_id}`

Retrieves all bookings for a specific user.

**Response:**
```json
[
  {
    "id": "integer",
    "salon_name": "string",
    "service_name": "string",
    "appointment_date": "YYYY-MM-DD",
    "appointment_time": "HH:MM",
    "status": "string",
    "total_amount": "decimal"
  }
]
```

## Payment Endpoints

### 1. Process Payment
**POST** `/payments/`

Processes a payment for a booking.

**Request Body:**
```json
{
  "booking_id": "integer",
  "payment_method": "mpesa|paypal|banktransfer|wise|visa|card",
  "amount": "decimal",
  "phone_number": "string (optional, for M-Pesa)"
}
```

**Response (Success):**
```json
{
  "success": true,
  "transaction_id": "string",
  "message": "M-Pesa payment completed successfully",
  "redirect_url": "/checkout-success"
}
```

**Response (Failure):**
```json
{
  "success": false,
  "message": "M-Pesa payment failed. Please try again.",
  "redirect_url": "/payment-failed"
}
```

### 2. Get Payment Status
**GET** `/payments/{transaction_id}/status/`

Retrieves the status of a payment by transaction ID.

**Response:**
```json
{
  "transaction_id": "string",
  "status": "pending|processing|completed|failed|cancelled",
  "amount": "decimal",
  "payment_method": "string",
  "created_at": "datetime",
  "payment_date": "datetime"
}
```

### 3. Cancel Payment
**POST** `/payments/{transaction_id}/cancel/`

Cancels a pending payment.

**Response:**
```json
{
  "success": true,
  "message": "Payment cancelled successfully"
}
```

## Notification Endpoints

### 1. Send Confirmation Email
**POST** `/notifications/send-confirmation/`

Sends a confirmation email for a booking.

**Request Body:**
```json
{
  "bookingId": "integer",
  "customerEmail": "string",
  "customerName": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Confirmation email sent successfully"
}
```

## Salon Endpoints

### 1. Get All Salons
**GET** `/salons/`

Retrieves all salons with their services and staff.

**Response:**
```json
[
  {
    "id": "integer",
    "name": "string",
    "description": "string",
    "address": "string",
    "phone": "string",
    "email": "string",
    "image_url": "string",
    "services": [
      {
        "id": "integer",
        "name": "string",
        "description": "string",
        "price": "decimal",
        "duration": "integer"
      }
    ],
    "staff": [
      {
        "id": "integer",
        "name": "string",
        "role": "string",
        "is_premium": "boolean"
      }
    ]
  }
]
```

### 2. Get Salon Details
**GET** `/salons/{id}/`

Retrieves detailed information about a specific salon.

### 3. Search Salons
**GET** `/salons/search/?q={search_term}&location={location}`

Searches for salons by name or location.

## Service Endpoints

### 1. Get All Services
**GET** `/services/`

Retrieves all services.

### 2. Get Services by Salon
**GET** `/salons/{salon_id}/services/`

Retrieves all services for a specific salon.

### 3. Search Services
**GET** `/services/search/?q={search_term}`

Searches for services by name.

## Staff Endpoints

### 1. Get All Staff
**GET** `/staff/`

Retrieves all staff members.

### 2. Get Staff by Salon
**GET** `/salons/{salon_id}/staff/`

Retrieves all staff members for a specific salon.

## Gift Booking Endpoints

### 1. Create Gift Booking
**POST** `/bookings/gift/`

Creates a gift booking for another person.

**Request Body:**
```json
{
  "purchaser_id": "string",
  "recipient_id": "string",
  "recipient_contact": "string",
  "gift_message": "string",
  "booking": {
    "userId": "string",
    "userName": "string",
    "salon": "integer",
    "service": "integer",
    "staff": "integer (optional)",
    "date": "YYYY-MM-DD",
    "time": "HH:MM",
    "status": "Confirmed",
    "notes": "string"
  }
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "error": "Invalid request data",
  "details": "Specific error details"
}
```

### 401 Unauthorized
```json
{
  "error": "Authentication required"
}
```

### 404 Not Found
```json
{
  "error": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "message": "Error details"
}
```

## Payment Method Support

The system supports the following payment methods:

1. **M-Pesa** - Mobile money payment (Kenya)
2. **PayPal** - International payment processing
3. **Bank Transfer** - Direct bank transfer
4. **Wise** - International money transfer
5. **Visa/Card** - Credit/debit card payments

## Booking Status Flow

1. **pending** - Initial booking created
2. **processing** - Payment being processed
3. **confirmed** - Payment successful, booking confirmed
4. **completed** - Service completed
5. **cancelled** - Booking cancelled
6. **failed** - Payment failed

## Payment Status Flow

1. **pending** - Payment initiated
2. **processing** - Payment being processed
3. **completed** - Payment successful
4. **failed** - Payment failed
5. **cancelled** - Payment cancelled

## Rate Limiting

- **Booking creation**: 10 requests per minute per user
- **Payment processing**: 5 requests per minute per user
- **Status updates**: 20 requests per minute per user

## Security

- All sensitive data is encrypted in transit (HTTPS)
- JWT tokens are required for authenticated endpoints
- Payment data is not stored in plain text
- CSRF protection is enabled for all POST requests

## Testing

For testing purposes, the payment processing includes simulated delays and success/failure rates:

- **M-Pesa**: 95% success rate, 2-second processing time
- **PayPal**: 97% success rate, 3-second processing time
- **Bank Transfer**: 100% success rate (manual confirmation)
- **Wise**: 96% success rate, 2.5-second processing time
- **Visa/Card**: 98% success rate, 1.5-second processing time 