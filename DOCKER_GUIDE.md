# SalonGenz Docker & Deployment Guide

## Project Overview
This project uses Docker Compose to manage the backend (Django), frontend (React or similar), and PostgreSQL database. Dock<PERSON> ensures that the app runs the same way on every machine and in the cloud.

---

## React/Node Docker Build Optimization

To make Docker builds for the React frontend as fast as possible:

- **Install dependencies first:**
  - `COPY package.json package-lock.json ./`
  - `RUN npm ci`
- **Copy app code after:**
  - `COPY public ./public`
  - `COPY src ./src`
- **Build app:**
  - `RUN npm run build`

This way, <PERSON><PERSON> will only re-install dependencies if `package.json` or `package-lock.json` changes. Most of the time, only your app code changes, so builds are very fast!

---

## Quick Start (Local Development)

### Speed Up Docker Builds with Split Requirements

To make Docker builds much faster, we split Python dependencies into two files:

- `requirements-base.txt`: Heavy, rarely changed, core dependencies (Django, M<PERSON>, numpy, torch, etc.)
- `requirements-dev.txt`: Fast-changing, dev-only, or project-specific dependencies. Always includes `-r requirements-base.txt` as the first line.

**How it works:**
- <PERSON><PERSON> will only re-install the heavy packages if you change `requirements-base.txt`.
- Most of the time, you only change `requirements-dev.txt`, so builds are much faster.

**How to add dependencies:**
- If it's a core, big, or rarely changed library, add it to `requirements-base.txt`.
- If it's a dev tool, something you change often, or your own app, add it to `requirements-dev.txt`.

**Dockerfile is set up to use both files.**

---

### Important: Speed Up Docker Builds with `.dockerignore`

To avoid slow builds, make sure you have a proper `.dockerignore` file in your project root. This prevents Docker from copying unnecessary files (like `node_modules`, `.git`, build outputs, etc.) into the build context, making builds much faster.

**Recommended `.dockerignore` content:**
```dockerignore
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
.husky

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Git
.git/
.gitignore

# Python
*.pyc
__pycache__/
salonvenv/
*.sqlite3

# Misc
.eslintcache
*.tgz
*.tar.gz
```

After updating `.dockerignore`, always rebuild your containers:
```sh
docker-compose build
docker-compose up
```

### 1. Prerequisites
- Install [Docker Desktop](https://www.docker.com/products/docker-desktop/) for your OS (Windows/Mac/Linux).
- Make sure Docker Desktop is running (look for the whale icon).

### 2. Clone the Project
```sh
git clone <your-repo-url>
cd salongenz
```

### 3. Environment Variables
Check or set up your `.env` file (example below):
```env
# Django settings
DEBUG=True
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1

# Postgres settings
POSTGRES_DB=salon_db
POSTGRES_USER=salon_user
POSTGRES_PASSWORD=salon_pass
POSTGRES_HOST=db
POSTGRES_PORT=5432

# (Optional) React API keys
REACT_APP_IPINFO_API_KEY=...
```

### 4. Build and Run
```sh
docker-compose build
docker-compose up
```
- The first run may take several minutes as Docker downloads images and installs dependencies. This is normal!

### 5. Database Migrations (First Time)
```sh
docker-compose exec backend python manage.py migrate
```

### 6. Create Superuser (Optional)
```sh
docker-compose exec backend python manage.py createsuperuser
```

---

## Common Commands
| Action                        | Command                                 |
|-------------------------------|-----------------------------------------|
| Build containers              | `docker-compose build`                  |
| Start containers              | `docker-compose up`                     |
| Stop/remove containers        | `docker-compose down`                   |
| Check Docker status           | `docker info`                           |
| List running containers       | `docker ps`                             |
| List all containers           | `docker ps -a`                          |
| Run migrations                | `docker-compose exec backend python manage.py migrate` |

---

## Updating Dependencies
- If you add new Python/Node packages, update `requirements.txt` or `package.json`, then run:
  ```sh
  docker-compose build
  docker-compose up
  ```

---

## Troubleshooting
- **First run is slow:** Downloading images and installing dependencies can take several minutes. This is normal.
- **If you see errors:** Check logs in your terminal, or run `docker-compose logs`.
- **Docker must be running:** Make sure Docker Desktop is open and running before using Compose commands.

---

## Cloud Deployment
- The same Docker setup can be used for cloud deployment (with minor changes for production).
- Ask the team or your AI assistant for cloud-specific instructions if needed.

---

## Need Help?
- Ask your AI assistant or check the [Docker documentation](https://docs.docker.com/get-started/).
