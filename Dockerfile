# Use Node.js 18 LTS - compatible with react-scripts 5.0.1
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl

# Copy package files first for better caching
COPY package*.json ./

# Clean npm cache and install dependencies
RUN npm cache clean --force \
    && npm install --legacy-peer-deps

# Copy source code
COPY . .

# Create a non-root user
RUN addgroup -g 1001 -S nodejs \
    && adduser -S reactuser -u 1001 -G nodejs \
    && chown -R reactuser:nodejs /app
USER reactuser

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Start the development server
CMD ["npm", "start"]
