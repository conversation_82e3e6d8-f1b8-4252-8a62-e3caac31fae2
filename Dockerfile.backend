# Dockerfile for Django Backend (Production)
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

WORKDIR /app

COPY backend/requirements-base.txt ./
RUN pip install --upgrade pip && pip install -r requirements-base.txt

COPY backend/requirements-dev.txt ./
RUN pip install -r requirements-dev.txt

COPY backend/ ./
COPY ai_engine ./ai_engine

# Collect static files for production
RUN mkdir -p /app/staticfiles && python manage.py collectstatic --noinput || true

EXPOSE 8000

CMD ["gunicorn", "backend.wsgi:application", "--bind", "0.0.0.0:8000", "--workers=3"]
