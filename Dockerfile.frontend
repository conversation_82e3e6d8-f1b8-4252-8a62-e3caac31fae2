# Dockerfile for React Frontend (Production)
FROM node:18-alpine as build
WORKDIR /app
# Install dependencies first for better cache
COPY package.json package-lock.json ./
RUN npm ci

# Copy app source
COPY public ./public
COPY src ./src

# Build app
RUN npm run build

FROM nginx:1.25-alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
