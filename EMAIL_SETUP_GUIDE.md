# 📧 SalonGenz Email Setup Guide

## 🚀 Quick Setup for Live Email Testing

### Option 1: SendGrid (Recommended - 100 emails/day free)

1. **Sign up for SendGrid**:
   - Go to: https://sendgrid.com/
   - Create free account
   - Verify your email

2. **Get API Key**:
   - Login to SendGrid dashboard
   - Go to Settings → API Keys
   - Click "Create API Key"
   - Choose "Restricted Access"
   - Give permissions: Mail Send → Full Access
   - Copy the API key (starts with SG.)

3. **Update backend/.env**:
   ```env
   # SendGrid Configuration
   EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
   EMAIL_HOST=smtp.sendgrid.net
   EMAIL_PORT=587
   EMAIL_HOST_USER=apikey
   EMAIL_HOST_PASSWORD=SG.your_sendgrid_api_key_here
   EMAIL_USE_TLS=True
   DEFAULT_FROM_EMAIL=SalonGenz <<EMAIL>>
   ```

4. **Test**:
   ```bash
   python test_live_email.py
   ```

### Option 2: Gmail App Password

1. **Enable 2FA on Gmail**:
   - Go to Google Account settings
   - Security → 2-Step Verification
   - Turn on 2FA

2. **Generate App Password**:
   - Go to: https://myaccount.google.com/apppasswords
   - Select app: Mail
   - Select device: Other (Custom name)
   - Name it: "SalonGenz"
   - Copy the 16-character password

3. **Update backend/.env**:
   ```env
   EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_HOST_USER=<EMAIL>
   EMAIL_HOST_PASSWORD=your_16_character_app_password
   EMAIL_USE_TLS=True
   DEFAULT_FROM_EMAIL=SalonGenz <<EMAIL>>
   ```

### Option 3: Mailgun (5,000 emails/month free for 3 months)

1. **Sign up**: https://www.mailgun.com/
2. **Get credentials** from dashboard
3. **Update backend/.env**:
   ```env
   EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
   EMAIL_HOST=smtp.mailgun.org
   EMAIL_PORT=587
   EMAIL_HOST_USER=postmaster@your_domain.mailgun.org
   EMAIL_HOST_PASSWORD=your_mailgun_password
   EMAIL_USE_TLS=True
   ```

## 🧪 Testing Commands

```bash
# Test email configuration
python test_live_email.py

# Send test <NAME_EMAIL>
python send_test_email.py

# Test complete email system
python test_email_system.py
```

## 🎯 Current Status

✅ **Email System**: Fully implemented
✅ **AI Integration**: Working with emojis and first names
✅ **HTML Templates**: Beautiful responsive design
✅ **Backend Integration**: Complete
✅ **Frontend Integration**: Ready
❌ **SMTP Credentials**: Need to be configured

## 🔧 Quick Fix

**For immediate testing**, update this line in `backend/.env`:
```env
EMAIL_HOST_PASSWORD=your_actual_credentials_here
```

Then run:
```bash
python test_live_email.py
```

## 📱 Expected Result

Once configured, <EMAIL> will receive:
- Subject: "🎁 SalonGenz sent you a gift booking at Glam Paradise!"
- Beautiful HTML email with emojis
- Personalized message using first names only
- Complete booking details
- Professional SalonGenz branding

## 🆘 Troubleshooting

**Authentication Error**: 
- Check credentials are correct
- For Gmail: Use app password, not regular password
- For SendGrid: Use "apikey" as username

**Connection Error**:
- Check internet connection
- Verify SMTP settings
- Try different email service

**Permission Error**:
- Check email service permissions
- Verify account is active
- Check spam/security settings
