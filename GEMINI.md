# GEMINI.md — Salongenz App

## Purpose
This project is a salon booking application that allows users to book appointments, manage their bookings, and view available services. It aims to provide a seamless and intuitive booking experience for both clients and salon staff.

## Technology Stack

### Frontend
- **Framework:** React
- **Styling:** CSS

### Backend
- **Framework:** Django
- **API:** Django REST Framework
- **Database:** SQLite (for development/local testing)
- **Language:** Python

## Project Structure & Folder Hierarchy

```
salongenz/
├── node_modules/       # Node.js dependencies (Frontend)
├── public/             # Public assets (Frontend: index.html, images, etc.)
├── src/                # Main application source code (Frontend)
│   ├── App.css         # Main app styles
│   ├── App.js          # Main application component
│   ├── App.test.js     # Tests for the main app component
│   ├── index.css       # Global styles
│   ├── index.js        # Entry point of the application
│   ├── logo.svg        # Application logo
│   ├── reportWebVitals.js # Web vitals reporting
│   ├── setupTests.js   # Test setup
│   ├── components/     # Reusable React components
│   │   ├── Admin/      # Admin-specific components
│   │   ├── Auth/       # Authentication related components
│   │   ├── Bookings/   # Booking related components
│   │   ├── Calendar/   # Calendar components
│   │   ├── Notifications/ # Notification components
│   │   └── PaymentPages/ # Payment related components
│   ├── context/        # React Context API for global state
│   ├── data/           # Mock data or static data
│   └── services/       # Frontend service utilities (API calls, etc.)
├── backend/            # Backend Django project
│   ├── db.sqlite3      # SQLite database file
│   ├── manage.py       # Django management script
│   ├── requirements.txt # Python dependencies
│   ├── salongenz_backend/ # Main Django project settings
│   │   ├── __init__.py
│   │   ├── asgi.py
│   │   ├── settings.py # Django project settings
│   │   ├── urls.py     # Main URL configurations
│   │   └── wsgi.py
│   └── salons_app/     # Django app for salon-related functionalities
│       ├── __init__.py
│       ├── admin.py    # Django admin configurations
│       ├── apps.py     # App configurations
│       ├── models.py   # Database models
│       ├── serializers.py # Django REST Framework serializers
│       ├── tests.py    # Backend tests
│       ├── urls.py     # App-specific URL configurations
│       ├── utils.py    # Utility functions
│       ├── views.py    # API views
│       ├── management/ # Custom Django management commands
│       │   └── commands/
│       │       └── populate_dummy_salons.py # Script to populate dummy data
│       └── migrations/ # Database migration files
├── .gitignore          # Git ignore file
├── package-lock.json   # Exact versions of Node.js dependencies
├── package.json        # Project metadata and Node.js dependencies
├── README.md           # Project README
└── salonvenv/          # Python virtual environment
```

## Notes
- Follow project-wide coding standards.
- Keep this file concise and up-to-date as the app evolves.