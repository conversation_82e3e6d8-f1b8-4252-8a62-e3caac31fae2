# Location Detection Flow: SalonGenz

## Overview
This document describes the logic, priorities, and fallback mechanisms for user location detection in the SalonGenz app.

---

## Detection Priority

1. **Browser Geolocation (navigator.geolocation)**
   - Attempts to get real-time, high-accuracy user location.
   - Prompts user for permission.
   - If successful, uses returned latitude/longitude for salon search and recommendations.

2. **IP-Based Location (ipinfo.io)**
   - If browser geolocation fails (denied, unavailable, or times out), falls back to IP-based lookup via ipinfo.io API.
   - Extracts city, region, country, latitude, and longitude from response.
   - Used as a less-precise fallback.

3. **Manual User Input**
   - If both browser and IP-based detection fail, presents a modal prompting user to enter their city or neighborhood.
   - Uses this input to perform a salon search by city.

---

## Fallback Points & Failure Handling

- **Browser Geolocation Failure:**
  - If denied, unavailable, or times out, triggers ipinfo.io fallback.
  - User is alerted to enable location services.

- **ipinfo.io Failure:**
  - If API request fails or returns no usable location, triggers manual input modal.
  - User is prompted: “We couldn’t detect your location. Please enter your city to find salons near you.”

- **Manual Input Failure:**
  - If user submits an empty or invalid input, prompts for valid entry.
  - If no salons are found for the entered location, displays a user-friendly error.

---

## Known Failure Scenarios

- **User denies browser location:**  
  Fallback to IP-based detection, then manual input if needed.
- **User is on VPN or IP lookup fails:**  
  Fallback to manual input.
- **Both browser and IP lookup fail (e.g., offline):**  
  Immediate manual input prompt.
- **No salons found for provided location:**  
  User is informed and can try a different location or expand search radius.

---

## Notes

- No Google Maps or Google Geolocation API is used anywhere in the codebase.
- All sensitive tokens (e.g., ipinfo) are now stored in environment variables.
- HTTPS is required for browser geolocation to function.

---

_Last updated: 2025-07-07_
