# Enforcing HTTPS with Nginx

To ensure browser geolocation works and secure your app, enforce HTTPS in your Nginx configuration:

```
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com www.yourdomain.com;
    # SSL certificate config here
    # ...
    location / {
        # Proxy or serve app
    }
}
```

If deploying to Netlify, Vercel, or similar, HTTPS is enforced by default.
