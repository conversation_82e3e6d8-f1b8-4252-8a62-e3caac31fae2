# Paystack Integration Setup Guide

## Overview
This guide explains how to set up Paystack payment integration for SalonGenz.

## Prerequisites
1. Paystack account (https://paystack.com)
2. Verified business account
3. API keys from Paystack dashboard

## Setup Steps

### 1. Create Paystack Account
1. Go to [Paystack.com](https://paystack.com)
2. Sign up for a business account
3. Complete KYC verification
4. Activate your account

### 2. Get API Keys
1. Login to your Paystack dashboard
2. Go to **Settings** → **API Keys & Webhooks**
3. Copy your **Public Key** and **Secret Key**
4. For testing, use the **Test Keys** (starts with `pk_test_` and `sk_test_`)
5. For production, use the **Live Keys** (starts with `pk_live_` and `sk_live_`)

### 3. Environment Configuration
Create a `.env` file in your project root:

```env
# Paystack Configuration
REACT_APP_PAYSTACK_PUBLIC_KEY=pk_test_your_public_key_here
REACT_APP_PAYSTACK_SECRET_KEY=sk_test_your_secret_key_here

# Other Environment Variables
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_ENVIRONMENT=development
```

### 4. Update PaystackService.js
Replace the placeholder keys in `src/services/PaystackService.js`:

```javascript
this.secretKey = process.env.REACT_APP_PAYSTACK_SECRET_KEY || 'sk_test_...';
this.publicKey = process.env.REACT_APP_PAYSTACK_PUBLIC_KEY || 'pk_test_...';
```

### 5. Configure Webhooks (Optional)
For real-time payment notifications:

1. In Paystack dashboard, go to **Settings** → **Webhooks**
2. Add webhook URL: `https://yourdomain.com/api/paystack/webhook`
3. Select events:
   - `charge.success`
   - `transfer.success`
   - `transfer.failed`

## Supported Payment Methods

### 1. Card Payments
- **Supported Cards**: Visa, Mastercard, Verve
- **Features**: 
  - Direct card charging
  - 3D Secure authentication
  - International cards support

### 2. Bank Transfers
- **Supported Banks**: All Nigerian banks
- **Features**:
  - Account number verification
  - Direct bank charging
  - Transfer initiation

### 3. USSD Payments
- **Supported Networks**: All major Nigerian networks
- **Features**:
  - USSD code generation
  - Mobile money integration

### 4. QR Code Payments
- **Features**:
  - QR code generation
  - Mobile app scanning

## API Endpoints

### Frontend Integration
```javascript
// Initialize transaction
const result = await paystackService.initializeTransaction({
  amount: 1000,
  email: '<EMAIL>',
  reference: 'SALON_123456',
  callback_url: 'https://yourdomain.com/payment/callback'
});

// Verify transaction
const verification = await paystackService.verifyTransaction('SALON_123456');

// Charge card directly
const charge = await paystackService.chargeCard({
  amount: 1000,
  email: '<EMAIL>',
  card_number: '****************',
  cvv: '408',
  expiry_month: '01',
  expiry_year: '2025',
  pin: '4081'
});
```

### Backend Integration (Optional)
If you want to handle payments server-side:

```python
# Django/Python example
import requests

def initialize_paystack_transaction(amount, email, reference):
    url = "https://api.paystack.co/transaction/initialize"
    headers = {
        "Authorization": f"Bearer {settings.PAYSTACK_SECRET_KEY}",
        "Content-Type": "application/json"
    }
    data = {
        "amount": int(amount * 100),  # Convert to kobo
        "email": email,
        "reference": reference,
        "callback_url": "https://yourdomain.com/payment/callback"
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()
```

## Testing

### Test Cards
Use these test cards for development:

| Card Type | Number | CVV | Expiry | PIN |
|-----------|--------|-----|--------|-----|
| Visa | **************** | 408 | 01/25 | 4081 |
| Mastercard | **************** | 564 | 01/25 | 564 |
| Verve | 5061460410120223210 | 780 | 01/25 | 780 |

### Test Bank Accounts
Use these test bank accounts:

| Bank | Account Number | Account Name |
|------|----------------|--------------|
| Access Bank | ********** | Test Account |
| GT Bank | ********** | Test Account |

## Security Best Practices

### 1. API Key Security
- Never expose secret keys in frontend code
- Use environment variables
- Rotate keys regularly
- Use different keys for test/production

### 2. Transaction Verification
- Always verify transactions server-side
- Check transaction status before confirming
- Validate amounts and references

### 3. Error Handling
- Handle all API errors gracefully
- Log failed transactions
- Implement retry mechanisms

### 4. Data Validation
- Validate all input data
- Sanitize user inputs
- Check transaction amounts

## Production Checklist

### Before Going Live
- [ ] Switch to live API keys
- [ ] Test all payment methods
- [ ] Configure webhooks
- [ ] Set up monitoring
- [ ] Implement error logging
- [ ] Test refund process
- [ ] Verify compliance requirements

### Monitoring
- [ ] Set up transaction monitoring
- [ ] Monitor webhook deliveries
- [ ] Track success/failure rates
- [ ] Set up alerts for failures

## Troubleshooting

### Common Issues

1. **Invalid API Key**
   - Check if keys are correct
   - Ensure you're using the right environment (test/live)

2. **Transaction Failed**
   - Check transaction logs
   - Verify card details
   - Check bank account details

3. **Webhook Not Received**
   - Verify webhook URL
   - Check server logs
   - Test webhook endpoint

4. **Amount Mismatch**
   - Ensure amount is in kobo (multiply by 100)
   - Check currency settings

### Support
- Paystack Documentation: https://paystack.com/docs
- Paystack Support: <EMAIL>
- API Status: https://status.paystack.com

## Cost Structure

### Transaction Fees
- **Local Cards**: 1.5% + ₦100
- **International Cards**: 3.9% + ₦100
- **Bank Transfers**: ₦50 flat fee
- **USSD**: ₦50 flat fee

### Settlement
- **Cards**: 24-48 hours
- **Bank Transfers**: 1-3 business days
- **USSD**: Instant

## Compliance

### PCI DSS
- Paystack is PCI DSS Level 1 compliant
- No card data stored on your servers
- Secure tokenization system

### Data Protection
- GDPR compliant
- NDPR compliant (Nigeria)
- Data encryption in transit and at rest

## Integration Examples

### React Component
```jsx
import { paystackService } from '../services/PaystackService';

const PaymentComponent = () => {
  const handlePayment = async () => {
    const result = await paystackService.initializeTransaction({
      amount: 1000,
      email: '<EMAIL>',
      reference: 'SALON_123456'
    });
    
    if (result.success) {
      window.location.href = result.authorization_url;
    }
  };
  
  return (
    <button onClick={handlePayment}>
      Pay with Paystack
    </button>
  );
};
```

### Callback Handler
```javascript
// Handle payment callback
const handlePaymentCallback = async (reference) => {
  const verification = await paystackService.verifyTransaction(reference);
  
  if (verification.success && verification.status === 'success') {
    // Payment successful
    console.log('Payment completed:', verification);
  } else {
    // Payment failed
    console.log('Payment failed:', verification);
  }
};
```

## Next Steps

1. **Test Integration**: Use test keys to verify functionality
2. **Customize UI**: Adapt payment forms to match your design
3. **Add Analytics**: Track payment success rates
4. **Implement Webhooks**: For real-time notifications
5. **Go Live**: Switch to production keys

For additional support, refer to the [Paystack Documentation](https://paystack.com/docs) or contact their support team. 