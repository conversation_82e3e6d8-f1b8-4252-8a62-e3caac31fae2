# Paystack Integration Test Guide

This guide provides comprehensive testing instructions for the Paystack payment integration in SalonGenz.

## 🔑 **API Keys Configuration**

Your Paystack test API keys are configured:
- **Public Key**: `pk_test_61f52f12e283a8733e00c3531d1a495f2cd3943f`
- **Secret Key**: `sk_test_e489adf2603650e51a685c403fd92bb249323374`

## 💳 **Test Cards**

### **Visa Cards**
- **Card Number**: `****************`
- **CVV**: `408`
- **PIN**: `4081`
- **Expiry**: Any future date

### **Mastercard**
- **Card Number**: `****************`
- **CVV**: `564`
- **PIN**: `564`
- **Expiry**: Any future date

### **Verve Cards**
- **Card Number**: `5061460410120223210`
- **CVV**: `780`
- **PIN**: `780`
- **Expiry**: Any future date

## 📱 **M-Pesa Testing**

### **Test Phone Numbers**
For M-Pesa testing in Kenya, use these test phone numbers:
- **Phone**: `************` (format: *********)
- **Phone**: `************` (format: *********)
- **Phone**: `************` (format: *********)

### **M-Pesa Test Flow**
1. Select **M-Pesa** as payment method
2. Enter any of the test phone numbers above
3. Paystack will simulate the M-Pesa prompt
4. Use **PIN**: `1234` for test transactions
5. Transaction will be marked as successful

### **M-Pesa Amount Limits**
- **Minimum**: KSh 1
- **Maximum**: KSh 70,000
- **Test Range**: KSh 10 - KSh 1,000 (recommended)

## 🏦 **Bank Transfer Testing**

### **Test Bank Accounts**
- **Bank**: Access Bank
- **Account Number**: `**********`
- **Account Name**: Test Account

### **Other Test Banks**
- **GT Bank**: `**********`
- **Zenith Bank**: `**********`
- **First Bank**: `**********`

## 🔄 **Testing Scenarios**

### **1. Successful Payment Flow**
1. Book a service (KSh 500-1000)
2. Select **Paystack** payment method
3. Choose **Card** payment
4. Use test card details above
5. Enter PIN when prompted
6. Verify successful payment

### **2. M-Pesa Payment Flow**
1. Book a service (KSh 100-500)
2. Select **M-Pesa** payment method
3. Enter test phone number
4. Wait for M-Pesa prompt simulation
5. Use PIN: `1234`
6. Verify successful payment

### **3. Bank Transfer Flow**
1. Book a service (KSh 1000-2000)
2. Select **Bank Transfer** payment method
3. Choose bank and enter account details
4. Complete transfer simulation
5. Verify successful payment

### **4. Failed Payment Testing**
- Use invalid card numbers
- Enter wrong PIN multiple times
- Use expired cards
- Test with insufficient funds

## 📱 **Mobile Testing**

### **Responsive Design**
- Test on mobile devices (320px - 768px)
- Verify payment forms are mobile-friendly
- Check touch targets are adequate
- Test keyboard navigation

### **Mobile-Specific Features**
- **M-Pesa**: Optimized for mobile M-Pesa app
- **USSD**: Test USSD code generation
- **QR Codes**: Verify QR code scanning
- **Touch Payments**: Test biometric authentication

## 🛠 **Troubleshooting**

### **Common Issues**

#### **1. "Invalid API Key" Error**
- Verify API keys in `.env` file
- Check for extra spaces or characters
- Ensure keys are for test environment

#### **2. "Transaction Failed" Error**
- Check internet connection
- Verify amount is within limits
- Ensure all required fields are filled
- Check Paystack dashboard for errors

#### **3. M-Pesa "Phone Number Invalid"**
- Ensure phone number starts with country code
- Check number format (10 digits for Kenya)
- Verify number is registered with M-Pesa

#### **4. "Bank Account Not Found"**
- Use test account numbers provided
- Verify bank code is correct
- Check account number format

### **Debug Steps**
1. Check browser console for errors
2. Verify network requests in DevTools
3. Check Paystack dashboard for transaction logs
4. Review server logs for backend errors

## 🔒 **Security Testing**

### **Input Validation**
- Test with SQL injection attempts
- Try XSS payloads in form fields
- Test with very large amounts
- Verify phone number validation

### **API Security**
- Test with invalid API keys
- Try accessing endpoints without authentication
- Verify CORS settings
- Test rate limiting

## 📊 **Performance Testing**

### **Load Testing**
- Test with multiple concurrent payments
- Verify response times under load
- Check memory usage during transactions
- Test with slow network connections

### **Error Handling**
- Test network timeouts
- Verify graceful error handling
- Check retry mechanisms
- Test with invalid data

## 🎯 **Test Checklist**

### **Payment Methods**
- [ ] Card payments (Visa, Mastercard, Verve)
- [ ] M-Pesa mobile money
- [ ] Bank transfers
- [ ] USSD payments
- [ ] QR code payments

### **User Experience**
- [ ] Payment form validation
- [ ] Loading states
- [ ] Success/failure messages
- [ ] Mobile responsiveness
- [ ] Accessibility features

### **Integration**
- [ ] Booking flow integration
- [ ] Payment confirmation
- [ ] Email notifications
- [ ] Transaction history
- [ ] Refund processing

## 🚀 **Production Checklist**

Before going live:
- [ ] Switch to production API keys
- [ ] Update webhook URLs
- [ ] Test with real payment methods
- [ ] Verify compliance requirements
- [ ] Set up monitoring and alerts
- [ ] Configure backup payment methods

## 📞 **Support**

For Paystack support:
- **Email**: <EMAIL>
- **Phone**: +234 1 888 9596
- **Documentation**: https://paystack.com/docs
- **Status Page**: https://status.paystack.com

---

**Note**: This guide covers testing with Paystack's test environment. For production testing, use real payment methods and production API keys. 