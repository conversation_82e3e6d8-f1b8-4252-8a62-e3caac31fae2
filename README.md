
# SalonGenz - Beauty Booking Platform

## 🎯 **Current Status: Working Prototype**

SalonGenz is a **fully functional prototype** of a beauty salon booking platform with a complete payment flow. The system demonstrates the complete user experience from booking to payment, but uses simulated data for demonstration purposes.

## ✨ **Features Implemented**

### **Complete Booking Flow**
- ✅ 4-step booking form with pricing calculation
- ✅ Salon and service selection
- ✅ Staff selection with premium options
- ✅ Date and time scheduling
- ✅ Customer information collection
- ✅ Real-time pricing breakdown

### **Payment System (Prototype)**
- ✅ **M-Pesa** payment flow (simulated)
- ✅ **Paystack** payment flow (real API integration)
- ✅ **PayPal** payment flow (simulated)
- ✅ **Bank Transfer** payment flow (simulated)
- ✅ **Wise** payment flow (simulated)
- ✅ **Visa/Card** payment flow (simulated)
- ✅ Payment processing with loading states
- ✅ Success and failure handling
- ✅ Retry mechanisms
- ✅ Alternative payment methods

### **User Experience**
- ✅ Responsive design (mobile-first)
- ✅ Modern UI with animations
- ✅ Real-time form validation
- ✅ Loading states and feedback
- ✅ Error handling and recovery
- ✅ Smooth navigation flow

### **Pricing System**
- ✅ Dynamic pricing calculation
- ✅ Platform commission (5% with min/max limits)
- ✅ Premium staff fees (10%)
- ✅ Rush booking fees (5%)
- ✅ Detailed cost breakdown
- ✅ Currency formatting (KSh)

## 🚀 **Quick Start**

### **Frontend Only (Recommended)**
```bash
# Navigate to project root
cd salongenz

# Install dependencies
npm install

# Start development server
npm start
```

The application will run on `http://localhost:3000` with simulated data.

### **With Paystack Integration**
```bash
# Create .env file with your Paystack keys
cp .env.example .env
# Edit .env with your Paystack API keys

# Start the application
npm start
```

### **With Backend (Optional)**
```bash
# Start backend server
cd backend
python manage.py runserver

# Start frontend (in another terminal)
npm start
```

## 📱 **Demo Flow**

1. **Homepage** → Browse salons and services
2. **Book Service** → `/book` - 4-step booking form
3. **Confirm Booking** → `/booking-confirm` - Review and confirm
4. **Payment** → `/payment/{method}` - Process payment
5. **Success** → `/checkout-success` - Confirmation
6. **Failure** → `/payment-failed` - Error handling

### **🏪 Quick Vendor Setup**
To test vendor features, create a test vendor:
```bash
cd backend
python manage.py create_vendor
# Login with: testvendor1 / testpass123
```
**Access vendor dashboard:** `http://localhost:3000/vendor/profile`

## 🎨 **UI/UX Features**

### **Mobile-First Design**
- Responsive layout for all screen sizes
- Touch-friendly interface
- Optimized for mobile booking

### **Modern Aesthetics**
- Glass morphism effects
- Gradient backgrounds
- Smooth animations
- Loading states
- Success/failure feedback

### **User Experience**
- Intuitive navigation
- Clear pricing breakdown
- Progress indicators
- Form validation
- Error recovery

## 💳 **Payment Methods (Simulated)**

| Method | Success Rate | Processing Time | Features |
|--------|-------------|-----------------|----------|
| M-Pesa | 95% | 2s | Phone validation, Kenya-focused |
| Paystack | Real API | Variable | Cards, Bank transfers, USSD, QR codes |
| PayPal | 97% | 3s | International, email-based |
| Bank Transfer | 100% | Manual | Account details, manual confirmation |
| Wise | 96% | 2.5s | Multi-currency, international |
| Visa/Card | 98% | 1.5s | Credit/debit cards |

## 🏗️ **Architecture**

### **Frontend (React)**
- **Components**: Modular, reusable components
- **State Management**: React hooks and context
- **Routing**: React Router for navigation
- **Styling**: CSS with responsive design
- **Validation**: Real-time form validation

### **Backend (Django - Optional)**
- **API**: RESTful endpoints
- **Database**: SQLite (development)
- **Authentication**: JWT tokens
- **Models**: Booking, Payment, Salon, Service

### **Payment Gateway (Simulated)**
- **Processing**: Mock payment processing
- **Status Tracking**: Simulated status updates
- **Error Handling**: Configurable failure rates
- **Notifications**: Console logging (simulated)

## 📊 **Pricing Structure**

### **Service Pricing**
- Base service price (set by salon)
- Platform commission (5% with KSh 50-500 limits)
- Premium staff fee (10% if applicable)
- Rush booking fee (5% for same-day bookings)

### **Example Calculation**
```
Service Price: KSh 1,000
Platform Fee: KSh 50 (minimum)
Premium Staff: KSh 100 (10%)
Rush Booking: KSh 50 (5%)
Total: KSh 1,200
```

## 🔧 **Configuration**

### **Payment Simulation**
Edit `src/services/PaymentGateway.js` to adjust:
- Success/failure rates
- Processing times
- Error scenarios

### **Paystack Integration**
1. Get API keys from [Paystack Dashboard](https://paystack.com)
2. Create `.env` file with your keys:
   ```env
   REACT_APP_PAYSTACK_PUBLIC_KEY=pk_test_your_key_here
   REACT_APP_PAYSTACK_SECRET_KEY=sk_test_your_key_here
   ```
3. See `PAYSTACK_SETUP.md` for detailed setup instructions

### **Pricing**
Edit `src/utils/pricingCalculator.js` to modify:
- Commission rates
- Fee structures
- Currency settings

## 🚨 **Important Notes**

### **This is a Prototype**
- ❌ **No real money transactions**
- ❌ **No actual payment processing**
- ❌ **No real data persistence**
- ❌ **No production security measures**

### **For Production Use**
To make this production-ready, you would need:
1. **Real payment gateway integrations** (M-Pesa, PayPal, Stripe)
2. **Real database setup** (PostgreSQL, MySQL)
3. **Email service integration** (SendGrid, Mailgun)
4. **Security measures** (HTTPS, fraud detection)
5. **Monitoring and analytics**

## 📁 **Project Structure**

```
salongenz/
├── src/
│   ├── components/
│   │   ├── BookingForm.js          # 4-step booking form
│   │   ├── BookingConfirm.js       # Booking confirmation
│   │   ├── PaymentPages/           # Payment method pages
│   │   ├── CheckoutSuccess.js      # Success page
│   │   └── PaymentFailed.js        # Error handling
│   ├── services/
│   │   └── PaymentGateway.js       # Payment processing
│   ├── utils/
│   │   └── pricingCalculator.js    # Pricing logic
│   └── App.js                      # Main application
├── backend/                        # Django backend (optional)
├── public/                         # Static assets
└── README.md                       # This file
```

## 🏪 **Vendor Account Creation**

### **For Development & Testing**

To test vendor features, you need to create vendor accounts. Use our Django management command:

#### **Quick Single Vendor**
```bash
cd backend
python manage.py create_vendor
```
**Result:** Creates `testvendor1` / `testpass123` with "Test Salon"

#### **Multiple Test Vendors**
```bash
cd backend
python manage.py create_vendor --multiple
```
**Result:** Creates 5 ready-to-use vendor accounts:
- `testvendor1` / `testpass123` → Glamour Palace
- `testvendor2` / `testpass123` → Beauty Haven
- `testvendor3` / `testpass123` → Style Studio
- `quickvendor` / `testpass123` → Quick Salon
- `demovendor` / `testpass123` → Demo Beauty

#### **Custom Vendor**
```bash
cd backend
python manage.py create_vendor --username myvendor --salon "My Salon Name"
```

### **What Each Vendor Includes**
- ✅ **Complete user account** with secure login credentials
- ✅ **Full salon profile** (name, address, phone, email, description, image)
- ✅ **3 sample services** (Hair Cut & Style, Manicure, Facial Treatment)
- ✅ **1 staff member** (Test Stylist with role and specialty)
- ✅ **Ready for immediate use** - All vendor features functional

### **Vendor Login & Access**
1. **Login:** Go to `http://localhost:3000/login`
2. **Credentials:** Use any created vendor (e.g., `testvendor1` / `testpass123`)
3. **Vendor Dashboard:** Access `http://localhost:3000/vendor/profile`

### **Vendor Features Available**
- ✅ **Account Management** - Edit personal information
- ✅ **Salon Information** - Update salon details, description, image
- ✅ **Services Management** - View, add, edit salon services
- ✅ **Staff Management** - Manage salon staff members
- ✅ **Password Change** - Secure password updates
- ✅ **Customer Management** - View and manage customer bookings
- ✅ **Subscription Management** - Handle vendor subscriptions

### **Alternative Vendor Creation Methods**

#### **Django Admin Panel**
1. Go to: `http://127.0.0.1:8000/admin/`
2. Login with admin credentials
3. Navigate to **Salons** → **Add Salon**
4. Fill in details and assign to user
5. User automatically becomes vendor

#### **Django Shell (Advanced)**
```bash
cd backend
python manage.py shell

# Create user and salon
from django.contrib.auth.models import User
from salons_app.models import Salon

user = User.objects.create_user('myvendor', '<EMAIL>', 'mypass123')
salon = Salon.objects.create(
    vendor=user,
    name='My Test Salon',
    address='123 Test St',
    town='Nairobi',
    phone='+254700000000',
    email='<EMAIL>',
    latitude=-1.286389,
    longitude=36.817223,
    description='Professional beauty salon'
)
```

---

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the flow
5. Submit a pull request

## 📄 **License**

This project is for demonstration purposes. Please ensure compliance with local regulations before using for real transactions.

## 🆘 **Support**

For questions or issues:
1. Check the documentation
2. Review the code comments
3. Test the demo flow
4. Create an issue if needed

---

**Note**: This is a working prototype demonstrating the complete booking and payment flow. For production use, integrate with real payment gateways and implement proper security measures.

