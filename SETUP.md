# 🏪 Salon Management System Setup Guide

## 🎯 Quick Start Options

### Option 1: Docker (Recommended for Consistency)
```powershell
# Start with Docker
.\docker-start.ps1
```

### Option 2: Local Development
```powershell
# Setup and start locally
.\setup-salon.ps1
```

## 📋 Prerequisites

### For Local Development:
- **Python 3.11+** with pip
- **Node.js 16.x or 18.x LTS** (NOT 24.x - compatibility issues)
- **Git**

### For Docker:
- **Docker Desktop** installed and running

## 🔧 Node.js Version Compatibility

**⚠️ IMPORTANT:** This project uses `react-scripts@5.0.1` which requires:
- ✅ **Node.js 16.x LTS** (Recommended)
- ✅ **Node.js 18.x LTS** (Recommended) 
- ❌ **Node.js 20.x+** (May have compatibility issues)
- ❌ **Node.js 24.x** (Known compatibility issues)

### Check Your Node.js Version:
```bash
node --version
```

### Install Compatible Node.js:
1. Download Node.js 18.x LTS from [nodejs.org](https://nodejs.org)
2. Or use Node Version Manager (nvm):
   ```bash
   nvm install 18
   nvm use 18
   ```

## 🚀 Manual Setup Steps

### Backend Setup:
```powershell
# 1. Create and activate virtual environment
python -m venv salonvenv
.\salonvenv\Scripts\Activate.ps1

# 2. Install dependencies
pip install -r backend\requirements.txt

# 3. Run migrations
python backend\manage.py migrate

# 4. Start backend server
python backend\manage.py runserver
```

### Frontend Setup:
```powershell
# 1. Clean npm cache
npm cache clean --force

# 2. Install dependencies
npm install --legacy-peer-deps

# 3. Fix React dependencies (if needed)
node fix-react-deps.js

# 4. Start frontend server
npm start
```

## 🌐 Access Points

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8000
- **Admin Panel:** http://localhost:8000/admin
  - Username: `admin`
  - Password: `aluru742!!`

## 🐳 Docker Commands

```powershell
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild containers
docker-compose up --build

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend sh
```

## 🔍 Troubleshooting

### React Won't Start:
1. Check Node.js version: `node --version`
2. Use Node.js 16 or 18 LTS
3. Clear cache: `npm cache clean --force`
4. Reinstall: `rm -rf node_modules && npm install --legacy-peer-deps`
5. Run fix script: `node fix-react-deps.js`

### Backend Issues:
1. Activate virtual environment: `.\salonvenv\Scripts\Activate.ps1`
2. Install requirements: `pip install -r backend\requirements.txt`
3. Run migrations: `python backend\manage.py migrate`

### Docker Issues:
1. Ensure Docker Desktop is running
2. Check available ports (3000, 8000)
3. Restart Docker: `docker-compose down && docker-compose up`

## 📁 Project Structure

```
salonpj/
├── backend/                 # Django backend
│   ├── salon_backend/      # Main Django project
│   ├── accounts/           # User management
│   ├── salons/            # Salon management
│   ├── bookings/          # Booking system
│   └── socials/           # Social features
├── src/                    # React frontend
├── public/                 # Static files
├── docker-compose.yml      # Docker configuration
├── Dockerfile             # Frontend Docker image
└── backend/Dockerfile     # Backend Docker image
```

## 🎯 Features Available

✅ **User Management** - Registration, login, profiles
✅ **Salon Management** - CRUD operations, search, filtering  
✅ **Booking System** - Appointments, scheduling, notifications
✅ **Social Features** - Comments, likes, follows, feed
✅ **Admin Panel** - Full administrative interface
✅ **API Endpoints** - RESTful API for all features
✅ **Responsive UI** - Mobile-friendly interface

## 🆘 Need Help?

If you encounter issues:
1. Check this troubleshooting guide
2. Verify Node.js version compatibility
3. Try Docker approach for consistent environment
4. Check container logs: `docker-compose logs`
