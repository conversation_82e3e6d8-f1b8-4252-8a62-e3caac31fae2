# Paystack M-Pesa Testing Guide

## 🧪 Test Files Overview

### 1. `test_paystack_mpesa.html`
**Purpose**: Standalone Paystack M-Pesa testing interface
**Usage**: Open directly in browser, no server required
**Features**:
- Real-time payment testing
- Demo mode support
- Interactive UI
- Payment status tracking

**How to Use**:
```bash
# Open in browser
file:///path/to/salon/test_paystack_mpesa.html
```

### 2. `test_paystack_integration.py`
**Purpose**: Automated backend API testing
**Usage**: Command line testing script
**Features**:
- Backend endpoint validation
- Payment flow testing
- Configuration verification
- Automated test suite

**How to Use**:
```bash
# Run the test script
./salonvenv/Scripts/python.exe test_paystack_integration.py
```

### 3. `test_mpesa_payment.py`
**Purpose**: Complete M-Pesa payment flow testing
**Usage**: End-to-end payment testing
**Features**:
- Payment initiation
- Callback simulation
- Payment verification
- Status checking

**How to Use**:
```bash
# Run payment flow test
./salonvenv/Scripts/python.exe test_mpesa_payment.py
```

### 4. `test_payment.html`
**Purpose**: Alternative payment testing interface
**Usage**: Browser-based testing
**Features**:
- Multiple payment methods
- Step-by-step testing
- JSON response display
- Error handling

## 📱 Test Phone Numbers

### Primary Test Number
- **Number**: `700000000`
- **Purpose**: Demo mode activation
- **Behavior**: Automatic success in demo mode

### Paystack Official Test Numbers
- **M-Pesa**: `+254 710 000 000`
- **MTN**: `055 123 498 7`
- **Purpose**: Real Paystack testing

## 🔧 Test Scenarios

### Scenario 1: Demo Mode Testing
**Objective**: Test without real Paystack keys
**Steps**:
1. Use placeholder keys in environment
2. Open test page
3. Enter phone: `700000000`
4. Click payment button
5. Verify demo flow

**Expected Result**: Demo payment simulation

### Scenario 2: Real Paystack Testing
**Objective**: Test with actual Paystack keys
**Steps**:
1. Update environment with real keys
2. Restart servers
3. Open test page
4. Enter test phone number
5. Complete Paystack flow

**Expected Result**: Real Paystack popup

### Scenario 3: Backend API Testing
**Objective**: Test backend endpoints
**Steps**:
1. Run integration test script
2. Check endpoint responses
3. Verify payment creation
4. Test verification flow

**Expected Result**: All endpoints working

## 🎯 Test Checklist

### Frontend Tests
- [ ] Paystack script loads
- [ ] Demo mode activates correctly
- [ ] Real mode opens Paystack popup
- [ ] Phone number validation works
- [ ] Amount processing correct
- [ ] Currency selection functional
- [ ] Error handling works
- [ ] Success navigation works

### Backend Tests
- [ ] Payment initiation endpoint
- [ ] Payment verification endpoint
- [ ] Callback handling endpoint
- [ ] Status checking endpoint
- [ ] Database record creation
- [ ] Payment logging works
- [ ] Error responses correct

### Integration Tests
- [ ] Frontend-backend communication
- [ ] Environment variable loading
- [ ] Demo mode detection
- [ ] Real key processing
- [ ] Payment flow completion
- [ ] Success page navigation

## 🚨 Common Test Issues

### Issue: "Please enter a valid Key"
**Cause**: Invalid Paystack public key
**Solution**: Use real test keys or verify demo mode

### Issue: "Payment not found"
**Cause**: Demo payment not in database
**Solution**: Expected behavior for demo mode

### Issue: Popup blocked
**Cause**: Browser popup blocker
**Solution**: Disable popup blocker for test domain

### Issue: Server not responding
**Cause**: Django server not running
**Solution**: Start Django server on port 8000

## 📊 Test Results Format

### Successful Test Output
```
✅ Paystack script loaded successfully!
✅ Demo mode activated
✅ Payment simulation completed
✅ Mock response generated
✅ Navigation successful
```

### Failed Test Output
```
❌ Error: Invalid Paystack key
❌ Payment failed: Network error
❌ Verification failed: Payment not found
```

## 🔄 Test Automation

### Continuous Testing
```bash
# Run all tests
./run_all_tests.sh

# Run specific test
./salonvenv/Scripts/python.exe test_paystack_integration.py
```

### Test Environment Setup
```bash
# Set test environment
export PAYSTACK_PUBLIC_KEY=pk_test_...
export PAYSTACK_SECRET_KEY=sk_test_...

# Run tests
python test_mpesa_payment.py
```

## 📈 Test Metrics

### Performance Targets
- **Payment Initiation**: < 2 seconds
- **Demo Simulation**: 2-3 seconds
- **Real Payment**: < 10 seconds
- **Verification**: < 1 second

### Success Criteria
- **Demo Mode**: 100% success rate
- **Real Mode**: Depends on Paystack
- **API Endpoints**: 100% availability
- **Error Handling**: Graceful failures

## 🛠️ Test Maintenance

### Regular Updates
- Update test phone numbers
- Refresh API keys
- Update test scenarios
- Maintain documentation

### Version Control
- Keep all test files in repository
- Document test changes
- Tag test versions
- Maintain test history

---

**Test Suite Version**: 1.0.0
**Last Updated**: July 28, 2025
**Compatibility**: Paystack API v1
