# 🔧 Vendor Payment Bypass Control Documentation

This guide explains how to enable, disable, and control the vendor payment bypass system for development and testing purposes.

## 🎯 **Quick Control Methods**

### **Method 1: Environment Variable Control (Recommended)**

**Enable Bypass:**
```bash
# In .env file
REACT_APP_BYPASS_VENDOR_PAYMENT=true
```

**Disable Bypass:**
```bash
# In .env file
REACT_APP_BYPASS_VENDOR_PAYMENT=false
# OR comment out the line:
# REACT_APP_BYPASS_VENDOR_PAYMENT=true
# OR delete the line entirely
```

**Restart Required:** After changing `.env`, restart React server:
```bash
npm start
```

---

### **Method 2: Test Phone Number Control**

**Enable Bypass:**
- Use phone number: `700000000` or `+************` during vendor registration

**Disable Bypass:**
- Use any other phone number during vendor registration

**No Restart Required:** Works immediately

---

## 🔒 **Production Safety Controls**

### **Automatic Production Disable**

The bypass is **automatically disabled** in production because:

```javascript
const isDevelopment = process.env.NODE_ENV === 'development';
// Only works when NODE_ENV === 'development'
```

**Production builds automatically set `NODE_ENV=production`**, so bypass never works in production.

### **Manual Production Disable**

For extra safety, you can force disable in any environment:

```bash
# In .env file - Force disable regardless of environment
REACT_APP_BYPASS_VENDOR_PAYMENT=false
REACT_APP_FORCE_DISABLE_BYPASS=true
```

---

## 📋 **Complete Control Configuration**

### **Current .env Configuration**

Your current `.env` file should look like this:

```bash
# React Frontend Environment Variables
# ESLint Suppression (Create React App)
DISABLE_ESLINT_PLUGIN=true
GENERATE_SOURCEMAP=true
SKIP_PREFLIGHT_CHECK=true

# IPinfo API Configuration (for geolocation services)
REACT_APP_IPINFO_API_KEY=082682c8d0e7a8

# AI API Configuration (for AI features)
REACT_APP_OPENAI_API_KEY=demo_key
REACT_APP_MISTRAL_API_KEY=1sYV0SNZ2mr4SFyInS9AqUjmnk8Fu9cw
REACT_APP_GROQ_API_KEY=********************************************************
REACT_APP_AI_BASE_URL=https://api.openai.com/v1
REACT_APP_AI_MODEL=gpt-3.5-turbo

# Paystack Configuration (for payment processing)
REACT_APP_PAYSTACK_PUBLIC_KEY=pk_live_22d10798ff283975b275fc96824e6325ca901a0c

# Development Settings - Vendor Payment Bypass
REACT_APP_BYPASS_VENDOR_PAYMENT=true
```

### **Control Options**

**Option 1: Enable Bypass**
```bash
REACT_APP_BYPASS_VENDOR_PAYMENT=true
```

**Option 2: Disable Bypass**
```bash
REACT_APP_BYPASS_VENDOR_PAYMENT=false
```

**Option 3: Comment Out (Disable)**
```bash
# REACT_APP_BYPASS_VENDOR_PAYMENT=true
```

**Option 4: Remove Line (Disable)**
```bash
# Just delete the entire line
```

---

## 🎮 **Testing Scenarios**

### **Scenario 1: Development with Bypass**
```bash
# .env
REACT_APP_BYPASS_VENDOR_PAYMENT=true
```
**Result:** Shows both payment and bypass buttons

### **Scenario 2: Development without Bypass**
```bash
# .env
REACT_APP_BYPASS_VENDOR_PAYMENT=false
# OR comment out the line
```
**Result:** Shows only payment button (like production)

### **Scenario 3: Test Phone Override**
```bash
# .env - even if bypass is disabled
REACT_APP_BYPASS_VENDOR_PAYMENT=false
```
**But use phone:** `700000000`
**Result:** Shows both payment and bypass buttons

### **Scenario 4: Production**
```bash
# Any .env setting
REACT_APP_BYPASS_VENDOR_PAYMENT=true
```
**But NODE_ENV=production**
**Result:** Shows only payment button (bypass disabled)

---

## 🔍 **Visual Indicators**

### **When Bypass is Available**
```
┌─────────────────────────────────────┐
│  Pay KSh 500 - Start Subscription  │ ← Real Payment
└─────────────────────────────────────┘

              ──── OR ────

┌─────────────────────────────────────┐
│  🚀 Skip Payment (Development Mode) │ ← Bypass
└─────────────────────────────────────┘
     For testing purposes only
```

### **When Bypass is Disabled**
```
┌─────────────────────────────────────┐
│  Pay KSh 500 - Start Subscription  │ ← Only Option
└─────────────────────────────────────┘

🔒 Secure payment via Paystack
```

---

## 🛠️ **Advanced Control (Code Level)**

### **Temporary Disable in Code**

If you want to temporarily disable bypass without changing `.env`:

```javascript
// In VendorSubscription.js, modify the isPaymentBypassAvailable function:

const isPaymentBypassAvailable = () => {
  // Temporary disable - uncomment next line to force disable
  // return false;
  
  if (!subscriptionData) return false;
  const isDevelopment = process.env.NODE_ENV === 'development';
  const bypassPayment = process.env.REACT_APP_BYPASS_VENDOR_PAYMENT === 'true';
  const isTestPhone = subscriptionData.phone === '700000000' || subscriptionData.phone === '+************';
  return isDevelopment && (bypassPayment || isTestPhone);
};
```

### **Force Enable for Testing**

```javascript
// Force enable bypass for testing (REMOVE BEFORE PRODUCTION)
const isPaymentBypassAvailable = () => {
  // Force enable - uncomment next line for testing
  // return true;
  
  // ... rest of function
};
```

---

## 📝 **Quick Reference Commands**

### **Enable Bypass**
```bash
# Edit .env file
echo "REACT_APP_BYPASS_VENDOR_PAYMENT=true" >> .env
npm start
```

### **Disable Bypass**
```bash
# Edit .env file - set to false or comment out
# Then restart
npm start
```

### **Check Current Status**
```bash
# Check if bypass is enabled
grep "REACT_APP_BYPASS_VENDOR_PAYMENT" .env
```

### **Verify in Browser**
1. Open Developer Console (F12)
2. Look for: `🚀 Development Mode: Bypassing vendor payment`
3. Check button text on payment page

---

## ⚠️ **Important Notes**

### **Always Remember:**
1. **Restart React server** after changing `.env`
2. **Bypass only works in development** (`NODE_ENV=development`)
3. **Test both scenarios** (with and without bypass)
4. **Remove bypass settings** before production deployment

### **Safety Checklist:**
- [ ] Bypass disabled in production builds
- [ ] Environment variables properly set
- [ ] React server restarted after changes
- [ ] Both payment flows tested
- [ ] Console logs checked for bypass activation

---

## 🚀 **Quick Start**

**To Enable Bypass Right Now:**
1. Open `.env` file
2. Ensure line exists: `REACT_APP_BYPASS_VENDOR_PAYMENT=true`
3. Save file
4. Restart: `npm start`
5. Test vendor registration

**To Disable Bypass Right Now:**
1. Open `.env` file
2. Change to: `REACT_APP_BYPASS_VENDOR_PAYMENT=false`
3. Save file
4. Restart: `npm start`
5. Only payment button will show

**Perfect control at your fingertips!** 🎯
