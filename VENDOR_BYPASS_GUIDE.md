# 🚀 Vendor Payment Bypass Guide

This guide shows you **4 different methods** to register vendors without making payments, perfect for development and testing.

## 🎯 **Method 1: Magic Test Phone Number (Easiest)**

**Steps:**
1. Go to vendor registration: `http://localhost:3000/register-vendor`
2. Fill in all salon details normally
3. Select any subscription plan and listing tier
4. When prompted for payment, use phone number: `*********`
5. The system will automatically simulate successful payment
6. Vendor account will be created instantly

**Why it works:** The backend has built-in test logic for phone number `*********`

---

## 🔧 **Method 2: Environment Variable Bypass**

**Setup:**
1. Create/edit `.env` file in your project root:
```bash
REACT_APP_BYPASS_VENDOR_PAYMENT=true
```

2. Restart your React development server:
```bash
npm start
```

**Usage:**
1. Go through normal vendor registration
2. System will automatically bypass payment in development mode
3. Shows alert: "Development Mode: Vendor registration bypassed!"
4. Redirects directly to vendor profile

---

## 🛠️ **Method 3: Direct Backend Creation (Most Reliable)**

**Option A: Using the Python Script**
```bash
# From project root directory
python create_test_vendors.py
```

This creates 5 test vendors:
- **Username:** testvendor1, testvendor2, testvendor3, testvendor4, testvendor5
- **Password:** testpass123
- **Salons:** Glamour Palace, Beauty Haven, Style Studio, Elegance Salon, Chic Boutique

**Option B: Django Admin Panel**
1. Start backend: `python manage.py runserver`
2. Go to: `http://127.0.0.1:8000/admin/`
3. Login with admin credentials
4. Navigate to **Salons** → **Add Salon**
5. Fill details and assign to any user
6. User automatically becomes vendor

**Option C: Direct API Calls**
```bash
# Create user
curl -X POST http://127.0.0.1:8000/api/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "myvendor",
    "password": "mypass123",
    "email": "<EMAIL>"
  }'

# Create salon (replace vendor ID)
curl -X POST http://127.0.0.1:8000/api/salons/ \
  -H "Content-Type: application/json" \
  -d '{
    "vendor": 1,
    "name": "My Test Salon",
    "address": "123 Main St",
    "town": "Nairobi",
    "phone": "+254*********",
    "email": "<EMAIL>",
    "latitude": -1.286389,
    "longitude": 36.817223,
    "description": "My test salon description"
  }'
```

---

## 🎮 **Method 4: Quick Manual Creation**

**For immediate testing:**

1. **Create User Account:**
   - Go to: `http://localhost:3000/signup`
   - Create any user account
   - Remember the username

2. **Use Django Shell:**
```bash
# In backend directory
python manage.py shell

# In Django shell:
from django.contrib.auth.models import User
from salons_app.models import Salon

# Get your user
user = User.objects.get(username='your_username')

# Create salon
salon = Salon.objects.create(
    vendor=user,
    name='Quick Test Salon',
    address='123 Test St',
    town='Nairobi',
    phone='+254*********',
    email='<EMAIL>',
    latitude=-1.286389,
    longitude=36.817223,
    description='Quick test salon'
)

print(f"Salon created: {salon.name}")
```

---

## 🧪 **Testing Your Vendor Account**

After creating a vendor account using any method:

1. **Login:** `http://localhost:3000/login`
2. **Access Vendor Profile:** `http://localhost:3000/vendor/profile`
3. **Check Features:**
   - Account tab: Edit personal info
   - Salon tab: Edit salon details
   - Services tab: Manage services
   - Staff tab: Manage staff
   - Password tab: Change password

---

## 🔍 **Verification**

**Check if vendor was created successfully:**

1. **Frontend Check:**
   - Login with vendor credentials
   - Navigation should show "Vendor Profile" instead of "Vendor Signup"
   - Welcome message should show vendor name

2. **Backend Check:**
```bash
# Django shell
python manage.py shell

# Check vendor
from salons_app.models import Salon
salons = Salon.objects.all()
for salon in salons:
    print(f"Salon: {salon.name}, Vendor: {salon.vendor.username}")
```

3. **Database Check:**
   - Admin panel: `http://127.0.0.1:8000/admin/salons_app/salon/`
   - Should see your salon listed

---

## 🚨 **Troubleshooting**

**Issue: "No vendor profile found"**
- Solution: Make sure salon is assigned to the correct user

**Issue: "Permission denied"**
- Solution: Check user has salon associated with their account

**Issue: "Environment variable not working"**
- Solution: Restart React server after adding `.env` file

**Issue: "API calls failing"**
- Solution: Make sure Django backend is running on port 8000

---

## 🎯 **Recommended Method**

For **quick testing**: Use **Method 1** (Magic phone number `*********`)

For **development**: Use **Method 2** (Environment variable bypass)

For **reliable testing**: Use **Method 3** (Python script or Django admin)

---

## 📝 **Notes**

- All methods create fully functional vendor accounts
- Vendors can access all features: profile editing, services, staff, customers
- No payment processing required
- Perfect for development and testing
- Can create unlimited test vendors

---

## 🔧 **Quick Control Commands**

**Enable Bypass:**
```bash
node toggle_bypass.js enable
```

**Disable Bypass:**
```bash
node toggle_bypass.js disable
```

**Check Status:**
```bash
node toggle_bypass.js status
```

**Manual Control:**
```bash
# Edit .env file
REACT_APP_BYPASS_VENDOR_PAYMENT=true   # Enable
REACT_APP_BYPASS_VENDOR_PAYMENT=false  # Disable
# REACT_APP_BYPASS_VENDOR_PAYMENT=true # Comment out to disable
```

**Always restart React server after changes:**
```bash
npm start
```

**Happy testing! 🚀**
