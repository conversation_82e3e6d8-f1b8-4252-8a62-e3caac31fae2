# 🚀 Vendor Creation - Quick Reference

## ⚡ **One-Command Setup**

```bash
cd backend
python manage.py create_vendor
```
**Result:** `testvendor1` / `testpass123` ready to use!

---

## 🎯 **Quick Commands**

| Command | Result |
|---------|--------|
| `python manage.py create_vendor` | Single vendor: testvendor1 |
| `python manage.py create_vendor --multiple` | 5 test vendors |
| `python manage.py create_vendor --username myname --salon "My Salon"` | Custom vendor |

---

## 🔑 **Ready-to-Use Accounts**

| Username | Password | Salon Name |
|----------|----------|------------|
| testvendor1 | testpass123 | Glamour Palace |
| testvendor2 | testpass123 | Beauty Haven |
| testvendor3 | testpass123 | Style Studio |
| quickvendor | testpass123 | Quick Salon |
| demovendor | testpass123 | Demo Beauty |

---

## 🔗 **Access URLs**

- **Login:** `http://localhost:3000/login`
- **Vendor Dashboard:** `http://localhost:3000/vendor/profile`
- **Admin Panel:** `http://127.0.0.1:8000/admin/`

---

## ✅ **What You Get**

Each vendor includes:
- ✅ User account with login
- ✅ Complete salon profile
- ✅ 3 sample services
- ✅ 1 staff member
- ✅ All vendor features working

---

## 🎮 **Test Workflow**

1. **Create:** `cd backend && python manage.py create_vendor`
2. **Login:** Go to `http://localhost:3000/login`
3. **Use:** `testvendor1` / `testpass123`
4. **Access:** `http://localhost:3000/vendor/profile`
5. **Test:** All vendor features available

---

## 🛠️ **Alternative Methods**

### Django Admin
1. Go to `http://127.0.0.1:8000/admin/`
2. Salons → Add Salon
3. Assign to user

### Django Shell
```bash
python manage.py shell
from django.contrib.auth.models import User
from salons_app.models import Salon
user = User.objects.create_user('vendor', 'email', 'pass')
salon = Salon.objects.create(vendor=user, name='Salon', ...)
```

---

**🚀 Ready in seconds - Test all vendor features immediately!**
