import os
import json
import logging
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import hashlib
import pickle
from pathlib import Path
import glob
import random
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# East African Beauty & Hair Culture Data
EAST_AFRICAN_HAIR_STYLES = {
    'traditional': [
        'Bantu Knots', 'Fulani Braids', 'Maasai Braids', 'Kikuyu Twists',
        'Ethiopian Braids', 'Somali Braids', 'Rwandan Crown Braids',
        'Ugandan Goddess Braids', 'Tanzanian Tribal Braids', 'Kenyan Cornrows',
        'Luo Traditional Braids', 'Kalenjin Warrior Braids', 'Luhya Crown Style'
    ],
    'modern_african': [
        'Afro Puff', 'Twist Out', 'Bantu Knot Out', 'Protective Braids',
        'African Threading', 'Loc Styles', 'Natural Afro', 'Finger Coils',
        'Flat Twist', 'Chunky Twists', 'Mini Twists', 'Senegalese Twists',
        'Kinyozi Cut', 'Nairobi Natural', 'Kampala Curls', 'Addis Waves'
    ],
    'fusion': [
        'Afro-Bohemian Braids', 'Modern Fulani', 'Contemporary Bantu',
        'Urban Locs', 'Afro-Chic Bob', 'Natural Silk Press', 'Textured Pixie',
        'African-Inspired Updo', 'Braided Crown', 'Twisted Halo',
        'East African Goddess', 'Safari Chic', 'Savanna Waves', 'Rift Valley Curls'
    ],
    'western_fusion': [
        'Afro-European Bob', 'Textured Layers', 'Curly Shag', 'Natural Blowout',
        'Protective Ponytail', 'Twist and Curl', 'Afro-Pixie Cut'
    ]
}

EAST_AFRICAN_BEAUTY_TERMS = {
    'swahili': {
        'beautiful': 'mzuri', 'hair': 'nywele', 'style': 'mtindo',
        'natural': 'asili', 'elegant': 'maridadi', 'gorgeous': 'mrembo',
        'queen': 'malkia', 'sister': 'dada', 'stunning': 'mzuri sana',
        'gift': 'zawadi', 'love': 'upendo', 'friend': 'rafiki',
        'time': 'wakati', 'perfect': 'kamili', 'special': 'maalum',
        'celebration': 'sherehe', 'joy': 'furaha', 'blessing': 'baraka'
    },
    'amharic': {
        'beautiful': 'konjo', 'hair': 'suf', 'style': 'design',
        'natural': 'tebeyawi', 'elegant': 'siltane', 'gorgeous': 'betam konjo',
        'gift': 'silt', 'love': 'fikir', 'friend': 'guadegna'
    },
    'local_expressions': [
        'slay queen', 'melanin queen', 'natural goddess', 'African queen',
        'beautiful sister', 'stunning mama', 'gorgeous sis', 'radiant beauty',
        'mrembo', 'dada mzuri', 'natural malkia', 'East African beauty'
    ],
    'gift_expressions': [
        'zawadi ya upendo', 'special treat', 'pamper session', 'self-care zawadi',
        'beauty blessing', 'mrembo moment', 'queen treatment', 'dada special'
    ],
    'scheduling_expressions': [
        'wakati kamili', 'perfect timing', 'mzuri time', 'blessed appointment',
        'special moment', 'pamper wakati', 'beauty time', 'self-care session'
    ],
    'salon_expressions': [
        'mzuri salon', 'perfect place', 'beauty haven', 'pamper paradise',
        'mrembo destination', 'queen palace', 'dada spot', 'natural sanctuary'
    ]
}

REGIONAL_HAIR_CHARACTERISTICS = {
    'kenyan': {
        'texture': ['4c texture', 'thick density', 'coily pattern', 'shrinkage prone'],
        'climate': 'equatorial humidity',
        'preferences': ['protective styles', 'natural looks', 'braided styles']
    },
    'ethiopian': {
        'texture': ['3c-4a texture', 'fine to medium', 'defined curls', 'versatile'],
        'climate': 'highland dry',
        'preferences': ['traditional braids', 'natural curls', 'elegant updos']
    },
    'ugandan': {
        'texture': ['4a-4c texture', 'high density', 'strong strands', 'moisture loving'],
        'climate': 'tropical humid',
        'preferences': ['goddess braids', 'protective twists', 'natural styles']
    },
    'tanzanian': {
        'texture': ['3b-4b texture', 'medium density', 'spiral curls', 'heat sensitive'],
        'climate': 'coastal humid',
        'preferences': ['tribal braids', 'twist outs', 'beachy waves']
    },
    'rwandan': {
        'texture': ['4a-4c texture', 'thick strands', 'tight coils', 'protective styling'],
        'climate': 'highland cool',
        'preferences': ['crown braids', 'elegant twists', 'traditional styles']
    }
}

class AIService:
    def __init__(self):
        # Define AI providers in priority order (best to fallback)
        self.ai_providers = [
            {
                'name': 'groq',
                'api_key': os.getenv('GROQ_API_KEY'),
                'api_url': 'https://api.groq.com/openai/v1/chat/completions',
                'model': 'llama3-8b-8192',
                'priority': 1
            },
            {
                'name': 'mistral',
                'api_key': os.getenv('MISTRAL_API_KEY'),
                'api_url': 'https://api.mistral.ai/v1/chat/completions',
                'model': 'mistral-small-latest',
                'priority': 2
            },
            {
                'name': 'openai',
                'api_key': os.getenv('OPENAI_API_KEY'),
                'api_url': 'https://api.openai.com/v1/chat/completions',
                'model': 'gpt-3.5-turbo',
                'priority': 3
            },
            {
                'name': 'gemini',
                'api_key': os.getenv('GEMINI_API_KEY'),
                'api_url': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
                'model': 'gemini-pro',
                'priority': 4
            }
        ]

        # Filter to only available providers (with API keys)
        self.available_providers = [p for p in self.ai_providers if p['api_key']]
        logger.info(f"Available AI providers: {[p['name'] for p in self.available_providers]}")

        # Cache setup
        base_dir = Path(__file__).resolve().parent
        self.cache_dir = base_dir / "cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_duration = timedelta(hours=6)  # 6 hours TTL as requested
    
    def _get_cache_key(self, prompt: str, feature: str) -> str:
        """Generate a cache key for the given prompt and feature"""
        content = f"{feature}:{prompt}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """Get the cache file path for a given key"""
        return self.cache_dir / f"{cache_key}.pkl"
    
    def _load_from_cache(self, cache_key: str) -> Optional[Dict]:
        """Load response from cache if available and not expired"""
        cache_path = self._get_cache_path(cache_key)
        if cache_path.exists():
            try:
                with open(cache_path, 'rb') as f:
                    cached_data = pickle.load(f)
                    if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                        logger.info(f"Cache hit for {cache_key}")
                        return cached_data['response']
            except Exception as e:
                logger.warning(f"Failed to load cache: {e}")
        return None
    
    def _save_to_cache(self, cache_key: str, response: Dict):
        """Save response to cache"""
        cache_path = self._get_cache_path(cache_key)
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump({
                    'response': response,
                    'timestamp': datetime.now()
                }, f)
            logger.info(f"Cached response for {cache_key}")
        except Exception as e:
            logger.warning(f"Failed to save cache: {e}")
    
    def _call_ai_api(self, prompt: str, system_message: str = "") -> Dict:
        """Call AI APIs in cascading order: Groq → Mistral → OpenAI → Gemini"""

        if not self.available_providers:
            logger.error("No AI providers available")
            return {
                'source': 'no_external_apis',
                'message': 'No external AI APIs configured'
            }

        # Try each provider in priority order
        for provider in self.available_providers:
            try:
                logger.info(f"🤖 [BACKEND] Trying {provider['name']} API...")
                logger.info(f"🌐 [BACKEND] API URL: {provider['api_url']}")
                logger.info(f"🎯 [BACKEND] Model: {provider['model']}")

                headers = {
                    'Authorization': f'Bearer {provider["api_key"]}',
                    'Content-Type': 'application/json',
                }

                messages = []
                if system_message:
                    messages.append({
                        'role': 'system',
                        'content': system_message
                    })

                messages.append({
                    'role': 'user',
                    'content': prompt
                })

                payload = {
                    'model': provider['model'],
                    'messages': messages,
                    'temperature': 0.7,
                    'max_tokens': 1000
                }

                response = requests.post(
                    provider['api_url'],
                    headers=headers,
                    json=payload,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    content = data['choices'][0]['message']['content']

                    logger.info(f"✅ [BACKEND] SUCCESS with {provider['name']} API")
                    logger.info(f"📝 [BACKEND] Raw response length: {len(content)}")
                    logger.info(f"📝 [BACKEND] Raw response preview: {content[:200]}...")

                    # Try to parse as JSON, fallback to text
                    try:
                        result = json.loads(content)
                        if isinstance(result, dict):
                            result['source'] = f"ai_api_{provider['name']}"
                            logger.info(f"🎯 [BACKEND] LIVE AI GENERATION from {provider['name']} - returning fresh recommendations")
                            return result
                        else:
                            # If result is not a dict, treat as text
                            return self._parse_text_response(content, provider['name'])
                    except json.JSONDecodeError:
                        logger.warning(f"⚠️ [BACKEND] JSON parse failed for {provider['name']}, trying text parsing")
                        return self._parse_text_response(content, provider['name'])
                else:
                    logger.warning(f"❌ [BACKEND] {provider['name']} API failed: {response.status_code}")
                    continue

            except Exception as e:
                logger.warning(f"❌ [BACKEND] {provider['name']} API error: {e}")
                continue

        # All providers failed
        logger.error("❌ [BACKEND] All AI providers failed - no live AI generation available")
        return {
            'source': 'all_apis_failed',
            'message': 'All external AI APIs failed'
        }
    
    def _parse_text_response(self, content: str, provider_name: str) -> Dict:
        """Parse text response into structured format"""
        # Clean up the response to extract JSON content
        cleaned_content = self._clean_ai_response(content)

        return {
            'response': cleaned_content,
            'confidence': 0.8,
            'source': f'ai_api_{provider_name}'
        }

    def _clean_ai_response(self, content: str) -> str:
        """Clean AI response to extract only the JSON content"""
        try:
            # Remove common prefixes and markdown formatting
            lines = content.strip().split('\n')
            cleaned_lines = []
            in_json_block = False
            json_started = False

            for line in lines:
                line = line.strip()

                # Skip introductory text
                if any(phrase in line.lower() for phrase in [
                    'based on the user profile',
                    'here are the personalized',
                    'recommendations:',
                    'note:',
                    'the suitability score'
                ]):
                    continue

                # Detect start of JSON block
                if line.startswith('```') and ('json' in line.lower() or not json_started):
                    in_json_block = True
                    continue

                # Detect end of JSON block
                if line.startswith('```') and in_json_block:
                    in_json_block = False
                    break

                # Detect JSON start
                if line.startswith('{') and not json_started:
                    json_started = True
                    in_json_block = True

                # Collect JSON content
                if in_json_block or json_started:
                    cleaned_lines.append(line)

                    # Stop at JSON end
                    if line.endswith('}') and json_started and line.count('}') >= line.count('{'):
                        break

            # Join and return cleaned content
            cleaned_content = '\n'.join(cleaned_lines).strip()

            # Validate it's proper JSON
            if cleaned_content.startswith('{') and cleaned_content.endswith('}'):
                # Test parse to ensure it's valid JSON
                json.loads(cleaned_content)
                return cleaned_content
            else:
                # If not valid JSON, return original content
                return content

        except Exception as e:
            logger.warning(f"Failed to clean AI response: {e}")
            return content

    def generate_smart_schedule(self, user_preferences: Dict, salon_availability: Dict, service_details: Dict) -> Dict:
        """Generate smart scheduling recommendations with East African cultural context"""
        # Detect user region for cultural localization
        user_region = self._detect_user_region(user_preferences)
        logger.info(f"🌍 [BACKEND] Generating smart schedule for {user_region} region")

        cache_key = self._get_cache_key(
            f"{json.dumps(user_preferences)}{json.dumps(salon_availability)}{json.dumps(service_details)}_{user_region}",
            "smart_schedule"
        )

        cached = self._load_from_cache(cache_key)
        if cached:
            # Add cultural context to cached response
            cached = self._add_scheduling_cultural_context(cached, user_region)
            logger.info(f"💾 [BACKEND] Returning cached schedule with {user_region} cultural context")
            return cached

        if self.available_providers:
            # Get culturally appropriate terms and expressions
            swahili_terms = EAST_AFRICAN_BEAUTY_TERMS['swahili']
            scheduling_expressions = EAST_AFRICAN_BEAUTY_TERMS['scheduling_expressions']

            prompt = f"""
Generate smart scheduling recommendations for a salon appointment with East African cultural awareness:

User Preferences: {json.dumps(user_preferences, indent=2)}
Salon Availability: {json.dumps(salon_availability, indent=2)}
Service Details: {json.dumps(service_details, indent=2)}
User Region: {user_region.title()}

CULTURAL CONSIDERATIONS:
- Consider East African work patterns and cultural preferences
- Account for family time and community obligations
- Respect traditional beauty preparation time
- Consider regional climate patterns (morning vs afternoon preferences)

Provide recommendations in JSON format:
{{
    "recommendations": [
        {{
            "date": "YYYY-MM-DD",
            "time": "HH:MM",
            "reason": "Why this time is optimal (include cultural considerations)",
            "confidence": 0.0-1.0,
            "culturalFit": "How this timing respects East African lifestyle"
        }}
    ],
    "optimalTime": {{
        "date": "YYYY-MM-DD",
        "time": "HH:MM",
        "reason": "Best time explanation with cultural context"
    }},
    "schedulingTips": ["Culturally-aware scheduling tips"],
    "conflicts": ["Potential cultural or practical conflicts"]
}}
"""
            system_message = f"""You are an East African salon scheduling expert who understands local culture, work patterns, and beauty traditions.
            Consider regional preferences, family obligations, and cultural timing preferences when making recommendations.
            Use warm, culturally-appropriate language that respects East African lifestyle and beauty traditions."""
            response = self._call_ai_api(prompt, system_message)

            # Add East African cultural context to scheduling response
            if response and response.get('source') not in ['no_external_apis', 'all_apis_failed']:
                response = self._add_scheduling_cultural_context(response, user_region)
                logger.info(f"🌍 [BACKEND] Added {user_region} cultural context to scheduling response")

            if response.get('source') in ['no_external_apis', 'all_apis_failed']:
                # Provide culturally-appropriate fallback
                cultural_fallbacks = [
                    f"Your {swahili_terms['perfect']} {swahili_terms['time']} awaits! ✨",
                    f"Let's find the {swahili_terms['beautiful']} moment for your appointment! 💫",
                    f"Your {swahili_terms['special']} scheduling moment is coming! 🌟",
                    f"Perfect timing for your {swahili_terms['queen']} treatment! 👑"
                ]
                response = {
                    'response': random.choice(cultural_fallbacks),
                    'source': 'cultural_fallback'
                }

            self._save_to_cache(cache_key, response)
            return response
        else:
            response = {
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
            self._save_to_cache(cache_key, response)
            return response
    
    def _find_recent_cached_tryons(self, user_profile: Dict, selected_style: str, feature: str = "virtual_tryon", hours: int = 6, max_results: int = 6) -> list:
        """Find and randomize recent cached virtual try-on simulations for similar user profiles and styles."""
        now = datetime.now()
        recent_tryons = []
        for cache_file in glob.glob(str(self.cache_dir / '*.pkl')):
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    timestamp = cached_data.get('timestamp')
                    response = cached_data.get('response')
                    if not timestamp or not response:
                        continue
                    if now - timestamp > timedelta(hours=hours):
                        continue
                    if feature not in os.path.basename(cache_file):
                        continue
                    # Check for similar user profile and style (simple match)
                    profile = cached_data.get('user_profile', {})
                    style = cached_data.get('selected_style', '')
                    if (profile.get('hairType') == user_profile.get('hairType') and
                        profile.get('faceShape') == user_profile.get('faceShape') and
                        style == selected_style):
                        recent_tryons.append(response)
            except Exception:
                continue
        random.shuffle(recent_tryons)
        return recent_tryons[:max_results]

    def generate_virtual_tryon(self, user_profile: Dict, selected_style: str) -> Dict:
        """Generate a virtual try-on simulation for a user and style, with dynamic fallback."""
        cache_key = self._get_cache_key(f"{user_profile}:{selected_style}", "virtual_tryon")
        cached_response = self._load_from_cache(cache_key)
        if cached_response:
            return cached_response
        # Dynamic fallback: search recent similar try-ons
        recent_tryons = self._find_recent_cached_tryons(user_profile, selected_style)
        if recent_tryons:
            return random.choice(recent_tryons)
        # AI model cascade
        for provider in self.available_providers:
            response = self._call_ai_api(
                prompt=f"Simulate a virtual try-on for profile: {user_profile}, style: {selected_style}",
                system_message="You are a virtual try-on AI assistant. Generate a realistic simulation for the user and style."
            )
            if response and response.get('success'):
                self._save_to_cache(cache_key, response)
                return response
        # Final fallback
        return {'message': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.'}
    
    def generate_trend_predictions(self, platform_data: Dict) -> Dict:
        """Generate trend predictions"""
        cache_key = self._get_cache_key(
            json.dumps(platform_data),
            "trend_predictions"
        )
        
        cached = self._load_from_cache(cache_key)
        if cached:
            return cached
        
        prompt = f"""
        Analyze social media data and predict upcoming hair style trends:
        
        Platform Data: {json.dumps(platform_data, indent=2)}
        
        Generate predictions in JSON format:
        {{
            "predictions": {{
                "summary": "Overall trend analysis",
                "emergingTrends": [
                    {{
                        "name": "Trend Name",
                        "description": "Description",
                        "confidence": 0.0-1.0,
                        "timeframe": "3-6 months",
                        "virality": {{
                            "score": 0.0-1.0,
                            "probability": 0.0-1.0,
                            "timeframe": "Expected timeframe"
                        }},
                        "salonImpact": "high/medium/low",
                        "targetAudience": "Age group and demographics"
                    }}
                ],
                "decliningTrends": [
                    {{
                        "name": "Declining Trend",
                        "reason": "Why it's declining",
                        "timeframe": "When it will fade"
                    }}
                ]
            }},
            "seasonalTrends": [
                {{
                    "season": "Spring/Summer/Fall/Winter",
                    "trends": ["Trend 1", "Trend 2"]
                }}
            ],
            "recommendations": [
                {{
                    "action": "What salons should do",
                    "priority": "high/medium/low",
                    "timeline": "When to implement"
                }}
            ]
        }}
        """
        
        system_message = "You are a trend analysis expert specializing in hair and beauty trends. Analyze social media data to predict upcoming trends."
        
        response = self._call_ai_api(prompt, system_message)
        if response.get('source') in ['no_external_apis', 'all_apis_failed']:
            response = {
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
        self._save_to_cache(cache_key, response)
        return response
    
    def generate_analytics_report(self, analytics_data: Dict) -> Dict:
        """Generate analytics insights"""
        cache_key = self._get_cache_key(
            json.dumps(analytics_data),
            "analytics_report"
        )
        
        cached = self._load_from_cache(cache_key)
        if cached:
            return cached
        
        prompt = f"""
        Analyze salon performance data and generate insights:
        
        Analytics Data: {json.dumps(analytics_data, indent=2)}
        
        Generate insights in JSON format:
        {{
            "insights": {{
                "summary": "Overall performance summary",
                "trends": [
                    {{
                        "metric": "Revenue/Bookings/Satisfaction",
                        "trend": "increasing/decreasing/stable",
                        "percentage": 0.0-100.0,
                        "insight": "What this means"
                    }}
                ],
                "opportunities": [
                    {{
                        "area": "Service/Time/Marketing",
                        "opportunity": "What to improve",
                        "impact": "high/medium/low",
                        "action": "Specific action to take"
                    }}
                ],
                "risks": [
                    {{
                        "risk": "Potential issue",
                        "severity": "high/medium/low",
                        "mitigation": "How to address it"
                    }}
                ]
            }},
            "predictions": {{
                "nextMonth": {{
                    "revenue": "Predicted revenue",
                    "bookings": "Predicted bookings",
                    "confidence": 0.0-1.0
                }}
            }},
            "recommendations": [
                {{
                    "category": "Marketing/Operations/Service",
                    "recommendation": "What to do",
                    "priority": "high/medium/low",
                    "expectedImpact": "Expected outcome"
                }}
            ]
        }}
        """
        
        system_message = "You are a business analytics expert specializing in salon performance. Analyze data to provide actionable insights and recommendations."
        
        response = self._call_ai_api(prompt, system_message)
        if response.get('source') in ['no_external_apis', 'all_apis_failed']:
            response = {
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
        self._save_to_cache(cache_key, response)
        return response
    
    def _get_user_recommendation_history(self, user_profile: Dict, days: int = 30) -> set:
        """Get all recommendations this user has received in the past X days to avoid duplicates."""
        now = datetime.now()
        user_history = set()
        user_key = f"{user_profile.get('hairType', '')}{user_profile.get('faceShape', '')}{user_profile.get('lifestyle', '')}"

        for cache_file in glob.glob(str(self.cache_dir / '*.pkl')):
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    timestamp = cached_data.get('timestamp')
                    response = cached_data.get('response')
                    if not timestamp or not response:
                        continue
                    if now - timestamp > timedelta(days=days):
                        continue

                    # Check if this cache is for the same user profile
                    cache_user_key = f"{response.get('user_profile', {}).get('hairType', '')}{response.get('user_profile', {}).get('faceShape', '')}{response.get('user_profile', {}).get('lifestyle', '')}"
                    if cache_user_key == user_key:
                        if 'recommendations' in response and isinstance(response['recommendations'], list):
                            for rec in response['recommendations']:
                                style_name = rec.get('style', rec.get('name', '')).lower().strip()
                                if style_name:
                                    user_history.add(style_name)
            except Exception as e:
                logger.warning(f"Failed to load cache file for history: {e}")

        logger.info(f"🔍 [BACKEND] User has {len(user_history)} previous recommendations: {list(user_history)[:5]}...")
        return user_history

    def _find_recent_cached_recommendations(self, user_profile: Dict, feature: str = "style_recommendations", hours: int = 6, max_results: int = 6) -> list:
        """Find and randomize recent cached recommendations for similar user profiles, ensuring uniqueness."""
        now = datetime.now()
        recent_recs = []
        user_history = self._get_user_recommendation_history(user_profile)

        for cache_file in glob.glob(str(self.cache_dir / '*.pkl')):
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    timestamp = cached_data.get('timestamp')
                    response = cached_data.get('response')
                    if not timestamp or not response:
                        continue
                    if now - timestamp > timedelta(hours=hours):
                        continue
                    # Only consider style_recommendations feature
                    if feature not in os.path.basename(cache_file):
                        continue
                    # Check for similar user profile (simple match on hairType, faceShape, preferences)
                    rec_profile = response.get('user_profile') or response.get('profile') or {}
                    if (
                        rec_profile.get('hairType') == user_profile.get('hairType') and
                        rec_profile.get('faceShape') == user_profile.get('faceShape')
                    ):
                        if 'recommendations' in response and isinstance(response['recommendations'], list):
                            for rec in response['recommendations']:
                                style_name = rec.get('style', rec.get('name', '')).lower().strip()
                                # Only add if user hasn't seen this style before
                                if style_name and style_name not in user_history:
                                    recent_recs.append(rec)
                                    logger.info(f"✨ [BACKEND] Adding novel cached recommendation: {style_name}")
                                else:
                                    logger.info(f"🚫 [BACKEND] Skipping duplicate recommendation: {style_name}")
            except Exception as e:
                logger.warning(f"Failed to load or parse cache file {cache_file}: {e}")

        # Randomize and return up to max_results
        random.shuffle(recent_recs)
        logger.info(f"🎲 [BACKEND] Returning {len(recent_recs[:max_results])} unique cached recommendations")
        return recent_recs[:max_results]

    def _detect_user_region(self, user_profile: Dict) -> str:
        """Detect user's East African region from profile data."""
        # Check explicit region/country field
        region = user_profile.get('region', '').lower()
        country = user_profile.get('country', '').lower()
        location = user_profile.get('location', '').lower()

        # Map to East African regions
        if any(term in f"{region} {country} {location}" for term in ['kenya', 'nairobi', 'mombasa', 'kisumu']):
            return 'kenyan'
        elif any(term in f"{region} {country} {location}" for term in ['ethiopia', 'addis', 'ethiopian']):
            return 'ethiopian'
        elif any(term in f"{region} {country} {location}" for term in ['uganda', 'kampala', 'ugandan']):
            return 'ugandan'
        elif any(term in f"{region} {country} {location}" for term in ['tanzania', 'dar es salaam', 'tanzanian']):
            return 'tanzanian'
        elif any(term in f"{region} {country} {location}" for term in ['rwanda', 'kigali', 'rwandan']):
            return 'rwandan'

        # Default to Kenyan (primary market)
        return 'kenyan'

    def _get_localized_styles(self, user_region: str, hair_type: str) -> List[str]:
        """Get culturally appropriate hair styles for the user's region."""
        # 90% East African, 10% Western fusion
        east_african_styles = []

        # Add traditional styles (40%)
        traditional = EAST_AFRICAN_HAIR_STYLES['traditional']
        east_african_styles.extend(random.sample(traditional, min(4, len(traditional))))

        # Add modern African styles (40%)
        modern = EAST_AFRICAN_HAIR_STYLES['modern_african']
        east_african_styles.extend(random.sample(modern, min(4, len(modern))))

        # Add fusion styles (10%)
        fusion = EAST_AFRICAN_HAIR_STYLES['fusion']
        east_african_styles.extend(random.sample(fusion, min(1, len(fusion))))

        # Add minimal western fusion (10%)
        western = EAST_AFRICAN_HAIR_STYLES['western_fusion']
        if western:
            east_african_styles.extend(random.sample(western, min(1, len(western))))

        # Filter based on hair characteristics for the region
        regional_prefs = REGIONAL_HAIR_CHARACTERISTICS.get(user_region, {}).get('preferences', [])

        # Prioritize styles that match regional preferences
        prioritized_styles = []
        for style in east_african_styles:
            style_lower = style.lower()
            if any(pref in style_lower for pref in [p.lower() for p in regional_prefs]):
                prioritized_styles.insert(0, style)  # Add to front
            else:
                prioritized_styles.append(style)

        return prioritized_styles[:8]  # Return top 8 styles

    def _add_cultural_context(self, response: Dict, user_region: str) -> Dict:
        """Add East African cultural context and beauty language to response."""
        # Add local beauty expressions
        local_expressions = EAST_AFRICAN_BEAUTY_TERMS['local_expressions']
        swahili_terms = EAST_AFRICAN_BEAUTY_TERMS['swahili']

        # Enhance greeting with local flavor
        if 'greeting' in response:
            greetings = [
                f"Habari {swahili_terms['gorgeous']}! ✨",
                f"Hey beautiful {swahili_terms['sister']}! 💫",
                f"Mambo {swahili_terms['queen']}! 🌟",
                "Hey gorgeous East African queen! ✨",
                f"Hello {random.choice(local_expressions)}! 💖"
            ]
            response['greeting'] = random.choice(greetings)

        # Enhance signature tip with cultural elements
        if 'signatureTip' in response:
            cultural_tips = [
                f"Remember, you're a natural {swahili_terms['queen']} - embrace your {swahili_terms['natural']} beauty! ✨",
                "Your melanin is magic, your hair is your crown! 👑",
                f"Stay {swahili_terms['beautiful']}, stay confident, stay authentically you! 💫",
                "African hair is versatile hair - celebrate your heritage! 🌍",
                f"You're not just {swahili_terms['gorgeous']}, you're uniquely East African gorgeous! ⭐"
            ]
            response['signatureTip'] = random.choice(cultural_tips)

        # Add regional hair characteristics to analysis
        if 'analysis' in response and user_region in REGIONAL_HAIR_CHARACTERISTICS:
            regional_info = REGIONAL_HAIR_CHARACTERISTICS[user_region]
            climate_note = f"Perfect for {regional_info['climate']} climate"
            response['analysis'] = f"{response['analysis']} {climate_note}."

        return response

    def _add_gift_cultural_context(self, message: str, relationship: str, occasion: str, user_region: str) -> str:
        """Add East African cultural context to gift messages."""
        swahili_terms = EAST_AFRICAN_BEAUTY_TERMS['swahili']
        gift_expressions = EAST_AFRICAN_BEAUTY_TERMS['gift_expressions']
        local_expressions = EAST_AFRICAN_BEAUTY_TERMS['local_expressions']

        # Add cultural greetings based on relationship
        cultural_greetings = {
            'bestie': [f"Hey {swahili_terms['sister']}! 💫", f"Mambo {swahili_terms['friend']}! ✨", "Hey gorgeous dada! 🌟"],
            'mom': [f"Mama {swahili_terms['beautiful']}! 👑", "Beautiful mama! 💖", f"Hey {swahili_terms['queen']} mama! ✨"],
            'sister': [f"Hey {swahili_terms['sister']}! 💫", f"Beautiful dada! 🌟", f"{swahili_terms['gorgeous']} sis! ✨"],
            'partner': [f"My {swahili_terms['love']}! 💕", f"Beautiful {swahili_terms['queen']}! 👑", "My gorgeous love! 💖"],
            'friend': [f"Hey {swahili_terms['friend']}! 🌟", f"Beautiful {swahili_terms['sister']}! ✨", "Hey gorgeous friend! 💫"]
        }

        # Add cultural gift expressions
        cultural_gift_phrases = [
            f"This {swahili_terms['gift']} is for you",
            f"A special {random.choice(gift_expressions)}",
            f"Time for some {swahili_terms['queen']} treatment",
            f"You deserve this {swahili_terms['special']} moment"
        ]

        # Enhance message with cultural elements
        if any(greeting in message.lower() for greeting in ['hey', 'hi', 'hello']):
            cultural_greeting = random.choice(cultural_greetings.get(relationship, cultural_greetings['friend']))
            message = message.replace('Hey', cultural_greeting.split('!')[0], 1)

        # Add cultural gift expression
        if 'gift' in message.lower() or 'treat' in message.lower():
            cultural_phrase = random.choice(cultural_gift_phrases)
            message = f"{cultural_phrase} - {message}"

        # Add cultural closing
        cultural_closings = [
            f"Enjoy your {swahili_terms['beautiful']} moment! ✨",
            f"You're a natural {swahili_terms['queen']}! 👑",
            f"Pamper yourself, {swahili_terms['gorgeous']}! 💫",
            f"Self-care time, {random.choice(local_expressions)}! 🌟"
        ]

        if not any(closing in message for closing in ['!', '✨', '💫', '🌟', '👑']):
            message += f" {random.choice(cultural_closings)}"

        return message

    def _format_gift_message(self, message: str) -> str:
        """Format gift message to be natural, concise, and left-aligned"""
        if not message:
            return message

        # Remove any AI-related mentions
        ai_terms = ['AI', 'model', 'assistant', 'generated', 'algorithm', 'system', 'provider', 'api']
        for term in ai_terms:
            message = re.sub(rf'\b{term}\b', '', message, flags=re.IGNORECASE)

        # Clean up extra spaces and formatting
        message = re.sub(r'\s+', ' ', message).strip()

        # Remove any markdown or code formatting
        message = re.sub(r'[*_`]', '', message)

        # Ensure it's not too long (max 100 words)
        words = message.split()
        if len(words) > 100:
            message = ' '.join(words[:100]) + '...'

        # Remove any source attribution or technical details
        message = re.sub(r'\(.*source.*\)', '', message, flags=re.IGNORECASE)
        message = re.sub(r'confidence.*', '', message, flags=re.IGNORECASE)

        return message.strip()

    def _get_salon_context(self, user_profile: Dict) -> str:
        """Get salon context for personalized messages"""
        if user_profile:
            salon_name = user_profile.get('salonName', 'The Glam Parlour')
            salon_location = user_profile.get('salonLocation', 'Westlands')
            service_name = user_profile.get('serviceName', 'hair styling')
            sender_name = user_profile.get('senderName', 'Your friend')

            # Extract first name only
            sender_first_name = sender_name.split(' ')[0] if sender_name else 'Your friend'

            return f"Salon: {salon_name} in {salon_location}, Service: {service_name}, Sender: {sender_first_name}"

        # Fallback to sample data if no profile provided
        import random
        kenyan_salons = [
            {"name": "The Glam Parlour", "location": "Westlands", "specialty": "hair styling and color"},
            {"name": "Beauty Lounge", "location": "Karen", "specialty": "luxury treatments"},
            {"name": "Chic Studio", "location": "Kilimani", "specialty": "modern cuts and styling"},
            {"name": "Elegance Salon", "location": "CBD", "specialty": "professional styling"},
            {"name": "Pamper Palace", "location": "Lavington", "specialty": "full beauty services"}
        ]

        salon = random.choice(kenyan_salons)
        services = ["hair styling", "hair color treatment", "deep conditioning", "hair cut and style", "keratin treatment", "highlights"]
        service = random.choice(services)

        return f"Salon: {salon['name']} in {salon['location']}, Service: {service}, Sender: Your friend"

    def _add_scheduling_cultural_context(self, response: Dict, user_region: str) -> Dict:
        """Add East African cultural context to scheduling responses."""
        swahili_terms = EAST_AFRICAN_BEAUTY_TERMS['swahili']
        scheduling_expressions = EAST_AFRICAN_BEAUTY_TERMS['scheduling_expressions']

        # Enhance scheduling recommendations with cultural context
        if 'recommendations' in response:
            for rec in response['recommendations']:
                if 'reason' in rec:
                    cultural_reasons = [
                        f"This {swahili_terms['time']} works {swahili_terms['perfect']}ly for you",
                        f"A {swahili_terms['beautiful']} time for self-care",
                        f"Perfect {random.choice(scheduling_expressions)}",
                        f"Ideal for your {swahili_terms['queen']} treatment"
                    ]
                    rec['reason'] = f"{random.choice(cultural_reasons)} - {rec['reason']}"

        # Add cultural scheduling tips
        if 'schedulingTips' in response:
            cultural_tips = [
                f"Remember, you're a {swahili_terms['queen']} - choose the time that feels right! 👑",
                f"Self-care is {swahili_terms['special']} - pick your perfect moment! ✨",
                f"Your {swahili_terms['beautiful']} time, your choice! 💫",
                f"Trust your instincts, {swahili_terms['gorgeous']}! 🌟"
            ]
            response['schedulingTips'].extend(cultural_tips[:2])

        return response

    def _add_salon_cultural_context(self, response: Dict, user_region: str) -> Dict:
        """Add East African cultural context to salon matching responses."""
        swahili_terms = EAST_AFRICAN_BEAUTY_TERMS['swahili']
        salon_expressions = EAST_AFRICAN_BEAUTY_TERMS['salon_expressions']

        # Enhance salon matches with cultural descriptions
        if 'matches' in response:
            for match in response['matches']:
                if 'reason' in match:
                    cultural_reasons = [
                        f"This {random.choice(salon_expressions)} is perfect for you",
                        f"A {swahili_terms['beautiful']} place for your {swahili_terms['queen']} treatment",
                        f"This salon celebrates your natural {swahili_terms['gorgeous']}ness",
                        f"Perfect for your {swahili_terms['special']} beauty journey"
                    ]
                    match['reason'] = f"{random.choice(cultural_reasons)} - {match['reason']}"

                if 'culturalFit' not in match:
                    match['culturalFit'] = random.choice([
                        f"Celebrates African beauty and {swahili_terms['natural']} hair",
                        f"Perfect for {swahili_terms['gorgeous']} East African queens",
                        f"Specializes in {swahili_terms['beautiful']} African hair textures",
                        f"Understands your {swahili_terms['natural']} beauty needs"
                    ])

        return response

    def _ensure_unique_recommendations(self, response: Dict, user_history: set) -> Dict:
        """Ensure AI-generated recommendations are truly unique and not duplicates."""
        if not response.get('recommendations'):
            return response

        unique_recommendations = []
        for rec in response['recommendations']:
            style_name = rec.get('style', '').lower().strip()
            if style_name and style_name not in user_history:
                unique_recommendations.append(rec)
                logger.info(f"✅ [BACKEND] AI generated unique recommendation: {style_name}")
            else:
                logger.warning(f"🚫 [BACKEND] AI generated duplicate recommendation: {style_name} - filtering out")

        # If we filtered out duplicates, add novelty indicators
        if len(unique_recommendations) < len(response['recommendations']):
            logger.info(f"🔄 [BACKEND] Filtered {len(response['recommendations']) - len(unique_recommendations)} duplicate AI recommendations")

            # Add novelty markers to remaining recommendations
            for rec in unique_recommendations:
                if 'description' in rec:
                    rec['description'] = f"✨ {rec['description']}"
                rec['novelty'] = True

        response['recommendations'] = unique_recommendations
        return response

    def _clean_style_response(self, raw_response):
        import re, json
        # Remove markdown code blocks
        cleaned = re.sub(r'```json|```', '', str(raw_response)).strip()
        # Remove boilerplate phrases
        cleaned = re.sub(r'Here are the (personalized )?hair style recommendations for the user:.*', '', cleaned, flags=re.IGNORECASE).strip()
        # Try to extract JSON from anywhere in the string
        match = re.search(r'\{[\s\S]*\}', cleaned)
        if match:
            try:
                data = json.loads(match.group(0))
                # Pass through all fields, including outfit
                return {
                    'greeting': data.get('greeting', "Hey love ✨"),
                    'recommendations': data.get('recommendations', []),
                    'signatureTip': data.get('signatureTip', "Keep your style fresh and your confidence fresher!"),
                    'analysis': data.get('analysis', ""),
                    'alternatives': data.get('alternatives', [])
                }
            except Exception:
                pass
        # Fallback: try to parse the whole cleaned string as JSON
        try:
            data = json.loads(cleaned)
            return {
                'greeting': data.get('greeting', "Hey love ✨"),
                'recommendations': data.get('recommendations', []),
                'signatureTip': data.get('signatureTip', "Keep your style fresh and your confidence fresher!"),
                'analysis': data.get('analysis', ""),
                'alternatives': data.get('alternatives', [])
            }
        except Exception:
            # Strict fallback
            return {
                'greeting': "Hey, let’s try that again 🌼",
                'recommendations': [],
                'signatureTip': "Sometimes a quick refresh works wonders!",
                'analysis': "The result came back a bit scrambled. We're working on smoothing it out!",
                'alternatives': []
            }
    
    def generate_style_recommendations(self, user_profile: Dict) -> Dict:
        """Generate personalized style recommendations with East African cultural context and dynamic fallback"""
        import random
        logger.info(f"🚀 [BACKEND] Generating style recommendations for user profile: {user_profile}")

        # Detect user's East African region for localization
        user_region = self._detect_user_region(user_profile)
        logger.info(f"🌍 [BACKEND] Detected user region: {user_region}")

        # Create cache key with timestamp to encourage fresh recommendations
        timestamp_factor = datetime.now().strftime("%Y%m%d%H")  # Changes every hour
        cache_key = self._get_cache_key(f"{json.dumps(user_profile)}_{timestamp_factor}_{user_region}", "style_recommendations")
        cached = self._load_from_cache(cache_key)
        if cached:
            logger.info(f"💾 [BACKEND] Returning CACHED recommendations from: {cache_key}")
            logger.info(f"🎯 [BACKEND] Cache source: {cached.get('source', 'unknown')}")
            # Add cultural context to cached response
            cached = self._add_cultural_context(cached, user_region)
            return cached
        # 1. Try to use recent, similar cached recommendations
        logger.info("🔍 [BACKEND] Checking for recent similar cached recommendations...")
        recent_recs = self._find_recent_cached_recommendations(user_profile)
        if recent_recs:
            logger.info(f"💾 [BACKEND] Found {len(recent_recs)} recent cached recommendations")
            response = {
                'greeting': "Hey love ✨",
                'recommendations': recent_recs,
                'signatureTip': random.choice([
                    "Your natural hair is your crown - wear it proudly! 👑",
                    "African hair is beautiful hair - embrace your heritage! 🌍",
                    "You're a natural queen - let your beauty shine! ✨",
                    "Melanin magic and natural hair power! 💫"
                ]),
                'analysis': "These fresh East African styles are picked just for you!",
                'alternatives': [],
                'source': 'cache_dynamic',
                'note': 'Generated from recent similar user profiles (randomized)',
                'user_profile': user_profile  # Add for history tracking
            }
            # Add cultural context to cached response
            response = self._add_cultural_context(response, user_region)
            logger.info(f"🎯 [BACKEND] Returning DYNAMIC CACHE recommendations with {user_region} cultural context")
            self._save_to_cache(cache_key, response)
            return response
        # 2. Try AI model cascade
        logger.info(f"🤖 [BACKEND] Available AI providers: {self.available_providers}")
        if self.available_providers:
            logger.info("🔄 [BACKEND] Trying LIVE AI model cascade...")

            # Get user's recommendation history to avoid duplicates
            user_history = self._get_user_recommendation_history(user_profile)
            avoid_styles = ", ".join(list(user_history)[:10]) if user_history else "none"

            # Get culturally appropriate styles for the region
            localized_styles = self._get_localized_styles(user_region, user_profile.get('hairType', ''))
            regional_context = REGIONAL_HAIR_CHARACTERISTICS.get(user_region, {})

            system_message = (
                "Always return JSON only. No markdown. No code blocks. No 'the user'. Fields: greeting, recommendations, signatureTip, analysis, alternatives. "
                "You are a friendly, expert East African hair stylist specializing in African hair textures and cultural styles. "
                "Give 2-3 UNIQUE, CULTURALLY-APPROPRIATE hairstyle recommendations that celebrate African beauty and heritage. "
                "IMPORTANT: Prioritize East African traditional, modern African, and fusion styles (90%) with minimal Western influence (10%). "
                "Focus on protective styles, natural textures, and culturally significant looks that work with African hair characteristics. "
                "Never mention AI, models, or providers. Use warm, culturally-aware language that celebrates African beauty. "
                "Add a greeting with local flavor and a signature tip that embraces natural African hair beauty. "
                f"AVOID these styles the user has seen before: {avoid_styles}. "
                f"PRIORITIZE these culturally-appropriate styles: {', '.join(localized_styles[:5])}"
            )
            prompt = f"""
Create 2-3 INNOVATIVE East African hairstyle recommendations that celebrate this user's natural beauty and cultural heritage.

User Profile: {json.dumps(user_profile, indent=2)}
User Region: {user_region.title()}
Regional Hair Characteristics: {regional_context.get('texture', [])}
Climate Considerations: {regional_context.get('climate', 'tropical')}

AVOID these styles (user has seen them): {avoid_styles}

CULTURAL REQUIREMENTS (90% East African, 10% Western):
- PRIORITIZE: Traditional African styles, protective braids, natural textures, cultural updos
- INCLUDE: Modern African fusion, contemporary twists on traditional styles
- MINIMAL: Western-influenced styles (only if they complement African hair)
- FOCUS: Styles that work with {regional_context.get('texture', ['African hair texture'])[0] if regional_context.get('texture') else 'natural African hair'}
- CONSIDER: {regional_context.get('climate', 'tropical')} climate and cultural preferences

STYLE SUGGESTIONS: {', '.join(localized_styles[:6])}

REQUIREMENTS:
- Suggest CULTURALLY-APPROPRIATE and NOVEL styles
- Each recommendation must celebrate African beauty and heritage
- Be specific with traditional/cultural style names
- Include cultural significance or protective benefits
- Make each recommendation feel authentic and empowering

Respond in this JSON format:
{{
  "greeting": "culturally-warm greeting",
    "recommendations": [
        {{
      "style": "Specific Cultural/Traditional Style Name",
      "description": "Detailed description emphasizing cultural elements and hair health",
      "whyRecommended": "Why this style suits their hair type and celebrates their heritage"
        }}
    ],
  "signatureTip": "empowering tip about African hair beauty",
  "analysis": "1-2 lines about their natural hair potential and cultural style fit",
  "alternatives": []
}}
"""
            raw_response = self._call_ai_api(prompt, system_message)
            logger.info(f"🔄 [BACKEND] Processing AI response: {type(raw_response)}")
            logger.info(f"🎯 [BACKEND] AI response source: {raw_response.get('source', 'unknown') if isinstance(raw_response, dict) else 'unknown'}")

            if isinstance(raw_response, dict) and isinstance(raw_response.get('response'), str):
                response = self._clean_style_response(raw_response['response'])
            else:
                response = self._clean_style_response(raw_response if isinstance(raw_response, str) else json.dumps(raw_response))

            # Ensure AI recommendations are truly unique
            response = self._ensure_unique_recommendations(response, user_history)

            # Add East African cultural context and beauty language
            response = self._add_cultural_context(response, user_region)
            logger.info(f"🌍 [BACKEND] Added {user_region} cultural context to response")

            # Post-process: Remove any model/provider mentions
            for key in ['greeting', 'signatureTip', 'analysis']:
                if key in response and isinstance(response[key], str):
                    for forbidden in ['AI', 'Groq', 'OpenAI', 'Mistral', 'Gemini', 'model', 'provider']:
                        response[key] = response[key].replace(forbidden, 'smart engine')

            # Add culturally-appropriate fallback signature tip if missing
            if 'signatureTip' not in response or not response['signatureTip']:
                cultural_tips = [
                    "Your natural hair is your crown - wear it proudly! 👑",
                    "African hair is beautiful hair - embrace your heritage! 🌍",
                    "You're a natural queen - let your beauty shine! ✨",
                    "Melanin magic and natural hair power! 💫"
                ]
                response['signatureTip'] = random.choice(cultural_tips)
            if response.get('source') in ['no_external_apis', 'all_apis_failed']:
                response = {
                    'greeting': "Hey, let’s try that again 🌼",
                    'recommendations': [],
                    'signatureTip': "Sometimes a quick refresh works wonders!",
                    'analysis': "The result came back a bit scrambled. We're working on smoothing it out!",
                    'alternatives': [],
                    'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                    'source': 'service_unavailable'
                }
            # Log final response details
            if response.get('source') in ['no_external_apis', 'all_apis_failed', 'service_unavailable']:
                logger.warning("⚠️ [BACKEND] AI APIs failed, returning service unavailable response")
            else:
                logger.info(f"✅ [BACKEND] Successfully generated recommendations with source: {response.get('source', 'unknown')}")
                logger.info(f"📊 [BACKEND] Generated {len(response.get('recommendations', []))} recommendations")

            # Add user profile to response for history tracking
            response['user_profile'] = user_profile

            self._save_to_cache(cache_key, response)
            logger.info(f"💾 [BACKEND] Cached response with key: {cache_key}")
            return response
        # 3. Final fallback: only the friendly sorry message
            response = {
            'greeting': "Hey, let’s try that again 🌼",
            'recommendations': [],
            'signatureTip': "Sometimes a quick refresh works wonders!",
            'analysis': "The result came back a bit scrambled. We're working on smoothing it out!",
            'alternatives': [],
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
        self._save_to_cache(cache_key, response)
        return response
    
    def _find_recent_cached_matches(self, user_preferences: Dict, available_salons: list, feature: str = "salon_matching", hours: int = 6, max_results: int = 6) -> list:
        """Find and randomize recent cached salon matches for similar user preferences."""
        now = datetime.now()
        recent_matches = []
        for cache_file in glob.glob(str(self.cache_dir / '*.pkl')):
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    timestamp = cached_data.get('timestamp')
                    response = cached_data.get('response')
                    if not timestamp or not response:
                        continue
                    if now - timestamp > timedelta(hours=hours):
                        continue
                    if feature not in os.path.basename(cache_file):
                        continue
                    rec_prefs = response.get('user_preferences') or response.get('preferences') or {}
                    # Simple match on budget and location (customize as needed)
                    if (
                        rec_prefs.get('budget') == user_preferences.get('budget') and
                        rec_prefs.get('location') == user_preferences.get('location')
                    ):
                        if 'matches' in response and isinstance(response['matches'], list):
                            for match in response['matches']:
                                recent_matches.append(match)
            except Exception as e:
                logger.warning(f"Failed to load or parse cache file {cache_file}: {e}")
        random.shuffle(recent_matches)
        return recent_matches[:max_results]

    def find_perfect_salons(self, user_preferences: Dict, available_salons: List[Dict]) -> Dict:
        """Find perfect salon matches with East African cultural context and dynamic fallback"""
        # Detect user region for cultural localization
        user_region = self._detect_user_region(user_preferences)
        logger.info(f"🌍 [BACKEND] Finding salon matches for {user_region} region")

        cache_key = self._get_cache_key(f"{json.dumps(user_preferences)}{json.dumps(available_salons)}_{user_region}", "salon_matching")
        cached = self._load_from_cache(cache_key)
        if cached:
            # Add cultural context to cached response
            cached = self._add_salon_cultural_context(cached, user_region)
            logger.info(f"💾 [BACKEND] Returning cached salon matches with {user_region} cultural context")
            return cached
        # 1. Try to use recent, similar cached matches
        recent_matches = self._find_recent_cached_matches(user_preferences, available_salons)
        if recent_matches:
            response = {
                'matches': recent_matches,
                'source': 'cache_dynamic',
                'note': 'Generated from recent similar user preferences (randomized)'
            }
            self._save_to_cache(cache_key, response)
            return response
        # 2. Try AI model cascade
        if self.available_providers:
            prompt = f"""
        Find the best salon matches for a user:
        User Preferences: {json.dumps(user_preferences, indent=2)}
        Available Salons: {json.dumps(available_salons, indent=2)}
        Return matches in JSON format:
        {{
            "matches": [
                {{
                    "salon": "Salon object",
                    "matchScore": 0.0-1.0,
                    "reasons": ["Reason 1", "Reason 2"],
                    "strengths": ["Strength 1", "Strength 2"],
                    "considerations": ["Consideration 1", "Consideration 2"]
                }}
            ],
            "summary": "Overall matching summary",
            "recommendations": [
                {{
                    "action": "What user should do",
                    "priority": "high/medium/low"
                }}
            ]
        }}
        """
            system_message = "You are a salon matching expert. Analyze user preferences and salon characteristics to find the best matches."
            response = self._call_ai_api(prompt, system_message)
            if response.get('source') in ['no_external_apis', 'all_apis_failed']:
                response = {
                    'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                    'source': 'service_unavailable'
                }
            self._save_to_cache(cache_key, response)
            return response

        # 3. Final fallback: only the friendly sorry message
        response = {
            'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
            'source': 'service_unavailable'
        }
        self._save_to_cache(cache_key, response)
        return response
    
    def _find_recent_cached_notifications(self, user_context: Dict, salon_data: Dict, feature: str = "smart_notifications", hours: int = 6, max_results: int = 6) -> list:
        """Find and randomize recent cached notifications for similar user context and salon data."""
        now = datetime.now()
        recent_notifications = []
        for cache_file in glob.glob(str(self.cache_dir / '*.pkl')):
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    timestamp = cached_data.get('timestamp')
                    response = cached_data.get('response')
                    if not timestamp or not response:
                        continue
                    if now - timestamp > timedelta(hours=hours):
                        continue
                    if feature not in os.path.basename(cache_file):
                        continue
                    rec_context = response.get('user_context') or response.get('context') or {}
                    rec_salon = response.get('salon_data') or response.get('salon') or {}
                    # Simple match on user id and salon id (customize as needed)
                    if (
                        rec_context.get('user_id') == user_context.get('user_id') and
                        rec_salon.get('salon_id') == salon_data.get('salon_id')
                    ):
                        if 'notifications' in response and isinstance(response['notifications'], list):
                            for notif in response['notifications']:
                                recent_notifications.append(notif)
            except Exception as e:
                logger.warning(f"Failed to load or parse cache file {cache_file}: {e}")
        random.shuffle(recent_notifications)
        return recent_notifications[:max_results]
    
    def generate_smart_notifications(self, user_context: Dict, salon_data: Dict) -> Dict:
        """Generate smart notification recommendations with dynamic fallback"""
        cache_key = self._get_cache_key(f"{json.dumps(user_context)}{json.dumps(salon_data)}", "smart_notifications")
        cached = self._load_from_cache(cache_key)
        if cached:
            return cached
        # 1. Try to use recent, similar cached notifications
        recent_notifications = self._find_recent_cached_notifications(user_context, salon_data)
        if recent_notifications:
            response = {
                'notifications': recent_notifications,
                'source': 'cache_dynamic',
                'note': 'Generated from recent similar user context and salon data (randomized)'
            }
            self._save_to_cache(cache_key, response)
            return response
        # 2. Try AI model cascade
        if self.available_providers:
            prompt = f"""
        Generate smart notification recommendations:
        
        User Context: {json.dumps(user_context, indent=2)}
        Salon Data: {json.dumps(salon_data, indent=2)}
        
        Return notifications in JSON format:
        {{
            "notifications": [
                {{
                    "type": "reminder/promotion/update",
                    "title": "Notification title",
                    "message": "Notification message",
                    "priority": "high/medium/low",
                    "timing": "When to send",
                    "action": "What user should do",
                    "personalization": "How to personalize"
                }}
            ],
            "strategy": {{
                "frequency": "How often to send",
                "timing": "Best times to send",
                "content": "Content strategy"
            }},
            "insights": [
                {{
                    "insight": "User behavior insight",
                    "action": "How to use this insight"
                }}
            ]
        }}
        """
            system_message = "You are a customer engagement expert. Generate personalized notification strategies that increase user engagement without being intrusive."
            response = self._call_ai_api(prompt, system_message)
            if response.get('source') in ['no_external_apis', 'all_apis_failed']:
                response = {
                    'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                    'source': 'service_unavailable'
                }
            self._save_to_cache(cache_key, response)
            return response
        # 3. Final fallback: only the friendly sorry message
            response = {
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
        self._save_to_cache(cache_key, response)
        return response

    def generate_gift_message(self, relationship: str, occasion: str, tone: str, recipient_name: str = '', user_profile: Dict = None) -> Dict:
        """Generate personalized gift messages for salon appointments with East African cultural context"""
        # Detect user region for cultural localization
        user_region = self._detect_user_region(user_profile or {})
        language = user_profile.get('language', 'english') if user_profile else 'english'
        logger.info(f"🌍 [BACKEND] Generating gift message for {user_region} region in {language}")

        cache_key = self._get_cache_key(
            f"{relationship}:{occasion}:{tone}:{recipient_name}:{user_region}:{language}",
            "gift_message"
        )
        cached_response = self._load_from_cache(cache_key)
        if cached_response:
            # Clean and format cached response
            if 'response' in cached_response:
                cached_response['response'] = self._format_gift_message(cached_response['response'])
            logger.info(f"💾 [BACKEND] Returning cached gift message")
            return cached_response

        if self.available_providers:
            # Language-specific system messages
            language_instructions = {
                'english': "Write in clear, natural English. Sound like a caring friend, not an AI.",
                'swahili': "Andika kwa Kiswahili safi na rahisi. Tumia maneno ya kawaida yanayoeleweka kwa kila mtu. Usichanganye na Kiingereza. Fanya iwe kama rafiki anayeongea kwa kawaida.",
                'sheng': "Write in Sheng - mix Swahili and English naturally like Kenyan Gen-Z. Be cool and trendy."
            }

            system_message = f"""You are a caring friend writing a personal gift message for a salon appointment.
            {language_instructions.get(language, language_instructions['english'])}

            CRITICAL RULES:
            - Write EXACTLY like a human friend would text
            - Never mention AI, models, or technology
            - Keep it under 100 words maximum
            - Be warm and personal
            - MUST include specific salon name and location details
            - MUST personalize with recipient's name (use FIRST NAME ONLY)
            - MUST mention the specific hair/beauty service being gifted
            - Sign off with sender's FIRST NAME ONLY
            - Use simple, natural language
            - MUST include relevant emojis to make it fun and engaging
            - Add 2-4 emojis throughout the message naturally"""

            # Get salon context for more personalized messages
            salon_context = self._get_salon_context(user_profile)
            context_parts = salon_context.split(", ")
            salon_info = context_parts[0].replace("Salon: ", "") if context_parts else "The Glam Parlour in Westlands"
            service_info = context_parts[1].replace("Service: ", "") if len(context_parts) > 1 else "hair styling"
            sender_info = context_parts[2].replace("Sender: ", "") if len(context_parts) > 2 else "Your friend"

            # Extract first names only
            recipient_first_name = recipient_name.split(' ')[0] if recipient_name else 'bestie'
            sender_first_name = sender_info.split(' ')[0] if sender_info else 'Your friend'

            prompt = f"""Write a personal gift message for a salon appointment that sounds like a real friend wrote it.

            Details:
            - Recipient: {recipient_first_name} ({relationship})
            - Occasion: {occasion}
            - Tone: {tone}
            - Language: {language}
            - Salon: {salon_info}
            - Service: {service_info}
            - Sender: {sender_first_name}

            EXAMPLE FORMAT:
            "Hey {recipient_first_name}! 💕 You're the best {relationship} I could ask for! Go get pampered at {salon_info} - treat yourself to that amazing {service_info} you've been wanting! ✨ You deserve every bit of this pampering. Love, {sender_first_name} 💖"

            REQUIREMENTS:
            - Use the EXACT salon name and location provided: {salon_info}
            - Mention the EXACT service being gifted: {service_info}
            - Sign off with sender's FIRST NAME only: {sender_first_name}
            - Use recipient's FIRST NAME only: {recipient_first_name}
            - Make it sound like a real friend wrote it, not AI
            - Keep it under 100 words
            - MUST include 2-4 relevant emojis naturally throughout the message
            - Be warm, personal, and specific to the gift being given"""

            response = self._call_ai_api(prompt, system_message)

            # Format and clean the AI response
            if 'response' in response and response['response']:
                response['response'] = self._format_gift_message(response['response'])
                logger.info(f"✅ [BACKEND] Formatted gift message")

            # Check if external APIs failed or are not available
            if response.get('source') in ['no_external_apis', 'all_apis_failed']:
                # Get salon context for fallback
                salon_context = self._get_salon_context(user_profile)
                context_parts = salon_context.split(", ")
                salon_info = context_parts[0].replace("Salon: ", "") if context_parts else "The Glam Parlour in Westlands"
                service_info = context_parts[1].replace("Service: ", "") if len(context_parts) > 1 else "hair styling"
                sender_info = context_parts[2].replace("Sender: ", "") if len(context_parts) > 2 else "Your friend"

                # Language-specific contextual fallback messages
                fallback_messages = {
                    'english': [
                        f"Hey {recipient_name or 'gorgeous'}! You're amazing and deserve the best! Go get pampered at {salon_info} - treat yourself to that {service_info} you've been wanting! Love, {sender_info} ✨",
                        f"{recipient_name or 'Bestie'}, you're the best {relationship} ever! Time for some serious pampering at {salon_info}! Get that beautiful {service_info} done and feel like the queen you are! Love, {sender_info} 💫",
                        f"Hey {recipient_name or 'beautiful'}! Go spoil yourself at {salon_info} - you deserve this amazing {service_info}! Can't wait to see your gorgeous new look! Love, {sender_info} 👑"
                    ],
                    'swahili': [
                        f"Habari {recipient_name or 'mrembo'}! Wewe ni mzuri sana! Nenda ujitunze {salon_info} - unastahili {service_info} hii! Mapenzi, {sender_info} ✨",
                        f"{recipient_name or 'Rafiki'}, wewe ni {relationship} mzuri zaidi! Wakati wa kujitunza {salon_info}! Fanya {service_info} uwe mzuri! Mapenzi, {sender_info} 💫",
                        f"Habari {recipient_name or 'malkia'}! Nenda ujitunze {salon_info} - unastahili {service_info} hii! Mapenzi, {sender_info} 👑"
                    ],
                    'sheng': [
                        f"Sasa {recipient_name or 'queen'}! You're the best {relationship} ever! Enda ujipamper {salon_info} - you deserve hii {service_info} yote! Love, {sender_info} ✨",
                        f"{recipient_name or 'Bestie'}, uko top! Time ya kujitunza {salon_info}! Fanya {service_info} yako iwe fire! Love, {sender_info} 💫",
                        f"Eeh {recipient_name or 'mrembo'}! Enda ujispoil {salon_info} - unastahili hii {service_info} na zaidi! Love, {sender_info} 👑"
                    ]
                }

                selected_messages = fallback_messages.get(language, fallback_messages['english'])
                response = {
                    'response': random.choice(selected_messages),
                    'confidence': 0.8,
                    'source': 'local_fallback'
                }

            self._save_to_cache(cache_key, response)
            return response
        else:
            response = {
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
        self._save_to_cache(cache_key, response)
        return response

    def process_voice_command(self, voice_input: str, user_context: Dict) -> Dict:
        """Process voice commands for salon booking and navigation"""
        cache_key = self._get_cache_key(
            f"{voice_input}:{json.dumps(user_context)}",
            "voice_command"
        )

        cached_response = self._load_from_cache(cache_key)
        if cached_response:
            return cached_response

        system_message = """You are an AI voice assistant for a luxury salon booking platform. Process voice commands and provide appropriate responses for booking appointments, finding salons, getting recommendations, and navigating the app."""

        prompt = f"""Process this voice command: "{voice_input}"

        User Context:
        {json.dumps(user_context, indent=2)}

        Determine the intent and provide:
        1. Action to take (book_appointment, find_salon, get_recommendations, navigate, etc.)
        2. Extracted parameters (location, service type, date, time, etc.)
        3. Confidence score (0-1)
        4. Response message for the user
        5. Next steps or follow-up questions if needed

        Return a structured response that the app can use to execute the command."""

        response = self._call_ai_api(prompt, system_message)
        if response.get('source') in ['no_external_apis', 'all_apis_failed']:
            response = {
                'response': 'Sorry, our smart engines are still busy now. Please come back in a few minutes.',
                'source': 'service_unavailable'
            }
        self._save_to_cache(cache_key, response)
        return response

# Create a singleton instance
ai_service = AIService()