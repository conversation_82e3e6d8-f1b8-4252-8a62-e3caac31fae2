# AI Engine Models (Disabled)

This directory previously contained the following AI models:

- **google/flan-t5-base**: A sequence-to-sequence model for structured text generation
- **mistralai/Mistral-7B-Instruct-v0.2**: A powerful instruction-tuned language model

## Status: DISABLED

The local AI Engine has been disabled in favor of using external API providers. The code is kept for reference purposes only.

## How to Re-Enable

If you need to re-enable the local AI Engine:

1. Download the models:
   ```bash
   python download_models.py
   ```

2. Update the `.env` file:
   ```
   AI_PROVIDER=local
   ENABLE_AI_ENGINE=true
   ```

3. Modify the Docker Compose file to include the AI Engine service.

## External API Alternatives

Instead of the local AI Engine, the system now uses one of the following external API providers:

- **Mistral AI**: For production use
- **OpenAI**: For advanced capabilities
- **Hugging Face**: For development and testing

Configure the provider in the `.env` file:
```
AI_PROVIDER=mistral  # or 'openai', 'huggingface'
```
