# Model Placeholder

This is a placeholder for the `flan-t5-base` model files. The actual model files are not included in the repository due to their large size.

## How to Download the Model

To download the model, run the following command from the project root:

```bash
cd ai_engine
python download_models.py --models flan-t5-base
```

This will download the model files from Hugging Face and place them in this directory.

## Model Information

- Name: flan-t5-base
- Source: google/flan-t5-base
- Type: seq2seq
- Size: ~944 MB

## Files Expected in this Directory

- config.json
- generation_config.json
- model.safetensors (large file, not included in repository)
- special_tokens_map.json
- spiece.model
- tokenizer.json
- tokenizer_config.json
