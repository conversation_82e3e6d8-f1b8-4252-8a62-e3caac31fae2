# Model Placeholder

This is a placeholder for the `mistral-7b-instruct` model files. The actual model files are not included in the repository due to their large size.

## How to Download the Model

To download the model, run the following command from the project root:

```bash
cd ai_engine
python download_models.py --models mistral-7b-instruct
```

This will download the model files from Hugging Face and place them in this directory.

## Model Information

- Name: mistral-7b-instruct
- Source: mistralai/Mistral-7B-Instruct-v0.2
- Type: causal
- Size: ~4.1 GB

## Files Expected in this Directory

- config.json
- generation_config.json
- model.safetensors (large file, not included in repository)
- tokenizer.json
- tokenizer_config.json
