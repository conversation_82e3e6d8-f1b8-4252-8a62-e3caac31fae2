# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
salonvenv/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Database
*.sqlite3
db.sqlite3

# Logs
*.log

# Media files (optional - uncomment if you don't want to include uploaded files)
# media/

# Static files (will be collected in container)
staticfiles/

# Environment variables
.env
.env.local
.env.production

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
