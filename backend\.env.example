# Django Backend Environment Variables (backend/.env.example)
# Copy this file to backend/.env to configure your local Django backend development
# For frontend React variables, see root .env.example

# IPinfo API Configuration (for geolocation services)
# Get your free API key from https://ipinfo.io/
# Used by backend/salons_app/utils.py
IPINFO_TOKEN=your_ipinfo_api_key_here

# Django Configuration
DJANGO_SECRET_KEY=your_django_secret_key_here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
USE_SQLITE=True

# PostgreSQL Configuration (for Docker/Production)
# POSTGRES_DB=salon_db
# POSTGRES_USER=salon_user
# POSTGRES_PASSWORD=salon_pass
# POSTGRES_HOST=db
# POSTGRES_PORT=5432

# AI Configuration (for backend AI services)
AI_PROVIDER=groq
ENABLE_AI_ENGINE=true
AI_CACHE_DURATION=3600
AI_TIMEOUT=30
