import sys
import os
import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
import logging

# AI service import - handle gracefully if not available
try:
    # Try local ai_engine first (Docker), then parent directory (development)
    try:
        from ai_engine.ai_service import ai_service
    except ImportError:
        # Fallback for development environment
        import sys
        sys.path.append(os.path.join(settings.BASE_DIR, '..'))
        from ai_engine.ai_service import ai_service
except ImportError as e:
    logging.warning(f"AI service not available: {e}")
    ai_service = None

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
def smart_scheduling(request):
    """AI Smart Scheduling endpoint"""
    try:
        data = json.loads(request.body)
        user_preferences = data.get('userPreferences', {})
        salon_availability = data.get('salonAvailability', {})
        service_details = data.get('serviceDetails', {})
        
        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)
        
        result = ai_service.generate_smart_schedule(
            user_preferences, 
            salon_availability, 
            service_details
        )
        
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Smart scheduling error: {e}")
        return JsonResponse({
            'error': 'Failed to generate smart schedule',
            'fallback': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def virtual_tryon(request):
    """AI Virtual Try-On endpoint"""
    try:
        data = json.loads(request.body)
        user_profile = data.get('userProfile', {})
        selected_style = data.get('selectedStyle', {})
        
        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)
        
        result = ai_service.generate_virtual_tryon(user_profile, selected_style)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Virtual try-on error: {e}")
        return JsonResponse({
            'error': 'Failed to generate virtual try-on',
            'fallback': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def trend_predictions(request):
    """AI Trend Predictor endpoint"""
    try:
        data = json.loads(request.body)
        platform_data = data.get('platformData', {})
        
        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)
        
        result = ai_service.generate_trend_predictions(platform_data)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Trend predictions error: {e}")
        return JsonResponse({
            'error': 'Failed to generate trend predictions',
            'fallback': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def analytics_report(request):
    """AI Analytics Dashboard endpoint"""
    try:
        data = json.loads(request.body)
        analytics_data = data.get('analyticsData', {})
        
        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)
        
        result = ai_service.generate_analytics_report(analytics_data)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Analytics report error: {e}")
        return JsonResponse({
            'error': 'Failed to generate analytics report',
            'fallback': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def style_recommendations(request):
    """AI Style Advisor endpoint"""
    try:
        data = json.loads(request.body)
        user_profile = data.get('userProfile', {})
        
        if not ai_service:
            return JsonResponse({
                'response': "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
                'confidence': 0.0,
                'source': 'service_unavailable',
                'isServiceUnavailable': True
            }, status=503)
        
        result = ai_service.generate_style_recommendations(user_profile)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Style recommendations error: {e}")
        return JsonResponse({
            'response': "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
            'confidence': 0.0,
            'source': 'service_unavailable',
            'isServiceUnavailable': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def salon_matching(request):
    """AI Salon Matcher endpoint"""
    try:
        data = json.loads(request.body)
        user_preferences = data.get('userPreferences', {})
        available_salons = data.get('availableSalons', [])
        
        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)
        
        result = ai_service.find_perfect_salons(user_preferences, available_salons)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Salon matching error: {e}")
        return JsonResponse({
            'error': 'Failed to find salon matches',
            'fallback': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def smart_notifications(request):
    """Smart Notifications endpoint"""
    try:
        data = json.loads(request.body)
        user_context = data.get('userContext', {})
        salon_data = data.get('salonData', {})
        
        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)
        
        result = ai_service.generate_smart_notifications(user_context, salon_data)
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Smart notifications error: {e}")
        return JsonResponse({
            'error': 'Failed to generate smart notifications',
            'fallback': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def gift_messages(request):
    """AI Gift Message Generation endpoint with East African localization"""
    try:
        data = json.loads(request.body)
        relationship = data.get('relationship', '')
        occasion = data.get('occasion', '')
        tone = data.get('tone', '')
        recipient_name = data.get('recipientName', '')
        user_profile = data.get('userProfile', {})  # For cultural localization

        if not ai_service:
            return JsonResponse({
                'response': "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
                'confidence': 0.0,
                'source': 'service_unavailable',
                'isServiceUnavailable': True
            }, status=503)

        result = ai_service.generate_gift_message(
            relationship,
            occasion,
            tone,
            recipient_name,
            user_profile
        )
        return JsonResponse(result)

    except Exception as e:
        logger.error(f"Gift messages error: {e}")
        return JsonResponse({
            'response': "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
            'confidence': 0.0,
            'source': 'service_unavailable',
            'isServiceUnavailable': True
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def voice_assistant(request):
    """AI Voice Assistant endpoint"""
    try:
        data = json.loads(request.body)
        voice_input = data.get('voiceInput', '')
        user_context = data.get('userContext', {})

        if not ai_service:
            return JsonResponse({
                'error': 'AI service not available',
                'fallback': True
            }, status=503)

        result = ai_service.process_voice_command(
            voice_input,
            user_context
        )

        return JsonResponse(result)

    except Exception as e:
        logger.error(f"Voice assistant error: {e}")
        return JsonResponse({
            'error': 'Failed to process voice command',
            'fallback': True
        }, status=500)

@require_http_methods(["GET"])
def health_check(request):
    """Health check endpoint for AI service"""
    try:
        if ai_service:
            return JsonResponse({
                'status': 'healthy',
                'ai_service': 'available'
            })
        else:
            return JsonResponse({
                'status': 'degraded',
                'ai_service': 'unavailable'
            }, status=503)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'error': str(e)
        }, status=500)