#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

from salons_app.models import Booking, Salon, Service, Staff

def check_and_populate_bookings():
    # Check existing bookings for KINYANJUI
    kinyanjui_bookings = Booking.objects.filter(userName__icontains='KINYANJUI')
    print(f"Found {kinyanjui_bookings.count()} bookings for KINYANJUI")
    
    if kinyanjui_bookings.count() == 0:
        print("No bookings found for KINYANJUI. Creating sample bookings...")
        
        # Get some salons, services, and staff
        salons = Salon.objects.all()[:3]
        if not salons:
            print("No salons found in database")
            return
            
        # Create sample bookings for KINYANJUI
        sample_bookings = [
            {
                'userName': 'KINYAN<PERSON><PERSON>',
                'salon': salons[0],
                'service': Service.objects.filter(salon=salons[0]).first(),
                'staff': Staff.objects.filter(salon=salons[0]).first(),
                'date': datetime.now().date() + timedelta(days=7),
                'time': '10:00:00',
                'status': 'Confirmed',
                'notes': 'Regular appointment'
            },
            {
                'userName': 'KINYANJUI',
                'salon': salons[1] if len(salons) > 1 else salons[0],
                'service': Service.objects.filter(salon=salons[1] if len(salons) > 1 else salons[0]).first(),
                'staff': Staff.objects.filter(salon=salons[1] if len(salons) > 1 else salons[0]).first(),
                'date': datetime.now().date() + timedelta(days=14),
                'time': '14:30:00',
                'status': 'Confirmed',
                'notes': 'Hair styling appointment'
            },
            {
                'userName': 'KINYANJUI',
                'salon': salons[2] if len(salons) > 2 else salons[0],
                'service': Service.objects.filter(salon=salons[2] if len(salons) > 2 else salons[0]).first(),
                'staff': Staff.objects.filter(salon=salons[2] if len(salons) > 2 else salons[0]).first(),
                'date': datetime.now().date() - timedelta(days=3),
                'time': '16:00:00',
                'status': 'Completed',
                'notes': 'Completed appointment'
            }
        ]
        
        created_bookings = []
        for booking_data in sample_bookings:
            if booking_data['service'] and booking_data['staff']:
                booking = Booking.objects.create(**booking_data)
                created_bookings.append(booking)
                print(f"Created booking: {booking.salon.name} - {booking.service.name} on {booking.date}")
        
        print(f"Successfully created {len(created_bookings)} bookings for KINYANJUI")
    else:
        print("KINYANJUI already has bookings:")
        for booking in kinyanjui_bookings:
            print(f"- {booking.salon.name} - {booking.service.name} on {booking.date} ({booking.status})")

if __name__ == '__main__':
    check_and_populate_bookings() 