#!/usr/bin/env python
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

from django.contrib.auth.models import User

def create_users():
    print("Creating users...")
    
    # Create/update superuser peter
    peter, created = User.objects.get_or_create(
        username='peter',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': 'Admin',
            'is_staff': True,
            'is_superuser': True
        }
    )
    peter.set_password('aluru742!!')
    peter.is_staff = True
    peter.is_superuser = True
    peter.save()
    print(f"{'Created' if created else 'Updated'} superuser: peter")
    
    # Create/update superuser admin
    admin, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'is_staff': True,
            'is_superuser': True
        }
    )
    admin.set_password('aluru742!!')
    admin.is_staff = True
    admin.is_superuser = True
    admin.save()
    print(f"{'Created' if created else 'Updated'} superuser: admin")
    
    # Update all existing superusers to have the same password
    superusers = User.objects.filter(is_superuser=True)
    for user in superusers:
        user.set_password('aluru742!!')
        user.save()
        print(f"Updated password for superuser: {user.username}")
    
    # Create normal user apollo
    apollo, created = User.objects.get_or_create(
        username='apollo',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Apollo',
            'last_name': 'Customer',
            'is_staff': False,
            'is_superuser': False
        }
    )
    apollo.set_password('aluru742!!')
    apollo.is_staff = False
    apollo.is_superuser = False
    apollo.save()
    print(f"{'Created' if created else 'Updated'} customer: apollo")
    
    print("\nUser creation completed!")
    print("Superusers: peter, admin (password: aluru742!!)")
    print("Customer: apollo (password: aluru742!!)")

if __name__ == '__main__':
    create_users()
