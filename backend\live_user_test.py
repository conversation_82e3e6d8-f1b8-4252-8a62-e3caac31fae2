#!/usr/bin/env python3
"""
LIVE USER GEOLOCATION TEST - Real User Location Detection
This script will detect YOUR actual location and find nearest salons
"""

import os
import sys
import django
import math
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
sys.path.append('.')
django.setup()

from salons_app.models import Salon

def haversine_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two points using Haversine formula"""
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    return c * 6371  # Earth radius in km

def get_user_location_from_ip():
    """Get user's actual location using IP geolocation services"""
    
    print("🌍 DETECTING YOUR ACTUAL LOCATION...")
    print("=" * 50)
    
    # Multiple IP geolocation services for accuracy
    services = [
        {
            'name': 'ipapi.co',
            'url': 'https://ipapi.co/json/',
            'parser': lambda data: {
                'latitude': data.get('latitude'),
                'longitude': data.get('longitude'),
                'city': data.get('city'),
                'region': data.get('region'),
                'country': data.get('country_name'),
                'ip': data.get('ip')
            }
        },
        {
            'name': 'ip-api.com',
            'url': 'http://ip-api.com/json/',
            'parser': lambda data: {
                'latitude': data.get('lat'),
                'longitude': data.get('lon'),
                'city': data.get('city'),
                'region': data.get('regionName'),
                'country': data.get('country'),
                'ip': data.get('query')
            }
        },
        {
            'name': 'ipinfo.io',
            'url': 'https://ipinfo.io/json',
            'parser': lambda data: {
                'latitude': float(data.get('loc', ',').split(',')[0]) if data.get('loc') else None,
                'longitude': float(data.get('loc', ',').split(',')[1]) if data.get('loc') else None,
                'city': data.get('city'),
                'region': data.get('region'),
                'country': data.get('country'),
                'ip': data.get('ip')
            }
        }
    ]
    
    for service in services:
        try:
            print(f"🔍 Trying {service['name']}...")
            response = requests.get(service['url'], timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                location = service['parser'](data)
                
                if location['latitude'] and location['longitude']:
                    print(f"✅ Location detected via {service['name']}:")
                    print(f"   📍 Coordinates: ({location['latitude']}, {location['longitude']})")
                    print(f"   🏙️  City: {location['city']}")
                    print(f"   🗺️  Region: {location['region']}")
                    print(f"   🌍 Country: {location['country']}")
                    print(f"   🌐 IP: {location['ip']}")
                    return location
                    
        except Exception as e:
            print(f"❌ {service['name']} failed: {e}")
            continue
    
    # Fallback: Manual input
    print("\n❌ Automatic location detection failed.")
    print("📍 Please enter your location manually:")
    
    try:
        lat = float(input("Enter your latitude: "))
        lon = float(input("Enter your longitude: "))
        
        return {
            'latitude': lat,
            'longitude': lon,
            'city': 'Manual Input',
            'region': 'Manual Input',
            'country': 'Manual Input',
            'ip': 'Manual'
        }
    except ValueError:
        print("❌ Invalid coordinates entered.")
        return None

def find_nearest_salons(user_location):
    """Find nearest salons to user's actual location"""
    
    if not user_location:
        print("❌ Cannot find salons without user location.")
        return
    
    user_lat = user_location['latitude']
    user_lon = user_location['longitude']
    
    print(f"\n🎯 FINDING NEAREST SALONS TO YOUR LOCATION:")
    print("=" * 50)
    print(f"📍 Your Location: ({user_lat}, {user_lon})")
    print(f"🏙️  Detected City: {user_location['city']}, {user_location['region']}")
    
    # Get all salons
    salons = Salon.objects.all()
    salon_distances = []
    
    print(f"\n🔍 Checking {salons.count()} salons in database...")
    
    for salon in salons:
        distance = haversine_distance(user_lat, user_lon, salon.latitude, salon.longitude)
        salon_distances.append({
            'salon': salon,
            'distance': distance
        })
    
    # Sort by distance (nearest first)
    salon_distances.sort(key=lambda x: x['distance'])
    
    print(f"\n📊 RESULTS - SALONS NEAREST TO YOU:")
    print("-" * 50)
    
    for i, result in enumerate(salon_distances[:10]):  # Show top 10
        salon = result['salon']
        distance = result['distance']
        
        if i == 0:
            print(f"🥇 NEAREST: {salon.name}")
        elif i == 1:
            print(f"🥈 2nd NEAREST: {salon.name}")
        elif i == 2:
            print(f"🥉 3rd NEAREST: {salon.name}")
        else:
            print(f"{i+1}. {salon.name}")
        
        print(f"   📍 Location: {salon.town}, {salon.county}")
        print(f"   📏 Distance: {distance:.2f} km from you")
        print(f"   🗺️  Coordinates: ({salon.latitude}, {salon.longitude})")
        print(f"   📞 Phone: {salon.phone}")
        print()
    
    # Test radius filtering
    print("🎯 RADIUS FILTERING TEST:")
    print("-" * 30)
    
    for radius in [5, 10, 20, 50]:
        within_radius = [r for r in salon_distances if r['distance'] <= radius]
        print(f"Within {radius}km: {len(within_radius)} salon(s)")
        
        if within_radius:
            for result in within_radius[:3]:  # Show top 3 in each radius
                salon = result['salon']
                print(f"  • {salon.name} ({result['distance']:.2f}km) - {salon.town}")
        print()
    
    # Special focus on our test salons
    print("🎯 FOCUS ON TEST SALONS (Ngong & Karen):")
    print("-" * 40)
    
    test_salon_names = ['Ngong Beauty Palace', 'Karen Glamour Studio']
    test_results = [r for r in salon_distances if r['salon'].name in test_salon_names]
    
    if test_results:
        for result in test_results:
            salon = result['salon']
            distance = result['distance']
            print(f"📍 {salon.name}:")
            print(f"   🏙️  {salon.town}, {salon.county}")
            print(f"   📏 {distance:.2f} km from your location")
            print(f"   🗺️  ({salon.latitude}, {salon.longitude})")
            print()
        
        # Determine which is closer
        if len(test_results) >= 2:
            closest_test = min(test_results, key=lambda x: x['distance'])
            print(f"🏆 CLOSEST TEST SALON TO YOU: {closest_test['salon'].name}")
            print(f"   📏 Distance: {closest_test['distance']:.2f} km")
    else:
        print("❌ Test salons (Ngong/Karen) not found in database")

def test_with_actual_user_coordinates():
    """Test with user's actual GPS coordinates"""

    print("🎯 TESTING WITH YOUR ACTUAL GPS COORDINATES")
    print("=" * 60)

    # Your actual GPS coordinates from your device
    actual_location = {
        'latitude': -1.4014745,
        'longitude': 36.698235,
        'city': 'Olkeri ward',
        'region': 'Kajiado County',
        'country': 'Kenya',
        'source': 'actual_gps'
    }

    print("📍 Using your ACTUAL GPS coordinates:")
    print(f"   Latitude: {actual_location['latitude']}")
    print(f"   Longitude: {actual_location['longitude']}")
    print(f"   Location: {actual_location['city']}, {actual_location['region']}")
    print()

    # Test with actual coordinates
    find_nearest_salons(actual_location)

    print("\n🎯 GPS-FIRST SYSTEM VALIDATION:")
    print("✅ This is how accurate the system will be with GPS!")
    print("✅ Ngong salon should be nearest (~7.6km)")
    print("✅ Karen salon should be second (~9.2km)")

def main():
    """Main function to run live user location test"""

    print("🚀 SALONGENZ GPS-FIRST GEOLOCATION TEST")
    print("=" * 60)
    print("Testing both IP detection and actual GPS coordinates!")
    print()

    # Test 1: IP-based detection (less accurate)
    print("🌐 TEST 1: IP-BASED LOCATION DETECTION")
    print("-" * 40)
    user_location = get_user_location_from_ip()

    if user_location:
        find_nearest_salons(user_location)
    else:
        print("❌ IP detection failed")

    print("\n" + "="*60 + "\n")

    # Test 2: Actual GPS coordinates (accurate)
    print("📱 TEST 2: ACTUAL GPS COORDINATES")
    print("-" * 40)
    test_with_actual_user_coordinates()

    print("\n🎉 COMPREHENSIVE TEST COMPLETE!")
    print("✅ GPS-first system ready for production!")
    print("🎯 Users will get accurate results with GPS enabled!")

if __name__ == "__main__":
    main()
