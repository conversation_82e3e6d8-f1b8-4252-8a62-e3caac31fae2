import json
import requests
from decimal import Decimal
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from .models import Payment, PaymentLog
import logging

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def initiate_mpesa_payment(request):
    """Initiate M-Pesa payment via Paystack or direct integration"""
    try:
        data = request.data
        
        # Validate required fields
        required_fields = ['amount', 'phone_number', 'email', 'reference']
        for field in required_fields:
            if not data.get(field):
                return Response({
                    'error': f'{field} is required'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create payment record
        payment = Payment.objects.create(
            amount=Decimal(str(data['amount'])),
            currency=data.get('currency', 'KES'),
            payment_method='mpesa',
            transaction_id=data['reference'],
            phone_number=data['phone_number'],
            email=data['email'],
            description=data.get('description', 'Salon service payment'),
            metadata=data.get('metadata', {}),
            status='pending'
        )
        
        # Log payment initiation
        PaymentLog.objects.create(
            payment=payment,
            action='payment_initiated',
            details={
                'phone': data['phone_number'],
                'amount': str(data['amount']),
                'reference': data['reference']
            }
        )
        
        # For testing with phone number 700000000, simulate successful payment
        if data['phone_number'] == '700000000':
            return Response({
                'success': True,
                'message': 'Test payment initiated successfully',
                'payment_id': payment.id,
                'transaction_id': payment.transaction_id,
                'status': 'pending',
                'test_mode': True
            })
        
        # In production, integrate with actual M-Pesa API here
        # For now, return success for testing
        return Response({
            'success': True,
            'message': 'M-Pesa payment initiated successfully',
            'payment_id': payment.id,
            'transaction_id': payment.transaction_id,
            'status': 'pending'
        })
        
    except Exception as e:
        logger.error(f"Error initiating M-Pesa payment: {str(e)}")
        return Response({
            'error': 'Failed to initiate payment'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def verify_payment(request):
    """Verify payment status"""
    try:
        data = request.data
        transaction_id = data.get('transaction_id')
        
        if not transaction_id:
            return Response({
                'error': 'transaction_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            payment = Payment.objects.get(transaction_id=transaction_id)
        except Payment.DoesNotExist:
            return Response({
                'error': 'Payment not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # For test phone number 700000000, simulate successful payment
        if payment.phone_number == '700000000' and payment.status == 'pending':
            payment.status = 'completed'
            payment.completed_at = timezone.now()
            payment.gateway_reference = f"TEST_REF_{payment.id}"
            payment.save()
            
            PaymentLog.objects.create(
                payment=payment,
                action='payment_completed',
                details={
                    'test_mode': True,
                    'gateway_reference': payment.gateway_reference
                }
            )
        
        return Response({
            'success': True,
            'payment': {
                'id': payment.id,
                'transaction_id': payment.transaction_id,
                'status': payment.status,
                'amount': str(payment.amount),
                'currency': payment.currency,
                'phone_number': payment.phone_number,
                'gateway_reference': payment.gateway_reference,
                'completed_at': payment.completed_at.isoformat() if payment.completed_at else None
            }
        })
        
    except Exception as e:
        logger.error(f"Error verifying payment: {str(e)}")
        return Response({
            'error': 'Failed to verify payment'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def simulate_mpesa_callback(request):
    """Simulate M-Pesa callback for testing"""
    try:
        data = request.data
        transaction_id = data.get('transaction_id')
        success = data.get('success', True)
        
        if not transaction_id:
            return Response({
                'error': 'transaction_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            payment = Payment.objects.get(transaction_id=transaction_id)
        except Payment.DoesNotExist:
            return Response({
                'error': 'Payment not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        if success:
            payment.status = 'completed'
            payment.completed_at = timezone.now()
            payment.gateway_reference = f"MPESA_TEST_{payment.id}"
            action = 'payment_completed'
        else:
            payment.status = 'failed'
            action = 'payment_failed'
        
        payment.save()
        
        PaymentLog.objects.create(
            payment=payment,
            action=action,
            details={
                'simulated': True,
                'success': success,
                'gateway_reference': payment.gateway_reference if success else None
            }
        )
        
        return Response({
            'success': True,
            'message': f'Payment {action.replace("_", " ")}',
            'payment_status': payment.status
        })
        
    except Exception as e:
        logger.error(f"Error in M-Pesa callback simulation: {str(e)}")
        return Response({
            'error': 'Failed to process callback'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def payment_status(request, transaction_id):
    """Get payment status by transaction ID"""
    try:
        payment = Payment.objects.get(transaction_id=transaction_id)
        
        return Response({
            'success': True,
            'payment': {
                'id': payment.id,
                'transaction_id': payment.transaction_id,
                'status': payment.status,
                'amount': str(payment.amount),
                'currency': payment.currency,
                'payment_method': payment.payment_method,
                'phone_number': payment.phone_number,
                'email': payment.email,
                'description': payment.description,
                'gateway_reference': payment.gateway_reference,
                'created_at': payment.created_at.isoformat(),
                'completed_at': payment.completed_at.isoformat() if payment.completed_at else None
            }
        })
        
    except Payment.DoesNotExist:
        return Response({
            'error': 'Payment not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error getting payment status: {str(e)}")
        return Response({
            'error': 'Failed to get payment status'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
