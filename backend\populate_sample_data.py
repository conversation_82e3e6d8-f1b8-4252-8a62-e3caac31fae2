#!/usr/bin/env python
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

from salons_app.models import Salon, Service, Staff
from django.contrib.auth.models import User

def populate_sample_data():
    print("Populating sample data...")
    
    # Create a test user if it doesn't exist
    user, created = User.objects.get_or_create(
        username='testvendor',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Vendor'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.username}")
    
    # Create sample salons
    salons_data = [
        {
            'name': 'Glam Studio Westlands',
            'address': '123 Westlands Road',
            'county': 'Nairobi County',
            'town': 'Westlands',
            'phone': '+254700123456',
            'email': '<EMAIL>',
            'latitude': -1.2544,
            'longitude': 36.8133,
            'description': 'Premium beauty salon in Westlands'
        },
        {
            'name': 'Beauty Palace Karen',
            'address': '456 Karen Road',
            'county': 'Nairobi County',
            'town': 'Karen',
            'phone': '+254700123457',
            'email': '<EMAIL>',
            'latitude': -1.3191,
            'longitude': 36.7089,
            'description': 'Luxury beauty services in Karen'
        },
        {
            'name': 'Elite Salon Kilimani',
            'address': '789 Kilimani Road',
            'county': 'Nairobi County',
            'town': 'Kilimani',
            'phone': '+254700123458',
            'email': '<EMAIL>',
            'latitude': -1.3008,
            'longitude': 36.8070,
            'description': 'Elite hair and beauty services'
        },
        {
            'name': 'Coastal Beauty Nyali',
            'address': '101 Nyali Road',
            'county': 'Mombasa County',
            'town': 'Nyali',
            'phone': '+254700123459',
            'email': '<EMAIL>',
            'latitude': -4.0435,
            'longitude': 39.6682,
            'description': 'Coastal beauty and spa services'
        },
        {
            'name': 'City Center Salon',
            'address': '202 CBD Street',
            'county': 'Kisumu County',
            'town': 'Milimani',
            'phone': '+254700123460',
            'email': '<EMAIL>',
            'latitude': -0.1022,
            'longitude': 34.7617,
            'description': 'City center beauty services'
        },
        {
            'name': 'Modern Beauty Nakuru',
            'address': '303 Nakuru Road',
            'county': 'Nakuru County',
            'town': 'Milimani',
            'phone': '+254700123461',
            'email': '<EMAIL>',
            'latitude': -0.3031,
            'longitude': 36.0800,
            'description': 'Modern beauty and styling'
        }
    ]
    
    created_salons = []
    for salon_data in salons_data:
        salon, created = Salon.objects.get_or_create(
            name=salon_data['name'],
            defaults=salon_data
        )
        if created:
            created_salons.append(salon)
            print(f"Created salon: {salon.name}")
    
    # Create sample services
    services_data = [
        {'name': 'Hair Cut & Style', 'price': 2500, 'duration': 60, 'category': 'Hair'},
        {'name': 'Hair Coloring', 'price': 4500, 'duration': 120, 'category': 'Hair'},
        {'name': 'Manicure & Pedicure', 'price': 1800, 'duration': 45, 'category': 'Nails'},
        {'name': 'Facial Treatment', 'price': 3200, 'duration': 75, 'category': 'Skincare'},
        {'name': 'Eyebrow Threading', 'price': 800, 'duration': 20, 'category': 'Beauty'},
        {'name': 'Hair Wash & Blow Dry', 'price': 1500, 'duration': 30, 'category': 'Hair'},
        {'name': 'Bridal Makeup', 'price': 8000, 'duration': 180, 'category': 'Makeup'},
        {'name': 'Deep Conditioning', 'price': 2200, 'duration': 45, 'category': 'Hair'},
        {'name': 'Hair Styling', 'price': 3000, 'duration': 90, 'category': 'Hair'},
        {'name': 'Nail Art', 'price': 2500, 'duration': 60, 'category': 'Nails'}
    ]
    
    created_services = []
    for i, service_data in enumerate(services_data):
        # Assign services to salons in a round-robin fashion
        salon = created_salons[i % len(created_salons)]
        service, created = Service.objects.get_or_create(
            name=service_data['name'],
            salon=salon,
            defaults={
                'price': service_data['price'],
                'duration': service_data['duration']
            }
        )
        if created:
            created_services.append(service)
            print(f"Created service: {service.name} at {service.salon.name}")
    
    # Create sample staff
    staff_data = [
        {'name': 'Sarah Wanjiku', 'role': 'Hair Stylist', 'specialty': 'Hair Styling'},
        {'name': 'Grace Muthoni', 'role': 'Nail Technician', 'specialty': 'Nail Art'},
        {'name': 'Mary Akinyi', 'role': 'Makeup Artist', 'specialty': 'Bridal Makeup'},
        {'name': 'Jane Wambui', 'role': 'Beautician', 'specialty': 'Facial Treatment'},
        {'name': 'Lucy Njeri', 'role': 'Senior Stylist', 'specialty': 'Hair Coloring'},
        {'name': 'Rose Atieno', 'role': 'Spa Therapist', 'specialty': 'Spa Treatment'}
    ]
    
    created_staff = []
    for i, staff_data_item in enumerate(staff_data):
        # Assign staff to salons in a round-robin fashion
        salon = created_salons[i % len(created_salons)]
        staff, created = Staff.objects.get_or_create(
            name=staff_data_item['name'],
            salon=salon,
            defaults={
                'role': staff_data_item['role'],
                'specialty': staff_data_item['specialty']
            }
        )
        if created:
            # Assign some services to staff
            services_for_staff = created_services[i % len(created_services):(i % len(created_services)) + 2]
            staff.services.set(services_for_staff)
            created_staff.append(staff)
            print(f"Created staff: {staff.name} at {staff.salon.name}")
    
    print(f"\nSample data populated successfully!")
    print(f"Created {len(created_salons)} salons")
    print(f"Created {len(created_services)} services")
    print(f"Created {len(created_staff)} staff members")

if __name__ == '__main__':
    populate_sample_data() 