from django.contrib import admin
from .models import Salon, Service, Staff, Booking, Recommendation, FriendRecommendation, UserLocation, Friendship, StylePost, RegionalTrending, SalonAnalytics, CustomerAnalytics, RevenueAnalytics, PerformanceMetrics, UserProfile, Customer

@admin.register(Salon)
class SalonAdmin(admin.ModelAdmin):
    list_display = ('name', 'vendor', 'imageUrl', 'image')
    search_fields = ('name', 'county', 'town', 'address', 'phone', 'email')
    list_filter = ('county', 'town')

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'salon', 'price', 'duration')
    search_fields = ('name', 'salon__name')
    list_filter = ('salon', 'price')

@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    list_display = ('name', 'role', 'salon')
    search_fields = ('name', 'role', 'salon__name')
    list_filter = ('role', 'salon')

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('userName', 'salon', 'service', 'date', 'time', 'status', 'is_gift')
    search_fields = ('userName', 'salon__name', 'service__name')
    list_filter = ('status', 'date', 'is_gift', 'salon')

@admin.register(Recommendation)
class RecommendationAdmin(admin.ModelAdmin):
    list_display = ('type', 'recommended_item', 'score', 'reason', 'created_at')
    search_fields = ('type', 'reason')
    list_filter = ('type', 'created_at')

@admin.register(FriendRecommendation)
class FriendRecommendationAdmin(admin.ModelAdmin):
    list_display = ('from_user_id', 'to_user_id', 'recommended_item', 'message', 'created_at')
    search_fields = ('from_user_id', 'to_user_id', 'message')
    list_filter = ('created_at',)

@admin.register(UserLocation)
class UserLocationAdmin(admin.ModelAdmin):
    list_display = ('user_id', 'latitude', 'longitude', 'county', 'town', 'last_updated')
    search_fields = ('user_id', 'county', 'town')
    list_filter = ('county', 'last_updated')

@admin.register(Friendship)
class FriendshipAdmin(admin.ModelAdmin):
    list_display = ('from_user_id', 'to_user_id', 'created_at')
    search_fields = ('from_user_id', 'to_user_id')
    list_filter = ('created_at',)

@admin.register(StylePost)
class StylePostAdmin(admin.ModelAdmin):
    list_display = ('user_id', 'booking', 'message', 'likes', 'created_at')
    search_fields = ('user_id', 'message')
    list_filter = ('created_at', 'likes')

@admin.register(RegionalTrending)
class RegionalTrendingAdmin(admin.ModelAdmin):
    list_display = ('county', 'town', 'trending_item', 'score', 'period', 'created_at')
    search_fields = ('county', 'town')
    list_filter = ('county', 'period', 'created_at')

# Analytics Admin
@admin.register(SalonAnalytics)
class SalonAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('salon', 'date', 'total_bookings', 'completed_bookings', 'total_revenue', 'average_rating')
    search_fields = ('salon__name',)
    list_filter = ('date', 'salon')
    readonly_fields = ('total_bookings', 'completed_bookings', 'cancelled_bookings', 'total_revenue', 'average_rating', 'customer_satisfaction_score')

@admin.register(CustomerAnalytics)
class CustomerAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('user_id', 'total_bookings', 'total_spent', 'favorite_salon', 'booking_frequency', 'last_booking_date')
    search_fields = ('user_id', 'favorite_salon__name')
    list_filter = ('last_booking_date', 'booking_frequency')
    readonly_fields = ('total_bookings', 'total_spent', 'average_booking_value', 'booking_frequency')

@admin.register(RevenueAnalytics)
class RevenueAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('salon', 'period', 'period_start', 'period_end', 'total_revenue', 'gift_bookings_revenue')
    search_fields = ('salon__name',)
    list_filter = ('period', 'period_start', 'salon')
    readonly_fields = ('total_revenue', 'service_revenue', 'staff_revenue', 'gift_bookings_revenue', 'regular_bookings_revenue')

@admin.register(PerformanceMetrics)
class PerformanceMetricsAdmin(admin.ModelAdmin):
    list_display = ('salon', 'date', 'average_booking_duration', 'customer_retention_rate')
    search_fields = ('salon__name',)
    list_filter = ('date', 'salon')
    readonly_fields = ('staff_performance', 'service_popularity', 'peak_booking_hours', 'customer_satisfaction_trends')

admin.site.register(UserProfile)

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone', 'salon', 'user', 'created_at')
    search_fields = ('name', 'email', 'phone')
    list_filter = ('salon',)
    autocomplete_fields = ['user', 'salon']
