"""
Intelligent Location System for SalonGenz
Handles town-to-county mapping and coordinate validation for all Kenya counties
"""

# Comprehensive Kenya Town-to-County Mapping
KENYA_TOWN_COUNTY_MAP = {
    # Nairobi County
    'Nairobi': 'Nairobi County',
    'Nairobi CBD': 'Nairobi County',
    'Westlands': 'Nairobi County',
    'Karen': 'Nairobi County',
    'Langata': 'Nairobi County',
    'Kasarani': 'Nairobi County',
    'Embakasi': 'Nairobi County',
    'Kibera': 'Nairobi County',
    'Mathare': 'Nairobi County',
    'Dagoretti': 'Nairobi County',
    'Roysambu': 'Nairobi County',
    
    # Kajiado County (CRITICAL: Kiserian is here!)
    'Kiserian': 'Kajiado County',
    'Ongata Rongai': 'Kajiado County',
    'Ngong': 'Kajiado County',
    'Kitengela': 'Kajiado County',
    'Kajiado': 'Kajiado County',
    'Namanga': 'Kajiado County',
    'Magadi': 'Kajiado County',
    'Bissil': 'Kajiado County',
    
    # Kiambu County
    'Kiambu': 'Kiambu County',
    'Thika': 'Kiambu County',
    'Ruiru': 'Kiambu County',
    'Juja': 'Kiambu County',
    'Limuru': 'Kiambu County',
    'Kikuyu': 'Kiambu County',
    'Githunguri': 'Kiambu County',
    'Gatundu': 'Kiambu County',
    
    # Mombasa County
    'Mombasa': 'Mombasa County',
    'Likoni': 'Mombasa County',
    'Changamwe': 'Mombasa County',
    'Jomba': 'Mombasa County',
    'Kisauni': 'Mombasa County',
    'Nyali': 'Mombasa County',
    
    # Kisumu County
    'Kisumu': 'Kisumu County',
    'Maseno': 'Kisumu County',
    'Ahero': 'Kisumu County',
    'Muhoroni': 'Kisumu County',
    'Chemelil': 'Kisumu County',
    
    # Nakuru County
    'Nakuru': 'Nakuru County',
    'Naivasha': 'Nakuru County',
    'Gilgil': 'Nakuru County',
    'Molo': 'Nakuru County',
    'Njoro': 'Nakuru County',
    
    # Uasin Gishu County
    'Eldoret': 'Uasin Gishu County',
    'Moiben': 'Uasin Gishu County',
    'Turbo': 'Uasin Gishu County',
    'Ainabkoi': 'Uasin Gishu County',
    
    # Machakos County
    'Machakos': 'Machakos County',
    'Athi River': 'Machakos County',
    'Mavoko': 'Machakos County',
    'Kangundo': 'Machakos County',
    
    # Meru County
    'Meru': 'Meru County',
    'Maua': 'Meru County',
    'Mikinduri': 'Meru County',
    'Nkubu': 'Meru County',
    
    # Add more counties as needed...
}

# Enhanced County Coordinate Bounds (Expanded for vendor protection)
KENYA_COUNTY_BOUNDS = {
    'Nairobi County': {
        'lat_min': -1.45, 'lat_max': -1.15,
        'lon_min': 36.65, 'lon_max': 37.05,
        'center': (-1.286389, 36.817223)
    },
    'Kajiado County': {  # CRITICAL: Kiserian is here
        'lat_min': -2.50, 'lat_max': -1.20,
        'lon_min': 36.40, 'lon_max': 37.20,
        'center': (-1.85, 36.80)
    },
    'Kiambu County': {
        'lat_min': -1.20, 'lat_max': -0.80,
        'lon_min': 36.70, 'lon_max': 37.20,
        'center': (-1.00, 36.95)
    },
    'Mombasa County': {
        'lat_min': -4.20, 'lat_max': -3.80,
        'lon_min': 39.45, 'lon_max': 39.85,
        'center': (-4.05, 39.65)
    },
    'Kisumu County': {
        'lat_min': -0.30, 'lat_max': 0.30,
        'lon_min': 34.55, 'lon_max': 35.05,
        'center': (0.00, 34.80)
    },
    'Nakuru County': {
        'lat_min': -0.55, 'lat_max': -0.05,
        'lon_min': 35.90, 'lon_max': 36.40,
        'center': (-0.30, 36.15)
    },
    'Uasin Gishu County': {
        'lat_min': 0.30, 'lat_max': 0.70,
        'lon_min': 35.10, 'lon_max': 35.50,
        'center': (0.50, 35.30)
    },
    'Machakos County': {
        'lat_min': -1.70, 'lat_max': -1.20,
        'lon_min': 36.80, 'lon_max': 37.50,
        'center': (-1.45, 37.15)
    },
    'Meru County': {
        'lat_min': -0.20, 'lat_max': 0.40,
        'lon_min': 37.40, 'lon_max': 38.20,
        'center': (0.10, 37.80)
    }
}

# Kenya-wide bounds for ultimate fallback
KENYA_BOUNDS = {
    'lat_min': -5.0, 'lat_max': 5.0,
    'lon_min': 33.0, 'lon_max': 43.0
}

def get_county_from_town(town_name):
    """
    Intelligent town-to-county mapping
    Returns the correct county for a given town
    """
    if not town_name:
        return None
    
    # Direct lookup
    town_clean = town_name.strip().title()
    if town_clean in KENYA_TOWN_COUNTY_MAP:
        return KENYA_TOWN_COUNTY_MAP[town_clean]
    
    # Fuzzy matching for common variations
    town_lower = town_name.lower()
    for town, county in KENYA_TOWN_COUNTY_MAP.items():
        if town.lower() in town_lower or town_lower in town.lower():
            return county
    
    return None

def validate_coordinates_for_county(lat, lon, county):
    """
    Validate if coordinates are within the expected bounds for a county
    """
    try:
        lat, lon = float(lat), float(lon)
        
        if county in KENYA_COUNTY_BOUNDS:
            bounds = KENYA_COUNTY_BOUNDS[county]
            return (bounds['lat_min'] <= lat <= bounds['lat_max'] and 
                    bounds['lon_min'] <= lon <= bounds['lon_max'])
        
        # Fallback to Kenya-wide validation
        return validate_kenya_coordinates(lat, lon)
        
    except (TypeError, ValueError):
        return False

def validate_kenya_coordinates(lat, lon):
    """
    Validate if coordinates are within Kenya bounds
    """
    try:
        lat, lon = float(lat), float(lon)
        return (KENYA_BOUNDS['lat_min'] <= lat <= KENYA_BOUNDS['lat_max'] and 
                KENYA_BOUNDS['lon_min'] <= lon <= KENYA_BOUNDS['lon_max'])
    except (TypeError, ValueError):
        return False

def get_county_center(county):
    """
    Get the center coordinates for a county
    """
    if county in KENYA_COUNTY_BOUNDS:
        return KENYA_COUNTY_BOUNDS[county]['center']
    
    # Default to Nairobi center
    return (-1.286389, 36.817223)

def auto_correct_salon_location(salon):
    """
    Automatically correct salon location based on town-county intelligence
    """
    corrections = []
    
    # Check if town matches county
    if salon.town:
        expected_county = get_county_from_town(salon.town)
        if expected_county and expected_county != salon.county:
            corrections.append({
                'field': 'county',
                'current': salon.county,
                'suggested': expected_county,
                'reason': f'Town "{salon.town}" is in {expected_county}'
            })
    
    # Check if coordinates match county
    if salon.county and not validate_coordinates_for_county(salon.latitude, salon.longitude, salon.county):
        center_lat, center_lon = get_county_center(salon.county)
        corrections.append({
            'field': 'coordinates',
            'current': (salon.latitude, salon.longitude),
            'suggested': (center_lat, center_lon),
            'reason': f'Coordinates outside {salon.county} bounds'
        })
    
    return corrections

def get_all_counties():
    """
    Get list of all supported counties
    """
    return list(KENYA_COUNTY_BOUNDS.keys())

def get_towns_in_county(county):
    """
    Get list of towns in a specific county
    """
    return [town for town, town_county in KENYA_TOWN_COUNTY_MAP.items() if town_county == county]
