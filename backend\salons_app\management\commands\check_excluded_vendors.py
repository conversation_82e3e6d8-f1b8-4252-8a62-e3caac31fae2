from django.core.management.base import BaseCommand
from salons_app.models import Salon
from salons_app.location_intelligence import (
    get_county_from_town,
    validate_coordinates_for_county,
    validate_kenya_coordinates,
    auto_correct_salon_location,
    KENYA_COUNTY_BOUNDS
)
import math

class Command(BaseCommand):
    help = 'Check for vendors that might be excluded due to coordinate validation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Fix excluded vendors by adjusting coordinates to nearest valid location',
        )

    def handle(self, *args, **options):
        """Intelligent vendor location audit and correction"""

        excluded_vendors = []
        location_mismatches = []
        total_salons = 0

        for salon in Salon.objects.all():
            total_salons += 1
            lat, lon = salon.latitude, salon.longitude
            county = salon.county or 'Unknown'
            town = salon.town or 'Unknown'

            # INTELLIGENT CHECK 1: Town-County Mismatch
            expected_county = get_county_from_town(town)
            if expected_county and expected_county != county:
                location_mismatches.append({
                    'salon': salon,
                    'current_county': county,
                    'expected_county': expected_county,
                    'town': town,
                    'issue': 'town_county_mismatch'
                })

            # INTELLIGENT CHECK 2: Coordinate Validation
            county_valid = validate_coordinates_for_county(lat, lon, county)
            kenya_valid = validate_kenya_coordinates(lat, lon)

            if not (county_valid or kenya_valid):
                excluded_vendors.append({
                    'salon': salon,
                    'county': county,
                    'town': town,
                    'coordinates': (lat, lon),
                    'county_valid': county_valid,
                    'kenya_valid': kenya_valid,
                    'issue': 'invalid_coordinates'
                })
        
        self.stdout.write(f"\n📊 INTELLIGENT LOCATION ANALYSIS:")
        self.stdout.write(f"Total salons: {total_salons}")
        self.stdout.write(f"Location mismatches: {len(location_mismatches)}")
        self.stdout.write(f"Excluded vendors: {len(excluded_vendors)}")

        # Report location mismatches
        if location_mismatches:
            self.stdout.write(self.style.WARNING(f"\n🔍 {len(location_mismatches)} TOWN-COUNTY MISMATCHES:"))

            for mismatch in location_mismatches:
                salon = mismatch['salon']
                self.stdout.write(f"  • {salon.name} in '{mismatch['town']}' listed as '{mismatch['current_county']}' but should be '{mismatch['expected_county']}'")

                if options['fix']:
                    salon.county = mismatch['expected_county']
                    salon.save()
                    self.stdout.write(self.style.SUCCESS(
                        f"    ✅ Fixed: {salon.name} → {mismatch['expected_county']}"
                    ))

        # Report excluded vendors
        if excluded_vendors:
            self.stdout.write(self.style.WARNING(f"\n⚠️ {len(excluded_vendors)} VENDORS WITH INVALID COORDINATES:"))

            for vendor in excluded_vendors:
                salon = vendor['salon']
                self.stdout.write(f"  • {salon.name} ({vendor['county']}/{vendor['town']}) - {vendor['coordinates']}")

                if options['fix']:
                    # Use location intelligence to fix coordinates
                    corrections = auto_correct_salon_location(salon)
                    if corrections:
                        for correction in corrections:
                            if correction['field'] == 'coordinates':
                                new_lat, new_lon = correction['suggested']
                                old_coords = (salon.latitude, salon.longitude)
                                salon.latitude = new_lat
                                salon.longitude = new_lon
                                salon.save()

                                self.stdout.write(self.style.SUCCESS(
                                    f"    ✅ Fixed coordinates: {old_coords} → ({new_lat}, {new_lon})"
                                ))

        if not location_mismatches and not excluded_vendors:
            self.stdout.write(self.style.SUCCESS("✅ All vendors have correct locations!"))
        
        # Special check for known problematic areas
        self.stdout.write(f"\n🔍 CHECKING SPECIFIC AREAS:")
        
        # Check Kiserian specifically
        kiserian_salons = Salon.objects.filter(town__icontains='Kiserian')
        if kiserian_salons.exists():
            self.stdout.write(f"Kiserian salons found: {kiserian_salons.count()}")
            for salon in kiserian_salons:
                lat, lon = salon.latitude, salon.longitude
                # Kiserian coordinates: -1.4251869, 36.6936512
                if abs(lat - (-1.4251869)) < 0.1 and abs(lon - 36.6936512) < 0.1:
                    self.stdout.write(self.style.SUCCESS(f"  ✅ {salon.name} has correct Kiserian coordinates"))
                else:
                    self.stdout.write(self.style.WARNING(f"  ⚠️ {salon.name} coordinates may be incorrect: ({lat}, {lon})"))
        else:
            self.stdout.write("No Kiserian salons found in database")
        
        if options['fix']:
            self.stdout.write(self.style.SUCCESS(f"\n✅ Vendor exclusion fixes applied!"))
        else:
            self.stdout.write(f"\nRun with --fix to automatically correct excluded vendors")
