from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from salons_app.models import Salon, Service, Staff

class Command(BaseCommand):
    help = 'Create test salons in Ngong (Kajiado) and Karen (Nairobi) for geolocation testing'

    def handle(self, *args, **options):
        self.stdout.write("🌍 Creating test salons for intelligent geolocation testing...")
        self.stdout.write("=" * 60)
        
        # Create test vendors
        vendors_data = [
            {
                'username': 'ngong_vendor',
                'email': '<EMAIL>',
                'salon_name': 'Ngong Beauty Palace',
                'county': 'Kajiado County',
                'town': 'Ngong',
                'address': 'Ngong Town Center, Magadi Road',
                'latitude': -1.3525,  # Ngong Town coordinates
                'longitude': 36.6500,
                'description': 'Premier beauty salon in Ngong Town, Kajiado County. Specializing in modern hair styling, nail art, and beauty treatments.',
                'phone': '+254712345001',
                'image': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=500&h=400&fit=crop'
            },
            {
                'username': 'karen_vendor', 
                'email': '<EMAIL>',
                'salon_name': '<PERSON>lamour <PERSON>',
                'county': 'Nairobi County',
                'town': 'Karen',
                'address': 'Karen Shopping Centre, Dagoretti Road',
                'latitude': -1.3197,  # Karen coordinates
                'longitude': 36.6859,
                'description': 'Luxury beauty studio in Karen, Nairobi County. Offering premium hair, nail, and spa services in an upscale environment.',
                'phone': '+254712345002',
                'image': 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=500&h=400&fit=crop'
            }
        ]
        
        # Services for both salons
        services_data = [
            {'name': 'Hair Cut & Styling', 'price': 1800, 'duration': 75},
            {'name': 'Hair Braiding', 'price': 2500, 'duration': 120},
            {'name': 'Manicure & Pedicure', 'price': 1200, 'duration': 60},
            {'name': 'Facial Treatment', 'price': 2200, 'duration': 90},
            {'name': 'Hair Coloring', 'price': 3500, 'duration': 150},
            {'name': 'Eyebrow Threading', 'price': 600, 'duration': 30},
        ]
        
        created_salons = []
        
        for vendor_data in vendors_data:
            # Create vendor user
            user, user_created = User.objects.get_or_create(
                username=vendor_data['username'],
                defaults={
                    'email': vendor_data['email'],
                    'first_name': vendor_data['salon_name'].split()[0],
                    'last_name': 'Owner'
                }
            )
            
            if user_created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f"✅ Created vendor: {vendor_data['username']}")
            else:
                self.stdout.write(f"ℹ️  Vendor exists: {vendor_data['username']}")
            
            # Create salon
            salon, salon_created = Salon.objects.get_or_create(
                vendor=user,
                name=vendor_data['salon_name'],
                defaults={
                    'address': vendor_data['address'],
                    'county': vendor_data['county'],
                    'town': vendor_data['town'],
                    'phone': vendor_data['phone'],
                    'email': vendor_data['email'],
                    'latitude': vendor_data['latitude'],
                    'longitude': vendor_data['longitude'],
                    'description': vendor_data['description'],
                    'imageUrl': vendor_data['image']
                }
            )
            
            if salon_created:
                self.stdout.write(f"✅ Created salon: {vendor_data['salon_name']}")
                self.stdout.write(f"   📍 Location: {vendor_data['town']}, {vendor_data['county']}")
                self.stdout.write(f"   🗺️  Coordinates: ({vendor_data['latitude']}, {vendor_data['longitude']})")
            else:
                self.stdout.write(f"ℹ️  Salon exists: {vendor_data['salon_name']}")
            
            created_salons.append(salon)
            
            # Add services
            for service_data in services_data:
                service, service_created = Service.objects.get_or_create(
                    salon=salon,
                    name=service_data['name'],
                    defaults={
                        'price': service_data['price'],
                        'duration': service_data['duration']
                    }
                )
                if service_created:
                    self.stdout.write(f"   ✅ Service: {service_data['name']} (KSh {service_data['price']})")
            
            # Add staff
            staff_data = [
                {'name': f'{vendor_data["town"]} Senior Stylist', 'role': 'Senior Hair Stylist', 'specialty': 'Hair Styling & Coloring'},
                {'name': f'{vendor_data["town"]} Nail Technician', 'role': 'Nail Technician', 'specialty': 'Manicure & Pedicure'},
            ]
            
            for staff_info in staff_data:
                staff, staff_created = Staff.objects.get_or_create(
                    salon=salon,
                    name=staff_info['name'],
                    defaults={
                        'role': staff_info['role'],
                        'specialty': staff_info['specialty']
                    }
                )
                if staff_created:
                    self.stdout.write(f"   ✅ Staff: {staff_info['name']}")
            
            self.stdout.write("")
        
        # Calculate distance between the two salons
        if len(created_salons) >= 2:
            salon1, salon2 = created_salons[0], created_salons[1]
            distance = self.calculate_distance(
                salon1.latitude, salon1.longitude,
                salon2.latitude, salon2.longitude
            )
            
            self.stdout.write("🎯 GEOLOCATION TEST SETUP COMPLETE!")
            self.stdout.write("=" * 60)
            self.stdout.write(f"📍 {salon1.name}: {salon1.town}, {salon1.county}")
            self.stdout.write(f"   Coordinates: ({salon1.latitude}, {salon1.longitude})")
            self.stdout.write(f"📍 {salon2.name}: {salon2.town}, {salon2.county}")
            self.stdout.write(f"   Coordinates: ({salon2.latitude}, {salon2.longitude})")
            self.stdout.write(f"📏 Distance between salons: {distance:.2f} km")
            self.stdout.write("")
            self.stdout.write("🔑 LOGIN CREDENTIALS:")
            self.stdout.write("   • ngong_vendor / testpass123")
            self.stdout.write("   • karen_vendor / testpass123")
            self.stdout.write("")
            self.stdout.write("🧪 TEST SCENARIOS:")
            self.stdout.write("   1. Search from Ngong area - should find Ngong salon first")
            self.stdout.write("   2. Search from Karen area - should find Karen salon first")
            self.stdout.write("   3. 20km radius from either location should show both salons")
            self.stdout.write("   4. Test intelligent county detection (Ngong → Kajiado)")
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """Calculate distance between two points using Haversine formula"""
        import math
        
        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Radius of earth in kilometers
        r = 6371
        
        return c * r
