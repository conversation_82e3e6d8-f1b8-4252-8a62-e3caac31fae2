"""
Django management command to create test vendors
Usage: python manage.py create_vendor
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from salons_app.models import Salon, Service, Staff

class Command(BaseCommand):
    help = 'Create test vendor accounts for development'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, default='testvendor1', help='Username for vendor')
        parser.add_argument('--salon', type=str, default='Test Salon', help='Salon name')
        parser.add_argument('--multiple', action='store_true', help='Create multiple test vendors')

    def handle(self, *args, **options):
        if options['multiple']:
            self.create_multiple_vendors()
        else:
            self.create_single_vendor(options['username'], options['salon'])

    def create_single_vendor(self, username, salon_name):
        """Create a single vendor"""
        self.stdout.write(f"🚀 Creating vendor: {username}")
        
        # Create user
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': f'{username}@test.com',
                'first_name': username.capitalize(),
                'last_name': 'Vendor',
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            self.stdout.write(self.style.SUCCESS(f"✅ User created: {username}"))
        else:
            self.stdout.write(f"ℹ️  User exists: {username}")
        
        # Create salon
        salon, created = Salon.objects.get_or_create(
            vendor=user,
            defaults={
                'name': salon_name,
                'address': '123 Test Street',
                'county': 'Nairobi',
                'town': 'Nairobi',
                'phone': '+254700000000',
                'email': f'{username}@test.com',
                'latitude': -1.286389,
                'longitude': 36.817223,
                'description': f'Professional beauty salon - {salon_name}',
                'imageUrl': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=500'
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f"✅ Salon created: {salon_name}"))
        else:
            self.stdout.write(f"ℹ️  Salon exists: {salon_name}")
        
        # Add services
        services = [
            {'name': 'Hair Cut & Style', 'price': 1500, 'duration': 60},
            {'name': 'Manicure', 'price': 800, 'duration': 45},
            {'name': 'Facial Treatment', 'price': 2000, 'duration': 90},
        ]
        
        for service_data in services:
            service, created = Service.objects.get_or_create(
                salon=salon,
                name=service_data['name'],
                defaults=service_data
            )
            if created:
                self.stdout.write(f"✅ Service: {service_data['name']}")
        
        # Add staff
        staff, created = Staff.objects.get_or_create(
            salon=salon,
            name='Test Stylist',
            defaults={
                'role': 'Senior Stylist',
                'specialty': 'Hair & Beauty'
            }
        )
        if created:
            self.stdout.write(f"✅ Staff: Test Stylist")
        
        self.stdout.write(self.style.SUCCESS(f"\n🎉 VENDOR READY!"))
        self.stdout.write(f"📋 Login: {username} / testpass123")
        self.stdout.write(f"🏪 Salon: {salon_name} (ID: {salon.id})")
        self.stdout.write(f"💼 Services: {salon.services.count()}")
        self.stdout.write(f"👥 Staff: {salon.staff.count()}")
        self.stdout.write(f"\n🔗 Login at: http://localhost:3000/login")
        self.stdout.write(f"🔗 Vendor Profile: http://localhost:3000/vendor/profile")

    def create_multiple_vendors(self):
        """Create multiple test vendors"""
        vendors = [
            ('testvendor1', 'Glamour Palace'),
            ('testvendor2', 'Beauty Haven'),
            ('testvendor3', 'Style Studio'),
            ('quickvendor', 'Quick Salon'),
            ('demovendor', 'Demo Beauty'),
        ]
        
        self.stdout.write("🚀 Creating multiple test vendors...")
        self.stdout.write("=" * 40)
        
        for username, salon_name in vendors:
            self.create_single_vendor(username, salon_name)
            self.stdout.write("")
        
        self.stdout.write(self.style.SUCCESS("✅ All vendors created!"))
        self.stdout.write("\n📋 Ready-to-use accounts:")
        for username, salon_name in vendors:
            self.stdout.write(f"   • {username} / testpass123 → {salon_name}")
