"""
Production Database Fix Command
Safely applies missing migrations and fixes database issues
"""
from django.core.management.base import BaseCommand
from django.db import connection, transaction
from django.core.management import call_command
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix production database issues - apply missing migrations safely'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force apply migrations even if database is locked',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(
            self.style.WARNING('🔧 Starting Production Database Fix...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.NOTICE('📋 DRY RUN MODE - No changes will be made')
            )
        
        try:
            # Check current migration status
            self.stdout.write('📊 Checking migration status...')
            self.check_migration_status()
            
            # Check for database locks
            if not dry_run:
                self.stdout.write('🔒 Checking for database locks...')
                if self.check_database_locks() and not force:
                    self.stdout.write(
                        self.style.ERROR('❌ Database is locked. Use --force to override.')
                    )
                    return
            
            # Apply missing migrations
            if not dry_run:
                self.stdout.write('🚀 Applying missing migrations...')
                self.apply_migrations()
            
            # Verify salon data
            self.stdout.write('✅ Verifying salon data...')
            self.verify_salon_data()
            
            # Check is_active field
            self.stdout.write('🔍 Checking is_active field...')
            self.check_is_active_field()
            
            self.stdout.write(
                self.style.SUCCESS('✅ Production database fix completed successfully!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during database fix: {str(e)}')
            )
            logger.error(f"Database fix error: {str(e)}")
            raise

    def check_migration_status(self):
        """Check which migrations are applied"""
        try:
            call_command('showmigrations', 'salons_app', verbosity=0)
            self.stdout.write('📋 Migration status checked')
        except Exception as e:
            self.stdout.write(f'⚠️ Could not check migrations: {str(e)}')

    def check_database_locks(self):
        """Check if database is locked"""
        try:
            with connection.cursor() as cursor:
                # Try a simple query to check if DB is accessible
                cursor.execute("SELECT 1")
                return False
        except Exception as e:
            if 'database is locked' in str(e).lower():
                return True
            return False

    def apply_migrations(self):
        """Apply missing migrations safely"""
        try:
            with transaction.atomic():
                call_command('migrate', 'salons_app', verbosity=1)
                self.stdout.write('✅ Migrations applied successfully')
        except Exception as e:
            self.stdout.write(f'❌ Migration failed: {str(e)}')
            raise

    def verify_salon_data(self):
        """Verify salon data exists"""
        try:
            from salons_app.models import Salon
            salon_count = Salon.objects.count()
            self.stdout.write(f'📊 Found {salon_count} salons in database')
            
            if salon_count == 0:
                self.stdout.write(
                    self.style.WARNING('⚠️ No salon data found. Consider running populate_dummy_salons')
                )
        except Exception as e:
            self.stdout.write(f'❌ Could not verify salon data: {str(e)}')

    def check_is_active_field(self):
        """Check if is_active field exists and works"""
        try:
            from salons_app.models import Salon
            # Try to query using is_active field
            active_salons = Salon.objects.filter(is_active=True).count()
            self.stdout.write(f'✅ is_active field working. {active_salons} active salons found')
        except Exception as e:
            if 'no such column' in str(e).lower():
                self.stdout.write(
                    self.style.ERROR('❌ is_active field missing. Migration 0016 not applied.')
                )
            else:
                self.stdout.write(f'❌ is_active field error: {str(e)}')
