import random
from django.core.management.base import BaseCommand
from salons_app.models import Salon, Service

SALON_NAMES = [
    "Glow Beauty Studio", "Nairobi Glam Hub", "Langata Luxe Salon", "Urban Chic Spa", "Prestige Cuts", "Royal Touch", "Modern Mane", "Elegance Hair Studio", "City Style Lounge", "Pearl Beauty Bar"
]
ROADS = [
    "Langata Road", "Ngong Road", "Moi Avenue", "Kenyatta Avenue", "Lavington", "Westlands", "Karen", "Hurlingham", "South B", "Eastleigh"
]
SERVICES = [
    ("Braiding", 1200), ("Haircut", 800), ("Manicure", 700), ("Pedicure", 900), ("Facial", 1500), ("Dreadlocks", 2500), ("Blow Dry", 500)
]
AVATARS = [
    "https://randomuser.me/api/portraits/women/1.jpg",
    "https://randomuser.me/api/portraits/men/2.jpg",
    "https://randomuser.me/api/portraits/women/3.jpg",
    "https://randomuser.me/api/portraits/men/4.jpg",
    "https://randomuser.me/api/portraits/women/5.jpg",
    "https://randomuser.me/api/portraits/men/6.jpg",
    "https://randomuser.me/api/portraits/women/7.jpg",
    "https://randomuser.me/api/portraits/men/8.jpg",
    "https://randomuser.me/api/portraits/women/9.jpg",
    "https://randomuser.me/api/portraits/men/10.jpg",
]

class Command(BaseCommand):
    help = 'Populate the database with dummy salons and services for Nairobi search/demo.'

    def handle(self, *args, **kwargs):
        for i in range(10):
            name = SALON_NAMES[i]
            town = ROADS[i]
            address = f"{random.randint(1, 100)} {town}"
            phone = f"+2547{random.randint(10000000,99999999)}"
            email = f"info{i}@demo.com"
            avatar = AVATARS[i]
            description = f"Welcome to {name}, your destination for beauty and style in Nairobi."
            salon = Salon.objects.create(
                name=name,
                county="Nairobi County",
                town=town,
                address=address,
                phone=phone,
                email=email,
                latitude=-1.2921 + random.uniform(-0.05, 0.05),
                longitude=36.8219 + random.uniform(-0.05, 0.05),
                description=description,
                imageUrl=avatar
            )
            for svc_name, svc_price in random.sample(SERVICES, 4):
                Service.objects.create(
                    salon=salon,
                    name=svc_name,
                    price=svc_price,
                    duration=random.choice([30, 45, 60, 90])
                )
        self.stdout.write(self.style.SUCCESS('Dummy Nairobi salons and services created!'))
