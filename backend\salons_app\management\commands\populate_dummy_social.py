import random
from django.core.management.base import BaseCommand
from salons_app.models import Salon, Service, StylePost, Like, Comment, Follow, Booking
from django.contrib.auth.models import User
from django.utils import timezone

MESSAGES = [
    "Loving my new braids!",
    "Fresh cut, fresh start.",
    "Best manicure ever!",
    "Highly recommend this salon!",
    "Amazing facial, glowing skin!",
    "Dreadlocks on point.",
    "Quick blow dry before work.",
    "Great service and friendly staff!",
    "My go-to place for style.",
    "Feeling fabulous!"
]

class Command(BaseCommand):
    help = 'Populate the database with dummy StylePosts, Likes, Comments, and Follows for social feed testing.'

    def handle(self, *args, **kwargs):
        users = list(User.objects.all())
        salons = list(Salon.objects.all())
        services = list(Service.objects.all())
        if not users or not salons or not services:
            self.stdout.write(self.style.ERROR('Need at least 1 user, salon, and service.'))
            return
        # Create StylePosts
        for i in range(20):
            user = random.choice(users)
            salon = random.choice(salons)
            service = random.choice(services)
            # Create a booking for the post
            booking = Booking.objects.create(
                userId=str(user.id),
                userName=user.username,
                salon=salon,
                service=service,
                staff=None,
                date=timezone.now().date(),
                time="10:00 AM",
                status="Completed"
            )
            post = StylePost.objects.create(
                user_id=user.id,
                booking=booking,
                message=random.choice(MESSAGES),
                image=random.choice([
                    "https://randomuser.me/api/portraits/women/1.jpg",
                    "https://randomuser.me/api/portraits/men/2.jpg",
                    "https://randomuser.me/api/portraits/women/3.jpg",
                    "https://randomuser.me/api/portraits/men/4.jpg",
                    None
                ]),
                created_at=timezone.now()
            )
            # Likes
            for _ in range(random.randint(0, 5)):
                liker = random.choice(users)
                Like.objects.get_or_create(user_id=liker.id, style_post=post)
            # Comments
            for _ in range(random.randint(0, 3)):
                commenter = random.choice(users)
                Comment.objects.create(user_id=commenter.id, style_post=post, text=random.choice(MESSAGES), created_at=timezone.now())
        # Follows
        for user in users:
            others = [u for u in users if u != user]
            if len(others) > 0:  # Only create follows if there are other users
                follow_count = min(5, len(others))
                for _ in range(random.randint(1, follow_count)):
                    following = random.choice(others)
                    Follow.objects.get_or_create(follower=user.id, following=following.id)
        self.stdout.write(self.style.SUCCESS('Dummy social feed data created!')) 