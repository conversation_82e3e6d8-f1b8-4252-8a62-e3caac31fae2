from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from salons_app.models import Salon
import random
import string

class Command(BaseCommand):
    help = 'Create a unique vendor user for each salon and assign as vendor. Prints credentials.'

    def handle(self, *args, **options):
        for salon in Salon.objects.all():
            if not salon.vendor:
                # Generate a unique username and email
                username = f'vendor_{salon.id}'
                email = f'vendor_{salon.id}@example.com'
                password = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
                user, created = User.objects.get_or_create(username=username, defaults={
                    'email': email
                })
                if created:
                    user.set_password(password)
                    user.save()
                    self.stdout.write(self.style.SUCCESS(f'Created vendor user: {username} / {password}'))
                else:
                    password = '(unchanged)'
                    self.stdout.write(self.style.WARNING(f'User {username} already exists.'))
                # Assign vendor to salon
                salon.vendor = user
                salon.save()
                self.stdout.write(self.style.SUCCESS(f'Assigned {username} as vendor for salon: {salon.name}'))
            else:
                self.stdout.write(self.style.WARNING(f'Salon {salon.name} already has a vendor: {salon.vendor.username}')) 