from django.core.management.base import BaseCommand
from salons_app.models import Salon

# Bounding boxes for major counties (add more as you scale)
COUNTY_BOUNDS = {
    'Nairobi County': {
        'lat_min': -1.50, 'lat_max': -1.10,  # EXPANDED: Includes Kiserian (-1.425) and outer Nairobi areas
        'lon_min': 36.60, 'lon_max': 37.10   # EXPANDED: Includes Kiserian (36.694) and outer areas
    },
    'Mombasa County': {
        'lat_min': -4.10, 'lat_max': -3.90,
        'lon_min': 39.55, 'lon_max': 39.75
    },
    'Kisumu County': {
        'lat_min': -0.20, 'lat_max': 0.20,
        'lon_min': 34.65, 'lon_max': 34.95
    },
    'Nakuru County': {
        'lat_min': -0.45, 'lat_max': -0.15,
        'lon_min': 36.00, 'lon_max': 36.30
    },
    'Uasin <PERSON>': {  # Eldoret
        'lat_min': 0.40, 'lat_max': 0.60,
        'lon_min': 35.20, 'lon_max': 35.40
    },
}
# Kenya-wide fallback
KENYA_BOUNDS = {
    'lat_min': -5.0, 'lat_max': 5.0,
    'lon_min': 33.5, 'lon_max': 42.0
}

def get_center(bounds):
    lat_center = (bounds['lat_min'] + bounds['lat_max']) / 2
    lon_center = (bounds['lon_min'] + bounds['lon_max']) / 2
    return lat_center, lon_center

class Command(BaseCommand):
    help = 'Validate and auto-fix salon coordinates by county.'

    def handle(self, *args, **kwargs):
        issues = 0
        for salon in Salon.objects.all():
            county = salon.county or ''
            lat, lon = salon.latitude, salon.longitude
            bounds = COUNTY_BOUNDS.get(county, KENYA_BOUNDS)
            if not (bounds['lat_min'] <= lat <= bounds['lat_max']) or not (bounds['lon_min'] <= lon <= bounds['lon_max']):
                new_lat, new_lon = get_center(bounds)
                self.stdout.write(self.style.WARNING(
                    f"Fixing '{salon.name}' in {county}, {salon.town}: ({lat}, {lon}) -> ({new_lat}, {new_lon})"
                ))
                salon.latitude = new_lat
                salon.longitude = new_lon
                salon.save()
                issues += 1
        if issues == 0:
            self.stdout.write(self.style.SUCCESS("All salon coordinates are within expected bounds!"))
        else:
            self.stdout.write(self.style.SUCCESS(f"{issues} salons were auto-fixed to county center coordinates.")) 