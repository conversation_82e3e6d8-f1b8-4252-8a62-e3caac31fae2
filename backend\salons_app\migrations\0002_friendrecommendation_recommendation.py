# Generated by Django 5.2.3 on 2025-06-29 12:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('salons_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FriendRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_user_id', models.CharField(max_length=255)),
                ('to_user_id', models.CharField(max_length=255)),
                ('object_id', models.PositiveIntegerField()),
                ('message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
        ),
        migrations.CreateModel(
            name='Recommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('popular', 'Popular'), ('cheap', 'Cheapest'), ('best_stylist', 'Best Stylist')], max_length=32)),
                ('object_id', models.PositiveIntegerField()),
                ('score', models.FloatField(default=0)),
                ('reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
        ),
    ]
