# Generated by Django 5.1 on 2025-06-30 22:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('salons_app', '0003_salon_county_salon_town'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=255, unique=True)),
                ('latitude', models.FloatField()),
                ('longitude', models.FloatField()),
                ('county', models.CharField(blank=True, max_length=100, null=True)),
                ('town', models.CharField(blank=True, max_length=100, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='booking',
            name='gift_message',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='booking',
            name='is_gift',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='booking',
            name='purchaser_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='recommendation',
            name='type',
            field=models.CharField(choices=[('popular', 'Popular'), ('cheap', 'Cheapest'), ('best_stylist', 'Best Stylist'), ('nearby', 'Nearby'), ('regional', 'Regional')], max_length=32),
        ),
        migrations.CreateModel(
            name='Friendship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_user_id', models.CharField(max_length=255)),
                ('to_user_id', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'unique_together': {('from_user_id', 'to_user_id')},
            },
        ),
        migrations.CreateModel(
            name='StylePost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('image', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('likes', models.IntegerField(default=0)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='salons_app.booking')),
            ],
        ),
        migrations.CreateModel(
            name='RegionalTrending',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('county', models.CharField(max_length=100)),
                ('town', models.CharField(blank=True, max_length=100, null=True)),
                ('object_id', models.PositiveIntegerField()),
                ('score', models.FloatField(default=0)),
                ('period', models.CharField(default='weekly', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'unique_together': {('county', 'town', 'content_type', 'object_id', 'period')},
            },
        ),
    ]
