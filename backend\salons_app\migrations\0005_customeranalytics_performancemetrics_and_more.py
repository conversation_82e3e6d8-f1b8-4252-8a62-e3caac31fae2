# Generated by Django 5.1 on 2025-06-30 23:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('salons_app', '0004_userlocation_booking_gift_message_booking_is_gift_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=255)),
                ('total_bookings', models.IntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('last_booking_date', models.DateField(blank=True, null=True)),
                ('average_booking_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('booking_frequency', models.FloatField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('favorite_salon', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='salons_app.salon')),
                ('favorite_service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='salons_app.service')),
            ],
        ),
        migrations.CreateModel(
            name='PerformanceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('staff_performance', models.JSONField(default=dict)),
                ('service_popularity', models.JSONField(default=dict)),
                ('customer_retention_rate', models.FloatField(default=0)),
                ('average_booking_duration', models.FloatField(default=0)),
                ('peak_booking_hours', models.JSONField(default=dict)),
                ('customer_satisfaction_trends', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='salons_app.salon')),
            ],
            options={
                'unique_together': {('salon', 'date')},
            },
        ),
        migrations.CreateModel(
            name='RevenueAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period', models.CharField(max_length=20)),
                ('period_start', models.DateField()),
                ('period_end', models.DateField()),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('service_revenue', models.JSONField(default=dict)),
                ('staff_revenue', models.JSONField(default=dict)),
                ('gift_bookings_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('regular_bookings_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='salons_app.salon')),
            ],
            options={
                'unique_together': {('salon', 'period', 'period_start')},
            },
        ),
        migrations.CreateModel(
            name='SalonAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_bookings', models.IntegerField(default=0)),
                ('completed_bookings', models.IntegerField(default=0)),
                ('cancelled_bookings', models.IntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('average_rating', models.FloatField(default=0)),
                ('customer_satisfaction_score', models.FloatField(default=0)),
                ('peak_hours', models.JSONField(default=dict)),
                ('popular_services', models.JSONField(default=dict)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='salons_app.salon')),
            ],
            options={
                'unique_together': {('salon', 'date')},
            },
        ),
    ]
