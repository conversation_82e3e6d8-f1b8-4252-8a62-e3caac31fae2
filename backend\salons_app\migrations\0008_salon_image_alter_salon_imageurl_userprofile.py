# Generated by Django 5.1 on 2025-07-07 14:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('salons_app', '0007_booking_recipient_contact'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='salon',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='salon_images/'),
        ),
        migrations.AlterField(
            model_name='salon',
            name='imageUrl',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('customer', 'Customer'), ('vendor', 'Vendor'), ('admin', 'Admin')], default='customer', max_length=20)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
