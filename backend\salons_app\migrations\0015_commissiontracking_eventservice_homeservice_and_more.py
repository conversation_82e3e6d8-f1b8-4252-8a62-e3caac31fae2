# Generated by Django 5.2.4 on 2025-07-31 16:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('salons_app', '0014_booking_payment_date_booking_payment_method_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_type', models.CharField(choices=[('subscription', 'Monthly Subscription'), ('home_service_booking', 'Home Service Booking'), ('home_service_lead', 'Home Service Lead'), ('event_service_booking', 'Event Service Booking'), ('event_service_lead', 'Event Service Lead'), ('advertising_click', 'Advertising Click')], max_length=30)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reference_id', models.Cha<PERSON><PERSON><PERSON>(help_text='Booking ID, Lead ID, etc.', max_length=255)),
                ('description', models.TextField()),
                ('date_earned', models.DateTimeField(auto_now_add=True)),
                ('paid_to_platform', models.BooleanField(default=False)),
                ('payment_date', models.DateTimeField(blank=True, null=True)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commissions', to='salons_app.salon')),
            ],
            options={
                'ordering': ['-date_earned'],
            },
        ),
        migrations.CreateModel(
            name='EventService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_name', models.CharField(max_length=255)),
                ('event_type', models.CharField(choices=[('wedding', 'Wedding'), ('corporate', 'Corporate Event'), ('party', 'Private Party'), ('photoshoot', 'Photoshoot'), ('graduation', 'Graduation'), ('other', 'Other')], max_length=20)),
                ('organizer_name', models.CharField(max_length=255)),
                ('organizer_phone', models.CharField(max_length=20)),
                ('organizer_email', models.EmailField(max_length=254)),
                ('event_date', models.DateTimeField()),
                ('event_venue', models.TextField()),
                ('number_of_clients', models.IntegerField()),
                ('services_requested', models.JSONField(default=list)),
                ('estimated_duration', models.IntegerField(help_text='Duration in minutes')),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('travel_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('equipment_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('inquiry', 'Inquiry'), ('quoted', 'Quoted'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='inquiry', max_length=20)),
                ('special_requirements', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_services', to='salons_app.salon')),
            ],
        ),
        migrations.CreateModel(
            name='HomeService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=255)),
                ('client_phone', models.CharField(max_length=20)),
                ('client_email', models.EmailField(max_length=254)),
                ('location_type', models.CharField(choices=[('home', 'Client Home'), ('office', 'Office/Workplace'), ('hotel', 'Hotel'), ('event_venue', 'Event Venue')], default='home', max_length=20)),
                ('address', models.TextField()),
                ('service_date', models.DateTimeField()),
                ('services_requested', models.JSONField(default=list)),
                ('estimated_duration', models.IntegerField(help_text='Duration in minutes')),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('travel_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('special_requirements', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='home_services', to='salons_app.salon')),
            ],
        ),
        migrations.CreateModel(
            name='PremiumSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tier', models.CharField(choices=[('featured', 'Featured'), ('spotlight', 'Spotlight'), ('elite', 'Elite')], default='featured', max_length=20)),
                ('service_types', models.JSONField(default=list)),
                ('status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('cancelled', 'Cancelled'), ('pending', 'Pending Payment'), ('trial', 'Trial'), ('trial_expired', 'Trial Expired')], default='pending', max_length=20)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('monthly_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('auto_renew', models.BooleanField(default=True)),
                ('home_service_enabled', models.BooleanField(default=False)),
                ('event_service_enabled', models.BooleanField(default=False)),
                ('is_trial', models.BooleanField(default=False)),
                ('trial_start_date', models.DateTimeField(blank=True, null=True)),
                ('trial_end_date', models.DateTimeField(blank=True, null=True)),
                ('trial_notifications_sent', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('salon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='premium_subscriptions', to='salons_app.salon')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TickerClick',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ip', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('referrer', models.URLField(blank=True, null=True)),
                ('clicked_at', models.DateTimeField(auto_now_add=True)),
                ('session_id', models.CharField(blank=True, max_length=255, null=True)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clicks', to='salons_app.premiumsubscription')),
            ],
            options={
                'ordering': ['-clicked_at'],
            },
        ),
        migrations.CreateModel(
            name='PremiumAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(auto_now_add=True)),
                ('ticker_impressions', models.IntegerField(default=0)),
                ('ticker_clicks', models.IntegerField(default=0)),
                ('profile_views', models.IntegerField(default=0)),
                ('bookings_generated', models.IntegerField(default=0)),
                ('revenue_generated', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('click_through_rate', models.FloatField(default=0)),
                ('conversion_rate', models.FloatField(default=0)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='salons_app.premiumsubscription')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('subscription', 'date')},
            },
        ),
    ]
