from django.db import models
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import User
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models.signals import pre_save, post_delete
from django.dispatch import receiver

class Salon(models.Model):
    vendor = models.ForeignKey(User, related_name='salons', on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=255)
    address = models.CharField(max_length=255)
    county = models.CharField(max_length=100, blank=True, null=True)
    town = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    latitude = models.FloatField()
    longitude = models.FloatField()
    description = models.TextField()
    imageUrl = models.URLField(blank=True, null=True)
    image = models.ImageField(upload_to='salon_images/', blank=True, null=True)

    # Archive fields for trial management
    is_active = models.BooleanField(default=True)
    archived_reason = models.CharField(max_length=255, blank=True, null=True)
    archived_at = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return self.name

    def clean(self):
        # Only allow users with vendor role to be assigned as vendor
        if self.vendor:
            profile = getattr(self.vendor, 'profile', None)
            if not profile or profile.role != 'vendor':
                raise ValidationError('Assigned vendor must have a vendor role.')

class Service(models.Model):
    salon = models.ForeignKey(Salon, related_name='services', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration = models.IntegerField(help_text="Duration in minutes")

    def __str__(self):
        return f"{self.name} at {self.salon.name}"

class Staff(models.Model):
    salon = models.ForeignKey(Salon, related_name='staff', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    role = models.CharField(max_length=255)
    specialty = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Staff specialty (e.g., Nail Technician, Hair Stylist, Barber, etc.)"
    )
    services = models.ManyToManyField(Service, related_name='staff_members')

    def __str__(self):
        return f"{self.name} ({self.role}) at {self.salon.name}"

class Booking(models.Model):
    user = models.ForeignKey(User, related_name='customer_bookings', on_delete=models.SET_NULL, null=True, blank=True)
    userId = models.CharField(max_length=255)
    userName = models.CharField(max_length=255)
    salon = models.ForeignKey(Salon, related_name='bookings', on_delete=models.CASCADE)
    service = models.ForeignKey(Service, related_name='bookings', on_delete=models.CASCADE)
    staff = models.ForeignKey(Staff, related_name='bookings', on_delete=models.CASCADE, null=True, blank=True)
    date = models.DateField()
    time = models.CharField(max_length=10)
    status_choices = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    status = models.CharField(max_length=20, choices=status_choices, default='pending')
    notes = models.TextField(blank=True, null=True)
    is_gift = models.BooleanField(default=False)
    gift_message = models.TextField(blank=True, null=True)
    purchaser_id = models.CharField(max_length=255, blank=True, null=True)
    recipient_contact = models.CharField(max_length=255, blank=True, null=True)
    
    # Payment-related fields
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    service_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    transaction_id = models.CharField(max_length=255, blank=True, null=True)
    payment_status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ], default='pending')
    payment_date = models.DateTimeField(null=True, blank=True)
    pricing_breakdown = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"Booking {self.id} by {self.userName} for {self.service.name} at {self.salon.name}"

# Enhanced Location & Social Models
class UserLocation(models.Model):
    user_id = models.CharField(max_length=255, unique=True)
    latitude = models.FloatField()
    longitude = models.FloatField()
    county = models.CharField(max_length=100, blank=True, null=True)
    town = models.CharField(max_length=100, blank=True, null=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Location for user {self.user_id}"

class Friendship(models.Model):
    from_user_id = models.CharField(max_length=255)
    to_user_id = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('from_user_id', 'to_user_id')

    def __str__(self):
        return f"Friendship: {self.from_user_id} -> {self.to_user_id}"

class StylePost(models.Model):
    user_id = models.CharField(max_length=255)
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE)
    message = models.TextField()
    image = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    likes = models.IntegerField(default=0)

    def __str__(self):
        return f"Style post by {self.user_id}"

class RegionalTrending(models.Model):
    county = models.CharField(max_length=100)
    town = models.CharField(max_length=100, blank=True, null=True)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    trending_item = GenericForeignKey('content_type', 'object_id')
    score = models.FloatField(default=0)
    period = models.CharField(max_length=20, default='weekly')  # daily, weekly, monthly
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('county', 'town', 'content_type', 'object_id', 'period')

    def __str__(self):
        return f"Trending in {self.county}: {self.trending_item}"

# Analytics & Business Intelligence Models
class SalonAnalytics(models.Model):
    salon = models.ForeignKey(Salon, on_delete=models.CASCADE)
    date = models.DateField()
    total_bookings = models.IntegerField(default=0)
    completed_bookings = models.IntegerField(default=0)
    cancelled_bookings = models.IntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    average_rating = models.FloatField(default=0)
    customer_satisfaction_score = models.FloatField(default=0)
    peak_hours = models.JSONField(default=dict)  # Store peak hours data
    popular_services = models.JSONField(default=dict)  # Store popular services data

    class Meta:
        unique_together = ('salon', 'date')

    def __str__(self):
        return f"Analytics for {self.salon.name} on {self.date}"

class CustomerAnalytics(models.Model):
    user_id = models.CharField(max_length=255)
    total_bookings = models.IntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    favorite_salon = models.ForeignKey(Salon, on_delete=models.SET_NULL, null=True, blank=True)
    favorite_service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True, blank=True)
    last_booking_date = models.DateField(null=True, blank=True)
    average_booking_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    booking_frequency = models.FloatField(default=0)  # Bookings per month
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Analytics for user {self.user_id}"

class RevenueAnalytics(models.Model):
    salon = models.ForeignKey(Salon, on_delete=models.CASCADE)
    period = models.CharField(max_length=20)  # daily, weekly, monthly, yearly
    period_start = models.DateField()
    period_end = models.DateField()
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    service_revenue = models.JSONField(default=dict)  # Revenue by service
    staff_revenue = models.JSONField(default=dict)  # Revenue by staff member
    gift_bookings_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    regular_bookings_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('salon', 'period', 'period_start')

    def __str__(self):
        return f"Revenue for {self.salon.name} - {self.period} ({self.period_start} to {self.period_end})"

class PerformanceMetrics(models.Model):
    salon = models.ForeignKey(Salon, on_delete=models.CASCADE)
    date = models.DateField()
    staff_performance = models.JSONField(default=dict)  # Staff performance metrics
    service_popularity = models.JSONField(default=dict)  # Service popularity metrics
    customer_retention_rate = models.FloatField(default=0)
    average_booking_duration = models.FloatField(default=0)  # in minutes
    peak_booking_hours = models.JSONField(default=dict)
    customer_satisfaction_trends = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('salon', 'date')

    def __str__(self):
        return f"Performance metrics for {self.salon.name} on {self.date}"

# Existing Recommendation Models
class Recommendation(models.Model):
    RECOMMENDATION_TYPE_CHOICES = [
        ('popular', 'Popular'),
        ('cheap', 'Cheapest'),
        ('best_stylist', 'Best Stylist'),
        ('nearby', 'Nearby'),
        ('regional', 'Regional'),
    ]
    type = models.CharField(max_length=32, choices=RECOMMENDATION_TYPE_CHOICES)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    recommended_item = GenericForeignKey('content_type', 'object_id')
    score = models.FloatField(default=0)
    reason = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_type_display()} recommendation for {self.recommended_item} (score: {self.score})"

class FriendRecommendation(models.Model):
    from_user_id = models.CharField(max_length=255)
    to_user_id = models.CharField(max_length=255)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    recommended_item = GenericForeignKey('content_type', 'object_id')
    message = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Friend recommendation from {self.from_user_id} to {self.to_user_id} for {self.recommended_item}"

class UserProfile(models.Model):
    USER_ROLES = [
        ('customer', 'Customer'),
        ('vendor', 'Vendor'),
        ('admin', 'Admin'),
    ]
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='profile')
    role = models.CharField(max_length=20, choices=USER_ROLES, default='customer')

    def __str__(self):
        return f"{self.user.username} ({self.role})"

class Customer(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='customer_profiles')
    salon = models.ForeignKey(Salon, on_delete=models.CASCADE, related_name='customers')
    name = models.CharField(max_length=255)
    email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.email}) - {self.salon.name}"

# Signal to ensure every vendor has at least one salon
@receiver(post_delete, sender=Salon)
def ensure_vendor_has_salon(sender, instance, **kwargs):
    vendor = instance.vendor
    if vendor and not Salon.objects.filter(vendor=vendor).exists():
        # Optionally, raise an error or log a warning
        print(f"Warning: Vendor {vendor.username} now has no salons.")

class Follow(models.Model):
    follower = models.CharField(max_length=255)
    following = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('follower', 'following')

    def __str__(self):
        return f"{self.follower} follows {self.following}"

class Like(models.Model):
    user_id = models.CharField(max_length=255)
    style_post = models.ForeignKey('StylePost', on_delete=models.CASCADE, related_name='likes_set')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user_id', 'style_post')

    def __str__(self):
        return f"{self.user_id} likes post {self.style_post.id}"

class Comment(models.Model):
    user_id = models.CharField(max_length=255)
    style_post = models.ForeignKey('StylePost', on_delete=models.CASCADE, related_name='comments')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Comment by {self.user_id} on post {self.style_post.id}"

class Review(models.Model):
    user_id = models.CharField(max_length=255)
    salon = models.ForeignKey('Salon', on_delete=models.CASCADE, related_name='reviews')
    rating = models.FloatField()
    text = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Review by {self.user_id} for {self.salon.name} ({self.rating})"

class Payment(models.Model):
    booking = models.ForeignKey(Booking, related_name='payments', on_delete=models.CASCADE)
    transaction_id = models.CharField(max_length=255, unique=True)
    payment_method = models.CharField(max_length=50)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='KES')
    status_choices = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=20, choices=status_choices, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    payment_date = models.DateTimeField(null=True, blank=True)
    gateway_response = models.JSONField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Payment {self.transaction_id} - {self.booking.id} - {self.status}"