# Premium Salon Models for Featured Ticker
from django.db import models
from django.utils import timezone
from datetime import timedelta
from .models import Salon, User

class PremiumSubscription(models.Model):
    """Premium subscription model for salon featured placement"""
    TIER_CHOICES = [
        ('featured', 'Featured'),
        ('spotlight', 'Spotlight'),
        ('elite', 'Elite'),
    ]

    SERVICE_TYPES = [
        ('ticker_placement', 'Ticker Placement'),
        ('home_service', 'Home Service'),
        ('event_service', 'Event Service'),
        ('priority_booking', 'Priority Booking'),
        ('premium_badge', 'Premium Badge'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
        ('pending', 'Pending Payment'),
        ('trial', 'Trial'),
        ('trial_expired', 'Trial Expired'),
    ]
    
    salon = models.ForeignKey(Salon, on_delete=models.CASCADE, related_name='premium_subscriptions')
    tier = models.CharField(max_length=20, choices=TIER_CHOICES, default='featured')
    service_types = models.JSONField(default=list)  # List of enabled services
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    monthly_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    auto_renew = models.BooleanField(default=True)
    home_service_enabled = models.BooleanField(default=False)
    event_service_enabled = models.BooleanField(default=False)
    
    # Trial fields
    is_trial = models.BooleanField(default=False)
    trial_start_date = models.DateTimeField(null=True, blank=True)
    trial_end_date = models.DateTimeField(null=True, blank=True)
    trial_notifications_sent = models.JSONField(default=list)  # Track sent notifications
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.salon.name} - {self.get_tier_display()} ({self.status})"
    
    @property
    def is_active(self):
        if self.status == 'trial':
            return self.trial_end_date and timezone.now() <= self.trial_end_date
        elif self.status == 'active':
            return self.start_date <= timezone.now() <= self.end_date
        return False
    
    @property
    def days_remaining(self):
        if self.status == 'trial' and self.trial_end_date:
            return (self.trial_end_date - timezone.now()).days
        elif self.status == 'active' and self.end_date:
            return (self.end_date - timezone.now()).days
        return 0
    
    @property
    def hours_remaining(self):
        if self.status == 'trial' and self.trial_end_date:
            total_seconds = (self.trial_end_date - timezone.now()).total_seconds()
            return max(0, int(total_seconds // 3600))
        return 0
    
    @property
    def trial_status(self):
        """Get detailed trial status for notifications"""
        if not self.is_trial or not self.trial_end_date:
            return None
        
        now = timezone.now()
        time_left = self.trial_end_date - now
        
        if time_left.total_seconds() <= 0:
            return 'expired'
        elif time_left.total_seconds() <= 3600:  # 1 hour
            return 'expiring_soon'
        elif time_left.total_seconds() <= 86400:  # 1 day
            return 'expiring_today'
        else:
            return 'active'
    
    def start_trial(self):
        """Start a 48-hour trial period"""
        self.is_trial = True
        self.status = 'trial'
        self.trial_start_date = timezone.now()
        self.trial_end_date = self.trial_start_date + timedelta(hours=48)
        self.trial_notifications_sent = []
        self.save()
    
    def should_send_notification(self, notification_type):
        """Check if notification should be sent"""
        if notification_type not in self.trial_notifications_sent:
            return True
        return False
    
    def mark_notification_sent(self, notification_type):
        """Mark notification as sent"""
        if notification_type not in self.trial_notifications_sent:
            self.trial_notifications_sent.append(notification_type)
            self.save()

class PremiumAnalytics(models.Model):
    """Analytics for premium salon performance"""
    subscription = models.ForeignKey(PremiumSubscription, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField(auto_now_add=True)
    ticker_impressions = models.IntegerField(default=0)
    ticker_clicks = models.IntegerField(default=0)
    profile_views = models.IntegerField(default=0)
    bookings_generated = models.IntegerField(default=0)
    revenue_generated = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    click_through_rate = models.FloatField(default=0)  # clicks/impressions * 100
    conversion_rate = models.FloatField(default=0)  # bookings/clicks * 100
    
    class Meta:
        unique_together = ('subscription', 'date')
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.subscription.salon.name} analytics - {self.date}"
    
    def calculate_rates(self):
        """Calculate CTR and conversion rates"""
        if self.ticker_impressions > 0:
            self.click_through_rate = (self.ticker_clicks / self.ticker_impressions) * 100
        
        if self.ticker_clicks > 0:
            self.conversion_rate = (self.bookings_generated / self.ticker_clicks) * 100
        
        self.save()

class TickerClick(models.Model):
    """Track individual ticker clicks for detailed analytics"""
    subscription = models.ForeignKey(PremiumSubscription, on_delete=models.CASCADE, related_name='clicks')
    user_ip = models.GenericIPAddressField()
    user_agent = models.TextField()
    referrer = models.URLField(blank=True, null=True)
    clicked_at = models.DateTimeField(auto_now_add=True)
    session_id = models.CharField(max_length=255, blank=True, null=True)
    
    class Meta:
        ordering = ['-clicked_at']
    
    def __str__(self):
        return f"Click on {self.subscription.salon.name} at {self.clicked_at}"

class HomeService(models.Model):
    """Home service bookings for premium salons"""
    LOCATION_TYPES = [
        ('home', 'Client Home'),
        ('office', 'Office/Workplace'),
        ('hotel', 'Hotel'),
        ('event_venue', 'Event Venue'),
    ]

    salon = models.ForeignKey(Salon, on_delete=models.CASCADE, related_name='home_services')
    client_name = models.CharField(max_length=255)
    client_phone = models.CharField(max_length=20)
    client_email = models.EmailField()
    location_type = models.CharField(max_length=20, choices=LOCATION_TYPES, default='home')
    address = models.TextField()
    service_date = models.DateTimeField()
    services_requested = models.JSONField(default=list)  # List of services
    estimated_duration = models.IntegerField(help_text="Duration in minutes")
    total_cost = models.DecimalField(max_digits=10, decimal_places=2)
    travel_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ], default='pending')
    special_requirements = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Home service: {self.salon.name} for {self.client_name} on {self.service_date}"

class EventService(models.Model):
    """Event service bookings for premium salons"""
    EVENT_TYPES = [
        ('wedding', 'Wedding'),
        ('corporate', 'Corporate Event'),
        ('party', 'Private Party'),
        ('photoshoot', 'Photoshoot'),
        ('graduation', 'Graduation'),
        ('other', 'Other'),
    ]

    salon = models.ForeignKey(Salon, on_delete=models.CASCADE, related_name='event_services')
    event_name = models.CharField(max_length=255)
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    organizer_name = models.CharField(max_length=255)
    organizer_phone = models.CharField(max_length=20)
    organizer_email = models.EmailField()
    event_date = models.DateTimeField()
    event_venue = models.TextField()
    number_of_clients = models.IntegerField()
    services_requested = models.JSONField(default=list)
    estimated_duration = models.IntegerField(help_text="Duration in minutes")
    total_cost = models.DecimalField(max_digits=10, decimal_places=2)
    travel_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    equipment_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=[
        ('inquiry', 'Inquiry'),
        ('quoted', 'Quoted'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ], default='inquiry')
    special_requirements = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Event service: {self.event_name} by {self.salon.name} on {self.event_date}"

class CommissionTracking(models.Model):
    """Track platform commissions from vendor services"""
    COMMISSION_TYPES = [
        ('subscription', 'Monthly Subscription'),
        ('home_service_booking', 'Home Service Booking'),
        ('home_service_lead', 'Home Service Lead'),
        ('event_service_booking', 'Event Service Booking'),
        ('event_service_lead', 'Event Service Lead'),
        ('advertising_click', 'Advertising Click'),
    ]

    salon = models.ForeignKey(Salon, on_delete=models.CASCADE, related_name='commissions')
    commission_type = models.CharField(max_length=30, choices=COMMISSION_TYPES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reference_id = models.CharField(max_length=255, help_text="Booking ID, Lead ID, etc.")
    description = models.TextField()
    date_earned = models.DateTimeField(auto_now_add=True)
    paid_to_platform = models.BooleanField(default=False)
    payment_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-date_earned']

    def __str__(self):
        return f"{self.salon.name} - {self.get_commission_type_display()}: KSh {self.amount}"
