from rest_framework import serializers
from .models import Salon, Service, Staff, Booking, Payment, Recommendation, FriendRecommendation, UserLocation, Friendship, StylePost, RegionalTrending, SalonAnalytics, CustomerAnalytics, RevenueAnalytics, PerformanceMetrics, Customer, Follow, Like, Comment, Review
from django.contrib.auth.models import User

class ServiceSerializer(serializers.ModelSerializer):
    salon_name = serializers.CharField(source='salon.name', read_only=True)
    
    class Meta:
        model = Service
        fields = '__all__'

class StaffSerializer(serializers.ModelSerializer):
    salon_name = serializers.CharField(source='salon.name', read_only=True)
    
    class Meta:
        model = Staff
        fields = '__all__'

class SalonSerializer(serializers.ModelSerializer):
    vendor_username = serializers.CharField(source='vendor.username', read_only=True)
    services = ServiceSerializer(many=True, read_only=True)
    staff = StaffSerializer(many=True, read_only=True)
    image_url = serializers.SerializerMethodField(read_only=True)

    def get_image_url(self, obj):
        request = self.context.get('request')
        if obj.image and hasattr(obj.image, 'url'):
            if request is not None:
                return request.build_absolute_uri(obj.image.url)
            else:
                return obj.image.url
        elif obj.imageUrl:
            return obj.imageUrl
        return ''

    class Meta:
        model = Salon
        fields = '__all__'
        extra_fields = ['image_url']

class BookingSerializer(serializers.ModelSerializer):
    salon_name = serializers.CharField(source='salon.name', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    staff_name = serializers.CharField(source='staff.name', read_only=True)
    salon_detail = SalonSerializer(source='salon', read_only=True)
    service_detail = ServiceSerializer(source='service', read_only=True)
    staff_detail = StaffSerializer(source='staff', read_only=True)
    
    class Meta:
        model = Booking
        fields = '__all__'

class RecommendationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Recommendation
        fields = '__all__'

class FriendRecommendationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FriendRecommendation
        fields = '__all__'

class UserLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserLocation
        fields = '__all__'

class FriendshipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Friendship
        fields = '__all__'

class StylePostSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='userName', read_only=True)
    salon_name = serializers.CharField(source='booking.salon.name', read_only=True)
    service_name = serializers.CharField(source='booking.service.name', read_only=True)
    
    class Meta:
        model = StylePost
        fields = '__all__'

class RegionalTrendingSerializer(serializers.ModelSerializer):
    class Meta:
        model = RegionalTrending
        fields = '__all__'

# Analytics Serializers
class SalonAnalyticsSerializer(serializers.ModelSerializer):
    salon_name = serializers.CharField(source='salon.name', read_only=True)
    
    class Meta:
        model = SalonAnalytics
        fields = '__all__'

class CustomerAnalyticsSerializer(serializers.ModelSerializer):
    favorite_salon_name = serializers.CharField(source='favorite_salon.name', read_only=True)
    favorite_service_name = serializers.CharField(source='favorite_service.name', read_only=True)
    
    class Meta:
        model = CustomerAnalytics
        fields = '__all__'

class RevenueAnalyticsSerializer(serializers.ModelSerializer):
    salon_name = serializers.CharField(source='salon.name', read_only=True)
    
    class Meta:
        model = RevenueAnalytics
        fields = '__all__'

class PerformanceMetricsSerializer(serializers.ModelSerializer):
    salon_name = serializers.CharField(source='salon.name', read_only=True)
    
    class Meta:
        model = PerformanceMetrics
        fields = '__all__'

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=False)
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_staff', 'is_superuser', 'is_active', 'password']
        read_only_fields = ['id', 'username']
    def validate(self, data):
        if self.instance is None:  # Only on create
            if not data.get('username'):
                raise serializers.ValidationError({'username': 'This field is required.'})
            if not data.get('password'):
                raise serializers.ValidationError({'password': 'This field is required.'})
        return data
    def create(self, validated_data):
        password = validated_data.pop('password', None)
        user = User(**validated_data)
        if password:
            user.set_password(password)
        user.save()
        return user

class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = '__all__'

class FollowSerializer(serializers.ModelSerializer):
    class Meta:
        model = Follow
        fields = '__all__'

class LikeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Like
        fields = '__all__'

class CommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Comment
        fields = '__all__'

class ReviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = Review
        fields = '__all__'

class PaymentSerializer(serializers.ModelSerializer):
    booking_details = BookingSerializer(source='booking', read_only=True)
    
    class Meta:
        model = Payment
        fields = '__all__'
