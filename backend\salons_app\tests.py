from django.test import TestCase
from .models import Salon, Service, Staff, Booking

# Create your tests here.

class SalonModelTest(TestCase):
    def test_create_salon(self):
        salon = Salon.objects.create(
            name="Test Salon",
            address="123 Test St",
            county="Test County",
            town="Test Town",
            phone="1234567890",
            email="<EMAIL>",
            latitude=1.23,
            longitude=4.56,
            description="A test salon",
            imageUrl="http://example.com/image.jpg"
        )
        self.assertEqual(str(salon), "Test Salon")

class ServiceModelTest(TestCase):
    def setUp(self):
        self.salon = Salon.objects.create(
            name="Test Salon",
            address="123 Test St",
            county="Test County",
            town="Test Town",
            phone="1234567890",
            email="<EMAIL>",
            latitude=1.23,
            longitude=4.56,
            description="A test salon",
            imageUrl="http://example.com/image.jpg"
        )
    def test_create_service(self):
        service = Service.objects.create(
            salon=self.salon,
            name="Test Service",
            price=100.00,
            duration=60
        )
        self.assertIn("Test Service", str(service))

class StaffModelTest(TestCase):
    def setUp(self):
        self.salon = Salon.objects.create(
            name="Test Salon",
            address="123 Test St",
            county="Test County",
            town="Test Town",
            phone="1234567890",
            email="<EMAIL>",
            latitude=1.23,
            longitude=4.56,
            description="A test salon",
            imageUrl="http://example.com/image.jpg"
        )
        self.service = Service.objects.create(
            salon=self.salon,
            name="Test Service",
            price=100.00,
            duration=60
        )
    def test_create_staff(self):
        staff = Staff.objects.create(
            salon=self.salon,
            name="Test Staff",
            role="Stylist"
        )
        staff.services.add(self.service)
        self.assertIn("Test Staff", str(staff))

class BookingModelTest(TestCase):
    def setUp(self):
        self.salon = Salon.objects.create(
            name="Test Salon",
            address="123 Test St",
            county="Test County",
            town="Test Town",
            phone="1234567890",
            email="<EMAIL>",
            latitude=1.23,
            longitude=4.56,
            description="A test salon",
            imageUrl="http://example.com/image.jpg"
        )
        self.service = Service.objects.create(
            salon=self.salon,
            name="Test Service",
            price=100.00,
            duration=60
        )
        self.staff = Staff.objects.create(
            salon=self.salon,
            name="Test Staff",
            role="Stylist"
        )
        self.staff.services.add(self.service)
    def test_create_booking(self):
        booking = Booking.objects.create(
            userId="user1",
            userName="Test User",
            salon=self.salon,
            service=self.service,
            staff=self.staff,
            date="2024-01-01",
            time="10:00 AM",
            status="Pending"
        )
        self.assertIn("Test User", str(booking))
