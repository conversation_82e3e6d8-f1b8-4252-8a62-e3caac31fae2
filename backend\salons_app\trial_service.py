# Trial Management Service for Vendor Trials
from django.utils import timezone
from datetime import timedelta
from .premium_models import PremiumSubscription
from .models import Salon
import logging

logger = logging.getLogger(__name__)

class TrialService:
    """Service to manage vendor trial periods and notifications"""
    
    TRIAL_DURATION_HOURS = 48
    NOTIFICATION_TYPES = {
        'trial_started': 'Trial started',
        'one_day_remaining': 'One day remaining',
        'one_hour_remaining': 'One hour remaining',
        'trial_expired': 'Trial expired'
    }
    
    @staticmethod
    def create_trial_for_vendor(salon):
        """Create a 48-hour trial for a new vendor"""
        try:
            # Check if vendor already has a trial or subscription
            existing_subscription = PremiumSubscription.objects.filter(salon=salon).first()
            
            if existing_subscription:
                if existing_subscription.is_trial and existing_subscription.is_active:
                    return existing_subscription, "Trial already active"
                elif existing_subscription.status == 'active':
                    return existing_subscription, "Already has active subscription"
            
            # Create new trial subscription
            trial_subscription = PremiumSubscription.objects.create(
                salon=salon,
                tier='featured',
                status='trial',
                is_trial=True,
                trial_start_date=timezone.now(),
                trial_end_date=timezone.now() + timedelta(hours=TrialService.TRIAL_DURATION_HOURS),
                monthly_price=0,
                total_paid=0,
                auto_renew=False,
                home_service_enabled=True,
                event_service_enabled=True,
                service_types=['ticker_placement', 'home_service', 'event_service'],
                trial_notifications_sent=['trial_started']
            )
            
            logger.info(f"Trial created for salon: {salon.name}")
            return trial_subscription, "Trial created successfully"
            
        except Exception as e:
            logger.error(f"Error creating trial for salon {salon.name}: {str(e)}")
            return None, f"Error creating trial: {str(e)}"
    
    @staticmethod
    def check_trial_status(salon):
        """Check current trial status for a salon"""
        try:
            subscription = PremiumSubscription.objects.filter(salon=salon).first()
            if not subscription or not subscription.is_trial:
                return None
            
            return {
                'is_trial': subscription.is_trial,
                'is_active': subscription.is_active,
                'trial_status': subscription.trial_status,
                'hours_remaining': subscription.hours_remaining,
                'days_remaining': subscription.days_remaining,
                'trial_end_date': subscription.trial_end_date,
                'notifications_sent': subscription.trial_notifications_sent
            }
        except Exception as e:
            logger.error(f"Error checking trial status for salon {salon.name}: {str(e)}")
            return None
    
    @staticmethod
    def process_trial_notifications():
        """Process and send trial notifications"""
        try:
            active_trials = PremiumSubscription.objects.filter(
                status='trial',
                is_trial=True
            )
            
            notifications_sent = []
            
            for trial in active_trials:
                if not trial.is_active:
                    # Trial has expired
                    if trial.should_send_notification('trial_expired'):
                        TrialService._send_trial_expired_notification(trial)
                        trial.mark_notification_sent('trial_expired')
                        trial.status = 'trial_expired'
                        trial.save()
                        notifications_sent.append(f"Expired notification sent to {trial.salon.name}")
                    continue
                
                # Check for 2 hour remaining notification
                if trial.hours_remaining <= 2 and trial.hours_remaining > 1 and trial.should_send_notification('two_hour_remaining'):
                    TrialService._send_two_hour_remaining_notification(trial)
                    trial.mark_notification_sent('two_hour_remaining')
                    notifications_sent.append(f"2 hour notification sent to {trial.salon.name}")

                # Check for 1 hour remaining notification
                elif trial.hours_remaining <= 1 and trial.should_send_notification('one_hour_remaining'):
                    TrialService._send_one_hour_remaining_notification(trial)
                    trial.mark_notification_sent('one_hour_remaining')
                    notifications_sent.append(f"1 hour notification sent to {trial.salon.name}")
            
            return notifications_sent
            
        except Exception as e:
            logger.error(f"Error processing trial notifications: {str(e)}")
            return []
    
    @staticmethod
    def _send_trial_expired_notification(trial):
        """Send trial expired notification"""
        try:
            # This would integrate with your notification system
            # For now, we'll log it
            logger.info(f"TRIAL EXPIRED: {trial.salon.name} - Trial has expired. Redirect to payment.")
            
            # You can integrate with email/SMS services here
            # send_email(trial.salon.user.email, "Trial Expired", "Your trial has expired...")
            # send_sms(trial.salon.phone, "Your SalonGenz trial has expired...")
            
        except Exception as e:
            logger.error(f"Error sending trial expired notification: {str(e)}")
    
    @staticmethod
    def _send_two_hour_remaining_notification(trial):
        """Send 2 hour remaining notification"""
        try:
            logger.info(f"TRIAL WARNING: {trial.salon.name} - Trial expires in 2 hours")

            # You can integrate with email/SMS services here
            # send_email(trial.salon.user.email, "Trial Expiring Soon", "Your trial expires in 2 hours...")
            # send_sms(trial.salon.phone, "Your SalonGenz trial expires in 2 hours...")

        except Exception as e:
            logger.error(f"Error sending 2 hour notification: {str(e)}")

    @staticmethod
    def _send_one_hour_remaining_notification(trial):
        """Send 1 hour remaining notification"""
        try:
            logger.info(f"TRIAL WARNING: {trial.salon.name} - Trial expires in 1 hour")

            # You can integrate with email/SMS services here
            # send_email(trial.salon.user.email, "Trial Expiring Soon", "Your trial expires in 1 hour...")
            # send_sms(trial.salon.phone, "Your SalonGenz trial expires in 1 hour...")

        except Exception as e:
            logger.error(f"Error sending 1 hour notification: {str(e)}")
    
    @staticmethod
    def get_trial_message(salon):
        """Get appropriate trial message for frontend display"""
        trial_status = TrialService.check_trial_status(salon)
        
        if not trial_status:
            return None
        
        if trial_status['trial_status'] == 'expired':
            return {
                'type': 'expired',
                'title': 'Trial Expired',
                'message': 'Your trial has expired. Your listing will be removed soon. Upgrade now to avoid disruption to your business!',
                'action': 'Upgrade Now',
                'action_url': '/vendor/subscription'
            }
        elif trial_status['trial_status'] == 'expiring_soon':
            return {
                'type': 'warning',
                'title': 'Trial Expiring Soon',
                'message': f'Your trial expires in {trial_status["hours_remaining"]} hour(s). Subscribe now to avoid interruption!',
                'action': 'Subscribe Now',
                'action_url': '/vendor/subscription'
            }
        elif trial_status['trial_status'] == 'expiring_today':
            return {
                'type': 'info',
                'title': 'Trial Expiring Today',
                'message': f'Your trial expires in {trial_status["days_remaining"]} day(s). Consider subscribing to continue!',
                'action': 'View Plans',
                'action_url': '/vendor/subscription'
            }
        else:
            return {
                'type': 'info',
                'title': 'Trial Active',
                'message': f'You have {trial_status["hours_remaining"]} hour(s) remaining in your trial.',
                'action': 'View Plans',
                'action_url': '/vendor/subscription'
            }

    @staticmethod
    def handle_trial_expiry():
        """Handle expired trials - archive salons after grace period"""
        try:
            from django.utils import timezone
            from datetime import timedelta

            now = timezone.now()
            grace_period_end = now - timedelta(hours=1)  # 1 hour grace period after trial end

            # Find expired trials that are past grace period
            expired_trials = PremiumSubscription.objects.filter(
                is_trial=True,
                status='trial',
                trial_end_date__lt=grace_period_end
            )

            archived_count = 0
            for subscription in expired_trials:
                salon = subscription.salon

                # Archive the salon
                salon.is_active = False
                salon.archived_reason = "Trial expired - no subscription"
                salon.archived_at = now
                salon.save()

                # Update subscription status
                subscription.status = 'trial_expired'
                subscription.save()

                logger.info(f"Archived salon {salon.name} due to trial expiry")
                archived_count += 1

            return archived_count

        except Exception as e:
            logger.error(f"Error handling trial expiry: {str(e)}")
            return 0