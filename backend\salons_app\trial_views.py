# Trial Management API Views
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from .models import Salon
from .trial_service import TrialService
from .premium_models import PremiumSubscription
import logging

logger = logging.getLogger(__name__)

class TrialStatusView(APIView):
    """Get current trial status for authenticated vendor"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        try:
            # Get vendor's salon
            salon = Salon.objects.filter(vendor=request.user).first()
            if not salon:
                return Response({
                    'error': 'No salon found for this user'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Get trial status
            trial_status = TrialService.check_trial_status(salon)
            trial_message = TrialService.get_trial_message(salon)
            
            return Response({
                'trial_status': trial_status,
                'trial_message': trial_message,
                'salon_name': salon.name
            })
            
        except Exception as e:
            logger.error(f"Error getting trial status: {str(e)}")
            return Response({
                'error': 'Failed to get trial status'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StartTrialView(APIView):
    """Start a trial for a vendor"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        try:
            # Get or create vendor's salon
            salon = Salon.objects.filter(vendor=request.user).first()
            if not salon:
                # Create a basic salon for the user to enable trial
                salon = Salon.objects.create(
                    vendor=request.user,
                    name=f"{request.user.username}'s Salon",
                    town="Not specified",
                    address="Not specified",
                    phone="Not specified",
                    email=request.user.email or "<EMAIL>",
                    description="Trial salon - please update your details",
                    image="https://via.placeholder.com/300x200?text=Update+Your+Salon+Image"
                )
                logger.info(f"Created trial salon for user: {request.user.username}")

            # Start trial
            subscription, message = TrialService.create_trial_for_vendor(salon)

            if subscription:
                return Response({
                    'success': True,
                    'message': message,
                    'trial_end_date': subscription.trial_end_date,
                    'hours_remaining': subscription.hours_remaining,
                    'salon_created': salon.name == f"{request.user.username}'s Salon"
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error starting trial: {str(e)}")
            return Response({
                'error': 'Failed to start trial'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TrialNotificationView(APIView):
    """Process trial notifications (admin endpoint)"""
    
    def post(self, request):
        try:
            # This endpoint would typically be called by a cron job or scheduled task
            notifications_sent = TrialService.process_trial_notifications()
            
            return Response({
                'success': True,
                'notifications_sent': notifications_sent,
                'count': len(notifications_sent)
            })
            
        except Exception as e:
            logger.error(f"Error processing trial notifications: {str(e)}")
            return Response({
                'error': 'Failed to process notifications'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class VendorTrialInfoView(APIView):
    """Get comprehensive trial information for vendor dashboard"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        try:
            # Get vendor's salon
            salon = Salon.objects.filter(vendor=request.user).first()
            if not salon:
                return Response({
                    'error': 'No salon found for this user'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Get subscription info
            subscription = PremiumSubscription.objects.filter(salon=salon).first()
            
            if not subscription:
                return Response({
                    'has_subscription': False,
                    'message': 'No subscription found'
                })
            
            trial_info = {
                'has_subscription': True,
                'is_trial': subscription.is_trial,
                'status': subscription.status,
                'is_active': subscription.is_active,
                'tier': subscription.tier,
                'features_enabled': subscription.service_types,
                'home_service_enabled': subscription.home_service_enabled,
                'event_service_enabled': subscription.event_service_enabled
            }
            
            if subscription.is_trial:
                trial_info.update({
                    'trial_start_date': subscription.trial_start_date,
                    'trial_end_date': subscription.trial_end_date,
                    'hours_remaining': subscription.hours_remaining,
                    'days_remaining': subscription.days_remaining,
                    'trial_status': subscription.trial_status,
                    'trial_message': TrialService.get_trial_message(salon)
                })
            else:
                trial_info.update({
                    'start_date': subscription.start_date,
                    'end_date': subscription.end_date,
                    'days_remaining': subscription.days_remaining,
                    'monthly_price': subscription.monthly_price,
                    'total_paid': subscription.total_paid
                })
            
            return Response(trial_info)
            
        except Exception as e:
            logger.error(f"Error getting trial info: {str(e)}")
            return Response({
                'error': 'Failed to get trial information'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 