from django.urls import path
from .views import (
    SalonListCreateView, SalonRetrieveUpdateDestroyView,
    ServiceListCreateView, ServiceRetrieveUpdateDestroyView,
    StaffListCreateView, StaffRetrieveUpdateDestroyView,
    BookingListCreateView, BookingRetrieveUpdateDestroyView, UserBookingsView,
    RecommendationListCreateView, RecommendationRetrieveUpdateDestroyView,
    FriendRecommendationListCreateView, FriendRecommendationRetrieveUpdateDestroyView,
    GeneratePopularVendorsView, GenerateCheapestServicesView,

    NearbySalonsView, NearbySalonsEnhancedView,
    TrendingRecommendationsView, RegionalTrendingView,
    FriendsActivityRecommendationsView, FriendsActivityView,
    PersonalizedRecommendationsView, ByLocationRecommendationsView,
    SalonSearchView, SalonRegisterView, SalonProfileUpdateView,
    UserLocationView, FriendshipView, StylePostView, GiftBookingView,
    SalonAnalyticsView, CustomerAnalyticsView, RevenueAnalyticsView, PerformanceMetricsView, DashboardAnalyticsView,
    VendorBookingsView, VendorAccountUpdateView, UserProfileView,
    VendorCustomersView, VendorCustomerBookingsView,

    NearbySalonsView,
    TrendingRecommendationsView,
    FriendsActivityRecommendationsView,
    PersonalizedRecommendationsView,
    ByLocationRecommendationsView,
    SalonSearchView,
    SalonRegisterView,
    SalonProfileUpdateView,
    UserListCreateView, UserRetrieveUpdateDestroyView,     TownsByCountyView, CountiesView,
    ServicesSearchView, StaffSearchView, AvailableSlotsView,
    service_search, staff_search,
    CustomerListCreateView, CustomerRetrieveUpdateDestroyView,
    FollowView, LikeView, CommentView, ReviewView,
    
    # Payment Views
    PaymentProcessView, PaymentStatusView, PaymentCancelView, BookingStatusUpdateView, NotificationView,
    PaystackInitializeView, PaystackVerifyView, PaystackBanksView, PaystackResolveAccountView, 
    PaystackTransferRecipientView, PaystackTransferView, PaystackChargeView
)
from .trial_views import TrialStatusView, StartTrialView, TrialNotificationView, VendorTrialInfoView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from .views_auth import RegisterView

urlpatterns = [
    path('salons/', SalonListCreateView.as_view(), name='salon-list-create'),
    path('salons/<int:pk>/', SalonRetrieveUpdateDestroyView.as_view(), name='salon-retrieve-update-destroy'),
    path('services/', ServiceListCreateView.as_view(), name='service-list-create'),
    path('services/<int:pk>/', ServiceRetrieveUpdateDestroyView.as_view(), name='service-retrieve-update-destroy'),
    path('staff/', StaffListCreateView.as_view(), name='staff-list-create'),
    path('staff/<int:pk>/', StaffRetrieveUpdateDestroyView.as_view(), name='staff-retrieve-update-destroy'),
    path('bookings/', BookingListCreateView.as_view(), name='booking-list-create'),
    path('bookings/<int:pk>/', BookingRetrieveUpdateDestroyView.as_view(), name='booking-retrieve-update-destroy'),
    path('user/bookings/', UserBookingsView.as_view(), name='user-bookings'),
    path('recommendations/', RecommendationListCreateView.as_view(), name='recommendation-list-create'),
    path('recommendations/<int:pk>/', RecommendationRetrieveUpdateDestroyView.as_view(), name='recommendation-retrieve-update-destroy'),
    path('friend-recommendations/', FriendRecommendationListCreateView.as_view(), name='friend-recommendation-list-create'),
    path('friend-recommendations/<int:pk>/', FriendRecommendationRetrieveUpdateDestroyView.as_view(), name='friend-recommendation-retrieve-update-destroy'),
    path('recommendations/generate/popular/', GeneratePopularVendorsView.as_view(), name='generate-popular-vendors'),
    path('recommendations/generate/cheap/', GenerateCheapestServicesView.as_view(), name='generate-cheapest-services'),
    path('recommendations/nearby/', NearbySalonsView.as_view(), name='nearby-salons'),
    path('recommendations/trending/', TrendingRecommendationsView.as_view(), name='trending-recommendations'),
    path('recommendations/friends-activity/', FriendsActivityRecommendationsView.as_view(), name='friends-activity-recommendations'),
    path('recommendations/personalized/', PersonalizedRecommendationsView.as_view(), name='personalized-recommendations'),
    path('recommendations/by-location/', ByLocationRecommendationsView.as_view(), name='by-location-recommendations'),
    path('salons/search/', SalonSearchView.as_view(), name='salon-search'),
    path('salons/register/', SalonRegisterView.as_view(), name='salon-register'),
    path('salons/profile/<int:pk>/', SalonProfileUpdateView.as_view(), name='salon-profile-update'),
    path('salons/<int:salon_id>/services/', ServiceListCreateView.as_view(), name='salon-services-list'),
    path('salons/<int:salon_id>/staff/', StaffListCreateView.as_view(), name='salon-staff-list'),

    path('user/location/', UserLocationView.as_view(), name='user-location'),
    path('recommendations/nearby-enhanced/', NearbySalonsEnhancedView.as_view(), name='nearby-salons-enhanced'),
    path('recommendations/regional-trending/', RegionalTrendingView.as_view(), name='regional-trending'),
    path('friendships/', FriendshipView.as_view(), name='friendship'),
    path('style-posts/', StylePostView.as_view(), name='style-post'),
    path('friends/activity/', FriendsActivityView.as_view(), name='friends-activity'),
    path('bookings/gift/', GiftBookingView.as_view(), name='gift-booking'),
    path('analytics/salon/<int:salon_id>/', SalonAnalyticsView.as_view(), name='salon-analytics'),
    path('analytics/customer/<str:user_id>/', CustomerAnalyticsView.as_view(), name='customer-analytics'),
    path('analytics/revenue/<int:salon_id>/', RevenueAnalyticsView.as_view(), name='revenue-analytics'),
    path('analytics/performance/<int:salon_id>/', PerformanceMetricsView.as_view(), name='performance-metrics'),
    path('analytics/dashboard/', DashboardAnalyticsView.as_view(), name='dashboard-analytics'),
    path('admin/analytics/', DashboardAnalyticsView.as_view(), name='admin-analytics'),
    path('vendor/bookings/', VendorBookingsView.as_view(), name='vendor-bookings'),
    path('vendor/account/', VendorAccountUpdateView.as_view(), name='vendor-account-update'),
    path('user/profile/', UserProfileView.as_view(), name='user-profile'),
    path('vendor/customers/', VendorCustomersView.as_view(), name='vendor-customers'),
    path('vendor/customers/<int:customer_id>/bookings/', VendorCustomerBookingsView.as_view(), name='vendor-customer-bookings'),
    
    # Trial Management URLs
    path('vendor/trial/status/', TrialStatusView.as_view(), name='trial-status'),
    path('vendor/trial/start/', StartTrialView.as_view(), name='start-trial'),
    path('vendor/trial/info/', VendorTrialInfoView.as_view(), name='vendor-trial-info'),
    path('admin/trial/notifications/', TrialNotificationView.as_view(), name='trial-notifications'),
]

urlpatterns += [
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('register/', RegisterView.as_view(), name='register'),

    path('users/', UserListCreateView.as_view(), name='user-list-create'),
    path('users/<int:pk>/', UserRetrieveUpdateDestroyView.as_view(), name='user-retrieve-update-destroy'),

    path('towns/', TownsByCountyView.as_view(), name='towns-by-county'),
    path('counties/', CountiesView.as_view(), name='counties'),
    path('services/search/', ServicesSearchView.as_view(), name='services-search'),
    path('staff/search/', StaffSearchView.as_view(), name='staff-search'),
    path('available-slots/', AvailableSlotsView.as_view(), name='available-slots'),
    # Paystack URLs
    path('paystack/initialize/', PaystackInitializeView.as_view(), name='paystack-initialize'),
    path('paystack/verify/<str:reference>/', PaystackVerifyView.as_view(), name='paystack-verify'),
    path('paystack/banks/', PaystackBanksView.as_view(), name='paystack-banks'),
    path('paystack/resolve-account/', PaystackResolveAccountView.as_view(), name='paystack-resolve-account'),
    path('paystack/transferrecipient/', PaystackTransferRecipientView.as_view(), name='paystack-transferrecipient'),
    path('paystack/transfer/', PaystackTransferView.as_view(), name='paystack-transfer'),
    path('paystack/charge/', PaystackChargeView.as_view(), name='paystack-charge'),
]

urlpatterns += [
    path('customers/', CustomerListCreateView.as_view(), name='customer-list-create'),
    path('customers/<int:pk>/', CustomerRetrieveUpdateDestroyView.as_view(), name='customer-retrieve-update-destroy'),
]

urlpatterns += [
    path('follows/', FollowView.as_view(), name='follow'),
    path('likes/', LikeView.as_view(), name='like'),
    path('comments/', CommentView.as_view(), name='comment'),
    path('reviews/', ReviewView.as_view(), name='review'),
]

# Payment and Notification URLs
urlpatterns += [
    path('payments/', PaymentProcessView.as_view(), name='payment-process'),
    path('payments/<str:transaction_id>/status/', PaymentStatusView.as_view(), name='payment-status'),
    path('payments/<str:transaction_id>/cancel/', PaymentCancelView.as_view(), name='payment-cancel'),
    path('bookings/<int:booking_id>/status/', BookingStatusUpdateView.as_view(), name='booking-status-update'),
    path('notifications/send-confirmation/', NotificationView.as_view(), name='send-confirmation'),
    
    # Paystack URLs
    path('paystack/initialize/', PaystackInitializeView.as_view(), name='paystack-initialize'),
    path('paystack/verify/<str:reference>/', PaystackVerifyView.as_view(), name='paystack-verify'),
]
