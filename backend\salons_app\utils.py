import requests

import os
IPINFO_TOKEN = os.environ.get("IPINFO_TOKEN")  # Now loaded from environment variable
IPINFO_URL = lambda ip, token: f"https://api.ipinfo.io/lite/{ip}?token={token}"

def get_geolocation_from_ip(ip):
    try:
        url = IPINFO_URL(ip, IPINFO_TOKEN)
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            return {
                'county': data.get('region'),
                'town': data.get('city'),
                'latitude': float(data.get('loc', ',').split(',')[0]) if 'loc' in data else None,
                'longitude': float(data.get('loc', ',').split(',')[1]) if 'loc' in data else None
            }
        else:
            print(f"ipinfo.io error: {response.status_code} {response.text}")
            # Friendly fallback message for logging
            print("Unable to detect location from IP. Prompting user for manual input.")
            return None
    except Exception:
        return None
