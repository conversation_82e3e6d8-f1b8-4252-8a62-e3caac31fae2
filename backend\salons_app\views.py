from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.contenttypes.models import ContentType
from django.db.models import Count, Min, Q, Sum, Avg, F, Max
from django.db import models
from .models import Salon, Service, Staff, Booking, Recommendation, FriendRecommendation, UserLocation, Friendship, StylePost, RegionalTrending, SalonAnalytics, CustomerAnalytics, RevenueAnalytics, PerformanceMetrics, Customer, Follow, Like, Comment, Review, Payment
from .serializers import SalonSerializer, ServiceSerializer, StaffSerializer, BookingSerializer, RecommendationSerializer, FriendRecommendationSerializer, UserLocationSerializer, FriendshipSerializer, StylePostSerializer, RegionalTrendingSerializer, SalonAnalyticsSerializer, CustomerAnalyticsSerializer, RevenueAnalyticsSerializer, PerformanceMetricsSerializer, UserSerializer, CustomerSerializer, FollowSerializer, LikeSerializer, CommentSerializer, ReviewSerializer, PaymentSerializer
import math
from django.utils import timezone
from datetime import datetime, timedelta
from .utils import get_geolocation_from_ip
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from django.contrib.auth.models import User
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser
from rest_framework.decorators import api_view, permission_classes
from rest_framework.exceptions import PermissionDenied
import requests
import json
import os
import sys
from django.conf import settings
from .location_intelligence import (
    get_county_from_town,
    validate_coordinates_for_county,
    validate_kenya_coordinates,
    auto_correct_salon_location,
    KENYA_COUNTY_BOUNDS,
    KENYA_BOUNDS
)

# Add the backend directory to Python path for services import
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if backend_dir not in sys.path:
    sys.path.append(backend_dir)

try:
    from services.email_service import EmailService
except ImportError:
    EmailService = None

# Analytics Views
class SalonAnalyticsView(APIView):
    def get(self, request, salon_id):
        try:
            salon = Salon.objects.get(id=salon_id)
        except Salon.DoesNotExist:
            return Response({'error': 'Salon not found'}, status=404)
        
        # Get date range from query params
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        if not start_date:
            start_date = (timezone.now().date() - timedelta(days=30)).isoformat()
        if not end_date:
            end_date = timezone.now().date().isoformat()
        
        # Get bookings for the period
        bookings = Booking.objects.filter(
            salon=salon,
            date__range=[start_date, end_date]
        )
        
        # Calculate analytics
        total_bookings = bookings.count()
        completed_bookings = bookings.filter(status='Completed').count()
        cancelled_bookings = bookings.filter(status='Cancelled').count()
        total_revenue = bookings.filter(status='Completed').aggregate(
            total=Sum('service__price')
        )['total'] or 0
        
        # Calculate peak hours
        peak_hours = {}
        for booking in bookings:
            hour = booking.time.split(':')[0]
            peak_hours[hour] = peak_hours.get(hour, 0) + 1
        
        # Calculate popular services
        popular_services = {}
        for booking in bookings:
            service_name = booking.service.name
            popular_services[service_name] = popular_services.get(service_name, 0) + 1
        
        analytics_data = {
            'salon_name': salon.name,
            'period': f"{start_date} to {end_date}",
            'total_bookings': total_bookings,
            'completed_bookings': completed_bookings,
            'cancelled_bookings': cancelled_bookings,
            'completion_rate': (completed_bookings / total_bookings * 100) if total_bookings > 0 else 0,
            'total_revenue': float(total_revenue),
            'average_booking_value': float(total_revenue / completed_bookings) if completed_bookings > 0 else 0,
            'peak_hours': peak_hours,
            'popular_services': popular_services,
        }
        
        return Response(analytics_data)

class CustomerAnalyticsView(APIView):
    def get(self, request, user_id):
        # Get customer analytics
        try:
            customer_analytics = CustomerAnalytics.objects.get(user_id=user_id)
        except CustomerAnalytics.DoesNotExist:
            return Response({'error': 'Customer analytics not found'}, status=404)
        
        return Response(CustomerAnalyticsSerializer(customer_analytics).data)
    
    def post(self, request):
        # Update customer analytics
        user_id = request.data.get('user_id')
        if not user_id:
            return Response({'error': 'User ID required'}, status=400)
        
        # Calculate customer analytics
        bookings = Booking.objects.filter(userId=user_id)
        total_bookings = bookings.count()
        total_spent = bookings.filter(status='Completed').aggregate(
            total=Sum('service__price')
        )['total'] or 0
        
        # Find favorite salon and service
        favorite_salon = bookings.values('salon').annotate(
            count=Count('id')
        ).order_by('-count').first()
        
        favorite_service = bookings.values('service').annotate(
            count=Count('id')
        ).order_by('-count').first()
        
        # Calculate booking frequency (bookings per month)
        if total_bookings > 0:
            first_booking = bookings.order_by('date').first()
            last_booking = bookings.order_by('date').last()
            if first_booking and last_booking:
                months_diff = (last_booking.date.year - first_booking.date.year) * 12 + (last_booking.date.month - first_booking.date.month) + 1
                booking_frequency = total_bookings / months_diff
            else:
                booking_frequency = 0
        else:
            booking_frequency = 0
        
        analytics_data = {
            'user_id': user_id,
            'total_bookings': total_bookings,
            'total_spent': float(total_spent),
            'favorite_salon': favorite_salon['salon'] if favorite_salon else None,
            'favorite_service': favorite_service['service'] if favorite_service else None,
            'last_booking_date': bookings.order_by('-date').first().date if bookings.exists() else None,
            'average_booking_value': float(total_spent / total_bookings) if total_bookings > 0 else 0,
            'booking_frequency': booking_frequency,
        }
        
        customer_analytics, created = CustomerAnalytics.objects.update_or_create(
            user_id=user_id,
            defaults=analytics_data
        )
        
        return Response(CustomerAnalyticsSerializer(customer_analytics).data)

class RevenueAnalyticsView(APIView):
    def get(self, request, salon_id):
        try:
            salon = Salon.objects.get(id=salon_id)
        except Salon.DoesNotExist:
            return Response({'error': 'Salon not found'}, status=404)
        
        period = request.query_params.get('period', 'monthly')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        if not start_date:
            start_date = (timezone.now().date() - timedelta(days=30)).isoformat()
        if not end_date:
            end_date = timezone.now().date().isoformat()
        
        # Get bookings for the period
        bookings = Booking.objects.filter(
            salon=salon,
            date__range=[start_date, end_date],
            status='Completed'
        )
        
        # Calculate revenue metrics
        total_revenue = bookings.aggregate(total=Sum('service__price'))['total'] or 0
        
        # Revenue by service
        service_revenue = {}
        for booking in bookings:
            service_name = booking.service.name
            service_revenue[service_name] = service_revenue.get(service_name, 0) + float(booking.service.price)
        
        # Revenue by staff
        staff_revenue = {}
        for booking in bookings:
            staff_name = booking.staff.name
            staff_revenue[staff_name] = staff_revenue.get(staff_name, 0) + float(booking.service.price)
        
        # Gift vs regular bookings revenue
        gift_revenue = bookings.filter(is_gift=True).aggregate(
            total=Sum('service__price')
        )['total'] or 0
        regular_revenue = total_revenue - gift_revenue
        
        revenue_data = {
            'salon_name': salon.name,
            'period': period,
            'period_start': start_date,
            'period_end': end_date,
            'total_revenue': float(total_revenue),
            'service_revenue': service_revenue,
            'staff_revenue': staff_revenue,
            'gift_bookings_revenue': float(gift_revenue),
            'regular_bookings_revenue': float(regular_revenue),
            'gift_percentage': (gift_revenue / total_revenue * 100) if total_revenue > 0 else 0,
        }
        
        return Response(revenue_data)

class PerformanceMetricsView(APIView):
    def get(self, request, salon_id):
        try:
            salon = Salon.objects.get(id=salon_id)
        except Salon.DoesNotExist:
            return Response({'error': 'Salon not found'}, status=404)
        
        date = request.query_params.get('date', timezone.now().date().isoformat())
        
        # Get bookings for the date
        bookings = Booking.objects.filter(salon=salon, date=date)
        
        # Staff performance
        staff_performance = {}
        for booking in bookings:
            staff_name = booking.staff.name
            if staff_name not in staff_performance:
                staff_performance[staff_name] = {
                    'total_bookings': 0,
                    'completed_bookings': 0,
                    'revenue': 0
                }
            staff_performance[staff_name]['total_bookings'] += 1
            if booking.status == 'Completed':
                staff_performance[staff_name]['completed_bookings'] += 1
                staff_performance[staff_name]['revenue'] += float(booking.service.price)
        
        # Service popularity
        service_popularity = {}
        for booking in bookings:
            service_name = booking.service.name
            service_popularity[service_name] = service_popularity.get(service_name, 0) + 1
        
        # Peak booking hours
        peak_hours = {}
        for booking in bookings:
            hour = booking.time.split(':')[0]
            peak_hours[hour] = peak_hours.get(hour, 0) + 1
        
        # Average booking duration
        total_duration = sum(booking.service.duration for booking in bookings)
        avg_duration = total_duration / len(bookings) if bookings.exists() else 0
        
        performance_data = {
            'salon_name': salon.name,
            'date': date,
            'staff_performance': staff_performance,
            'service_popularity': service_popularity,
            'peak_booking_hours': peak_hours,
            'average_booking_duration': avg_duration,
            'total_bookings': len(bookings),
        }
        
        return Response(performance_data)

class DashboardAnalyticsView(APIView):
    def get(self, request):
        # Get overall platform analytics
        total_salons = Salon.objects.count()
        total_bookings = Booking.objects.count()
        total_revenue = Booking.objects.filter(status='Completed').aggregate(
            total=Sum('service__price')
        )['total'] or 0
        
        # Recent activity
        recent_bookings = Booking.objects.filter(
            date__gte=timezone.now().date() - timedelta(days=7)
        ).count()
        
        # Popular services across all salons
        popular_services = Booking.objects.values('service__name').annotate(
            count=Count('id')
        ).order_by('-count')[:5]
        
        # Top performing salons
        top_salons = Booking.objects.filter(status='Completed').values(
            'salon__name'
        ).annotate(
            revenue=Sum('service__price'),
            bookings=Count('id')
        ).order_by('-revenue')[:5]
        
        dashboard_data = {
            'total_salons': total_salons,
            'total_bookings': total_bookings,
            'total_revenue': float(total_revenue),
            'recent_bookings': recent_bookings,
            'popular_services': list(popular_services),
            'top_salons': list(top_salons),
        }
        
        return Response(dashboard_data)

# Enhanced Location-Based Views
class UserLocationView(APIView):
    def get(self, request):
        user = request.user
        if not user or not user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=401)
        try:
            location = UserLocation.objects.get(user_id=user.username)
            return Response(UserLocationSerializer(location).data)
        except UserLocation.DoesNotExist:
            return Response({'error': 'User location not found'}, status=404)

    def post(self, request):
        user = request.user
        if not user or not user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=401)
        lat = request.data.get('latitude')
        lng = request.data.get('longitude')
        county = request.data.get('county')
        town = request.data.get('town')
        if not lat or not lng:
            return Response({'error': 'Missing required fields'}, status=400)
        location, created = UserLocation.objects.update_or_create(
            user_id=user.username,
            defaults={
                'latitude': float(lat),
                'longitude': float(lng),
                'county': county,
                'town': town
            }
        )
        return Response(UserLocationSerializer(location).data)

    def options(self, request, *args, **kwargs):
        response = super().options(request, *args, **kwargs)
        return response

class NearbySalonsEnhancedView(APIView):
    def post(self, request):
        try:
            user = request.user
            if not user or not user.is_authenticated:
                return Response({'error': 'Authentication required'}, status=401)

            radius_km = float(request.data.get('radius_km', 5))

            # SURGICAL FIX: Dynamic location fetching with caching
            user_location = None
            try:
                user_location = UserLocation.objects.get(user_id=user.username)
                # Check if location is stale (older than 1 hour) or invalid
                from django.utils import timezone
                one_hour_ago = timezone.now() - timezone.timedelta(hours=1)
                if (user_location.last_updated < one_hour_ago or
                    not self._validate_kenya_coordinates(user_location.latitude, user_location.longitude)):
                    print(f"🔄 User location stale or invalid, will refresh dynamically")
                    user_location = None
            except UserLocation.DoesNotExist:
                print(f"🔄 No user location found, will fetch dynamically")
                user_location = None

            # If no valid cached location, try to get fresh location from IP
            if not user_location:
                try:
                    ip_location = self._get_fresh_ip_location(request)
                    if ip_location:
                        user_location, created = UserLocation.objects.update_or_create(
                            user_id=user.username,
                            defaults={
                                'latitude': ip_location['latitude'],
                                'longitude': ip_location['longitude'],
                                'county': ip_location.get('county', 'Nairobi County'),
                                'town': ip_location.get('town', 'Nairobi CBD')
                            }
                        )
                        print(f"✅ {'Created' if created else 'Updated'} user location from IP: {ip_location}")
                except Exception as e:
                    print(f"❌ Failed to get IP location: {e}")

            if not user_location:
                return Response({'error': 'User location not found. Please enable location services.'}, status=404)

            def haversine_accurate(lat1, lon1, lat2, lon2):
                """Enhanced Haversine formula with better precision for Kenya"""
                try:
                    # Validate coordinates are within Kenya bounds
                    if not (self._validate_kenya_coordinates(lat1, lon1) and
                            self._validate_kenya_coordinates(lat2, lon2)):
                        print(f"⚠️ Invalid coordinates: ({lat1}, {lon1}) or ({lat2}, {lon2})")
                        return float('inf')

                    R = 6371.0  # Earth radius in kilometers (more precise)
                    lat1_rad, lon1_rad = math.radians(lat1), math.radians(lon1)
                    lat2_rad, lon2_rad = math.radians(lat2), math.radians(lon2)

                    dlat = lat2_rad - lat1_rad
                    dlon = lon2_rad - lon1_rad

                    # More precise Haversine calculation
                    a = (math.sin(dlat / 2) ** 2 +
                         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2) ** 2)
                    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

                    distance = R * c
                    return round(distance, 3)  # Round to 3 decimal places for accuracy

                except (TypeError, ValueError, AttributeError) as e:
                    print(f"Haversine calculation error: {e}")
                    return float('inf')

            # INTELLIGENT SEARCH: Get ALL salons across Kenya for distance-based search
            try:
                # Try to filter by is_active if the field exists
                salons = Salon.objects.filter(is_active=True).select_related('vendor').prefetch_related('services')
            except Exception:
                # Fallback if is_active field doesn't exist yet (migration not applied)
                salons = Salon.objects.all().select_related('vendor').prefetch_related('services')
            nearby = []

            # PRODUCTION OPTIMIZATION: Filter salons with valid coordinates first
            valid_salons = salons.exclude(latitude__isnull=True).exclude(longitude__isnull=True)

            print(f"🌍 INTELLIGENT SEARCH: Searching {valid_salons.count()} salons across ALL Kenya counties for distance-based results")

            # Check if user location has valid coordinates
            if user_location.latitude is None or user_location.longitude is None:
                return Response({'error': 'Invalid user location coordinates'}, status=400)

            # SURGICAL FIX: Intelligent location validation with auto-correction
            for salon in valid_salons:
                try:
                    # INTELLIGENT VALIDATION: Check if town matches county
                    expected_county = get_county_from_town(salon.town)
                    if expected_county and expected_county != salon.county:
                        print(f"🔍 LOCATION INTELLIGENCE: {salon.name} in '{salon.town}' should be in '{expected_county}', not '{salon.county}'")
                        # Auto-correct for search accuracy
                        salon.county = expected_county
                        print(f"✅ Auto-corrected county for search: {salon.name} → {expected_county}")

                    # Enhanced validation: Check county-specific bounds first, then Kenya bounds
                    county_valid = self._validate_county_coordinates(salon.latitude, salon.longitude, salon.county)
                    kenya_valid = self._validate_kenya_coordinates(salon.latitude, salon.longitude)

                    if not (county_valid or kenya_valid):
                        print(f"⚠️ Invalid salon coordinates for {salon.name} in {salon.county}: ({salon.latitude}, {salon.longitude})")
                        # VENDOR PROTECTION: Still include salon but log for manual review
                        print(f"🛡️ Including salon anyway to protect vendor revenue")

                    # VENDOR PROTECTION: Always calculate distance, even for edge cases

                    dist = haversine_accurate(
                        float(user_location.latitude),
                        float(user_location.longitude),
                        float(salon.latitude),
                        float(salon.longitude)
                    )

                    if dist <= radius_km and dist != float('inf'):
                        s = SalonSerializer(salon).data
                        s['distance_km'] = dist  # Use precise distance (already rounded to 3 decimals)
                        nearby.append(s)

                except (TypeError, ValueError, AttributeError) as e:
                    print(f"Error processing salon {salon.id}: {e}")
                    continue

            return Response({'nearby_salons': sorted(nearby, key=lambda x: x['distance_km'])})

        except Exception as e:
            # PRODUCTION: Better error logging and response
            import traceback
            print(f"NearbySalonsEnhancedView error: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return Response({
                'error': 'Unable to fetch nearby salons',
                'nearby_salons': []  # Return empty list instead of error
            }, status=200)  # Return 200 with empty data instead of 500

    def _validate_kenya_coordinates(self, lat, lon):
        """Validate coordinates are within Kenya bounds using location intelligence"""
        return validate_kenya_coordinates(lat, lon)

    def _validate_county_coordinates(self, lat, lon, county):
        """Intelligent county-specific validation using location intelligence"""
        return validate_coordinates_for_county(lat, lon, county)

    def _get_fresh_ip_location(self, request):
        """Get fresh IP-based location with multiple fallbacks"""
        try:
            import requests
            from django.conf import settings

            # Get client IP
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')

            # Skip localhost IPs
            if ip in ['127.0.0.1', 'localhost', '::1']:
                ip = None

            # Try multiple IP location services
            services = [
                {
                    'name': 'ipinfo',
                    'url': f'https://ipinfo.io/{ip}/json' if ip else 'https://ipinfo.io/json',
                    'token': getattr(settings, 'IPINFO_TOKEN', None)
                },
                {
                    'name': 'geojs',
                    'url': 'https://get.geojs.io/v1/ip/geo.json',
                    'token': None
                }
            ]

            for service in services:
                try:
                    url = service['url']
                    headers = {}
                    if service['token']:
                        headers['Authorization'] = f'Bearer {service["token"]}'

                    response = requests.get(url, headers=headers, timeout=3)
                    if response.status_code == 200:
                        data = response.json()

                        if service['name'] == 'ipinfo':
                            if 'loc' in data:
                                lat, lon = map(float, data['loc'].split(','))
                                return {
                                    'latitude': lat,
                                    'longitude': lon,
                                    'county': data.get('region', 'Nairobi County'),
                                    'town': data.get('city', 'Nairobi CBD'),
                                    'source': 'ipinfo'
                                }
                        elif service['name'] == 'geojs':
                            if 'latitude' in data and 'longitude' in data:
                                return {
                                    'latitude': float(data['latitude']),
                                    'longitude': float(data['longitude']),
                                    'county': data.get('region', 'Nairobi County'),
                                    'town': data.get('city', 'Nairobi CBD'),
                                    'source': 'geojs'
                                }
                except Exception as e:
                    print(f"❌ {service['name']} failed: {e}")
                    continue

            # Fallback to Nairobi center if all services fail
            print("🔄 All IP services failed, using Nairobi center as fallback")
            return {
                'latitude': -1.286389,
                'longitude': 36.817223,
                'county': 'Nairobi County',
                'town': 'Nairobi CBD',
                'source': 'fallback'
            }

        except Exception as e:
            print(f"❌ IP location error: {e}")
            return None

class RegionalTrendingView(APIView):
    def get(self, request):
        county = request.query_params.get('county')
        town = request.query_params.get('town')
        period = request.query_params.get('period', 'weekly')
        
        trending = RegionalTrending.objects.filter(period=period)
        if county:
            trending = trending.filter(county=county)
        if town:
            trending = trending.filter(town=town)
        
        trending = trending.order_by('-score')[:10]
        return Response(RegionalTrendingSerializer(trending, many=True).data)

# Social Features Views
class FriendshipView(APIView):
    def post(self, request):
        from_user_id = request.data.get('from_user_id')
        to_user_id = request.data.get('to_user_id')
        
        if not from_user_id or not to_user_id:
            return Response({'error': 'Missing user IDs'}, status=400)
        
        friendship, created = Friendship.objects.get_or_create(
            from_user_id=from_user_id,
            to_user_id=to_user_id
        )
        
        return Response(FriendshipSerializer(friendship).data)
    
    def get(self, request):
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response({'error': 'User ID required'}, status=400)
        
        friends = Friendship.objects.filter(
            Q(from_user_id=user_id) | Q(to_user_id=user_id)
        )
        return Response(FriendshipSerializer(friends, many=True).data)

class StylePostView(APIView):
    def post(self, request):
        user_id = request.data.get('user_id')
        booking_id = request.data.get('booking_id')
        message = request.data.get('message', '')
        image = request.data.get('image', '')
        
        try:
            booking = Booking.objects.get(id=booking_id, userId=user_id)
        except Booking.DoesNotExist:
            return Response({'error': 'Booking not found'}, status=404)
        
        style_post = StylePost.objects.create(
            user_id=user_id,
            booking=booking,
            message=message,
            image=image
        )
        
        # Update regional trending
        self.update_regional_trending(booking)
        
        return Response(StylePostSerializer(style_post).data)
    
    def get(self, request):
        user_id = request.query_params.get('user_id')
        if user_id:
            posts = StylePost.objects.filter(user_id=user_id).order_by('-created_at')
        else:
            posts = StylePost.objects.all().order_by('-created_at')
        
        return Response(StylePostSerializer(posts, many=True).data)
    
    def update_regional_trending(self, booking):
        salon = booking.salon
        service = booking.service
        
        # Update salon trending
        salon_trending, created = RegionalTrending.objects.get_or_create(
            county=salon.county,
            town=salon.town,
            content_type=ContentType.objects.get_for_model(Salon),
            object_id=salon.id,
            period='weekly',
            defaults={'score': 1}
        )
        if not created:
            salon_trending.score += 1
            salon_trending.save()
        
        # Update service trending
        service_trending, created = RegionalTrending.objects.get_or_create(
            county=salon.county,
            town=salon.town,
            content_type=ContentType.objects.get_for_model(Service),
            object_id=service.id,
            period='weekly',
            defaults={'score': 1}
        )
        if not created:
            service_trending.score += 1
            service_trending.save()

class FriendsActivityView(APIView):
    def post(self, request):
        user_id = request.data.get('user_id')
        days = int(request.data.get('days', 7))
        
        # Get user's friends
        friendships = Friendship.objects.filter(
            Q(from_user_id=user_id) | Q(to_user_id=user_id)
        )
        friend_ids = []
        for friendship in friendships:
            if friendship.from_user_id == user_id:
                friend_ids.append(friendship.to_user_id)
            else:
                friend_ids.append(friendship.from_user_id)
        
        # Get recent bookings by friends
        recent_date = timezone.now().date() - timezone.timedelta(days=days)
        friend_bookings = Booking.objects.filter(
            userId__in=friend_ids,
            date__gte=recent_date
        ).order_by('-date')
        
        # Get style posts by friends
        friend_posts = StylePost.objects.filter(
            user_id__in=friend_ids
        ).order_by('-created_at')
        
        return Response({
            'friend_bookings': BookingSerializer(friend_bookings, many=True).data,
            'friend_posts': StylePostSerializer(friend_posts, many=True).data
        })

# Gift Booking Views
class GiftBookingView(APIView):
    def post(self, request):
        purchaser_id = request.data.get('purchaser_id')
        recipient_id = request.data.get('recipient_id')
        recipient_contact = request.data.get('recipient_contact', '')
        gift_message = request.data.get('gift_message', '')
        booking_data = request.data.get('booking')
        
        # Create booking with gift flag
        booking_data['is_gift'] = True
        booking_data['gift_message'] = gift_message
        booking_data['purchaser_id'] = purchaser_id
        booking_data['userId'] = recipient_id
        booking_data['recipient_contact'] = recipient_contact
        
        serializer = BookingSerializer(data=booking_data)
        if serializer.is_valid():
            booking = serializer.save()
            return Response(BookingSerializer(booking).data)
        return Response(serializer.errors, status=400)

# Existing Views (keeping for compatibility)
class SalonSearchView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        query = request.query_params.get('q', '').strip()
        county = request.query_params.get('county', '').strip()
        town = request.query_params.get('town', '').strip()

        # INTELLIGENT SEARCH: Use select_related for better performance
        salons = Salon.objects.select_related('vendor').prefetch_related('services')

        # INTELLIGENT FILTERING: Apply location intelligence
        if county:
            # SPECIFIC COUNTY SEARCH: Filter by exact county (user choice)
            salons = salons.filter(county__iexact=county)
            print(f"🏛️ COUNTY-SPECIFIC SEARCH: Filtering salons in '{county}'")
        else:
            # INTELLIGENT SEARCH: Apply location corrections for better results
            print(f"🌍 INTELLIGENT SEARCH: Searching ALL salons with location intelligence")

        if town:
            # INTELLIGENT TOWN SEARCH: Auto-correct county if needed
            expected_county = get_county_from_town(town)
            if expected_county and not county:
                # Auto-expand search to correct county
                salons = salons.filter(
                    models.Q(town__iexact=town) |
                    models.Q(county__iexact=expected_county)
                )
                print(f"🔍 INTELLIGENT TOWN SEARCH: '{town}' → Including '{expected_county}' county")
            else:
                salons = salons.filter(town__iexact=town)

        if query:
            search_conditions = (
                models.Q(name__icontains=query) |
                models.Q(address__icontains=query) |
                models.Q(town__icontains=query) |
                models.Q(description__icontains=query) |
                models.Q(services__name__icontains=query)
            )
            salons = salons.filter(search_conditions)

        # PRODUCTION FIX: Increase limit for better user experience, add pagination support
        limit = int(request.query_params.get('limit', 20))  # Default 20, max 50
        limit = min(limit, 50)  # Cap at 50 for performance

        salons = salons.distinct().order_by('name')[:limit]
        return Response(SalonSerializer(salons, many=True).data)

class SalonListCreateView(generics.ListCreateAPIView):
    serializer_class = SalonSerializer

    def get_queryset(self):
        queryset = Salon.objects.all()
        vendor_username = self.request.query_params.get('vendor')
        if vendor_username:
            queryset = queryset.filter(vendor__username=vendor_username)
        return queryset

class SalonRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Salon.objects.all()
    serializer_class = SalonSerializer
    permission_classes = [AllowAny]

class ServiceListCreateView(generics.ListCreateAPIView):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer

class ServiceRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [AllowAny]

class StaffListCreateView(generics.ListCreateAPIView):
    queryset = Staff.objects.all()
    serializer_class = StaffSerializer

class StaffRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Staff.objects.all()
    serializer_class = StaffSerializer

class BookingListCreateView(generics.ListCreateAPIView):
    queryset = Booking.objects.all()
    serializer_class = BookingSerializer
    permission_classes = [AllowAny]

class UserBookingsView(APIView):
    def get(self, request):
        # Get user ID from request (assuming it's passed in headers or query params)
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response({'error': 'User ID required'}, status=400)
        
        # Filter bookings by user ID
        bookings = Booking.objects.filter(userId=user_id).order_by('-date', '-time')
        serializer = BookingSerializer(bookings, many=True)
        return Response(serializer.data)

class BookingRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Booking.objects.all()
    serializer_class = BookingSerializer

class RecommendationListCreateView(generics.ListCreateAPIView):
    queryset = Recommendation.objects.all()
    serializer_class = RecommendationSerializer

    def options(self, request, *args, **kwargs):
        response = super().options(request, *args, **kwargs)
        return response

class RecommendationRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Recommendation.objects.all()
    serializer_class = RecommendationSerializer

class FriendRecommendationListCreateView(generics.ListCreateAPIView):
    queryset = FriendRecommendation.objects.all()
    serializer_class = FriendRecommendationSerializer

class FriendRecommendationRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = FriendRecommendation.objects.all()
    serializer_class = FriendRecommendationSerializer

# Keep existing recommendation views for backward compatibility
class NearbySalonsView(APIView):
    def post(self, request):
        lat = float(request.data.get('lat'))
        lng = float(request.data.get('lng'))
        radius_km = float(request.data.get('radius_km', 5))
        def haversine(lat1, lon1, lat2, lon2):
            R = 6371
            phi1, phi2 = math.radians(lat1), math.radians(lat2)
            dphi = math.radians(lat2 - lat1)
            dlambda = math.radians(lon2 - lon1)
            a = math.sin(dphi/2)**2 + math.cos(phi1)*math.cos(phi2)*math.sin(dlambda/2)**2
            return R * 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        salons = Salon.objects.all()
        nearby = []
        for salon in salons:
            dist = haversine(lat, lng, salon.latitude, salon.longitude)
            if dist <= radius_km:
                s = SalonSerializer(salon).data
                s['distance_km'] = round(dist, 2)
                nearby.append(s)
        nearby = sorted(nearby, key=lambda x: x['distance_km'])
        return Response({'nearby_salons': nearby})

class TrendingRecommendationsView(APIView):
    def get(self, request):
        days = int(request.query_params.get('days', 7))
        now = timezone.now().date()
        recent_bookings = Booking.objects.filter(date__gte=now - timezone.timedelta(days=days))
        trending_salons = (
            recent_bookings.values('salon')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        salon_ids = [item['salon'] for item in trending_salons]
        salons = Salon.objects.filter(id__in=salon_ids)
        salons_serialized = SalonSerializer(salons, many=True).data
        trending_services = (
            recent_bookings.values('service')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        service_ids = [item['service'] for item in trending_services]
        services = Service.objects.filter(id__in=service_ids)
        services_serialized = ServiceSerializer(services, many=True).data
        return Response({
            'trending_salons': salons_serialized,
            'trending_services': services_serialized
        })

class FriendsActivityRecommendationsView(APIView):
    def post(self, request):
        user_id = request.data.get('userId')
        friends_user_ids = request.data.get('friends_user_ids', [])
        days = int(request.data.get('days', 7))
        now = timezone.now().date()
        friend_bookings = Booking.objects.filter(userId__in=friends_user_ids, date__gte=now - timezone.timedelta(days=days))
        friend_salons = (
            friend_bookings.values('salon')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        friend_services = (
            friend_bookings.values('service')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        salon_ids = [item['salon'] for item in friend_salons]
        service_ids = [item['service'] for item in friend_services]
        salons = Salon.objects.filter(id__in=salon_ids)
        services = Service.objects.filter(id__in=service_ids)
        return Response({
            'friends_trending_salons': SalonSerializer(salons, many=True).data,
            'friends_trending_services': ServiceSerializer(services, many=True).data
        })

class ByLocationRecommendationsView(APIView):
    def post(self, request):
        ip = request.data.get('ip')
        county = request.data.get('county')
        town = request.data.get('town')
        salons = Salon.objects.all()
        location_info = None

        if ip and not (county or town):
            location_info = get_geolocation_from_ip(ip)
            county = location_info.get('county') if location_info else None
            town = location_info.get('town') if location_info else None

        # INTELLIGENT LOCATION FILTERING
        if town:
            # INTELLIGENT TOWN SEARCH: Auto-correct county if needed
            expected_county = get_county_from_town(town)
            if expected_county:
                print(f"🔍 INTELLIGENT RECOMMENDATION: '{town}' → Including '{expected_county}' county")
                salons = salons.filter(
                    models.Q(town__iexact=town) |
                    models.Q(county__iexact=expected_county)
                )
                # Update county for response accuracy
                county = expected_county
            else:
                salons = salons.filter(town__iexact=town)
        elif county:
            salons = salons.filter(county__iexact=county)

        return Response({
            'location': {
                'ip': ip,
                'county': county,
                'town': town,
                'resolved': location_info
            },
            'salons': SalonSerializer(salons, many=True).data
        })

class PersonalizedRecommendationsView(APIView):
    def post(self, request):
        user_id = request.data.get('userId')
        lat = request.data.get('lat')
        lng = request.data.get('lng')
        friends_user_ids = request.data.get('friends_user_ids', [])
        radius_km = float(request.data.get('radius_km', 5))
        days = int(request.data.get('days', 7))
        now = timezone.now().date()
        results = {}
        if lat and lng:
            lat = float(lat)
            lng = float(lng)
            def haversine(lat1, lon1, lat2, lon2):
                R = 6371
                phi1, phi2 = math.radians(lat1), math.radians(lat2)
                dphi = math.radians(lat2 - lat1)
                dlambda = math.radians(lon2 - lon1)
                a = math.sin(dphi/2)**2 + math.cos(phi1)*math.cos(phi2)*math.sin(dlambda/2)**2
                return R * 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
            salons = Salon.objects.all()
            nearby = []
            for salon in salons:
                dist = haversine(lat, lng, salon.latitude, salon.longitude)
                if dist <= radius_km:
                    s = SalonSerializer(salon).data
                    s['distance_km'] = round(dist, 2)
                    nearby.append(s)
            results['nearby_salons'] = sorted(nearby, key=lambda x: x['distance_km'])
        recent_bookings = Booking.objects.filter(date__gte=now - timezone.timedelta(days=days))
        trending_salons = (
            recent_bookings.values('salon')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        salon_ids = [item['salon'] for item in trending_salons]
        salons = Salon.objects.filter(id__in=salon_ids)
        results['trending_salons'] = SalonSerializer(salons, many=True).data
        trending_services = (
            recent_bookings.values('service')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        service_ids = [item['service'] for item in trending_services]
        services = Service.objects.filter(id__in=service_ids)
        results['trending_services'] = ServiceSerializer(services, many=True).data
        if friends_user_ids:
            friend_bookings = Booking.objects.filter(userId__in=friends_user_ids, date__gte=now - timezone.timedelta(days=days))
            friend_salons = (
                friend_bookings.values('salon')
                .annotate(num_bookings=Count('id'))
                .order_by('-num_bookings')[:5]
            )
            friend_services = (
                friend_bookings.values('service')
                .annotate(num_bookings=Count('id'))
                .order_by('-num_bookings')[:5]
            )
            salon_ids = [item['salon'] for item in friend_salons]
            service_ids = [item['service'] for item in friend_services]
            salons = Salon.objects.filter(id__in=salon_ids)
            services = Service.objects.filter(id__in=service_ids)
            results['friends_trending_salons'] = SalonSerializer(salons, many=True).data
            results['friends_trending_services'] = ServiceSerializer(services, many=True).data
        all_bookings = Booking.objects.all()
        popular_salons = (
            all_bookings.values('salon')
            .annotate(num_bookings=Count('id'))
            .order_by('-num_bookings')[:5]
        )
        salon_ids = [item['salon'] for item in popular_salons]
        salons = Salon.objects.filter(id__in=salon_ids)
        results['most_popular_salons'] = SalonSerializer(salons, many=True).data
        cheapest_services = Service.objects.order_by('price')[:5]
        results['cheapest_services'] = ServiceSerializer(cheapest_services, many=True).data
        return Response(results)

# Keep existing recommendation generation views
class GeneratePopularVendorsView(APIView):
    def post(self, request):
        Recommendation.objects.filter(type='popular').delete()
        popular_salons = (
            Salon.objects.annotate(num_bookings=Count('bookings'))
            .order_by('-num_bookings')[:5]
        )
        content_type = ContentType.objects.get_for_model(Salon)
        for salon in popular_salons:
            Recommendation.objects.create(
                type='popular',
                content_type=content_type,
                object_id=salon.id,
                score=salon.num_bookings,
                reason=f"Popular vendor with {salon.num_bookings} bookings"
            )
        return Response({'status': 'Popular vendor recommendations generated.'}, status=status.HTTP_201_CREATED)

class GenerateCheapestServicesView(APIView):
    def post(self, request):
        Recommendation.objects.filter(type='cheap').delete()
        cheapest_services = Service.objects.order_by('price')[:5]
        content_type = ContentType.objects.get_for_model(Service)
        for service in cheapest_services:
            Recommendation.objects.create(
                type='cheap',
                content_type=content_type,
                object_id=service.id,
                score=float(service.price),
                reason=f"Cheapest service at {service.salon.name} for {service.price}"
            )
        return Response({'status': 'Cheapest service recommendations generated.'}, status=status.HTTP_201_CREATED)

# Keep existing salon registration views
class SalonRegisterView(APIView):
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request):
        serializer = SalonSerializer(data=request.data)
        if serializer.is_valid():
            salon = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SalonProfileUpdateView(APIView):
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    
    def put(self, request, pk):
        try:
            salon = Salon.objects.get(pk=pk)
        except Salon.DoesNotExist:
            return Response({'error': 'Salon not found'}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = SalonSerializer(salon, data=request.data, partial=True)
        if serializer.is_valid():
            salon = serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class VendorBookingsView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        vendor = request.user
        salons = Salon.objects.filter(vendor=vendor)
        bookings = Booking.objects.filter(salon__in=salons)
        serializer = BookingSerializer(bookings, many=True)
        return Response(serializer.data)

class VendorAccountUpdateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        return Response({
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
        })

    def put(self, request):
        user = request.user
        data = request.data
        user.email = data.get('email', user.email)
        user.first_name = data.get('first_name', user.first_name)
        user.last_name = data.get('last_name', user.last_name)
        user.save()
        return Response({
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
        })

class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        print("UserProfileView called!")
        print(f"User: {request.user}")
        print(f"User username: {request.user.username}")
        print(f"User is_superuser: {request.user.is_superuser}")
        print(f"User is_staff: {request.user.is_staff}")
        
        user = request.user
        # Check if user is a vendor
        is_vendor = Salon.objects.filter(vendor=user).exists()
        print(f"User is_vendor: {is_vendor}")
        
        user_data = {
            'id': user.id,
            'userId': user.id,  # For compatibility with frontend
            'username': user.username,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'is_staff': user.is_staff,
            'is_vendor': is_vendor,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'date_joined': user.date_joined
        }
        
        print(f"Returning user_data: {user_data}")
        return Response(user_data)

    def put(self, request):
        user = request.user
        data = request.data
        user.email = data.get('email', user.email)
        user.first_name = data.get('first_name', user.first_name)
        user.last_name = data.get('last_name', user.last_name)
        user.save()
        is_vendor = Salon.objects.filter(vendor=user).exists()
        user_data = {
            'id': user.id,
            'userId': user.id,  # For compatibility with frontend
            'username': user.username,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'is_staff': user.is_staff,
            'is_vendor': is_vendor,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'date_joined': user.date_joined
        }
        return Response(user_data)

class UserListCreateView(generics.ListCreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer

class UserRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer

# --- New endpoint for counties with search ---
class CountiesView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        search = request.query_params.get('search', '').strip()
        counties = Salon.objects.values_list('county', flat=True).distinct()
        counties = sorted(set([c for c in counties if c]))
        
        if search:
            counties = [c for c in counties if search.lower() in c.lower()]
        
        # Convert to list of objects with id and name, limit to 5
        county_objects = [{'id': i+1, 'name': county} for i, county in enumerate(counties[:5])]
        return Response(county_objects)

# --- New endpoint for towns by county with search ---
class TownsByCountyView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        county = request.query_params.get('county', '').strip()
        search = request.query_params.get('search', '').strip()
        
        if not county:
            return Response({'error': 'county parameter required'}, status=400)
        
        # Try to find county by ID first, then by name
        try:
            county_id = int(county)
            # Get county name by ID (assuming counties are indexed by ID)
            counties = Salon.objects.values_list('county', flat=True).distinct()
            counties = sorted(set([c for c in counties if c]))
            if 1 <= county_id <= len(counties):
                county_name = counties[county_id - 1]  # Convert 1-based ID to 0-based index
            else:
                county_name = county  # Fallback to treating as name
        except ValueError:
            county_name = county  # Treat as name if not a number
        
        towns = Salon.objects.filter(county__iexact=county_name).values_list('town', flat=True).distinct()
        towns = sorted(set([t for t in towns if t]))
        
        if search:
            towns = [t for t in towns if search.lower() in t.lower()]
        
        # Convert to list of objects with id and name, limit to 5
        town_objects = [{'id': i+1, 'name': town} for i, town in enumerate(towns[:5])]
        return Response(town_objects)

# --- New endpoint for services with search ---
class ServicesSearchView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        salon = request.query_params.get('salon', '').strip()
        search = request.query_params.get('search', '').strip()
        
        services = Service.objects.all()
        
        if salon:
            services = services.filter(salon_id=salon)
        
        if search:
            services = services.filter(name__icontains=search)

        # Order alphabetically and limit to 5 results
        services = services.order_by('name')[:5]
        return Response(ServiceSerializer(services, many=True).data)

# --- New endpoint for staff with search ---
class StaffSearchView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        salon = request.query_params.get('salon', '').strip()
        service = request.query_params.get('service', '').strip()
        search = request.query_params.get('search', '').strip()
        
        staff = Staff.objects.all()
        
        if salon:
            staff = staff.filter(salon_id=salon)
        
        if service:
            # Try to find staff by service, but if none found, return all staff for salon
            service_staff = staff.filter(services__id=service)
            if service_staff.exists():
                staff = service_staff
            # If no staff found for specific service, keep all staff for salon
        
        if search:
            staff = staff.filter(name__icontains=search)

        # Order alphabetically and limit to 5 results
        staff = staff.distinct().order_by('name')[:5]
        return Response(StaffSerializer(staff, many=True).data)

class AvailableSlotsView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        salon = request.query_params.get('salon', '').strip()
        date = request.query_params.get('date', '').strip()
        
        if not salon or not date:
            return Response({'error': 'salon and date parameters required'}, status=400)
        
        # Generate available time slots (9 AM to 6 PM)
        slots = []
        for hour in range(9, 19):  # 9 AM to 6 PM
            time_slot = f"{hour:02d}:00"
            slots.append({
                'time': time_slot,
                'available': True
            })
        
        return Response(slots)

# Service search endpoint
@api_view(['GET'])
@permission_classes([AllowAny])
def service_search(request):
    salon_id = request.GET.get('salon')
    query = request.GET.get('q', '').strip()
    services = Service.objects.all()
    if salon_id:
        services = services.filter(salon_id=salon_id)
    if query:
        services = services.filter(name__icontains=query)
    return Response(ServiceSerializer(services, many=True).data)

# Staff search endpoint
@api_view(['GET'])
@permission_classes([AllowAny])
def staff_search(request):
    salon_id = request.GET.get('salon')
    service_id = request.GET.get('service')
    query = request.GET.get('q', '').strip()
    staff = Staff.objects.all()
    if salon_id:
        staff = staff.filter(salon_id=salon_id)
    if service_id:
        staff = staff.filter(services__id=service_id)
    if query:
        staff = staff.filter(name__icontains=query)
    staff = staff.distinct()
    return Response(StaffSerializer(staff, many=True).data)

class VendorCustomersView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        vendor = request.user
        salons = Salon.objects.filter(vendor=vendor)
        bookings = Booking.objects.filter(salon__in=salons)
        # Group by user (registered) and by userName/userId (guests)
        customers = {}
        for b in bookings:
            key = b.user.id if b.user else b.userId
            if key not in customers:
                customers[key] = {
                    'user_id': b.user.id if b.user else None,
                    'userName': b.userName,
                    'userId': b.userId,
                    'email': b.user.email if b.user else '',
                    'booking_count': 0,
                    'last_booking_date': b.date,
                }
            customers[key]['booking_count'] += 1
            if b.date > customers[key]['last_booking_date']:
                customers[key]['last_booking_date'] = b.date
        return Response(list(customers.values()))

class CustomerListCreateView(generics.ListCreateAPIView):
    serializer_class = CustomerSerializer
    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return Customer.objects.all()
        # Vendor: only their salon's customers
        salons = Salon.objects.filter(vendor=user)
        return Customer.objects.filter(salon__in=salons)
    def perform_create(self, serializer):
        user = self.request.user
        if user.is_superuser:
            serializer.save()
        else:
            # Vendor: can only create for their own salons
            salons = Salon.objects.filter(vendor=user)
            if 'salon' in self.request.data and int(self.request.data['salon']) in salons.values_list('id', flat=True):
                serializer.save()
            else:
                raise PermissionDenied('You can only add customers to your own salons.')

class CustomerRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = CustomerSerializer
    queryset = Customer.objects.all()
    def get_queryset(self):
        user = self.request.user
        if user.is_superuser:
            return Customer.objects.all()
        salons = Salon.objects.filter(vendor=user)
        return Customer.objects.filter(salon__in=salons)

class VendorCustomerBookingsView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, customer_id):
        vendor = request.user
        # Get all salons for this vendor
        salons = Salon.objects.filter(vendor=vendor)
        # Get bookings for this customer at these salons
        bookings = Booking.objects.filter(salon__in=salons, user_id=customer_id)
        return Response(BookingSerializer(bookings, many=True).data)

class FollowView(generics.ListCreateAPIView):
    queryset = Follow.objects.all()
    serializer_class = FollowSerializer
    permission_classes = [IsAuthenticated]

class LikeView(generics.ListCreateAPIView):
    queryset = Like.objects.all()
    serializer_class = LikeSerializer
    permission_classes = [IsAuthenticated]

class CommentView(generics.ListCreateAPIView):
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer
    permission_classes = [IsAuthenticated]

class ReviewView(generics.ListCreateAPIView):
    queryset = Review.objects.all()
    serializer_class = ReviewSerializer

# Payment Views
class PaymentProcessView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        """Process a payment for a booking"""
        try:
            booking_id = request.data.get('booking_id')
            payment_method = request.data.get('payment_method')
            amount = request.data.get('amount')
            phone_number = request.data.get('phone_number')
            
            if not all([booking_id, payment_method, amount]):
                return Response({
                    'success': False,
                    'message': 'Missing required fields: booking_id, payment_method, amount'
                }, status=400)
            
            # Get booking
            try:
                booking = Booking.objects.get(id=booking_id)
            except Booking.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Booking not found'
                }, status=404)
            
            # Generate transaction ID
            transaction_id = f"{payment_method.upper()}_{int(timezone.now().timestamp())}_{booking_id}"
            
            # Create payment record
            payment = Payment.objects.create(
                booking=booking,
                transaction_id=transaction_id,
                payment_method=payment_method,
                amount=amount,
                status='processing'
            )
            
            # Update booking status
            booking.status = 'processing'
            booking.payment_status = 'processing'
            booking.transaction_id = transaction_id
            booking.payment_method = payment_method
            booking.save()
            
            # Simulate payment processing (replace with actual gateway integration)
            import random
            import time
            time.sleep(2)  # Simulate processing time
            
            # Simulate success/failure (95% success rate for demo)
            is_success = random.random() > 0.05
            
            if is_success:
                payment.status = 'completed'
                payment.payment_date = timezone.now()
                payment.gateway_response = {
                    'status': 'success',
                    'transaction_id': transaction_id,
                    'processed_at': timezone.now().isoformat()
                }
                payment.save()
                
                booking.status = 'confirmed'
                booking.payment_status = 'completed'
                booking.payment_date = timezone.now()
                booking.save()
                
                return Response({
                    'success': True,
                    'transaction_id': transaction_id,
                    'message': f'{payment_method.title()} payment completed successfully',
                    'redirect_url': '/checkout-success'
                })
            else:
                payment.status = 'failed'
                payment.error_message = f'{payment_method.title()} payment failed'
                payment.save()
                
                booking.status = 'failed'
                booking.payment_status = 'failed'
                booking.save()
                
                return Response({
                    'success': False,
                    'message': f'{payment_method.title()} payment failed. Please try again.',
                    'redirect_url': '/payment-failed'
                })
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Payment processing failed: {str(e)}'
            }, status=500)

class PaymentStatusView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request, transaction_id):
        """Get payment status by transaction ID"""
        try:
            payment = Payment.objects.get(transaction_id=transaction_id)
            return Response({
                'transaction_id': payment.transaction_id,
                'status': payment.status,
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'created_at': payment.created_at,
                'payment_date': payment.payment_date
            })
        except Payment.DoesNotExist:
            return Response({
                'error': 'Payment not found'
            }, status=404)

class PaymentCancelView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request, transaction_id):
        """Cancel a payment"""
        try:
            payment = Payment.objects.get(transaction_id=transaction_id)
            
            if payment.status in ['completed', 'cancelled']:
                return Response({
                    'success': False,
                    'message': f'Payment cannot be cancelled. Current status: {payment.status}'
                }, status=400)
            
            payment.status = 'cancelled'
            payment.save()
            
            # Update booking status
            booking = payment.booking
            booking.status = 'cancelled'
            booking.payment_status = 'cancelled'
            booking.save()
            
            return Response({
                'success': True,
                'message': 'Payment cancelled successfully'
            })
            
        except Payment.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Payment not found'
            }, status=404)

class BookingStatusUpdateView(APIView):
    permission_classes = [AllowAny]
    
    def patch(self, request, booking_id):
        """Update booking status"""
        try:
            booking = Booking.objects.get(id=booking_id)
            new_status = request.data.get('status')
            
            if new_status not in dict(Booking.status_choices):
                return Response({
                    'error': f'Invalid status: {new_status}'
                }, status=400)
            
            booking.status = new_status
            booking.save()
            
            return Response({
                'success': True,
                'message': f'Booking status updated to {new_status}',
                'booking_id': booking.id,
                'status': booking.status
            })
            
        except Booking.DoesNotExist:
            return Response({
                'error': 'Booking not found'
            }, status=404)

class NotificationView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        """Send confirmation email or gift notification"""
        try:
            # Check if this is a gift notification or regular confirmation
            notification_type = request.data.get('type', 'confirmation')

            if notification_type == 'gift':
                return self._send_gift_notification(request)
            else:
                return self._send_confirmation_email(request)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to send notification: {str(e)}'
            }, status=500)

    def _send_gift_notification(self, request):
        """Send gift booking notification email"""
        try:
            # Extract gift notification data
            recipient_email = request.data.get('recipientEmail')
            recipient_name = request.data.get('recipientName')
            gift_message = request.data.get('giftMessage')
            sender_name = request.data.get('senderName')
            salon_details = request.data.get('salonDetails', {})
            booking_details = request.data.get('bookingDetails', {})

            if not all([recipient_email, recipient_name, gift_message, sender_name]):
                return Response({
                    'success': False,
                    'message': 'Missing required fields for gift notification'
                }, status=400)

            # Use EmailService if available
            if EmailService:
                result = EmailService.send_gift_notification(
                    recipient_email=recipient_email,
                    recipient_name=recipient_name,
                    gift_message=gift_message,
                    salon_details=salon_details,
                    booking_details=booking_details,
                    sender_name=sender_name
                )

                if result['success']:
                    return Response({
                        'success': True,
                        'message': '🎁 Gift notification email sent successfully!',
                        'details': result
                    })
                else:
                    return Response({
                        'success': False,
                        'message': f'Failed to send gift notification: {result["message"]}'
                    }, status=500)
            else:
                # Fallback: just log the attempt
                print(f"📧 Gift notification email would be sent to {recipient_email}")
                print(f"🎁 Message: {gift_message}")
                print(f"👤 From: {sender_name}")

                return Response({
                    'success': True,
                    'message': 'Gift notification logged (EmailService not available)'
                })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error sending gift notification: {str(e)}'
            }, status=500)

    def _send_confirmation_email(self, request):
        """Send regular booking confirmation email"""
        try:
            booking_id = request.data.get('bookingId')
            customer_email = request.data.get('customerEmail')
            customer_name = request.data.get('customerName')
            booking_details = request.data.get('bookingDetails', {})

            if not all([booking_id, customer_email, customer_name]):
                return Response({
                    'success': False,
                    'message': 'Missing required fields'
                }, status=400)

            # Use EmailService if available
            if EmailService:
                result = EmailService.send_booking_confirmation(
                    customer_email=customer_email,
                    customer_name=customer_name,
                    booking_details=booking_details
                )

                if result['success']:
                    return Response({
                        'success': True,
                        'message': '✅ Confirmation email sent successfully!',
                        'details': result
                    })
                else:
                    return Response({
                        'success': False,
                        'message': f'Failed to send confirmation: {result["message"]}'
                    }, status=500)
            else:
                # Fallback: just log the attempt
                print(f"📧 Confirmation email would be sent to {customer_email} for booking {booking_id}")

                return Response({
                    'success': True,
                    'message': 'Confirmation email logged (EmailService not available)'
                })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to send email: {str(e)}'
            }, status=500)

class PaystackInitializeView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        try:
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Prepare the request data
            payload = {
                'email': request.data.get('email'),
                'amount': request.data.get('amount'),
                'currency': request.data.get('currency', 'KES'),
                'callback_url': request.data.get('callback_url'),
                'metadata': request.data.get('metadata', {})
            }
            
            # Add reference if provided
            if request.data.get('reference'):
                payload['reference'] = request.data.get('reference')
            
            # Add channels if provided
            if request.data.get('channels'):
                payload['channels'] = request.data.get('channels')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                'https://api.paystack.co/transaction/initialize',
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Transaction initialized successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to initialize transaction')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error initializing transaction: {str(e)}'
            }, status=500)

class PaystackVerifyView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request, reference):
        try:
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f'https://api.paystack.co/transaction/verify/{reference}',
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Transaction verified successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to verify transaction')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error verifying transaction: {str(e)}'
            }, status=500)

class PaystackBanksView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                'https://api.paystack.co/bank',
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Banks retrieved successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to retrieve banks')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error retrieving banks: {str(e)}'
            }, status=500)

class PaystackResolveAccountView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            account_number = request.query_params.get('account_number')
            bank_code = request.query_params.get('bank_code')
            
            if not account_number or not bank_code:
                return Response({
                    'success': False,
                    'message': 'account_number and bank_code are required'
                }, status=400)
            
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f'https://api.paystack.co/bank/resolve?account_number={account_number}&bank_code={bank_code}',
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Account resolved successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to resolve account')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error resolving account: {str(e)}'
            }, status=500)

class PaystackTransferRecipientView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        try:
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                'https://api.paystack.co/transferrecipient',
                headers=headers,
                json=request.data
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Transfer recipient created successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to create transfer recipient')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error creating transfer recipient: {str(e)}'
            }, status=500)

class PaystackTransferView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        try:
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                'https://api.paystack.co/transfer',
                headers=headers,
                json=request.data
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Transfer initiated successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to initiate transfer')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error initiating transfer: {str(e)}'
            }, status=500)

class PaystackChargeView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        try:
            # Get Paystack secret key from environment
            secret_key = os.getenv('REACT_APP_PAYSTACK_SECRET_KEY', 'sk_test_e489adf2603650e51a685c403fd92bb249323374')
            
            # Make request to Paystack API
            headers = {
                'Authorization': f'Bearer {secret_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                'https://api.paystack.co/charge',
                headers=headers,
                json=request.data
            )
            
            if response.status_code == 200:
                result = response.json()
                return Response({
                    'success': True,
                    'data': result['data'],
                    'message': 'Charge processed successfully'
                })
            else:
                error_data = response.json()
                return Response({
                    'success': False,
                    'message': error_data.get('message', 'Failed to process charge')
                }, status=400)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error processing charge: {str(e)}'
            }, status=500)
