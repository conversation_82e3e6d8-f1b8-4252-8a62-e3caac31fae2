"""
Email Service for SalonGenz
Handles all email notifications including gift bookings
"""

from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending various types of emails"""
    
    @staticmethod
    def send_gift_notification(recipient_email, recipient_name, gift_message, salon_details, booking_details, sender_name):
        """
        Send gift booking notification email to recipient
        
        Args:
            recipient_email (str): Recipient's email address
            recipient_name (str): Recipient's name
            gift_message (str): Personalized gift message from AI
            salon_details (dict): Salon information
            booking_details (dict): Booking information
            sender_name (str): Name of the person sending the gift
        """
        try:
            # Extract first name only
            recipient_first_name = recipient_name.split(' ')[0] if recipient_name else 'Friend'
            sender_first_name = sender_name.split(' ')[0] if sender_name else 'Someone special'
            
            subject = f"🎁 {sender_first_name} sent you a gift booking at {salon_details.get('name', 'a salon')}!"
            
            # Email context
            context = {
                'recipient_name': recipient_first_name,
                'sender_name': sender_first_name,
                'gift_message': gift_message,
                'salon_name': salon_details.get('name', 'Salon'),
                'salon_location': salon_details.get('location', 'Location'),
                'service_name': booking_details.get('service', 'Beauty Service'),
                'booking_date': booking_details.get('date', 'TBD'),
                'booking_time': booking_details.get('time', 'TBD'),
                'booking_id': booking_details.get('id', 'N/A')
            }
            
            # Create HTML email content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Gift Booking from {sender_first_name}</title>
                <style>
                    body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }}
                    .container {{ max-width: 600px; margin: 0 auto; background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }}
                    .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }}
                    .content {{ padding: 30px; }}
                    .gift-message {{ background: #f8f9ff; border-left: 4px solid #667eea; padding: 20px; margin: 20px 0; border-radius: 8px; font-style: italic; }}
                    .booking-details {{ background: #fff5f5; border: 1px solid #fed7d7; border-radius: 8px; padding: 20px; margin: 20px 0; }}
                    .cta-button {{ display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }}
                    .footer {{ background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎁 You've Received a Gift!</h1>
                        <p>From {sender_first_name} with love</p>
                    </div>
                    
                    <div class="content">
                        <h2>Hi {recipient_first_name}! 👋</h2>
                        
                        <div class="gift-message">
                            <h3>💌 Personal Message:</h3>
                            <p>{gift_message}</p>
                        </div>
                        
                        <div class="booking-details">
                            <h3>🏪 Your Booking Details:</h3>
                            <p><strong>Salon:</strong> {salon_details.get('name', 'Salon')} 📍 {salon_details.get('location', 'Location')}</p>
                            <p><strong>Service:</strong> {booking_details.get('service', 'Beauty Service')} ✨</p>
                            <p><strong>Date:</strong> {booking_details.get('date', 'TBD')} 📅</p>
                            <p><strong>Time:</strong> {booking_details.get('time', 'TBD')} ⏰</p>
                            <p><strong>Booking ID:</strong> {booking_details.get('id', 'N/A')}</p>
                        </div>
                        
                        <p>🌟 <strong>What's Next?</strong></p>
                        <ul>
                            <li>📞 Call the salon to confirm your appointment</li>
                            <li>💅 Get ready to be pampered!</li>
                            <li>📸 Don't forget to share your amazing new look</li>
                        </ul>
                        
                        <a href="https://salongenz.com/bookings" class="cta-button">View My Bookings 📱</a>
                    </div>
                    
                    <div class="footer">
                        <p>💜 Sent with love from SalonGenz</p>
                        <p>Making beauty accessible, one gift at a time ✨</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Plain text version
            plain_text = f"""
🎁 You've Received a Gift from {sender_first_name}!

Hi {recipient_first_name}! 👋

💌 Personal Message:
{gift_message}

🏪 Your Booking Details:
Salon: {salon_details.get('name', 'Salon')} 📍 {salon_details.get('location', 'Location')}
Service: {booking_details.get('service', 'Beauty Service')} ✨
Date: {booking_details.get('date', 'TBD')} 📅
Time: {booking_details.get('time', 'TBD')} ⏰
Booking ID: {booking_details.get('id', 'N/A')}

🌟 What's Next?
📞 Call the salon to confirm your appointment
💅 Get ready to be pampered!
📸 Don't forget to share your amazing new look

💜 Sent with love from SalonGenz
Making beauty accessible, one gift at a time ✨
            """
            
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=plain_text,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[recipient_email]
            )
            email.attach_alternative(html_content, "text/html")
            
            # Send email
            result = email.send()
            
            if result:
                logger.info(f"✅ Gift notification email sent successfully to {recipient_email}")
                return {
                    'success': True,
                    'message': 'Gift notification email sent successfully',
                    'recipient': recipient_email
                }
            else:
                logger.error(f"❌ Failed to send gift notification email to {recipient_email}")
                return {
                    'success': False,
                    'message': 'Failed to send email',
                    'recipient': recipient_email
                }
                
        except Exception as e:
            logger.error(f"❌ Error sending gift notification email: {str(e)}")
            return {
                'success': False,
                'message': f'Email sending error: {str(e)}',
                'recipient': recipient_email
            }
    
    @staticmethod
    def send_booking_confirmation(customer_email, customer_name, booking_details):
        """Send booking confirmation email"""
        try:
            customer_first_name = customer_name.split(' ')[0] if customer_name else 'Customer'
            
            subject = f"✅ Booking Confirmed - {booking_details.get('salon_name', 'SalonGenz')}"
            
            html_content = f"""
            <h2>Hi {customer_first_name}! 👋</h2>
            <p>Your booking has been confirmed! ✅</p>
            
            <div style="background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>📅 Booking Details:</h3>
                <p><strong>Salon:</strong> {booking_details.get('salon_name', 'Salon')} 🏪</p>
                <p><strong>Service:</strong> {booking_details.get('service_name', 'Service')} ✨</p>
                <p><strong>Date:</strong> {booking_details.get('date', 'TBD')} 📅</p>
                <p><strong>Time:</strong> {booking_details.get('time', 'TBD')} ⏰</p>
            </div>
            
            <p>See you soon! 💜</p>
            """
            
            plain_text = f"""
Hi {customer_first_name}! 👋

Your booking has been confirmed! ✅

📅 Booking Details:
Salon: {booking_details.get('salon_name', 'Salon')} 🏪
Service: {booking_details.get('service_name', 'Service')} ✨
Date: {booking_details.get('date', 'TBD')} 📅
Time: {booking_details.get('time', 'TBD')} ⏰

See you soon! 💜
            """
            
            email = EmailMultiAlternatives(
                subject=subject,
                body=plain_text,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[customer_email]
            )
            email.attach_alternative(html_content, "text/html")
            
            result = email.send()
            
            if result:
                logger.info(f"✅ Booking confirmation email sent to {customer_email}")
                return {'success': True, 'message': 'Confirmation email sent'}
            else:
                logger.error(f"❌ Failed to send confirmation email to {customer_email}")
                return {'success': False, 'message': 'Failed to send email'}
                
        except Exception as e:
            logger.error(f"❌ Error sending confirmation email: {str(e)}")
            return {'success': False, 'message': f'Error: {str(e)}'}
