#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

from salons_app.models import Booking

def verify_bookings():
    print("=== VERIFYING BOOKINGS LOGIC ===")
    
    # Get all bookings
    all_bookings = Booking.objects.all()
    print(f"Total bookings in database: {all_bookings.count()}")
    
    # Group by user
    users = {}
    for booking in all_bookings:
        user_name = booking.userName
        if user_name not in users:
            users[user_name] = []
        users[user_name].append(booking)
    
    print(f"\nBookings grouped by user:")
    for user_name, bookings in users.items():
        print(f"\nUser: {user_name} ({len(bookings)} bookings)")
        for booking in bookings:
            print(f"  - {booking.salon.name} | {booking.service.name} | {booking.date} | {booking.status}")
    
    # Check KINYANJUI specifically
    kinyanjui_bookings = Booking.objects.filter(userName__icontains='KINYANJUI')
    print(f"\n=== KINYANJUI BOOKINGS ===")
    print(f"Found {kinyanjui_bookings.count()} bookings for KINYANJUI")
    for booking in kinyanjui_bookings:
        print(f"  - {booking.salon.name} | {booking.service.name} | {booking.date} | {booking.status}")
    
    print("\n✅ Bookings logic verification complete!")

if __name__ == '__main__':
    verify_bookings() 