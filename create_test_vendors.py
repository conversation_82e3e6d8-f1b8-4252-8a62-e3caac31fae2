#!/usr/bin/env python3
"""
Quick script to create test vendors without payment
Run this from the backend directory: python ../create_test_vendors.py
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salon_project.settings')
django.setup()

from django.contrib.auth.models import User
from salons_app.models import Salon

def create_test_vendor(username, salon_name, email=None, phone="+254700000000"):
    """Create a test vendor with salon"""
    
    # Create user
    if email is None:
        email = f"{username}@testvendor.com"
    
    user, created = User.objects.get_or_create(
        username=username,
        defaults={
            'email': email,
            'first_name': username.capitalize(),
            'last_name': 'Vendor',
            'is_staff': False,
            'is_superuser': False
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created user: {username}")
    else:
        print(f"ℹ️  User {username} already exists")
    
    # Create salon
    salon, created = Salon.objects.get_or_create(
        vendor=user,
        defaults={
            'name': salon_name,
            'address': '123 Test Street',
            'county': 'Nairobi',
            'town': 'Nairobi',
            'phone': phone,
            'email': email,
            'latitude': -1.286389,
            'longitude': 36.817223,
            'description': f'Test salon for {salon_name}. Professional beauty services.',
            'imageUrl': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=500'
        }
    )
    
    if created:
        print(f"✅ Created salon: {salon_name}")
    else:
        print(f"ℹ️  Salon {salon_name} already exists")
    
    return user, salon

def main():
    """Create multiple test vendors"""
    print("🚀 Creating Test Vendors")
    print("=" * 40)
    
    test_vendors = [
        ('testvendor1', 'Glamour Palace'),
        ('testvendor2', 'Beauty Haven'),
        ('testvendor3', 'Style Studio'),
        ('testvendor4', 'Elegance Salon'),
        ('testvendor5', 'Chic Boutique'),
    ]
    
    for username, salon_name in test_vendors:
        try:
            user, salon = create_test_vendor(username, salon_name)
            print(f"🎉 Vendor ready: {username} -> {salon_name}")
            print(f"   Login: {username} / testpass123")
            print(f"   Salon ID: {salon.id}")
            print()
        except Exception as e:
            print(f"❌ Error creating {username}: {e}")
    
    print("✅ Test vendor creation complete!")
    print("\n📋 Login Credentials:")
    print("Username: testvendor1, testvendor2, testvendor3, testvendor4, testvendor5")
    print("Password: testpass123")
    print("\n🔗 Access vendor profile at: http://localhost:3000/vendor/profile")

if __name__ == '__main__':
    main()
