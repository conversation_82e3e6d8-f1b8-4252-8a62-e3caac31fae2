#!/usr/bin/env python3
"""
🚀 Complete Database Vendor Creation Script
Creates vendors directly in the database, bypassing all frontend/payment logic
"""

import os
import sys
import django
from datetime import datetime

# Add the backend directory to Python path
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.append(backend_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salon_project.settings')
django.setup()

from django.contrib.auth.models import User
from salons_app.models import Salon, Service, Staff

def create_complete_vendor(username, salon_name, email=None, phone="+254700000000", password="testpass123"):
    """Create a complete vendor with salon, services, and staff"""
    
    print(f"🚀 Creating complete vendor: {username}")
    print("=" * 50)
    
    # Create user
    if email is None:
        email = f"{username}@salontest.com"
    
    try:
        # Check if user exists
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'first_name': username.capitalize(),
                'last_name': 'Vendor',
                'is_staff': False,
                'is_superuser': False
            }
        )
        
        if created:
            user.set_password(password)
            user.save()
            print(f"✅ Created user: {username}")
        else:
            print(f"ℹ️  User {username} already exists")
        
        # Create salon
        salon, created = Salon.objects.get_or_create(
            vendor=user,
            defaults={
                'name': salon_name,
                'address': '123 Beauty Street',
                'county': 'Nairobi',
                'town': 'Nairobi',
                'phone': phone,
                'email': email,
                'latitude': -1.286389,
                'longitude': 36.817223,
                'description': f'Professional beauty salon offering premium services. {salon_name} is your destination for luxury beauty treatments.',
                'imageUrl': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=500'
            }
        )
        
        if created:
            print(f"✅ Created salon: {salon_name}")
        else:
            print(f"ℹ️  Salon {salon_name} already exists")
        
        # Create sample services
        services_data = [
            {'name': 'Hair Cut & Style', 'price': 1500, 'duration': 60},
            {'name': 'Hair Coloring', 'price': 3000, 'duration': 120},
            {'name': 'Manicure', 'price': 800, 'duration': 45},
            {'name': 'Pedicure', 'price': 1000, 'duration': 60},
            {'name': 'Facial Treatment', 'price': 2000, 'duration': 90},
            {'name': 'Eyebrow Threading', 'price': 500, 'duration': 30},
        ]
        
        for service_data in services_data:
            service, created = Service.objects.get_or_create(
                salon=salon,
                name=service_data['name'],
                defaults={
                    'price': service_data['price'],
                    'duration': service_data['duration'],
                    'description': f"Professional {service_data['name'].lower()} service"
                }
            )
            if created:
                print(f"✅ Created service: {service_data['name']}")
        
        # Create sample staff
        staff_data = [
            {'name': 'Sarah Johnson', 'role': 'Senior Stylist', 'specialty': 'Hair Cutting & Styling'},
            {'name': 'Maria Garcia', 'role': 'Nail Technician', 'specialty': 'Manicure & Pedicure'},
            {'name': 'Lisa Chen', 'role': 'Esthetician', 'specialty': 'Facial Treatments'},
        ]
        
        for staff_member in staff_data:
            staff, created = Staff.objects.get_or_create(
                salon=salon,
                name=staff_member['name'],
                defaults={
                    'role': staff_member['role'],
                    'specialty': staff_member['specialty'],
                    'phone': f"+25470{len(staff_member['name'])}000000",
                    'email': f"{staff_member['name'].lower().replace(' ', '.')}@{salon_name.lower().replace(' ', '')}.com"
                }
            )
            if created:
                print(f"✅ Created staff: {staff_member['name']}")
        
        print(f"\n🎉 VENDOR SETUP COMPLETE!")
        print(f"📋 Login Details:")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        print(f"   Email: {email}")
        print(f"\n🏪 Salon Details:")
        print(f"   Name: {salon_name}")
        print(f"   ID: {salon.id}")
        print(f"   Services: {salon.services.count()}")
        print(f"   Staff: {salon.staff.count()}")
        print(f"\n🔗 Access URLs:")
        print(f"   Login: http://localhost:3000/login")
        print(f"   Vendor Profile: http://localhost:3000/vendor/profile")
        print(f"   Admin Panel: http://127.0.0.1:8000/admin/salons_app/salon/{salon.id}/")
        
        return user, salon
        
    except Exception as e:
        print(f"❌ Error creating vendor {username}: {e}")
        return None, None

def create_custom_vendor():
    """Interactive vendor creation"""
    print("🎯 Custom Vendor Creation")
    print("=" * 30)
    
    username = input("Enter username: ").strip()
    salon_name = input("Enter salon name: ").strip()
    email = input("Enter email (optional): ").strip() or None
    phone = input("Enter phone (optional): ").strip() or "+254700000000"
    
    if username and salon_name:
        return create_complete_vendor(username, salon_name, email, phone)
    else:
        print("❌ Username and salon name are required")
        return None, None

def main():
    """Main execution"""
    print("🚀 Database Vendor Creation Tool")
    print("=" * 40)
    print("Choose an option:")
    print("1. Create pre-configured test vendors")
    print("2. Create custom vendor")
    print("3. Create single quick vendor")
    print("4. List existing vendors")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        # Create multiple test vendors
        test_vendors = [
            ('testvendor1', 'Glamour Palace'),
            ('testvendor2', 'Beauty Haven'),
            ('testvendor3', 'Style Studio'),
            ('quickvendor', 'Quick Test Salon'),
            ('demovendor', 'Demo Beauty Center'),
        ]
        
        for username, salon_name in test_vendors:
            create_complete_vendor(username, salon_name)
            print()
    
    elif choice == "2":
        create_custom_vendor()
    
    elif choice == "3":
        # Quick single vendor
        timestamp = int(datetime.now().timestamp())
        username = f"vendor{timestamp}"
        salon_name = f"Salon {timestamp}"
        create_complete_vendor(username, salon_name)
    
    elif choice == "4":
        # List existing vendors
        print("📋 Existing Vendors:")
        print("-" * 30)
        salons = Salon.objects.all()
        for salon in salons:
            print(f"• {salon.vendor.username} → {salon.name}")
            print(f"  Services: {salon.services.count()}, Staff: {salon.staff.count()}")
            print(f"  Login: {salon.vendor.username} / testpass123")
            print()
    
    else:
        print("❌ Invalid choice")

if __name__ == '__main__':
    main()
