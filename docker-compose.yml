
services:
  # Database Service
  db:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_DB: salon_db
      POSTGRES_USER: salon_user
      POSTGRES_PASSWORD: salon_pass
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  # Backend Django Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: salon_backend
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: salon_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    stdin_open: true
    tty: true

volumes:
  pgdata:
