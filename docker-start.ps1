# Docker Salon Management System Startup Script
Write-Host "🐳 Starting Salon Management System with Docker" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Check if Docker is running
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not available. Please install Docker Desktop." -ForegroundColor Red
    exit 1
}

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose down

# Build and start containers
Write-Host "🔨 Building and starting containers..." -ForegroundColor Blue
docker-compose up --build -d

# Wait for services to be ready
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check container status
Write-Host "`n📊 Container Status:" -ForegroundColor Cyan
docker-compose ps

Write-Host "`n✅ Salon Management System is running!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host "🎨 Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔧 Backend:  http://localhost:8000" -ForegroundColor Cyan
Write-Host "👤 Admin:    http://localhost:8000/admin" -ForegroundColor Cyan
Write-Host "   Username: admin" -ForegroundColor White
Write-Host "   Password: aluru742!!" -ForegroundColor White

Write-Host "`n📝 Useful Commands:" -ForegroundColor Yellow
Write-Host "   View logs:     docker-compose logs -f" -ForegroundColor White
Write-Host "   Stop services: docker-compose down" -ForegroundColor White
Write-Host "   Restart:       docker-compose restart" -ForegroundColor White

Write-Host "`n🔍 Monitoring containers..." -ForegroundColor Blue
Write-Host "Press Ctrl+C to stop monitoring (containers will keep running)" -ForegroundColor Yellow

# Follow logs
docker-compose logs -f
