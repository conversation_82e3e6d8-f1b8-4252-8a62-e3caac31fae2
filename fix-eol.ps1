# fix-eol.ps1
# Recursively normalize all JS/TS/JSON/MD/etc files to LF and remove BOMs

$root = Get-Location
$extensions = @("*.js", "*.jsx", "*.ts", "*.tsx", "*.json", "*.md", "*.html", "*.css")
$folders = @("src", "public", "backend", "ai_engine")

foreach ($folder in $folders) {
    $target = Join-Path $root $folder
    if (Test-Path $target) {
        foreach ($ext in $extensions) {
            Get-ChildItem -Path $target -Recurse -Include $ext -File | ForEach-Object {
                $content = Get-Content -Raw -Encoding Byte $_.FullName
                if ($content.Length -ge 3 -and $content[0] -eq 0xEF -and $content[1] -eq 0xBB -and $content[2] -eq 0xBF) {
                    $content = $content[3..($content.Length - 1)]
                }
                $utf8Text = [System.Text.Encoding]::UTF8.GetString($content)
                $utf8Text = $utf8Text -replace "`r`n", "`n"
                [System.IO.File]::WriteAllText($_.FullName, $utf8Text, (New-Object System.Text.UTF8Encoding($false)))
                Write-Host "Fixed: $($_.FullName)"
            }
        }
    }
}
Write-Host "\nDone! All matching files normalized to LF without BOMs."
