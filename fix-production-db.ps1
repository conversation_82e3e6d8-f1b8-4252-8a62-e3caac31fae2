# Production Database Fix Script
# Safely fixes database migration and locking issues

Write-Host "🔧 SalonGenz Production Database Fix" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Check if virtual environment exists
if (!(Test-Path "salonvenv")) {
    Write-Host "❌ Virtual environment 'salonvenv' not found!" -ForegroundColor Red
    Write-Host "Please run setup-salon.ps1 first" -ForegroundColor Yellow
    exit 1
}

# Activate virtual environment
Write-Host "🔄 Activating virtual environment..." -ForegroundColor Yellow
& ".\salonvenv\Scripts\Activate.ps1"

# Navigate to backend directory
Set-Location backend

Write-Host "`n📊 Checking current migration status..." -ForegroundColor Green
try {
    python manage.py showmigrations salons_app
} catch {
    Write-Host "⚠️ Could not check migrations: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n🔍 Running database fix (dry-run first)..." -ForegroundColor Green
try {
    python manage.py fix_production_db --dry-run
} catch {
    Write-Host "⚠️ Dry run failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Ask user if they want to proceed
Write-Host "`n❓ Do you want to apply the fixes? (y/N): " -ForegroundColor Yellow -NoNewline
$response = Read-Host

if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "`n🚀 Applying database fixes..." -ForegroundColor Green
    
    try {
        # Try normal migration first
        Write-Host "📋 Applying migrations..." -ForegroundColor Cyan
        python manage.py migrate salons_app
        
        Write-Host "✅ Migrations applied successfully!" -ForegroundColor Green
        
        # Verify the fix
        Write-Host "`n🔍 Verifying fixes..." -ForegroundColor Cyan
        python manage.py fix_production_db --dry-run
        
    } catch {
        Write-Host "❌ Migration failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "🔧 Trying force fix..." -ForegroundColor Yellow
        
        try {
            python manage.py fix_production_db --force
            Write-Host "✅ Force fix completed!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Force fix also failed: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "🆘 Manual intervention required!" -ForegroundColor Red
        }
    }
    
    # Check salon data
    Write-Host "`n📊 Checking salon data..." -ForegroundColor Cyan
    try {
        python manage.py shell -c "from salons_app.models import Salon; print(f'Salons in DB: {Salon.objects.count()}')"
    } catch {
        Write-Host "⚠️ Could not check salon data: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    # Populate data if needed
    Write-Host "`n❓ Do you want to populate sample salon data? (y/N): " -ForegroundColor Yellow -NoNewline
    $populateResponse = Read-Host
    
    if ($populateResponse -eq 'y' -or $populateResponse -eq 'Y') {
        Write-Host "🏪 Populating sample salon data..." -ForegroundColor Cyan
        try {
            python manage.py populate_dummy_salons
            Write-Host "✅ Sample data populated!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to populate data: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
} else {
    Write-Host "❌ Database fix cancelled by user" -ForegroundColor Yellow
}

# Return to root directory
Set-Location ..

Write-Host "`n✅ Database fix script completed!" -ForegroundColor Green
Write-Host "🔄 You may need to restart your backend server" -ForegroundColor Cyan
