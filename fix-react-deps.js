const fs = require('fs');
const path = require('path');

// Create missing InlineChunkHtmlPlugin
const inlineChunkHtmlPluginPath = path.join(__dirname, 'node_modules', 'react-dev-utils', 'InlineChunkHtmlPlugin.js');
const inlineChunkHtmlPluginContent = `'use strict';

class InlineChunkHtmlPlugin {
  constructor(htmlWebpackPlugin, tests) {
    this.htmlWebpackPlugin = htmlWebpackPlugin;
    this.tests = tests;
  }

  getInlinedTag(publicPath, assets, tag) {
    if (tag.tagName !== 'script' || !(tag.attributes && tag.attributes.src)) {
      return tag;
    }
    const scriptName = publicPath
      ? tag.attributes.src.replace(publicPath, '')
      : tag.attributes.src;
    if (!this.tests.some(test => scriptName.match(test))) {
      return tag;
    }
    const asset = assets[scriptName];
    if (asset == null) {
      return tag;
    }
    return { tagName: 'script', innerHTML: asset.source(), closeTag: true };
  }

  apply(compiler) {
    let publicPath = compiler.options.output.publicPath || '';
    if (publicPath && !publicPath.endsWith('/')) {
      publicPath += '/';
    }

    compiler.hooks.compilation.tap('InlineChunkHtmlPlugin', compilation => {
      const tagFunction = tag =>
        this.getInlinedTag(publicPath, compilation.assets, tag);

      const hooks = this.htmlWebpackPlugin.getHooks(compilation);
      hooks.alterAssetTagGroups.tap('InlineChunkHtmlPlugin', assets => {
        assets.headTags = assets.headTags.map(tagFunction);
        assets.bodyTags = assets.bodyTags.map(tagFunction);
      });
    });
  }
}

module.exports = InlineChunkHtmlPlugin;`;

// Create enhanced-resolve index.js
const enhancedResolveIndexPath = path.join(__dirname, 'node_modules', 'enhanced-resolve', 'lib', 'index.js');
const enhancedResolveIndexContent = `'use strict';

const ResolverFactory = require('./ResolverFactory');

module.exports = ResolverFactory;
module.exports.ResolverFactory = ResolverFactory;
module.exports.Resolver = require('./Resolver');
module.exports.CachedInputFileSystem = require('./CachedInputFileSystem');
module.exports.create = ResolverFactory.createResolver;
module.exports.createResolver = ResolverFactory.createResolver;
module.exports.NodeJsInputFileSystem = require('fs');`;

// Create enhanced-resolve identifier.js
const enhancedResolveIdentifierPath = path.join(__dirname, 'node_modules', 'enhanced-resolve', 'lib', 'util', 'identifier.js');
const enhancedResolveIdentifierContent = `'use strict';

function createIdentifier(request) {
  if (typeof request !== 'string') {
    return '';
  }
  return request;
}

function parseIdentifier(identifier) {
  return identifier;
}

module.exports = {
  createIdentifier,
  parseIdentifier
};`;

// Function to create file if it doesn't exist
function createFileIfNotExists(filePath, content) {
  try {
    if (!fs.existsSync(filePath)) {
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      fs.writeFileSync(filePath, content);
      console.log(`Created: ${filePath}`);
    } else {
      console.log(`Already exists: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error creating ${filePath}:`, error.message);
  }
}

// Create missing files
console.log('Fixing React dependencies...');
createFileIfNotExists(inlineChunkHtmlPluginPath, inlineChunkHtmlPluginContent);
createFileIfNotExists(enhancedResolveIndexPath, enhancedResolveIndexContent);
createFileIfNotExists(enhancedResolveIdentifierPath, enhancedResolveIdentifierContent);
console.log('React dependencies fixed!');
