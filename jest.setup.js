window.matchMedia = window.matchMedia || function() {
  return {
    matches: false,
    addEventListener: function(type, listener) {
      if (type === 'change') this.addListener(listener);
    },
    removeEventListener: function(type, listener) {
      if (type === 'change') this.removeListener(listener);
    },
    addListener: function() {},
    removeListener: function() {}
  };
};

if (typeof window.IntersectionObserver === 'undefined') {
  window.IntersectionObserver = class {
    constructor() {}
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

if (typeof global.fetch === 'undefined') {
  global.fetch = jest.fn(() => Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  }));
} 