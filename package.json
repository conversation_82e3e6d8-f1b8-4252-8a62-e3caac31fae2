{"name": "salongenz", "version": "0.1.0", "private": true, "proxy": "http://127.0.0.1:8000", "dependencies": {"@phosphor-icons/react": "^2.1.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "bootstrap": "^5.3.7", "bootstrap-icons": "^1.13.1", "chart.js": "^4.5.0", "enhanced-resolve": "^5.18.2", "prompts": "^2.4.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-calendar": "^6.0.0", "react-chartjs-2": "^5.3.0", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.16.0", "react-scripts": "^5.0.1", "react-select": "^5.10.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "babel-jest": "^30.0.4", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "lint-staged": "^16.1.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{json,css,md}": ["prettier --write"]}}