<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#4B0082" />
    <!-- Cache busting meta tag -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta
      name="description"
      content="Salongenz - Professional Salon Booking Platform"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Salongenz</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Paystack Inline JavaScript Library v2 -->
    <script src="https://js.paystack.co/v2/inline.js"></script>
    
    <!-- Mobile loading optimization -->
    <!-- Removed styles.css reference as file does not exist -->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Mobile loading indicator -->
    <div id="mobile-loading" class="mobile-loading">
      <div class="mobile-spinner"></div>
    </div>
    
    <div id="root"></div>

    <!-- Location Modal -->
    <div id="location-modal" class="location-modal" style="display: none;">
      <div class="location-modal-content">
        <div class="location-modal-header">
          <h3>📍 Location Required</h3>
          <button id="close-location-modal" class="close-location-modal">×</button>
        </div>
        <div class="location-modal-body">
          <p>We couldn't detect your location. Please enter your city or town to find salons near you.</p>
          <form id="location-modal-form">
            <div class="location-input-group">
              <input
                type="text"
                id="location-modal-input"
                placeholder="Enter your city or town..."
                class="location-input"
                required
              />
              <button type="submit" class="location-submit-btn">Find Salons</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Mobile loading script -->
    <script>
      // Hide loading spinner when app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('mobile-loading');
          if (loading) {
            loading.classList.add('hidden');
            setTimeout(function() {
              loading.style.display = 'none';
            }, 300);
          }
        }, 500);
      });

      // Prevent zoom on double tap for mobile
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
    </script>

    <script src="%PUBLIC_URL%/scripts.js" defer></script>

    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
