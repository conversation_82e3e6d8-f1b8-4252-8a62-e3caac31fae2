// SalonGenz Public Scripts
// This file contains any public JavaScript that needs to be loaded globally

console.log('SalonGenz scripts loaded successfully');

// Global utility functions
window.SalonGenz = {
  // Utility function to show loading state
  showLoading: function(element) {
    if (element) {
      element.style.opacity = '0.6';
      element.disabled = true;
    }
  },

  // Utility function to hide loading state
  hideLoading: function(element) {
    if (element) {
      element.style.opacity = '1';
      element.disabled = false;
    }
  },

  // Utility function to format currency
  formatCurrency: function(amount, currency = 'KSH') {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: currency
    }).format(amount);
  },

  // Utility function to format date
  formatDate: function(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  },

  // Utility function to show notification
  showNotification: function(message, type = 'info') {
    // Check if toast is available
    if (window.toast) {
      window.toast[type](message);
    } else {
      // Fallback to alert
      alert(message);
    }
  }
};

// Location Modal Functionality
let locationModalCallback = null;

function showLocationModal(onSubmit) {
  const modal = document.getElementById('location-modal');
  if (modal) {
    modal.style.display = 'flex';
    
    const closeBtn = document.getElementById('close-location-modal');
    if (closeBtn && !closeBtn._locationModalHandlerAttached) {
      closeBtn.onclick = closeLocationModal;
      closeBtn._locationModalHandlerAttached = true;
    }
    
    const form = document.getElementById('location-modal-form');
    if (form && !form._locationModalHandlerAttached) {
      form.onsubmit = function(e) {
        e.preventDefault();
        const input = document.getElementById('location-modal-input');
        const value = input ? input.value.trim() : '';
        
        if (value) {
          closeLocationModal();
          if (typeof locationModalCallback === 'function') {
            locationModalCallback(value);
          }
        }
      };
      form._locationModalHandlerAttached = true;
    }
    
    locationModalCallback = onSubmit;
  }
}

function closeLocationModal() {
  const modal = document.getElementById('location-modal');
  if (modal) {
    modal.style.display = 'none';
  }
}

function hideLocationModalIfVisible() {
  const modal = document.getElementById('location-modal');
  if (modal && modal.style.display === 'flex') {
    closeLocationModal();
  }
}

// Export location modal functions globally
window.showLocationModal = showLocationModal;
window.closeLocationModal = closeLocationModal;
window.hideLocationModalIfVisible = hideLocationModalIfVisible;

// Initialize any global event listeners
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing SalonGenz scripts');
  
  // Add any global initialization code here
  // For example, setting up global error handlers
  
  // Global error handler
  window.addEventListener('error', function(event) {
    console.error('Global error caught:', event.error);
  });
  
  // Global unhandled promise rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
  });
  
  // Initialize location modal if it exists
  const locationModal = document.getElementById('location-modal');
  if (locationModal) {
    console.log('Location modal found and initialized');
  }
});

// Export for module systems if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ...window.SalonGenz,
    showLocationModal,
    closeLocationModal,
    hideLocationModalIfVisible
  };
} 