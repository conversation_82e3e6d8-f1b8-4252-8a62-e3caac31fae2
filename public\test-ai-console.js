// AI Style Advisor B<PERSON>er Console Test
// Copy and paste this into browser console to test AI functionality

console.log('🧪 AI Style Advisor Internal Testing Started...');

// Test profiles with different metrics
const testProfiles = [
  {
    name: "Young Professional - Curly Hair",
    profile: {
      hairType: 'Curly',
      faceShape: 'Oval',
      age: 25,
      occasion: 'Professional',
      stylePreferences: ['modern', 'low-maintenance'],
      budget: 'Premium',
      maintenanceLevel: 'Low',
      lifestyle: 'busy',
      hairLength: 'medium'
    }
  },
  {
    name: "Gen-Z Student - Straight Hair", 
    profile: {
      hairType: 'Straight',
      faceShape: 'Round',
      age: 20,
      occasion: 'Everyday',
      stylePreferences: ['trendy', 'bold', 'colorful'],
      budget: 'Budget',
      maintenanceLevel: 'Medium',
      lifestyle: 'active',
      hairLength: 'long'
    }
  },
  {
    name: "Creative Artist - Mixed Hair",
    profile: {
      hairType: 'Mixed',
      faceShape: 'Heart',
      age: 30,
      occasion: 'Creative',
      stylePreferences: ['artistic', 'unique', 'experimental'],
      budget: 'Mid-range',
      maintenanceLevel: 'Medium',
      lifestyle: 'creative',
      hairLength: 'medium'
    }
  }
];

// Function to test AI with different profiles
async function testAIWithProfile(profile, profileName) {
  console.log(`\n📋 Testing: ${profileName}`);
  console.log('Profile:', profile);
  
  try {
    // Call the AI service directly
    const response = await fetch('/ai/style-recommendations/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userProfile: profile
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ AI Response received:');
      console.log('Source:', result.source);
      console.log('Confidence:', result.confidence);
      
      if (result.response && typeof result.response === 'string') {
        try {
          const parsed = JSON.parse(result.response);
          
          console.log('\n📊 AI Insights:');
          if (parsed.recommendations) {
            console.log(`Found ${parsed.recommendations.length} recommendations:`);
            parsed.recommendations.forEach((rec, i) => {
              console.log(`${i+1}. ${rec.style} (${rec.suitabilityScore} match)`);
              console.log(`   Cost: ${rec.cost}, Maintenance: ${rec.maintenance}`);
              console.log(`   Why: ${rec.whyRecommended}`);
            });
          }
          
          if (parsed.analysis) {
            console.log('\n🔍 Analysis:');
            console.log('Face Shape:', parsed.analysis.faceShapeAnalysis);
            console.log('Hair Type:', parsed.analysis.hairTypeAnalysis);
            console.log('Lifestyle:', parsed.analysis.lifestyleFit);
          }
          
          // Validate metrics
          const metrics = validateAIMetrics(profile, parsed);
          console.log('\n📈 Metrics Validation:', metrics);
          
        } catch (e) {
          console.log('Raw response:', result.response.substring(0, 300));
        }
      }
    } else {
      console.log('❌ API Error:', response.status);
      
      // Try direct AI API fallback
      console.log('🔄 Trying direct AI API...');
      await testDirectAI(profile);
    }
    
  } catch (error) {
    console.log('❌ Network Error:', error.message);
    
    // Try direct AI API fallback
    console.log('🔄 Trying direct AI API...');
    await testDirectAI(profile);
  }
}

// Test direct AI API (Groq)
async function testDirectAI(profile) {
  const apiKey = '********************************************************';
  
  const prompt = `Generate personalized hairstyle recommendations for this user profile:
  
Hair Type: ${profile.hairType}
Face Shape: ${profile.faceShape}
Age: ${profile.age}
Occasion: ${profile.occasion}
Style Preferences: ${profile.stylePreferences.join(', ')}
Budget: ${profile.budget}
Maintenance Level: ${profile.maintenanceLevel}
Lifestyle: ${profile.lifestyle}

Provide recommendations in JSON format:
{
  "recommendations": [
    {
      "style": "Style Name",
      "description": "Description",
      "suitabilityScore": 0.0-1.0,
      "maintenance": "Low/Medium/High",
      "cost": "Budget/Mid-range/Premium",
      "stylingTime": "X minutes",
      "products": ["Product 1", "Product 2"],
      "stylingTips": ["Tip 1", "Tip 2"],
      "whyRecommended": "Why this style works for you"
    }
  ],
  "analysis": {
    "faceShapeAnalysis": "How your face shape affects style choices",
    "hairTypeAnalysis": "How your hair type affects styling",
    "lifestyleFit": "How styles fit your lifestyle"
  }
}`;

  try {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3-8b-8192',
        messages: [
          {
            role: 'system',
            content: 'You are a professional hairstylist and style advisor. Provide personalized recommendations based on user characteristics and preferences.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        max_tokens: 1000
      })
    });

    if (response.ok) {
      const data = await response.json();
      const content = data.choices[0].message.content;
      
      console.log('✅ Direct AI Response:');
      console.log('Source: ai_api_groq');
      
      try {
        // Clean the response
        let cleanContent = content;
        if (cleanContent.includes('```')) {
          cleanContent = cleanContent.replace(/^.*?```(?:json)?\s*/s, '');
          cleanContent = cleanContent.replace(/```.*$/s, '');
        }
        cleanContent = cleanContent.replace(/^.*?(?=\{)/s, '');
        cleanContent = cleanContent.replace(/\}.*$/s, '}');
        
        const parsed = JSON.parse(cleanContent);
        
        console.log('\n📊 Direct AI Insights:');
        if (parsed.recommendations) {
          console.log(`Found ${parsed.recommendations.length} recommendations:`);
          parsed.recommendations.forEach((rec, i) => {
            console.log(`${i+1}. ${rec.style} (${rec.suitabilityScore} match)`);
            console.log(`   Cost: ${rec.cost}, Maintenance: ${rec.maintenance}`);
            console.log(`   Why: ${rec.whyRecommended}`);
          });
        }
        
        if (parsed.analysis) {
          console.log('\n🔍 Analysis:');
          console.log('Face Shape:', parsed.analysis.faceShapeAnalysis);
          console.log('Hair Type:', parsed.analysis.hairTypeAnalysis);
          console.log('Lifestyle:', parsed.analysis.lifestyleFit);
        }
        
        // Validate metrics
        const metrics = validateAIMetrics(profile, parsed);
        console.log('\n📈 Metrics Validation:', metrics);
        
      } catch (parseError) {
        console.log('Raw AI content:', content);
      }
      
    } else {
      console.log('❌ Direct AI API failed:', response.status);
    }
    
  } catch (error) {
    console.log('❌ Direct AI Error:', error.message);
  }
}

// Validate that AI considers different metrics
function validateAIMetrics(profile, aiResponse) {
  const responseText = JSON.stringify(aiResponse).toLowerCase();
  
  return {
    hairTypeConsidered: responseText.includes(profile.hairType.toLowerCase()),
    faceShapeConsidered: responseText.includes(profile.faceShape.toLowerCase()),
    budgetConsidered: responseText.includes(profile.budget.toLowerCase()),
    maintenanceConsidered: responseText.includes(profile.maintenanceLevel.toLowerCase()),
    ageAppropriate: profile.age < 25 ? 
      (responseText.includes('young') || responseText.includes('trendy')) :
      (responseText.includes('mature') || responseText.includes('professional')),
    lifestyleConsidered: responseText.includes(profile.lifestyle) || responseText.includes('lifestyle')
  };
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive AI testing...\n');
  
  for (let i = 0; i < testProfiles.length; i++) {
    const test = testProfiles[i];
    await testAIWithProfile(test.profile, test.name);
    console.log('\n' + '='.repeat(60));
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n🏁 All tests completed!');
}

// Auto-run tests
console.log('Starting tests in 3 seconds...');
setTimeout(runAllTests, 3000);
