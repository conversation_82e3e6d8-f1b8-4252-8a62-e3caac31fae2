#!/usr/bin/env python3
"""
🚀 Quick Vendor Database Setup - No Frontend Interference
Creates vendors directly in database, completely separate from payment logic
"""

import os
import sys
import django

# Setup Django from backend directory
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.append(backend_path)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salon_project.settings')
django.setup()

from django.contrib.auth.models import User
from salons_app.models import Salon, Service, Staff

def create_ready_vendor(username="quickvendor", salon_name="Quick Test Salon", password="testpass123"):
    """Create a complete, ready-to-use vendor account"""
    
    print(f"🚀 Creating vendor: {username}")
    
    # Create user
    user, created = User.objects.get_or_create(
        username=username,
        defaults={
            'email': f'{username}@test.com',
            'first_name': username.capitalize(),
            'last_name': 'Vendor',
        }
    )
    
    if created:
        user.set_password(password)
        user.save()
        print(f"✅ User created: {username}")
    else:
        print(f"ℹ️  User exists: {username}")
    
    # Create salon
    salon, created = Salon.objects.get_or_create(
        vendor=user,
        defaults={
            'name': salon_name,
            'address': '123 Test Street',
            'county': 'Nairobi',
            'town': 'Nairobi',
            'phone': '+************',
            'email': f'{username}@test.com',
            'latitude': -1.286389,
            'longitude': 36.817223,
            'description': f'Professional beauty salon - {salon_name}',
            'imageUrl': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=500'
        }
    )
    
    if created:
        print(f"✅ Salon created: {salon_name}")
    else:
        print(f"ℹ️  Salon exists: {salon_name}")
    
    # Add services
    services = [
        {'name': 'Hair Cut', 'price': 1500, 'duration': 60},
        {'name': 'Manicure', 'price': 800, 'duration': 45},
        {'name': 'Facial', 'price': 2000, 'duration': 90},
    ]
    
    for service_data in services:
        Service.objects.get_or_create(
            salon=salon,
            name=service_data['name'],
            defaults=service_data
        )
    
    # Add staff
    Staff.objects.get_or_create(
        salon=salon,
        name='Test Stylist',
        defaults={
            'role': 'Senior Stylist',
            'specialty': 'Hair & Beauty',
            'phone': '+254700000001',
            'email': f'stylist@{salon_name.lower().replace(" ", "")}.com'
        }
    )
    
    print(f"\n🎉 VENDOR READY!")
    print(f"📋 Login: {username} / {password}")
    print(f"🏪 Salon: {salon_name} (ID: {salon.id})")
    print(f"💼 Services: {salon.services.count()}")
    print(f"👥 Staff: {salon.staff.count()}")
    print(f"\n🔗 Login at: http://localhost:3000/login")
    print(f"🔗 Vendor Profile: http://localhost:3000/vendor/profile")
    
    return user, salon

def create_multiple_vendors():
    """Create several test vendors"""
    vendors = [
        ('testvendor1', 'Glamour Palace'),
        ('testvendor2', 'Beauty Haven'),
        ('testvendor3', 'Style Studio'),
        ('quickvendor', 'Quick Salon'),
        ('demovendor', 'Demo Beauty'),
    ]
    
    print("🚀 Creating multiple test vendors...")
    print("=" * 40)
    
    for username, salon_name in vendors:
        create_ready_vendor(username, salon_name)
        print()
    
    print("✅ All vendors created!")
    print("\n📋 Ready-to-use accounts:")
    for username, salon_name in vendors:
        print(f"   • {username} / testpass123 → {salon_name}")

def list_existing_vendors():
    """List all existing vendors"""
    print("📋 Existing Vendors:")
    print("=" * 30)
    
    salons = Salon.objects.all()
    if not salons:
        print("No vendors found.")
        return
    
    for salon in salons:
        print(f"• {salon.vendor.username} → {salon.name}")
        print(f"  Services: {salon.services.count()}, Staff: {salon.staff.count()}")
        print(f"  Login: {salon.vendor.username} / testpass123")
        print()

def main():
    """Main menu"""
    print("🚀 Quick Vendor Database Setup")
    print("=" * 35)
    print("1. Create single quick vendor")
    print("2. Create multiple test vendors")
    print("3. List existing vendors")
    print("4. Create custom vendor")
    
    choice = input("\nChoice (1-4): ").strip()
    
    if choice == "1":
        create_ready_vendor()
    elif choice == "2":
        create_multiple_vendors()
    elif choice == "3":
        list_existing_vendors()
    elif choice == "4":
        username = input("Username: ").strip()
        salon_name = input("Salon name: ").strip()
        if username and salon_name:
            create_ready_vendor(username, salon_name)
        else:
            print("❌ Username and salon name required")
    else:
        print("❌ Invalid choice")

if __name__ == '__main__':
    main()
