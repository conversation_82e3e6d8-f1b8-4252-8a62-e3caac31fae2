# Gemini CLI Modular Refactor Guide

Welcome to the structured refactor guide for Gemini CLI. Each section targets a specific module of the codebase and outlines scoped, surgical changes. Do not exceed the scope defined per section.

---

## 📁 Module: Social Feed (`SocialFeed.js`, `StylePost.js`)

### 🔧 Task: Refactor Template Literals

Refactor how the following are rendered using template literals and cleaner JSX:

* Salon/service name display
* Like and comment counts (handle singular/plural)
* Comment text (trim whitespace, sanitize)
* Review summary (`${rating}★ from ${reviewCount} reviews`)
* Pagination text (`Showing ${current} of ${total} posts`)

### ✅ Constraints:

* Only modify: `components/SocialFeed.js`, `components/StylePost.js`
* Do NOT rename files or props
* Preserve routing and all state logic
* Avoid adding new libraries

---

## 📁 Module: AI Analytics (`AIAnalyticsDashboard.js`)

### 🔧 Task: Code Hygiene + ESLint Fixes

Resolve the following issues:

* Remove unused imports: `getPriorityColor`, `getSeverityColor`
* Fix `label` a11y issue (ensure `htmlFor` links to a control)
* Break long template expressions to new lines as per `react/jsx-one-expression-per-line`
* Follow max line length (100 chars)

### ✅ Constraints:

* Only modify: `components/AIAnalyticsDashboard.js`
* Use existing utils in `utils/formatting.js`
* No logic change — just style and lint compliance

---

## 📁 Module: Admin Panel (`AdminDashboard.js`)

### 🔧 Task: Enhance Data Visualization (Upcoming)

*TODO*: Add charts and interactive filtering using Recharts or Chart.js.
Define reusable `<MetricCard />` component.

---

## 📁 Global Refactor: Code Hygiene Across All Components

### 🧼 Task: ESLint + Prettier Cleanup (Full Sweep)

> **NOTE:** As of [DATE], ESLint rules are intentionally relaxed. Do NOT enforce linting errors or warnings until further notice. See top of this file for policy.

**Target Directories:**

* `src/components/`
* `src/services/`
* `src/utils/`
* `src/pages/` *(optional: confirm before processing)*

### 🎯 Objectives

1. **ESLint Compliance**

   * Fix all linting issues including:

     * `no-multiple-empty-lines`
     * `indent`
     * `jsx-a11y/label-has-associated-control`
     * `react/jsx-one-expression-per-line`
     * `react/jsx-indent-props`
     * `react/jsx-closing-bracket-location`
     * `import/order`
     * `react/prop-types`
     * `eol-last`

2. **Prettier Formatting**

   * Enforce formatting with:

     * Line width: 100
     * Indent: 2 spaces
     * `singleQuote: true`, `semi: true`, `trailingComma: none`

3. **Code Behavior Integrity**

   * 🔒 Do not change logic, only formatting
   * 🔒 No component extraction or relocation
   * 🔒 Do not rename files, variables, or props unless flagged unused

4. **PropTypes Enforcement**

   * Add missing `PropTypes` to all components that use props
   * Only declare props that are actually used

5. **Import Order Hygiene**

   * Order imports in this sequence:

     1. Node/React libraries
     2. Context/hooks
     3. Local modules/components
     4. Stylesheets (CSS/SCSS)

6. **Expression Hygiene**

   * Fix flagged JSX expression formatting: break long lines, separate adjacent text/nodes
   * Sanitize unescaped strings (apostrophes, quotes)

### 📤 Output

* Use `write_file()` to commit changes per file.
* Insert comment on top of file: `// Cleaned by Gemini CLI: ESLint + Prettier compliance (YYYY-MM-DD)`
* Summarize changes (e.g., `"prop-types added", "indentation fixes"`) per file

---

## 👨‍💻 Usage Guide

Run Gemini CLI with this file:

```bash
gemini run --md ./refactor-guide.md
```

To target one module:

```bash
gemini run --md ./refactor-guide.md --section "Social Feed"
```
Here's what to do next — Surgical Gemini CLI Fix Path:
🔧 Step 1: Expand refactor-guide.md


📁 Module: Core Components Lint 

🔧 Task: ESLint and Prettier Compliance

Apply code hygiene tasks to the following files:

    components/CheapestServicesSection.js

    components/FriendSearchModal.js

    components/HeroSection.js

    components/Home.js

    components/NearbySalonsSection.js

    components/PopularSalonsSection.js

    components/TrendingNowSection.js

🎯 Objectives:

    ESLint Fixes

        max-len: Break long lines to respect 100 characters

        no-unused-vars: Remove unused variables/imports/functions

        no-plusplus: Avoid i++, use i += 1

        no-shadow: Rename conflicting variable names

        no-empty: Remove or add comments in empty blocks

        react/jsx-one-expression-per-line: Split expressions into multiple lines

        jsx-a11y/label-has-associated-control: Ensure every <label> has htmlFor and corresponding <input>

        react/button-has-type: Add type="button" to <button> elements

        react/require-default-props: Add defaultProps for optional props

        react/no-array-index-key: Replace index-based keys with stable unique values where possible

    Prettier Formatting

        Enforce 2-space indentation

        Add missing final newlines

        Respect trailing comma, semicolon, and quote rules

    Dependencies

        If prop-types errors persist, ensure npm i prop-types is actually run and imported properly

    Constraints

        ❌ No new libraries

        ❌ No logic rewrites

        ✅ Only fix lint + style violations

        ✅ Use write_file() for each update

        ✅ Summarize changes per file before committing

🔧 Step 2: Run Gemini CLI

Now run Gemini with the full guide:

gemini run --md ./refactor-guide.md

Or for only one module (e.g., Hero section):

gemini run --md ./refactor-guide.md --section "Core Components Lint Fixes"

