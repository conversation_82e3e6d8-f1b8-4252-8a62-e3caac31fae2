services:
  # Django Backend Service
  - type: web
    name: salongenz-backend
    env: python
    region: oregon
    plan: free
    buildCommand: |
      cd backend &&
      pip install --upgrade pip &&
      pip install -r requirements-base.txt &&
      pip install -r requirements-dev.txt &&
      python manage.py collectstatic --noinput --clear
    startCommand: |
      cd backend &&
      python manage.py migrate --run-syncdb &&
      gunicorn salongenz_backend.wsgi:application --bind 0.0.0.0:$PORT --workers=3
    envVars:
      - key: DJANGO_SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: "False"
      - key: USE_SQLITE
        value: "False"
      - key: ALLOWED_HOSTS
        value: "salongenz-backend.onrender.com,.onrender.com"
      - key: CORS_ALLOWED_ORIGINS
        value: "https://salongenz.onrender.com"
    
  # React Frontend Service  
  - type: web
    name: salongenz-frontend
    env: node
    region: oregon
    plan: free
    buildCommand: npm install && npm run build
    staticPublishPath: ./build
    envVars:
      - key: REACT_APP_API_URL
        value: "https://salongenz-backend.onrender.com"

databases:
  - name: salongenz-db
    databaseName: salon_db
    user: salon_user
    region: oregon
    plan: free
