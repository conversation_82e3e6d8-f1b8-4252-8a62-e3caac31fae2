#!/usr/bin/env python3
"""
Send Test Gift <NAME_EMAIL>
Tests the complete email system with a real bestie message
"""

import os
import sys
import django
from pathlib import Path

# Setup Django environment
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))
sys.path.insert(0, str(project_root))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

# Now import Django components
from ai_engine.ai_service import AIService
from services.email_service import EmailService

def generate_bestie_message():
    """Generate a fun bestie gift message with emojis"""
    print("🤖 Generating bestie gift message with AI...")
    
    try:
        ai_service = AIService()
        
        # Generate a bestie message
        result = ai_service.generate_gift_message(
            relationship="bestie",
            occasion="just because",
            tone="fun",
            recipient_name="Astella Suite",  # <PERSON> extract "Astella"
            user_profile={
                "location": "Kenya",
                "region": "Nairobi",
                "language": "english",
                "senderName": "SalonGenz AI Team",  # Will extract "SalonGenz"
                "salonName": "Glam Paradise",
                "salonLocation": "Westlands",
                "serviceName": "full glam makeover",
                "servicePrice": "5000"
            }
        )
        
        # Extract message text from result
        if isinstance(result, dict):
            message_text = result.get('response', str(result))
            # Remove quotes if present
            if message_text.startswith('"') and message_text.endswith('"'):
                message_text = message_text[1:-1]
        else:
            message_text = str(result)
        
        print(f"✅ Generated message: {message_text}")
        return message_text
        
    except Exception as e:
        print(f"❌ Failed to generate AI message: {e}")
        # Fallback message with emojis
        return "Hey Astella! 💕 Your bestie here! I got you something special - a full glam makeover at Glam Paradise in Westlands! ✨ You deserve to be pampered, queen! Time to slay! 👑 Love, SalonGenz 💖"

def send_test_email():
    """Send test gift <NAME_EMAIL>"""
    print("📧 Sending test gift email...")
    
    try:
        # Generate the gift message
        gift_message = generate_bestie_message()
        
        # Send the email
        result = EmailService.send_gift_notification(
            recipient_email="<EMAIL>",
            recipient_name="Astella Suite",  # Will extract "Astella"
            gift_message=gift_message,
            salon_details={
                "name": "Glam Paradise",
                "location": "Westlands, Nairobi"
            },
            booking_details={
                "id": "TEST-BESTIE-001",
                "service": "Full Glam Makeover",
                "date": "2024-01-20",
                "time": "2:00 PM"
            },
            sender_name="SalonGenz AI Team"  # Will extract "SalonGenz"
        )
        
        print(f"📧 Email Result: {result}")
        
        if result['success']:
            print("🎉 SUCCESS! Gift email <NAME_EMAIL>")
            print("📱 Check your inbox for the bestie gift message!")
            print("💌 The email includes:")
            print("   ✅ Personalized AI message with emojis")
            print("   ✅ Beautiful HTML template")
            print("   ✅ Booking details")
            print("   ✅ First names only (Astella, SalonGenz)")
            return True
        else:
            print(f"❌ Failed to send email: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending test email: {e}")
        return False

def main():
    """Run the test email sending"""
    print("🚀 SENDING TEST BESTIE GIFT EMAIL")
    print("=" * 50)
    print("📧 Recipient: <EMAIL>")
    print("🎁 Type: Bestie gift message")
    print("🤖 AI: Enabled with emojis")
    print("👤 Names: First names only")
    print("=" * 50)
    
    success = send_test_email()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ TEST EMAIL SENT SUCCESSFULLY!")
        print("📱 Check <EMAIL> inbox")
        print("💌 Look for email from 'SalonGenz'")
        print("🎉 Subject: '🎁 SalonGenz sent you a gift booking at Glam Paradise!'")
    else:
        print("❌ TEST EMAIL FAILED")
        print("🔧 Check email configuration in backend/.env")
        print("📧 Verify SMTP settings and credentials")

if __name__ == "__main__":
    main()
