# Salon Management System Setup Script
# This script sets up both backend and frontend with proper versions

Write-Host "🏪 Salon Management System Setup" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check current Node.js version
$nodeVersion = node --version
Write-Host "Current Node.js version: $nodeVersion" -ForegroundColor Yellow

# Check if Node.js version is compatible
if ($nodeVersion -match "v(\d+)\.") {
    $majorVersion = [int]$matches[1]
    if ($majorVersion -gt 18) {
        Write-Host "⚠️  WARNING: Node.js $nodeVersion may have compatibility issues" -ForegroundColor Red
        Write-Host "   Recommended: Node.js 16.x or 18.x LTS" -ForegroundColor Yellow
        Write-Host "   Consider using Docker for consistent environment" -ForegroundColor Green
    }
}

Write-Host "`n🔧 Setting up Backend..." -ForegroundColor Green

# Activate virtual environment
if (Test-Path "salonvenv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Blue
    & .\salonvenv\Scripts\Activate.ps1
} else {
    Write-Host "Creating virtual environment..." -ForegroundColor Blue
    python -m venv salonvenv
    & .\salonvenv\Scripts\Activate.ps1
}

# Install backend dependencies
Write-Host "Installing Python dependencies..." -ForegroundColor Blue
pip install -r backend\requirements.txt

# Run migrations
Write-Host "Running database migrations..." -ForegroundColor Blue
python backend\manage.py migrate

Write-Host "`n🎨 Setting up Frontend..." -ForegroundColor Green

# Clean npm cache
Write-Host "Cleaning npm cache..." -ForegroundColor Blue
npm cache clean --force

# Install frontend dependencies with legacy peer deps for compatibility
Write-Host "Installing Node.js dependencies..." -ForegroundColor Blue
npm install --legacy-peer-deps

# Fix missing React dev utils
Write-Host "Fixing React dependencies..." -ForegroundColor Blue
node fix-react-deps.js

Write-Host "`n✅ Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host "Backend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Admin: http://localhost:8000/admin (admin/aluru742!!)" -ForegroundColor Cyan

Write-Host "`n🚀 Starting servers..." -ForegroundColor Green
Write-Host "Use Ctrl+C to stop servers" -ForegroundColor Yellow

# Start backend in background
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; .\salonvenv\Scripts\Activate.ps1; python backend\manage.py runserver" -WindowStyle Minimized

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend
Write-Host "Starting React development server..." -ForegroundColor Blue
npm start
