:root {
  --primary-color: #8B4513; /* Saddle Brown - Richer, deeper brown */
  --secondary-color: #A0522D; /* Sienna - Warm, inviting brown */
  --accent-color: #FFD700; /* Gold - Brighter, more luxurious */
  --text-light: #F5F5DC; /* Beige - Softer off-white */
  --text-dark: #2F4F4F; /* Dark Slate Gray - Deeper contrast */
  --bg-gradient-start: #4B0082; /* Indigo - Deeper, more mysterious */
  --bg-gradient-end: #8A2BE2;   /* Blue Violet - Vibrant, elegant */
  --section-spacing: 80px; /* Increased spacing between major sections */
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
  color: var(--text-light);
  overflow-x: hidden;
  font-family: 'Montserrat', sans-serif; /* Uniform font family */
}

/* Universal font family for all elements */
* {
  font-family: 'Montserrat', sans-serif;
}

.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  text-align: center;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column; /* Allow sections to stack */
  justify-content: flex-start; /* Align content to top */
  align-items: center;
  padding: 20px 20px 0 20px;
  width: 100%;
}

.error-text {
  color: #FF6347; /* Tomato - More vibrant error color */
  font-size: 14px;
  margin-top: 8px;
}

/* Custom Styles for Glam Look */

.glam-jumbotron {
  background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
  color: var(--text-light);
  padding: 100px 20px; /* More vertical padding */
  border-radius: 20px; /* Slightly more rounded */
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4); /* Deeper, softer shadow */
  animation: fadeInScale 1.8s ease-out; /* New animation */
  position: relative;
  overflow: hidden;
  margin-bottom: var(--section-spacing);
}

.glam-jumbotron::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.08); /* More visible subtle overlay */
  pointer-events: none;
  z-index: 1;
  border-radius: 20px;
}

.glam-jumbotron h1 {
  font-family: 'Montserrat', sans-serif; /* Uniform font family */
  font-weight: 700;
  color: var(--accent-color);
  text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.6); /* More pronounced shadow */
  margin-bottom: 25px;
  font-size: 3.8rem; /* Larger heading */
}

.glam-jumbotron p {
  font-size: 1.4rem; /* Larger lead text */
  font-weight: 300;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.6;
}

.glam-jumbotron .btn {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--text-dark);
  font-weight: 700; /* Bolder button text */
  padding: 15px 40px; /* Larger button */
  border-radius: 35px; /* More rounded button */
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); /* Smoother transition */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.35);
  margin-top: 30px; /* More space above button */
}

.glam-jumbotron .btn:hover {
  background: #E6B800; /* Slightly darker gold on hover */
  transform: translateY(-5px) scale(1.05); /* More pronounced hover effect */
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.45);
}

.glam-card {
  background: rgba(255, 255, 255, 0.1); /* Slightly less transparent */
  backdrop-filter: blur(12px); /* Stronger frosted glass effect */
  border: 1px solid rgba(255, 255, 255, 0.2); /* More visible border */
  border-radius: 18px; /* Slightly more rounded */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); /* Deeper shadow */
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); /* Smoother transition */
  overflow: hidden;
  position: relative;
}

.glam-card:hover {
  transform: translateY(-8px) scale(1.03); /* More pronounced hover effect */
  box-shadow: 0 18px 45px rgba(0, 0, 0, 0.45);
  border-color: var(--accent-color); /* Highlight border on hover */
}

.glam-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%); /* More visible gradient overlay */
  pointer-events: none;
  border-radius: 18px;
}

.glam-card .card-title {
  font-family: 'Montserrat', sans-serif; /* Uniform font family */
  color: var(--accent-color);
  font-weight: 700;
  font-size: 1.5rem; /* Larger card title */
}

.glam-card .card-text {
  color: rgba(255, 255, 255, 0.85);
  font-size: 1.1rem; /* Slightly larger card text */
}

.glam-contact-card {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  color: var(--text-light);
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  animation: fadeIn 1.5s ease-out;
  margin-top: var(--section-spacing);
  padding: 50px 20px; /* More padding */
}

.glam-contact-card h2 {
  font-family: 'Montserrat', sans-serif; /* Uniform font family */
  color: var(--accent-color);
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
  font-size: 3rem; /* Larger heading */
}

.glam-contact-card p {
  font-size: 1.3rem; /* Larger lead text */
  line-height: 1.6;
}

.glam-contact-card a {
  color: var(--text-light);
  text-decoration: underline;
  transition: color 0.3s ease;
}

.glam-contact-card a:hover {
  color: var(--accent-color);
}

/* Animations */
@keyframes fadeInScale {
  from { opacity: 0; transform: translateY(30px) scale(0.95); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Adjustments for overall layout */
.container {
  max-width: 1300px; /* Slightly wider container */
  padding-left: 15px;
  padding-right: 15px;
}

.row {
  justify-content: center;
}

.list-group-item {
  background-color: transparent !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
  color: var(--text-light) !important;
}

.list-group-item .badge {
  background-color: var(--accent-color) !important;
  color: var(--text-dark) !important;
}

/* Section Headings */
.container h2.display-5, .container h1.display-4 {
  font-family: 'Montserrat', sans-serif; /* Uniform font family */
  color: var(--accent-color);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
  margin-bottom: 40px; /* More space below headings */
  font-size: 2.8rem; /* Larger section headings */
}

/* Filter Section */
.d-flex.justify-content-between.align-items-center.mb-4 {
  margin-top: var(--section-spacing);
  margin-bottom: 40px; /* More space below filter heading */
}

.form-select, .form-control {
  background-color: rgba(255, 255, 255, 0.1); /* Frosted input fields */
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-light);
  padding: 12px 15px;
  border-radius: 10px;
}

.form-select option {
  background-color: var(--secondary-color); /* Dark background for options */
  color: var(--text-light); /* Light text for options */
}

.form-select::placeholder, .form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-select:focus, .form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.25rem rgba(255, 215, 0, 0.25); /* Gold glow on focus */
  color: var(--text-light);
}

.input-group .btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--text-dark);
  font-weight: 600;
  transition: all 0.3s ease;
}

.input-group .btn-primary:hover {
  background-color: #E6B800;
  border-color: #E6B800;
  transform: translateY(-2px);
}

/* Responsive Adjustments - REMOVED - Now handled in responsive.css */
/* All responsive styles have been moved to responsive.css for better organization */

/* Hero Section styles moved to external HeroSection.css file */

/* Modern Find Salons Section Styles */
.find-salons-section {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.15) 0%, rgba(138, 43, 226, 0.15) 100%);
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 40px 0;
  position: relative;
  overflow: hidden;
}

.find-salons-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  border-radius: 25px;
}

.search-filters-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.search-filters-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.3);
}

.filter-group {
  position: relative;
}

.filter-group .form-label {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group .form-select,
.filter-group .form-control {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  color: white !important;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.filter-group .form-select:focus,
.filter-group .form-control:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--accent-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25) !important;
  transform: translateY(-2px);
}

.filter-group .form-select option {
  background: #2c2c2c !important;
  color: white !important;
  padding: 10px;
}

.quick-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.quick-filters .btn-group {
  display: flex;
  gap: 8px;
}

.quick-filters .btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 0.85rem;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quick-filters .btn:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--text-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.glam-btn {
  background: linear-gradient(45deg, var(--accent-color), #FFA500);
  border: none;
  color: var(--text-dark);
  font-weight: 700;
  font-size: 1rem;
  padding: 12px 24px;
  border-radius: 25px;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.glam-btn:hover {
  background: linear-gradient(45deg, #FFA500, var(--accent-color));
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
  color: var(--text-dark);
}

.glam-btn:disabled {
  opacity: 0.6;
  transform: none;
  cursor: not-allowed;
}

/* Responsive adjustments for Find Salons */
@media (max-width: 768px) {
  .find-salons-section {
    margin: 20px 0;
    padding: 20px 15px;
  }
  
  .search-filters-card {
    padding: 20px;
  }
  
  .quick-filters {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .quick-filters .btn-group {
    flex-wrap: wrap;
  }
}

/* Booking Cards Layout */
.booking-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

/* Force horizontal layout for booking cards */
.row.g-3 {
  display: flex !important;
  flex-wrap: wrap !important;
}

.row.g-3 > div {
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure cards are side by side */
.col-6 {
  width: 50% !important;
  float: left !important;
}

.col-lg-3 {
  width: 25% !important;
  float: left !important;
}

@media (max-width: 991px) {
  .col-lg-3 {
    width: 50% !important;
  }
}

/* Debug and ensure horizontal layout */
.booking-cards-container {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 1rem !important;
}

.booking-cards-container > div {
  flex: 0 0 calc(50% - 0.5rem) !important;
  max-width: calc(50% - 0.5rem) !important;
}

@media (min-width: 992px) {
  .booking-cards-container > div {
    flex: 0 0 calc(25% - 0.75rem) !important;
    max-width: calc(25% - 0.75rem) !important;
  }
}

.booking-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  border-color: var(--accent-color);
}

.booking-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.booking-card .card-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.booking-card .card-text p {
  margin-bottom: 0.5rem;
}

.booking-card .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.booking-card .btn {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
}

/* Responsive adjustments for booking cards */
@media (max-width: 767px) {
  .booking-card .card-title {
    font-size: 1rem;
  }
  
  .booking-card .card-text {
    font-size: 0.85rem;
  }
  
  .booking-card .card-text p {
    margin-bottom: 0.25rem;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .booking-card .card-title {
    font-size: 1.05rem;
  }
}