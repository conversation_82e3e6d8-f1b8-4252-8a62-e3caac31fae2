import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';
import Header from './components/Header';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
import Home from './components/Home';
import PremiumTicker from './components/PremiumTicker';
import TrialBanner from './components/TrialBanner';

import PrivateRoute from './components/Auth/PrivateRoute';
import NotificationDisplay from './components/Notifications/NotificationDisplay';
import './components/Modal.css';
import './responsive.css';
import ServiceDetail from './components/ServiceDetail';
import { ResponsiveProvider } from './context/ResponsiveContext';
import BookingConfirm from './components/BookingConfirm';
import VendorCustomers from './components/VendorCustomers';
import AIFeatures from './components/AIFeatures';
import GiftBookingForm from './components/GiftBookingForm';

const BookingForm = lazy(() => import('./components/BookingForm'));
const AISalonMatcher = lazy(() => import('./components/AISalonMatcher'));
const AIStyleAdvisor = lazy(() => import('./components/AIStyleAdvisor'));
const SmartNotifications = lazy(() => import('./components/SmartNotifications'));
const AIVoiceIntegration = lazy(() => import('./components/AIVoiceIntegration'));
const AIAnalyticsDashboard = lazy(() => import('./components/AIAnalyticsDashboard'));
const AITrendPredictor = lazy(() => import('./components/AITrendPredictor'));
const AIVirtualTryOn = lazy(() => import('./components/AIVirtualTryOn'));
const AISmartScheduling = lazy(() => import('./components/AISmartScheduling'));
const AdminDashboard = lazy(() => import('./components/Admin/AdminDashboard'));
const AnalyticsDashboard = lazy(() => import('./components/Admin/AnalyticsDashboard'));
const UserBookings = lazy(() => import('./components/Bookings/UserBookings'));
const ManageBookings = lazy(() => import('./components/Admin/ManageBookings'));
const CalendarView = lazy(() => import('./components/Calendar/CalendarView'));
const MpesaPayment = lazy(() => import('./components/PaymentPages/MpesaPayment'));
const PayPalPayment = lazy(() => import('./components/PaymentPages/PayPalPayment'));
const BankTransferPayment = lazy(() => import('./components/PaymentPages/BankTransferPayment'));
const WisePayment = lazy(() => import('./components/PaymentPages/WisePayment'));
const VisaPayment = lazy(() => import('./components/PaymentPages/VisaPayment'));
const PaystackPayment = lazy(() => import('./components/PaymentPages/PaystackPayment'));
const PaystackInlinePayment = lazy(() => import('./components/PaymentPages/PaystackInlinePayment'));
const VendorSubscription = lazy(() => import('./components/VendorSubscription'));
const VendorPayment = lazy(() => import('./components/PaymentPages/VendorPayment'));
const TrialStart = lazy(() => import('./components/TrialStart'));
const Login = lazy(() => import('./components/Auth/Login'));
const Signup = lazy(() => import('./components/Auth/Signup'));
const SalonDetail = lazy(() => import('./components/SalonDetail'));

const BookingSuccess = lazy(() => import('./components/BookingSuccess'));
const CheckoutSuccess = lazy(() => import('./components/CheckoutSuccess'));
const PaymentFailed = lazy(() => import('./components/PaymentFailed'));
const SearchResults = lazy(() => import('./components/SearchResults'));
const Messages = lazy(() => import('./components/Messages'));
const VendorProfileEdit = lazy(() => import('./components/Auth/VendorProfileEdit'));
const UserProfileEdit = lazy(() => import('./components/Auth/UserProfileEdit'));
const RegisterVendor = lazy(() => import('./components/Auth/RegisterVendor'));
const PrivacyPolicy = lazy(() => import('./components/Legal/PrivacyPolicy'));
const TermsOfService = lazy(() => import('./components/Legal/TermsOfService'));
const CookiePolicy = lazy(() => import('./components/Legal/CookiePolicy'));

const LoadingSpinner = () => (
  <div className="d-flex justify-content-center align-items-center" style={{ height: '80vh' }}>
    <div className="spinner-border text-light" role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  </div>
);

function App() {
  return (
    <ResponsiveProvider>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <div className="App">
          <TrialBanner />
          <Header />
          <NotificationDisplay />
          <main>
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/ai-features" element={<AIFeatures />} />
                <Route path="/ai-salon-matcher" element={<AISalonMatcher />} />
                <Route path="/ai-style-advisor" element={<AIStyleAdvisor />} />
                <Route path="/smart-notifications" element={<SmartNotifications />} />
                <Route path="/ai-voice-integration" element={<AIVoiceIntegration />} />
                <Route path="/ai-analytics-dashboard" element={<AIAnalyticsDashboard />} />
                <Route path="/ai-trend-predictor" element={<AITrendPredictor />} />
                <Route path="/ai-virtual-tryon" element={<AIVirtualTryOn />} />
                <Route path="/ai-smart-scheduling" element={<AISmartScheduling />} />
                <Route path="/book" element={<PrivateRoute><BookingForm /></PrivateRoute>} />
                <Route path="/booking-form" element={<PrivateRoute><BookingForm /></PrivateRoute>} />
                <Route path="/gift-book" element={<PrivateRoute><GiftBookingForm /></PrivateRoute>} />
                <Route path="/gift-booking" element={<PrivateRoute><GiftBookingForm /></PrivateRoute>} />
                <Route path="/admin" element={<PrivateRoute adminOnly={true}><AdminDashboard /></PrivateRoute>} />
                <Route path="/admin/analytics" element={<PrivateRoute adminOnly={true}><AnalyticsDashboard /></PrivateRoute>} />
                <Route path="/my-bookings" element={<PrivateRoute><UserBookings /></PrivateRoute>} />
                <Route path="/admin/bookings" element={<PrivateRoute><ManageBookings /></PrivateRoute>} />
                <Route path="/calendar" element={<CalendarView />} />
                <Route path="/payment/mpesa" element={<PrivateRoute><MpesaPayment /></PrivateRoute>} />
                <Route path="/payment/paypal" element={<PrivateRoute><PayPalPayment /></PrivateRoute>} />
                <Route path="/payment/bank" element={<PrivateRoute><BankTransferPayment /></PrivateRoute>} />
                <Route path="/payment/wise" element={<PrivateRoute><WisePayment /></PrivateRoute>} />
                <Route path="/payment/visa" element={<PrivateRoute><VisaPayment /></PrivateRoute>} />
                <Route path="/payment/paystack" element={<PrivateRoute><PaystackPayment /></PrivateRoute>} />
                <Route path="/payment/paystack-inline" element={<PrivateRoute><PaystackInlinePayment /></PrivateRoute>} />
                <Route path="/payment/vendor-subscription" element={<VendorPayment />} />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/salon/:id" element={<SalonDetail />} />

                <Route path="/checkout-success" element={<PrivateRoute><CheckoutSuccess /></PrivateRoute>} />
                <Route path="/payment-failed" element={<PrivateRoute><PaymentFailed /></PrivateRoute>} />
                <Route path="/booking-success" element={<BookingSuccess />} />
                <Route path="/vendor/profile" element={<PrivateRoute><VendorProfileEdit /></PrivateRoute>} />
                <Route path="/service/:id" element={<ServiceDetail />} />
                <Route path="/profile" element={<PrivateRoute><UserProfileEdit /></PrivateRoute>} />
                <Route path="/booking-confirm" element={<BookingConfirm />} />
                <Route path="/vendor/customers" element={<VendorCustomers />} />
                <Route path="/vendor/subscription" element={<VendorSubscription />} />
                <Route path="/vendor/trial/start" element={<PrivateRoute><TrialStart /></PrivateRoute>} />
                <Route path="/search" element={<SearchResults />} />
                <Route path="/messages" element={<PrivateRoute><Messages /></PrivateRoute>} />
                <Route path="/vendor-signup" element={<RegisterVendor />} />
                <Route path="/register-vendor" element={<RegisterVendor />} />
                <Route path="/privacy" element={<PrivacyPolicy />} />
                <Route path="/terms" element={<TermsOfService />} />
                <Route path="/policy" element={<CookiePolicy />} />
              </Routes>
            </Suspense>
          </main>
          <Footer />
          <ScrollToTop />
        </div>
      </Router>
    </ResponsiveProvider>
  );
}

export default App;
