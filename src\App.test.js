import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';
import AuthContext from './context/AuthContext';
import NotificationContext from './context/NotificationContext';
import ResponsiveContext from './context/ResponsiveContext';

test('renders SalonGenz in the header', () => {
  render(
    <AuthContext.Provider value={{ user: { name: 'Test User' }, logout: jest.fn() }}>
      <NotificationContext.Provider value={{ notification: null, showNotification: jest.fn() }}>
        <ResponsiveContext.Provider value={{ isMobile: false }}>
          <App />
        </ResponsiveContext.Provider>
      </NotificationContext.Provider>
    </AuthContext.Provider>
  );
  const headerElements = screen.getAllByText(/SalonGenz/i);
  expect(headerElements.length).toBeGreaterThan(0);
});
