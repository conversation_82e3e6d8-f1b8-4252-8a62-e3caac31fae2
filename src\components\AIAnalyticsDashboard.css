/* ===== AI Analytics Dashboard - Enterprise Gen Z Design ===== */

.ai-analytics-dashboard-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.analytics-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.analytics-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: analyticsFloat 20s ease-in-out infinite;
}

.analytics-orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  top: 10%;
  left: -22%;
  animation-delay: 0s;
}

.analytics-orb-2 {
  width: 240px;
  height: 240px;
  background: linear-gradient(135deg, #2196f3, #03a9f4);
  top: 85%;
  right: -20%;
  animation-delay: 9s;
}

.analytics-orb-3 {
  width: 260px;
  height: 260px;
  background: linear-gradient(135deg, #ff9800, #ffc107);
  bottom: 40%;
  left: 35%;
  animation-delay: 16s;
}

@keyframes analyticsFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  10% { transform: translateY(-45px) rotate(36deg) scale(1.3); }
  20% { transform: translateY(35px) rotate(72deg) scale(0.7); }
  30% { transform: translateY(-30px) rotate(108deg) scale(1.15); }
  40% { transform: translateY(40px) rotate(144deg) scale(0.8); }
  50% { transform: translateY(-35px) rotate(180deg) scale(1.2); }
  60% { transform: translateY(25px) rotate(216deg) scale(0.85); }
  70% { transform: translateY(-40px) rotate(252deg) scale(1.1); }
  80% { transform: translateY(30px) rotate(288deg) scale(0.9); }
  90% { transform: translateY(-25px) rotate(324deg) scale(1.05); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(76, 175, 80, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Modern Header */
.analytics-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.analytics-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.analytics-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #4caf50;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.analytics-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #4caf50, #2196f3, #ff9800);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: analyticsGradientShift 10s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: analyticsSparkle 5s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes analyticsGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes analyticsSparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.9) rotate(180deg); opacity: 0.1; }
}

.analytics-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

.back-button:hover {
  background: rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #4caf50;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.analytics-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.analytics-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analytics-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 20px 0;
}

.analytics-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.enhanced-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1.2rem 2rem;
  border-radius: 18px;
  background: rgba(255,255,255,0.04);
  box-shadow: 0 4px 32px 0 rgba(76,175,80,0.08), 0 1.5px 8px 0 rgba(33,150,243,0.08);
  backdrop-filter: blur(12px);
  border: 1.5px solid rgba(255,255,255,0.08);
}
.time-range-select-wrapper {
  position: relative;
  display: inline-block;
}
.enhanced-select {
  appearance: none;
  background: rgba(255,255,255,0.12);
  border: none;
  border-radius: 999px;
  padding: 0.7rem 2.5rem 0.7rem 1.2rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #f0f6fc;
  box-shadow: 0 2px 12px 0 rgba(33,150,243,0.10);
  outline: none;
  transition: box-shadow 0.2s, background 0.2s;
}
.enhanced-select:focus {
  box-shadow: 0 0 0 2px #4caf50, 0 2px 12px 0 rgba(33,150,243,0.10);
  background: rgba(76,175,80,0.13);
}
.dropdown-icon {
  position: absolute;
  right: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  color: #8bc34a;
  pointer-events: none;
  transition: color 0.2s;
}
.enhanced-refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.7rem 1.7rem;
  border-radius: 999px;
  background: linear-gradient(90deg, #4caf50 0%, #2196f3 100%);
  color: #fff;
  font-weight: 700;
  font-size: 1.1rem;
  border: none;
  box-shadow: 0 2px 16px 0 rgba(33,150,243,0.13);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
}
.enhanced-refresh-btn:not(:disabled):hover {
  background: linear-gradient(90deg, #2196f3 0%, #4caf50 100%);
  box-shadow: 0 4px 24px 0 rgba(76,175,80,0.18);
  transform: translateY(-2px) scale(1.04);
}
.enhanced-refresh-btn:active {
  transform: scale(0.98);
}
.enhanced-refresh-btn.loading {
  opacity: 0.7;
  pointer-events: none;
}
.refresh-spinner {
  width: 1.3em;
  height: 1.3em;
  border: 3px solid #fff;
  border-top: 3px solid #4caf50;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  display: inline-block;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.time-range-select {
  padding: 10px 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.time-range-select option {
  background: #333;
  color: white;
}

.refresh-btn {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Analytics Tabs */
.analytics-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.tab {
  color: #fff !important;
  font-weight: 700;
  background: none;
  border: none;
  font-size: 1.15rem;
  padding: 0.8rem 2.2rem;
  border-radius: 999px;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: none;
  position: relative;
  z-index: 1;
}
.tab .tab-emoji {
  font-size: 1.3em;
  margin-right: 0.5em;
  filter: drop-shadow(0 1px 4px rgba(255,255,255,0.25));
}
.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.tab.active {
  background: linear-gradient(90deg, #fff 0%, #e3f9e5 100%);
  color: #222 !important;
  box-shadow: 0 2px 12px 0 rgba(255,255,255,0.12);
}
.tab.active .tab-emoji {
  filter: drop-shadow(0 2px 8px #4caf50) brightness(1.2);
}

/* Analytics Content */
.analytics-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Overview Section */
.overview-section {
  max-width: 1000px;
  margin: 0 auto;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20px;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.metric-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.metric-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.metric-value {
  margin: 0 0 5px 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}

.metric-growth {
  font-size: 0.9rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 15px;
}

.metric-growth.positive {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.metric-growth.negative {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.top-services {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.top-services h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.service-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.1rem;
}

.service-info p {
  margin: 0;
  color: #666;
  font-weight: 600;
}

.service-growth {
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
}

.service-growth.positive {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

/* Insights Section */
.insights-section {
  max-width: 1000px;
  margin: 0 auto;
}

.insights-summary {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  margin-bottom: 30px;
}

.insights-summary h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.3rem;
}

.insights-summary p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.insight-category h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
}

.insight-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.insight-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
}

.insight-card.anomaly {
  border-left: 4px solid #F44336;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.insight-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.impact-badge,
.severity-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.insight-card p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.recommendation,
.suggestion {
  background: rgba(102, 126, 234, 0.1);
  padding: 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #333;
}

/* Predictions Section */
.predictions-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.predictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.prediction-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.prediction-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.prediction-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.confidence-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.prediction-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin: 0 0 10px 0;
}

.prediction-reasoning {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.recommendations-section {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.recommendations-section h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-card {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #667eea;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.recommendation-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.priority-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.recommendation-card p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.expected-impact {
  background: rgba(255, 193, 7, 0.1);
  padding: 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #333;
}

/* Behavior Section */
.behavior-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.behavior-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.behavior-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.behavior-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.behavior-card h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
}

.pattern-item,
.segment-item,
.preference-item,
.frequency-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.pattern-item:last-child,
.segment-item:last-child,
.preference-item:last-child,
.frequency-item:last-child {
  border-bottom: none;
}

.pattern-item span:first-child,
.segment-item span:first-child,
.preference-item span:first-child,
.frequency-item span:first-child {
  font-weight: 600;
  color: #333;
}

.pattern-item span:last-child,
.segment-item span:last-child,
.preference-item span:last-child,
.frequency-item span:last-child {
  color: #667eea;
  font-weight: 600;
}

.preferences-list,
.frequency-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dist-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.dist-item span:first-child {
  font-weight: 600;
  color: #333;
}

.dist-item span:last-child {
  color: #667eea;
  font-weight: 600;
}

/* 1. Pro badge and shimmer/glow */
/* 2. Glassmorphic cards and header */
/* 3. Animated emoji badges for tabs */
/* 4. Confetti/sparkle animation */
/* 5. Floating action button, Share/Download buttons */
/* 6. Pro Tips, Trends carousel, Compare Periods toggle */
/* 7. Responsive and accessible enhancements */

/* Responsive Design */
@media (max-width: 768px) {
  .ai-analytics-dashboard {
    padding: 15px;
  }
  
  .analytics-header {
    padding: 20px;
  }
  
  .analytics-header h2 {
    font-size: 2rem;
  }
  
  .analytics-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .analytics-tabs {
    gap: 8px;
  }
  
  .tab {
    padding: 10px 16px;
    font-size: 0.8rem;
  }
  
  .analytics-content {
    padding: 20px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .predictions-grid {
    grid-template-columns: 1fr;
  }
  
  .behavior-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-card {
    padding: 20px;
  }
  
  .insight-card {
    padding: 15px;
  }
  
  .prediction-card {
    padding: 20px;
  }
  
  .behavior-card {
    padding: 20px;
  }

  .enhanced-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem 0.5rem;
    margin-bottom: 1.2rem;
  }
  .time-range-select-wrapper {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  .enhanced-select {
    width: 100%;
    font-size: 1rem;
    padding: 0.7rem 2.5rem 0.7rem 1.2rem;
  }
  .dropdown-icon {
    right: 1.2rem;
    font-size: 1.1rem;
  }
  .enhanced-refresh-btn {
    width: 100%;
    justify-content: center;
    font-size: 1rem;
    padding: 0.7rem 0;
  }
  .compare-toggle {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .analytics-header h2 {
    font-size: 1.8rem;
  }
  
  .analytics-header p {
    font-size: 1rem;
  }
  
  .metric-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .metric-icon {
    font-size: 2rem;
  }
  
  .prediction-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .recommendation-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .enhanced-controls {
    padding: 0.7rem 0.2rem;
    gap: 0.7rem;
  }
  .enhanced-select {
    font-size: 0.98rem;
    padding: 0.6rem 2.2rem 0.6rem 1rem;
  }
  .enhanced-refresh-btn {
    font-size: 0.98rem;
    padding: 0.6rem 0;
  }
  .compare-toggle {
    font-size: 0.98rem;
    margin-top: 0.3rem;
  }
} 