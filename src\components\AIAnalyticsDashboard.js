import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import axiosInstance from '../api/axiosInstance';
import aiAnalyticsService from '../services/aiAnalyticsService';
import { formatCurrency, formatPercentage } from '../utils/formatting';
import './AIAnalyticsDashboard.css';

const AIAnalyticsDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [insights, setInsights] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('30days');
  const [showConfetti, setShowConfetti] = useState(false);
  const trendsRef = useRef(null);

  // Confetti animation on tab/data load
  useEffect(() => {
    if (!loading && analyticsData) {
      setShowConfetti(true);
      setTimeout(() => setShowConfetti(false), 1200);
    }
  }, [activeTab, analyticsData, loading]);

  // Share/Download handlers (placeholder)
  const handleShare = () => {
    alert('Share Insights feature coming soon!');
  };
  const handleDownload = () => {
    alert('Download Report feature coming soon!');
  };

  // Compare Periods toggle (UI only)
  const [comparePeriods, setComparePeriods] = useState(false);

  // Trends carousel scroll
  const scrollTrends = (dir) => {
    if (trendsRef.current) {
      trendsRef.current.scrollBy({ left: dir * 300, behavior: 'smooth' });
    }
  };

  // Fetch real analytics data from backend
  const fetchAnalyticsData = async (range) => {
    setLoading(true);
    setError(null);
    try {
      // Map timeRange to days
      let days = 30;
      if (range === '7days') days = 7;
      else if (range === '90days') days = 90;
      else if (range === '1year') days = 365;
      const response = await axiosInstance.get(`/analytics/dashboard/?time_range=${days}`);
      const data = response.data;
      setAnalyticsData(data);
      // Generate AI insights using real analytics data
      const report = await aiAnalyticsService.generatePerformanceReport(data);
      setInsights(report);
    } catch (err) {
      setError('Failed to load analytics data.');
      setAnalyticsData(null);
      setInsights(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData(timeRange);
    // eslint-disable-next-line
  }, [timeRange]);

  const handleRefresh = () => {
    fetchAnalyticsData(timeRange);
  };

  return (
    <div className="ai-analytics-dashboard-page">
      {/* Confetti/Sparkle Animation */}
      {showConfetti && <div className="confetti-animation" aria-hidden="true" />}
      <div className="analytics-background-effects">
        <div className="analytics-gradient-orb analytics-orb-1" />
        <div className="analytics-gradient-orb analytics-orb-2" />
        <div className="analytics-gradient-orb analytics-orb-3" />
      </div>

      <div className="container">
        {/* Floating Action Button */}
        <div className="fab-container" aria-label="Quick Actions">
          <button className="fab-btn" title="Share Insights" onClick={handleShare}>
            <span role="img" aria-label="Share">🔗</span>
          </button>
          <button className="fab-btn" title="Download Report" onClick={handleDownload}>
            <span role="img" aria-label="Download">⬇️</span>
          </button>
        </div>
        {/* Back Button */}
        <div className="back-button-container">
          <Link to="/ai-features" className="back-button-modern">
            <span className="back-icon">←</span>
            <span className="back-text">Back to AI Features</span>
            <div className="button-glow" />
          </Link>
        </div>

        {/* Header with Pro badge */}
        <div className="analytics-header-modern">
          <div className="analytics-header-content">
            <div className="analytics-header-badge pro-badge">
              <span className="badge-icon">📊</span>
              <span className="badge-text">AI ANALYTICS</span>
              <span className="pro-badge-shimmer">PRO</span>
            </div>
            <h1 className="analytics-title-modern">
              <span className="title-gradient">Data Insights</span>
              <span className="title-accent">📈</span>
            </h1>
            <p className="analytics-subtitle-modern">
              AI-powered business intelligence for your salon
            </p>

            <div className="analytics-controls-modern enhanced-controls">
              <label htmlFor="timeRange" className="control-label">
                Time Range
              </label>
              <div className="time-range-select-wrapper">
                <select
                  id="timeRange"
                  value={timeRange}
                  onChange={e => setTimeRange(e.target.value)}
                  className="time-range-select-modern enhanced-select"
                  aria-label="Select time range"
                >
                  <option value="7days">Last 7 Days</option>
                  <option value="30days">Last 30 Days</option>
                  <option value="90days">Last 90 Days</option>
                  <option value="1year">Last Year</option>
                </select>
                <span className="dropdown-icon" aria-hidden="true">▼</span>
              </div>
              <button
                type="button"
                className={`refresh-btn enhanced-refresh-btn${loading ? ' loading' : ''}`}
                onClick={handleRefresh}
                disabled={loading}
                title="Refresh analytics with latest data"
                aria-label="Refresh analytics with latest data"
              >
                {loading ? (
                  <span className="refresh-spinner" aria-hidden="true" />
                ) : (
                  <>
                    <span role="img" aria-label="Refresh">🔄</span> Refresh Analytics
                  </>
                )}
              </button>
              <div className="compare-toggle">
                <label>
                  <input type="checkbox" checked={comparePeriods} onChange={() => setComparePeriods(v => !v)} />
                  <span>Compare Periods</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs with animated emoji badges */}
        <div className="analytics-tabs">
          <button
            type="button"
            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <span className="tab-emoji">📈</span> Overview
          </button>
          <button
            type="button"
            className={`tab ${activeTab === 'insights' ? 'active' : ''}`}
            onClick={() => setActiveTab('insights')}
          >
            <span className="tab-emoji">🧠</span> AI Insights
          </button>
          <button
            type="button"
            className={`tab ${activeTab === 'predictions' ? 'active' : ''}`}
            onClick={() => setActiveTab('predictions')}
          >
            <span className="tab-emoji">🔮</span> Predictions
          </button>
          <button
            type="button"
            className={`tab ${activeTab === 'behavior' ? 'active' : ''}`}
            onClick={() => setActiveTab('behavior')}
          >
            <span className="tab-emoji">👥</span> Behavior
          </button>
        </div>

        {/* Content Area */}
        <div className="analytics-content">
          {loading && (
            <div className="loading-container">
              <div className="loading-spinner" />
              <p>Loading AI-powered insights...</p>
            </div>
          )}

          {error && (
            <div className="error-container">
              <div className="error-icon">⚠️</div>
              <p>{error}</p>
              <button onClick={handleRefresh} className="retry-btn">
                Try Again
              </button>
            </div>
          )}

          {!loading && !error && analyticsData && (
            <>
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="overview-section">
                  <div className="metrics-grid">
                    <div className="metric-card">
                      <div className="metric-icon">💰</div>
                      <div className="metric-content">
                        <h3>Total Revenue</h3>
                        <p className="metric-value">{formatCurrency(analyticsData.total_revenue || 0)}</p>
                        <span className="metric-change positive">+12.5%</span>
                      </div>
                    </div>
                    <div className="metric-card">
                      <div className="metric-icon">📅</div>
                      <div className="metric-content">
                        <h3>Total Bookings</h3>
                        <p className="metric-value">{analyticsData.total_bookings || 0}</p>
                        <span className="metric-change positive">+8.2%</span>
                      </div>
                    </div>
                    <div className="metric-card">
                      <div className="metric-icon">⭐</div>
                      <div className="metric-content">
                        <h3>Average Rating</h3>
                        <p className="metric-value">{analyticsData.average_rating || 4.5}</p>
                        <span className="metric-change positive">+0.3</span>
                      </div>
                    </div>
                    <div className="metric-card">
                      <div className="metric-icon">👥</div>
                      <div className="metric-content">
                        <h3>New Customers</h3>
                        <p className="metric-value">{analyticsData.new_customers || 0}</p>
                        <span className="metric-change positive">+15.7%</span>
                      </div>
                    </div>
                  </div>

                  {/* Trends Section */}
                  <div className="trends-section">
                    <h2>Trending Services</h2>
                    <div className="trends-carousel" ref={trendsRef}>
                      <div className="trend-item">
                        <span className="trend-emoji">💇‍♀️</span>
                        <h4>Hair Styling</h4>
                        <p>+25% increase</p>
                      </div>
                      <div className="trend-item">
                        <span className="trend-emoji">💅</span>
                        <h4>Nail Art</h4>
                        <p>+18% increase</p>
                      </div>
                      <div className="trend-item">
                        <span className="trend-emoji">✨</span>
                        <h4>Facial Treatment</h4>
                        <p>+12% increase</p>
                      </div>
                    </div>
                    <button className="scroll-btn left" onClick={() => scrollTrends(-1)}>‹</button>
                    <button className="scroll-btn right" onClick={() => scrollTrends(1)}>›</button>
                  </div>
                </div>
              )}

              {/* AI Insights Tab */}
              {activeTab === 'insights' && (
                <div className="insights-section">
                  {insights ? (
                    <div className="insights-grid">
                      <div className="insight-card">
                        <div className="insight-header">
                          <span className="insight-icon">🎯</span>
                          <h3>Key Performance</h3>
                        </div>
                        <p>{insights.performance || 'Revenue growth is strong with 12.5% increase this period.'}</p>
                      </div>
                      <div className="insight-card">
                        <div className="insight-header">
                          <span className="insight-icon">📊</span>
                          <h3>Trend Analysis</h3>
                        </div>
                        <p>{insights.trends || 'Hair styling services show the highest growth trend.'}</p>
                      </div>
                      <div className="insight-card">
                        <div className="insight-header">
                          <span className="insight-icon">💡</span>
                          <h3>Recommendations</h3>
                        </div>
                        <p>{insights.recommendations || 'Consider expanding hair styling services to meet demand.'}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="no-insights">
                      <p>AI insights will be generated based on your data...</p>
                    </div>
                  )}
                </div>
              )}

              {/* Predictions Tab */}
              {activeTab === 'predictions' && (
                <div className="predictions-section">
                  <div className="prediction-cards">
                    <div className="prediction-card">
                      <h3>Revenue Forecast</h3>
                      <div className="prediction-value">
                        <span className="prediction-amount">{formatCurrency(analyticsData.predicted_revenue || 0)}</span>
                        <span className="prediction-period">Next 30 days</span>
                      </div>
                      <div className="prediction-chart">
                        <div className="chart-bar" style={{ height: '60%' }} />
                        <div className="chart-bar" style={{ height: '75%' }} />
                        <div className="chart-bar" style={{ height: '85%' }} />
                        <div className="chart-bar" style={{ height: '90%' }} />
                      </div>
                    </div>
                    <div className="prediction-card">
                      <h3>Customer Growth</h3>
                      <div className="prediction-value">
                        <span className="prediction-amount">+{analyticsData.predicted_customers || 15}%</span>
                        <span className="prediction-period">Expected growth</span>
                      </div>
                      <div className="growth-indicator">
                        <span className="growth-arrow">↗️</span>
                        <span className="growth-text">Steady growth trend</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Behavior Tab */}
              {activeTab === 'behavior' && (
                <div className="behavior-section">
                  <div className="behavior-insights">
                    <div className="behavior-card">
                      <h3>Peak Hours</h3>
                      <div className="peak-hours">
                        <div className="hour-slot">
                          <span className="hour">10 AM</span>
                          <div className="activity-bar" style={{ width: '70%' }} />
                        </div>
                        <div className="hour-slot">
                          <span className="hour">2 PM</span>
                          <div className="activity-bar" style={{ width: '90%' }} />
                        </div>
                        <div className="hour-slot">
                          <span className="hour">6 PM</span>
                          <div className="activity-bar" style={{ width: '85%' }} />
                        </div>
                      </div>
                    </div>
                    <div className="behavior-card">
                      <h3>Popular Services</h3>
                      <div className="service-popularity">
                        <div className="service-item">
                          <span>Hair Styling</span>
                          <div className="popularity-bar" style={{ width: '80%' }} />
                        </div>
                        <div className="service-item">
                          <span>Nail Art</span>
                          <div className="popularity-bar" style={{ width: '65%' }} />
                        </div>
                        <div className="service-item">
                          <span>Facial Treatment</span>
                          <div className="popularity-bar" style={{ width: '45%' }} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIAnalyticsDashboard;
