jest.mock('axios', () => ({
  create: () => ({
    get: jest.fn(),
    post: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  }),
}));

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AIAnalyticsDashboard from './AIAnalyticsDashboard';
import axiosInstance from '../api/axiosInstance';
import { MemoryRouter } from 'react-router-dom';

jest.mock('../api/axiosInstance');

const mockAnalyticsData = {
  total_salons: 10,
  total_bookings: 100,
  total_revenue: 5000,
  recent_bookings: 20,
  popular_services: [
    { 'service__name': 'Haircut', count: 40 },
    { 'service__name': 'Color', count: 30 }
  ],
  top_salons: [
    { 'salon__name': 'Salon A', revenue: 2000, bookings: 30 },
    { 'salon__name': 'Salon B', revenue: 1500, bookings: 25 }
  ]
};

const mockInsights = {
  insights: { summary: 'Great performance', trends: [], predictions: [], recommendations: [], anomalies: [] },
  behavior: { preferredTimes: ['10:00', '14:00'], servicePreferences: [['Haircut', 40]], bookingFrequency: { average: 2, distribution: { '1-2 times': 5 } }, seasonalTrends: {}, customerSegments: {} },
  predictions: { revenuePrediction: { nextMonth: '$6000' } },
  generatedAt: new Date().toISOString()
};

jest.mock('../services/aiAnalyticsService', () => ({
  __esModule: true,
  default: {
    generatePerformanceReport: jest.fn(() => Promise.resolve(mockInsights))
  }
}));

describe('AIAnalyticsDashboard', () => {
  beforeEach(() => {
    axiosInstance.get.mockResolvedValue({ data: mockAnalyticsData });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders all main tabs', async () => {
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    expect(screen.getByText(/Overview/i)).toBeInTheDocument();
    expect(screen.getByText(/AI Insights/i)).toBeInTheDocument();
    expect(screen.getByText(/Predictions/i)).toBeInTheDocument();
    expect(screen.getByText(/Behavior/i)).toBeInTheDocument();
    await waitFor(() => expect(screen.getByText(/Data Insights/i)).toBeInTheDocument());
  });

  it('loads and displays analytics data', async () => {
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    await waitFor(() => expect(screen.getByText(/Data Insights/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Total Revenue/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Total Bookings/i)).toBeInTheDocument());
  });

  it('shows loading spinner while fetching', async () => {
    axiosInstance.get.mockImplementation(() => new Promise(() => {}));
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    expect(screen.getByLabelText(/Refresh analytics with latest data/i)).toBeDisabled();
  });

  it('handles error state', async () => {
    axiosInstance.get.mockRejectedValue(new Error('401'));
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    await waitFor(() => expect(screen.getByText(/Failed to load analytics data/i)).toBeInTheDocument());
  });

  it('switches tabs and shows correct content', async () => {
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    await waitFor(() => expect(screen.getByText(/Data Insights/i)).toBeInTheDocument());
    fireEvent.click(screen.getByText(/AI Insights/i));
    expect(screen.getByText(/Great performance/)).toBeInTheDocument();
    fireEvent.click(screen.getByText(/Predictions/i));
    expect(screen.getByText(/Predictions/i)).toBeInTheDocument();
    fireEvent.click(screen.getByText(/Behavior/i));
    expect(screen.getByText(/Haircut/i)).toBeInTheDocument();
  });

  it('handles unauthenticated (401) gracefully', async () => {
    axiosInstance.get.mockRejectedValue({ response: { status: 401 } });
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    await waitFor(() => expect(screen.getByText(/Failed to load analytics data/i)).toBeInTheDocument());
  });

  it('is responsive: controls visible on mobile', async () => {
    window.innerWidth = 375;
    window.dispatchEvent(new Event('resize'));
    render(
      <MemoryRouter>
        <AIAnalyticsDashboard />
      </MemoryRouter>
    );
    await waitFor(() => expect(screen.getByText(/Time Range/i)).toBeInTheDocument());
    expect(screen.getByLabelText(/Select time range/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Refresh analytics with latest data/i)).toBeInTheDocument();
  });
}); 