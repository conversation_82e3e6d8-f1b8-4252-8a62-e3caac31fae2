/* ===== AI Features Page - Enterprise Gen Z Design ===== */

.ai-features-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.ai-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.ai-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.ai-orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.ai-orb-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.ai-orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(255, 107, 157, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Header Section */
.ai-header {
  margin-bottom: 2rem;
}

.ai-header-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: float 3s ease-in-out infinite;
}

.ai-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ai-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

/* Stats Section */
.ai-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #FFD700;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Modern Header Styles */
.ai-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.ai-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.ai-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 107, 157, 0.1);
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #ff6b9d;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.ai-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #ff6b9d, #667eea, #ffeaa7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: sparkle 2s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

.ai-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

.ai-stats-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.1);
}

.stat-card .stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.stat-card .stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #f0f6fc;
  display: block;
  margin-bottom: 0.25rem;
}

.stat-card .stat-label {
  font-size: 0.85rem;
  color: #8b949e;
  font-weight: 500;
}

/* Modern Tabs */
.ai-tabs-modern {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.ai-tab-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0;
  color: #8b949e;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.ai-tab-modern .tab-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  position: relative;
  z-index: 2;
}

.ai-tab-modern .tab-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #ff6b9d, #667eea);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.ai-tab-modern:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 107, 157, 0.3);
  color: #f0f6fc;
}

.ai-tab-modern.active {
  background: rgba(255, 107, 157, 0.1);
  border-color: rgba(255, 107, 157, 0.3);
  color: #ff6b9d;
}

.ai-tab-modern.active .tab-indicator {
  transform: scaleX(1);
}

/* Focused Pagination Grid - 3 desktop, 1 mobile */
.ai-features-grid-modern {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Mobile: 1 column for focused viewing */
@media (max-width: 768px) {
  .ai-features-grid-modern {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Ensure consistent 3-column layout on larger screens */
@media (min-width: 1200px) {
  .ai-features-grid-modern {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.ai-feature-card-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
  min-height: 120px;
}

.ai-feature-card-modern .card-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.1), transparent);
  transition: left 0.5s ease;
}

.ai-feature-card-modern:hover .card-glow {
  left: 100%;
}

.ai-feature-card-modern:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.5rem;
}

.feature-icon-modern {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.feature-status-modern {
  background: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.65rem;
  font-weight: 600;
  border: 1px solid rgba(46, 204, 113, 0.3);
  margin-bottom: 0.5rem;
}

.feature-content {
  flex: 1;
  margin-bottom: 0.5rem;
}

.feature-title-modern {
  font-size: 0.9rem;
  font-weight: 700;
  color: #f0f6fc;
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.feature-description-modern {
  color: #8b949e;
  line-height: 1.3;
  font-size: 0.7rem;
}

.feature-link-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b9d, #667eea);
  color: white;
  text-decoration: none;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.65rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-top: auto;
}

.feature-link-modern .link-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.feature-link-modern:hover .link-glow {
  left: 100%;
}

.feature-link-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
  text-decoration: none;
  color: white;
}

.link-arrow {
  transition: transform 0.3s ease;
}

.feature-link-modern:hover .link-arrow {
  transform: translateX(3px);
}

/* Sleek Level Up Section */
.level-up-section {
  text-align: center;
  margin-top: 2rem;
}

.level-up-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f0f6fc;
  margin-bottom: 0.5rem;
}

.level-up-subtitle {
  font-size: 0.9rem;
  color: #8b949e;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.level-up-cards {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.level-up-card {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.level-up-card.primary {
  background: linear-gradient(135deg, #ff6b9d, #667eea);
  color: white;
}

.level-up-card.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.level-up-card:hover {
  transform: translateY(-2px);
  text-decoration: none;
}

.level-up-card.primary:hover {
  box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
  color: white;
}

.level-up-card.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 107, 157, 0.3);
}

.card-icon {
  font-size: 1rem;
}

.card-text {
  font-size: 0.85rem;
  font-weight: 600;
}

/* Mobile Responsiveness for Level Up Section */
@media (max-width: 768px) {
  .level-up-title {
    font-size: 1.2rem;
  }

  .level-up-subtitle {
    font-size: 0.85rem;
    margin-bottom: 1.25rem;
  }

  .level-up-cards {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .level-up-card {
    width: 100%;
    max-width: 280px;
    padding: 0.9rem 1rem;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .level-up-title {
    font-size: 1.1rem;
  }

  .level-up-subtitle {
    font-size: 0.8rem;
    margin-bottom: 1rem;
  }

  .level-up-card {
    max-width: 260px;
    padding: 0.8rem 0.9rem;
    font-size: 0.8rem;
  }

  .card-icon {
    font-size: 0.9rem;
  }

  .card-text {
    font-size: 0.8rem;
  }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .ai-orb-1, .ai-orb-2, .ai-orb-3 {
    width: 150px;
    height: 150px;
    filter: blur(40px);
  }

  .container {
    padding: 16px;
  }

  .ai-title-modern {
    font-size: 2.5rem;
  }

  .ai-subtitle-modern {
    font-size: 1rem;
  }

  .ai-stats-modern {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .ai-tabs-modern {
    gap: 0.25rem;
  }

  .ai-tab-modern .tab-content {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }



  .ai-feature-card-modern {
    padding: 1.5rem;
    min-height: 200px;
    border-radius: 16px;
  }

  .feature-icon-modern {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
  }

  .feature-title-modern {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .feature-description-modern {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1rem;
  }

  .feature-link-modern {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    border-radius: 10px;
  }

  .feature-status-modern {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
  }

  .feature-header {
    margin-bottom: 1rem;
  }

  .feature-content {
    margin-bottom: 1.5rem;
  }



  .cta-primary, .cta-secondary {
    width: 100%;
    max-width: 300px;
    justify-content: center;
    padding: 1.2rem 2rem;
    font-size: 1rem;
    min-height: 56px;
  }

  .cta-icon {
    font-size: 1.2rem;
  }

  .cta-text {
    font-size: 1rem;
    font-weight: 600;
  }
}

@media (max-width: 480px) {
  .ai-title-modern {
    font-size: 2rem;
  }

  .ai-header-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }

  .ai-tabs-modern {
    flex-direction: column;
    align-items: center;
  }

  .ai-tab-modern {
    width: 100%;
    max-width: 200px;
  }




}

/* Tabs Navigation */
.ai-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.ai-tab {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 44px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ai-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.ai-tab.active {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-label {
  font-size: 0.9rem;
}

/* Features Grid */
.ai-features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.ai-feature-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.ai-feature-card:hover::before {
  left: 100%;
}

.ai-feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.feature-description {
  font-size: 0.95rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.feature-status {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.feature-link {
  color: #FFD700;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.feature-link:hover {
  color: #FFA500;
}

/* Gradient Colors */
.gradient-purple {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(108, 46, 181, 0.2));
}

.gradient-pink {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(255, 142, 83, 0.2));
}

.gradient-blue {
  background: linear-gradient(135deg, rgba(100, 149, 237, 0.2), rgba(70, 130, 180, 0.2));
}

.gradient-green {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));
}

.gradient-orange {
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.2), rgba(255, 140, 0, 0.2));
}

/* Live Features Section */
.live-features-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.live-feature-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.live-feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.feature-header .feature-icon {
  font-size: 2rem;
  margin-bottom: 0;
}

.feature-header .feature-title {
  margin-bottom: 0;
  flex: 1;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.live {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: white;
}

.try-button {
  display: inline-block;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.try-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
  color: #333;
}

/* Coming Soon Section */
.coming-soon-section {
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.coming-soon-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.coming-soon-card:hover {
  background: rgba(255, 255, 255, 0.1);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  margin: 1rem 0;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.eta {
  font-size: 0.85rem;
  opacity: 0.7;
  font-style: italic;
}

/* Roadmap Section */
.roadmap-section {
  margin-bottom: 2rem;
}

.roadmap-timeline {
  position: relative;
  padding-left: 2rem;
}

.roadmap-timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #FFD700, #FFA500);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -1.5rem;
  top: 0;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  backdrop-filter: blur(10px);
}

.timeline-item.completed .timeline-marker {
  background: linear-gradient(135deg, #51cf66, #40c057);
}

.timeline-item.current .timeline-marker {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  animation: pulse 2s infinite;
}

.timeline-content h4 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.timeline-content p {
  font-size: 0.95rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.timeline-date {
  font-size: 0.85rem;
  opacity: 0.7;
  font-style: italic;
}

/* Call to Action */
.ai-cta {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-cta h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.ai-cta p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
  }
}

/* Tablet Styles (768px+) */
@media (min-width: 768px) {
  .ai-title {
    font-size: 3rem;
  }

  .ai-subtitle {
    font-size: 1.2rem;
  }

  .ai-tabs {
    gap: 1rem;
  }

  .ai-tab {
    padding: 1rem 2rem;
  }

  .ai-features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .cta-buttons {
    flex-direction: row;
  }
}

/* Desktop Styles (1200px+) */
@media (min-width: 1200px) {
  .ai-features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .roadmap-timeline {
    padding-left: 3rem;
  }

  .timeline-item {
    padding-left: 3rem;
  }
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .ai-title {
    font-size: 2rem;
  }

  .ai-header-icon {
    font-size: 3rem;
  }

  .ai-stats {
    gap: 1rem;
  }

  .stat-item {
    min-width: 80px;
    padding: 0.75rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .ai-tabs {
    gap: 0.25rem;
  }

  .ai-tab {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .ai-feature-card {
    padding: 1rem;
  }

  .feature-icon {
    font-size: 2.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }



  .cta-title {
    font-size: 1.3rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.75rem !important;
    word-wrap: break-word;
    hyphens: auto;
  }

  .cta-subtitle {
    font-size: 0.9rem !important;
    line-height: 1.3 !important;
    margin-bottom: 1.5rem !important;
    word-wrap: break-word;
    hyphens: auto;
  }

  .cta-buttons-modern {
    flex-direction: column !important;
    gap: 0.75rem !important;
    align-items: center !important;
    width: 100% !important;
  }

  .cta-primary, .cta-secondary {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem 1rem !important;
    font-size: 0.9rem !important;
    min-height: 48px !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    word-wrap: break-word;
  }

  .cta-icon {
    font-size: 1rem !important;
    flex-shrink: 0;
  }

  .cta-text {
    text-align: center;
    flex: 1;
  }
}

/* Extra Small Mobile Devices */
@media (max-width: 360px) {
  .level-up-title {
    font-size: 1rem;
  }

  .level-up-subtitle {
    font-size: 0.75rem;
    margin-bottom: 0.9rem;
  }

  .level-up-card {
    max-width: 240px;
    padding: 0.7rem 0.8rem;
    font-size: 0.75rem;
  }

  .card-icon {
    font-size: 0.85rem;
  }

  .card-text {
    font-size: 0.75rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .ai-header-icon,
  .ai-feature-card::before,
  .timeline-item.current .timeline-marker {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .ai-feature-card,
  .live-feature-item,
  .coming-soon-card,
  .ai-cta {
    border: 2px solid white;
  }
}

/* Modern Pagination Styles */
.ai-pagination-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
}

.pagination-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.pagination-btn:hover:not(.disabled) {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
}

.pagination-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  font-weight: 600;
  color: #f0f6fc;
}

.current-page {
  color: #667eea;
  font-size: 1.1rem;
  font-weight: 700;
}

.separator {
  color: #8b949e;
  font-size: 0.9rem;
}

.total-pages {
  color: #c9d1d9;
  font-size: 1rem;
}

/* Mobile pagination adjustments */
@media (max-width: 480px) {
  .ai-pagination-modern {
    gap: 0.75rem;
    margin: 1.5rem 0;
  }

  .pagination-btn {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .pagination-info {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}