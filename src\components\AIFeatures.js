import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './AIFeatures.css';
import './AIFeaturesProfile.css';

const AIFeatures = () => {
  const [currentPage, setCurrentPage] = useState(1);

  const aiFeatures = [
    {
      id: 'gift-messages',
      title: '🎁 AI Gift Messages',
      description: 'Generate personalized gift messages for salon appointments',
      status: '✅ Live',
      icon: '🎁',
      color: 'gradient-purple',
      link: '/gift-booking'
    },
    {
      id: 'style-recommendations',
      title: '💇‍♀️ AI Style Advisor',
      description: 'Get personalized hairstyle recommendations based on your preferences',
      status: '✅ Live',
      icon: '💇‍♀️',
      color: 'gradient-pink',
      link: '/ai-style-advisor'
    },
    {
      id: 'smart-booking',
      title: '📅 AI Smart Scheduling',
      description: 'AI-powered appointment scheduling with optimal time recommendations',
      status: '✅ Live',
      icon: '📅',
      color: 'gradient-blue',
      link: '/ai-smart-scheduling'
    },
    {
      id: 'salon-matcher',
      title: '🏪 AI Salon Matcher',
      description: 'Find the perfect salon based on your style preferences and budget',
      status: '✅ Live',
      icon: '🏪',
      color: 'gradient-green',
      link: '/ai-salon-matcher'
    },
    {
      id: 'smart-notifications',
      title: '🧠 Smart Notifications',
      description: 'AI-powered contextual notifications based on your preferences',
      status: '✅ Live',
      icon: '🧠',
      color: 'gradient-purple',
      link: '/smart-notifications'
    },
    {
      id: 'voice-integration',
      title: '🎤 AI Voice Assistant',
      description: 'Control your salon experience with voice commands',
      status: '✅ Live',
      icon: '🎤',
      color: 'gradient-blue',
      link: '/ai-voice-integration'
    },
    {
      id: 'ai-analytics',
      title: '📊 AI Analytics Dashboard',
      description: 'Intelligent insights and predictions for salon business',
      status: '✅ Live',
      icon: '📊',
      color: 'gradient-green',
      link: '/ai-analytics-dashboard'
    },
    {
      id: 'trend-predictor',
      title: '🔮 AI Trend Predictor',
      description: 'Discover upcoming style trends before they go viral',
      status: '✅ Live',
      icon: '🔮',
      color: 'gradient-orange',
      link: '/ai-trend-predictor'
    },
    {
      id: 'virtual-tryon',
      title: '🎭 AI Virtual Try-On',
      description: 'See how different hairstyles look on you with AI simulation',
      status: '✅ Live',
      icon: '🎭',
      color: 'gradient-purple',
      link: '/ai-virtual-try-on'
    }
  ];

  // Removed tabs - keeping only Overview for focused experience



  // Pagination logic - focused viewing experience
  const itemsPerPage = window.innerWidth <= 768 ? 1 : 3; // 1 on mobile, 3 on desktop
  const totalPages = Math.ceil(aiFeatures.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentFeatures = aiFeatures.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Smooth scroll to top of features section
    const featuresSection = document.querySelector('.ai-features-grid-modern');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="ai-pagination-modern">
        <button
          className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ←
        </button>

        <div className="pagination-info">
          <span className="current-page">{currentPage}</span>
          <span className="separator">of</span>
          <span className="total-pages">{totalPages}</span>
        </div>

        <button
          className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          →
        </button>
      </div>
    );
  };



  return (
    <div className="ai-features-profile-container">
      <div className="profile-container">
        {/* Floating Background Effects */}
        <div className="ai-sparkles">
          <span className="ai-sparkle">✨</span>
          <span className="ai-sparkle">🤖</span>
          <span className="ai-sparkle">💫</span>
          <span className="ai-sparkle">⚡</span>
          <span className="ai-sparkle">🚀</span>
        </div>

        {/* Header Section */}
        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">🤖</div>
          </div>
          <h1 className="profile-title">AI Vibes✨</h1>
          <p className="profile-subtitle">Next-gen beauty intelligence that gets you</p>

          {/* Stats - Tiny Neat Design */}
          <div className="ai-stats-tiny">
            <div className="stat-tiny">
              🚀 <span className="stat-number">9</span> Live Features
            </div>
            <div className="stat-tiny">
              ⚡ <span className="stat-number">24/7</span> AI Support
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="profile-content">
          {/* Back Button Section */}
          <div className="profile-section">
            <h3 className="section-title">🏠 Navigation</h3>
            <div className="back-button-container">
              <Link to="/" className="back-button-modern">
                <span className="back-icon">←</span>
                <span className="back-text">Back to Home</span>
                <div className="button-glow"></div>
              </Link>
            </div>
          </div>



          {/* AI Features Grid */}
          <div className="profile-section">
            <h3 className="section-title">✨ AI Features</h3>
            <div className="ai-features-grid-modern">
              {currentFeatures.map(feature => (
                <div key={feature.id} className={`ai-feature-card-modern ${feature.color}`}>
                  <div className="card-glow"></div>
                  <div className="feature-header">
                    <div className="feature-icon-modern">{feature.icon}</div>
                    <div className="feature-status-modern">{feature.status}</div>
                  </div>
                  <div className="feature-content">
                    <h3 className="feature-title-modern">{feature.title.replace(/^[^\w\s]+\s*/, '')}</h3>
                    <p className="feature-description-modern">{feature.description}</p>
                  </div>
                  {feature.status === '✅ Live' && (
                    <Link to={feature.link} className="feature-link-modern">
                      <span className="link-text">Experience Now</span>
                      <span className="link-arrow">→</span>
                      <div className="link-glow"></div>
                    </Link>
                  )}
                </div>
              ))}
            </div>
            {renderPagination()}
          </div>

          {/* Level Up Section - Sleek & Minimal */}
          <div className="profile-section">
            <div className="level-up-section">
              <h3 className="level-up-title">🚀 Ready to Level Up?</h3>
              <p className="level-up-subtitle">Join thousands experiencing the future of beauty tech</p>

              <div className="level-up-cards">
                <Link to="/gift-booking" className="level-up-card primary">
                  <div className="card-icon">🎁</div>
                  <span className="card-text">Start with AI Messages</span>
                </Link>
                <button type="button" className="level-up-card secondary">
                  <div className="card-icon">📧</div>
                  <span className="card-text">Get Early Access</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIFeatures; 
