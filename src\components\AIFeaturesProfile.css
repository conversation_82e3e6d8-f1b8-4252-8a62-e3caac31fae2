/* AI Features Page - Profile Design Pattern with AI Theme */

/* Main Container - Profile Pattern with AI Theme */
.ai-features-profile-container {
  min-height: 100vh;
  background: #0d1117;
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Floating Background Effects */
.ai-features-profile-container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(75, 0, 130, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(72, 61, 139, 0.08) 0%, transparent 50%);
  animation: aiBackgroundFloat 30s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes aiBackgroundFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.ai-features-profile-container .profile-container {
  max-width: 1200px;
  width: 100%;
  margin: 2rem auto;
  background: #161b22;
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 15px 35px rgba(0, 0, 0, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: visible;
  z-index: 1;
}

.ai-features-profile-container .profile-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(138, 43, 226, 0.4), 
    rgba(75, 0, 130, 0.4), 
    transparent);
  animation: aiBorderGlow 4s ease-in-out infinite;
}

@keyframes aiBorderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Floating AI Sparkles */
.ai-features-profile-container .ai-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.ai-features-profile-container .ai-sparkle {
  position: absolute;
  font-size: 1rem;
  color: rgba(138, 43, 226, 0.6);
  animation: aiSparkleFloat 8s ease-in-out infinite;
}

.ai-features-profile-container .ai-sparkle:nth-child(1) {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.ai-features-profile-container .ai-sparkle:nth-child(2) {
  top: 25%;
  right: 15%;
  animation-delay: 1.5s;
}

.ai-features-profile-container .ai-sparkle:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 3s;
}

.ai-features-profile-container .ai-sparkle:nth-child(4) {
  top: 40%;
  right: 25%;
  animation-delay: 4.5s;
}

.ai-features-profile-container .ai-sparkle:nth-child(5) {
  bottom: 20%;
  right: 10%;
  animation-delay: 6s;
}

@keyframes aiSparkleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
  50% { transform: translateY(-15px) rotate(180deg); opacity: 1; }
}

/* Header - Profile Pattern with AI Theme */
.ai-features-profile-container .profile-header {
  background: rgba(255, 255, 255, 0.05);
  padding: 2.5rem 2rem 1.5rem 2rem;
  text-align: center;
  color: #f0f6fc;
  position: relative;
  z-index: 2;
  border-radius: 24px 24px 0 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-features-profile-container .auth-icon-wrapper {
  margin-bottom: 1rem;
}

.ai-features-profile-container .auth-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(138, 43, 226, 0.2) 0%,
    rgba(75, 0, 130, 0.2) 100%);
  border: 3px solid rgba(138, 43, 226, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 12px 25px rgba(138, 43, 226, 0.2);
  animation: aiIconPulse 3s ease-in-out infinite;
}

@keyframes aiIconPulse {
  0%, 100% { transform: scale(1); box-shadow: 0 12px 25px rgba(138, 43, 226, 0.2); }
  50% { transform: scale(1.05); box-shadow: 0 15px 30px rgba(138, 43, 226, 0.3); }
}

.ai-features-profile-container .profile-title {
  font-size: 2.5rem;
  font-weight: 900;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg,
    #667eea 0%,
    #ff6b9d 50%,
    #ffeaa7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.ai-features-profile-container .profile-subtitle {
  color: #8b949e;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 2rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Tiny Stats Design */
.ai-features-profile-container .ai-stats-tiny {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.ai-features-profile-container .stat-tiny {
  color: #f0f6fc;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.ai-features-profile-container .stat-tiny .stat-number {
  color: #667eea;
  font-weight: 700;
}

.ai-features-profile-container .stat-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(138, 43, 226, 0.1);
  border-radius: 16px;
  padding: 1.25rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.ai-features-profile-container .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(138, 43, 226, 0.15);
  border-color: rgba(138, 43, 226, 0.2);
}

.ai-features-profile-container .stat-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.ai-features-profile-container .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 900;
  color: #4c1d95;
  margin-bottom: 0.25rem;
}

.ai-features-profile-container .stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content Area - Profile Pattern */
.ai-features-profile-container .profile-content {
  padding: 0 2rem 2.5rem 2rem;
  position: relative;
  z-index: 2;
}

/* Custom Scrollbar */
.ai-features-profile-container .profile-content::-webkit-scrollbar {
  width: 8px;
}

.ai-features-profile-container .profile-content::-webkit-scrollbar-track {
  background: rgba(138, 43, 226, 0.05);
  border-radius: 4px;
}

.ai-features-profile-container .profile-content::-webkit-scrollbar-thumb {
  background: rgba(138, 43, 226, 0.3);
  border-radius: 4px;
}

.ai-features-profile-container .profile-content::-webkit-scrollbar-thumb:hover {
  background: rgba(138, 43, 226, 0.5);
}

/* Sections - Profile Pattern with AI Theme */
.ai-features-profile-container .profile-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.ai-features-profile-container .profile-section:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.ai-features-profile-container .section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #f0f6fc;
  margin: 0 0 1.25rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Back Button Styling */
.ai-features-profile-container .back-button-container {
  display: flex;
  justify-content: center;
}

.ai-features-profile-container .back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #f0f6fc;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-features-profile-container .back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.ai-features-profile-container .back-icon {
  font-size: 1.2rem;
}

.ai-features-profile-container .back-text {
  font-size: 0.95rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .ai-features-profile-container {
    padding: 0.75rem 0.25rem;
  }

  .ai-features-profile-container .profile-container {
    border-radius: 20px;
    max-width: 100%;
    margin: 0 0.5rem;
  }

  .ai-features-profile-container .profile-header {
    padding: 2rem 1.5rem 1.25rem 1.5rem;
  }

  .ai-features-profile-container .profile-title {
    font-size: 2rem;
  }

  .ai-features-profile-container .profile-content {
    padding: 0 1.5rem 2rem 1.5rem;
    max-height: 65vh;
  }

  .ai-features-profile-container .auth-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .ai-features-profile-container .profile-section {
    padding: 1.25rem;
  }

  .ai-features-profile-container .ai-stats-tiny {
    gap: 1.5rem;
    flex-direction: column;
    align-items: center;
  }

  .ai-features-profile-container .stat-tiny {
    font-size: 1rem;
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
  }
}

@media (max-width: 480px) {
  .ai-features-profile-container .profile-header {
    padding: 1.5rem 1rem;
  }

  .ai-features-profile-container .profile-title {
    font-size: 1.75rem;
  }

  .ai-features-profile-container .profile-content {
    padding: 0 1rem 1.5rem 1rem;
    max-height: 60vh;
  }

  .ai-features-profile-container .profile-section {
    padding: 1rem;
  }

  .ai-features-profile-container .auth-icon {
    width: 50px;
    height: 50px;
    font-size: 1.75rem;
  }

  .ai-features-profile-container .section-title {
    font-size: 1.1rem;
  }

  .ai-features-profile-container .back-button-modern {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
  }
}
