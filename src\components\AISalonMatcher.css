/* ===== AI Salon Matcher - Enterprise Gen Z Design ===== */

.ai-salon-matcher-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
  /* Mobile-first optimizations */
  will-change: auto;
  contain: layout style paint;
}

.matcher-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  /* Reduce blur on mobile for performance */
  filter: blur(30px);
}

.matcher-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: matcherFloat 14s ease-in-out infinite;
  /* Mobile performance optimizations */
  will-change: transform;
  transform: translateZ(0);
}

.matcher-orb-1 {
  width: 270px;
  height: 270px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  top: 30%;
  left: -15%;
  animation-delay: 0s;
}

.matcher-orb-2 {
  width: 210px;
  height: 210px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  top: 70%;
  right: -12%;
  animation-delay: 6s;
}

.matcher-orb-3 {
  width: 230px;
  height: 230px;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  bottom: 25%;
  left: 45%;
  animation-delay: 10s;
}

@keyframes matcherFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  16% { transform: translateY(-30px) rotate(60deg) scale(1.15); }
  33% { transform: translateY(20px) rotate(120deg) scale(0.85); }
  50% { transform: translateY(-15px) rotate(180deg) scale(1.05); }
  66% { transform: translateY(25px) rotate(240deg) scale(0.9); }
  83% { transform: translateY(-20px) rotate(300deg) scale(1.1); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  /* Mobile-first container */
  padding: 16px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(255, 107, 157, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Modern Header */
.matcher-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.matcher-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.matcher-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 107, 157, 0.1);
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #ff6b9d;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.matcher-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #ff6b9d, #667eea, #ffeaa7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: matcherGradientShift 7s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: matcherSparkle 3.5s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes matcherGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes matcherSparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.6) rotate(180deg); opacity: 0.4; }
}

.matcher-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Modern Form */
.preferences-form-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.preferences-form-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(102, 126, 234, 0.05));
  opacity: 0.5;
}

.form-section-modern {
  position: relative;
  z-index: 2;
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.section-icon {
  font-size: 1.5rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #f0f6fc;
  margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .matcher-orb-1, .matcher-orb-2, .matcher-orb-3 {
    /* Reduce animation complexity on mobile */
    animation-duration: 20s;
    filter: blur(40px);
  }

  .container {
    padding: 12px;
  }

  .matcher-title-modern {
    font-size: 2.5rem;
  }

  .matcher-subtitle-modern {
    font-size: 1rem;
  }

  .preferences-form-modern {
    padding: 1.5rem;
  }

  .section-header {
    margin-bottom: 1.5rem;
  }

  .section-icon {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .matcher-title-modern {
    font-size: 2rem;
  }

  .matcher-header-badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
  }

  .preferences-form-modern {
    padding: 1rem;
    border-radius: 16px;
  }
  
  /* Optimize button for mobile touch */
  .matcher-btn {
    min-height: 56px !important;
    min-width: 100% !important;
    font-size: 1rem !important;
    border-radius: 12px !important;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Reduce animations on mobile for performance */
  .matcher-gradient-orb {
    animation: none;
    opacity: 0.4;
  }
  
  .option-card, .service-option {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: white;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

/* Header Section */
.matcher-header {
  margin-bottom: 2rem;
}

.matcher-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: float 3s ease-in-out infinite;
}

.matcher-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.matcher-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

/* Form Section */
.preferences-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #FFD700;
  text-align: center;
}

/* Preference Groups */
.preference-group {
  margin-bottom: 2rem;
}

.form-label {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: block;
  color: white;
}

/* Option Grids */
.option-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.option-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.option-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.option-card.selected {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-color: #FFD700;
  color: #333;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.option-emoji {
  font-size: 2rem;
  display: block;
}

.option-label {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.3;
}

/* Service Grid */
.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.service-option {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.service-option:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.service-option.selected {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-color: #FFD700;
  color: #333;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.service-emoji {
  font-size: 1.5rem;
  display: block;
}

.service-label {
  font-size: 0.8rem;
  font-weight: 600;
}

/* Form Controls */
.form-control {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 15px !important;
  color: white !important;
  font-size: 16px;
  padding: 16px 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 48px;
  width: 100%;
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: #FFD700 !important;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2) !important;
  outline: none;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Matcher Button */
.matcher-btn {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: none;
  color: #333;
  font-weight: 700;
  font-size: 16px;
  padding: 16px 32px;
  border-radius: 30px;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-height: 48px;
  width: 100%;
  max-width: 300px;
}

.matcher-btn:hover {
  background: linear-gradient(135deg, #FFA500, #FFD700);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
  color: #333;
}

.matcher-btn:disabled {
  opacity: 0.6;
  transform: none;
  cursor: not-allowed;
}

/* Results Section */
.results-section {
  max-width: 1200px;
  margin: 0 auto;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.results-header .section-title {
  margin-bottom: 0;
  text-align: left;
}

/* Matches Grid */
.matches-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.match-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.match-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.match-card:hover::before {
  left: 100%;
}

.match-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Match Header */
.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.match-rank {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  font-weight: 700;
  font-size: 1.2rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  min-width: 50px;
  text-align: center;
}

.match-score {
  text-align: center;
}

.score-number {
  font-size: 2rem;
  font-weight: 800;
  color: #FFD700;
  display: block;
}

.score-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Salon Info */
.salon-info {
  margin-bottom: 1.5rem;
}

.salon-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
}

.salon-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.stars {
  font-size: 1rem;
}

.rating-text {
  font-size: 0.9rem;
  opacity: 0.8;
}

.salon-location {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0;
}

/* Match Reasoning */
.match-reasoning {
  margin-bottom: 1.5rem;
}

.reasoning-text {
  font-size: 0.95rem;
  line-height: 1.5;
  opacity: 0.9;
  margin-bottom: 0;
}

/* Specialties */
.salon-specialties,
.best-for {
  margin-bottom: 1rem;
}

.salon-specialties h6,
.best-for h6 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #FFD700;
}

.specialty-tags,
.best-for-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.specialty-tag,
.best-for-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.best-for-tag {
  background: rgba(255, 215, 0, 0.2);
  color: #FFD700;
}

/* Special Considerations */
.special-considerations {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.consideration-badge {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* AI Insights */
.ai-insights {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.insights-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #FFD700;
}

.insights-content p {
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 0.75rem;
  opacity: 0.9;
}

.insights-content p:last-child {
  margin-bottom: 0;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Tablet Styles (768px+) */
@media (min-width: 768px) {
  .matcher-title {
    font-size: 3rem;
  }
  
  .matcher-subtitle {
    font-size: 1.2rem;
  }
  
  .option-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .form-control {
    font-size: 1rem;
    padding: 12px 16px;
    min-height: 44px;
  }
  
  .matcher-btn {
    font-size: 1.1rem;
    padding: 15px 30px;
    min-height: 44px;
    width: auto;
  }
  
  .matches-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* Desktop Styles (1200px+) */
@media (min-width: 1200px) {
  .option-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .matches-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .matcher-title {
    font-size: 2rem;
  }
  
  .matcher-icon {
    font-size: 3rem;
  }
  
  .form-section {
    padding: 1.5rem;
  }
  
  .option-card {
    padding: 1rem;
    min-height: 70px;
  }
  
  .option-emoji {
    font-size: 1.5rem;
  }
  
  .option-label {
    font-size: 0.8rem;
  }
  
  .service-option {
    padding: 0.75rem;
    min-height: 50px;
  }
  
  .service-emoji {
    font-size: 1.2rem;
  }
  
  .service-label {
    font-size: 0.7rem;
  }
  
  .match-card {
    padding: 1.5rem;
  }
  
  .salon-name {
    font-size: 1.3rem;
  }
  
  .score-number {
    font-size: 1.5rem;
  }
  
  .results-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .results-header .section-title {
    text-align: center;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .matcher-icon,
  .match-card::before {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .option-card,
  .service-option,
  .match-card,
  .ai-insights {
    border: 2px solid white;
  }
} 