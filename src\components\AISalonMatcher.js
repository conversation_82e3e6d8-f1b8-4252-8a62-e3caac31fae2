import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useNotification } from '../context/NotificationContext';
import salonMatcherService from '../services/salonMatcherService';
import './AISalonMatcher.css';

const AISalonMatcher = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  const [isLoading, setIsLoading] = useState(false);
  const [matchedSalons, setMatchedSalons] = useState([]);
  const [showResults, setShowResults] = useState(false);
  const [availableSalons, setAvailableSalons] = useState([]);

  const [preferences, setPreferences] = useState({
    stylePreference: '',
    budget: '',
    location: '',
    hairType: '',
    preferredServices: [],
    specialRequirements: ''
  });

  // Mock salon data (in real app, fetch from API)

  // TIP: Always use this pattern for useMemo with arrays:
  // const arr = React.useMemo(() => ([ ... ]), []);
  // TIP: Always use this pattern for useMemo with arrays:
  // const arr = React.useMemo(() => ([ ... ]), []);
  const mockSalons = React.useMemo(() => ([
    {
      id: 1,
      name: 'Trendy Cuts Studio',
      specialties: ['trendy', 'modern', 'color'],
      price_range: 'high',
      rating: 4.8,
      location: 'Nairobi CBD',
      services: ['Haircut', 'Color', 'Styling', 'Treatment']
    },
    {
      id: 2,
      name: 'Classic Beauty Salon',
      specialties: ['classic', 'traditional', 'bridal'],
      price_range: 'moderate',
      rating: 4.5,
      location: 'Westlands',
      services: ['Haircut', 'Styling', 'Bridal', 'Treatment']
    },
    {
      id: 3,
      name: 'Budget Hair Studio',
      specialties: ['basic', 'quick', 'affordable'],
      price_range: 'low',
      rating: 4.2,
      location: 'Eastlands',
      services: ['Haircut', 'Basic Styling']
    },
    {
      id: 4,
      name: 'Luxury Hair & Spa',
      specialties: ['luxury', 'premium', 'spa'],
      price_range: 'high',
      rating: 4.9,
      location: 'Karen',
      services: ['Haircut', 'Color', 'Styling', 'Spa', 'Treatment']
    },
    {
      id: 5,
      name: 'Curly Hair Experts',
      specialties: ['curly', 'natural', 'textured'],
      price_range: 'moderate',
      rating: 4.6,
      location: 'Kilimani',
      services: ['Haircut', 'Curly Styling', 'Treatment']
    }
  ]), []);

  useEffect(() => {
    setAvailableSalons(mockSalons);
  }, [mockSalons]);

  const styleOptions = [
    { value: 'trendy', label: '✨ Trendy & Modern', emoji: '✨' },
    { value: 'classic', label: '👑 Classic & Elegant', emoji: '👑' },
    { value: 'edgy', label: '🔥 Edgy & Bold', emoji: '🔥' },
    { value: 'natural', label: '🌿 Natural & Minimal', emoji: '🌿' },
    { value: 'vintage', label: '🎭 Vintage & Retro', emoji: '🎭' }
  ];

  const budgetOptions = [
    { value: 'low', label: '💰 Budget-Friendly', emoji: '💰' },
    { value: 'moderate', label: '💎 Mid-Range', emoji: '💎' },
    { value: 'high', label: '💎💎 Premium', emoji: '💎💎' }
  ];

  const hairTypeOptions = [
    { value: 'straight', label: '🟢 Straight', emoji: '🟢' },
    { value: 'wavy', label: '🌊 Wavy', emoji: '🌊' },
    { value: 'curly', label: '🌀 Curly', emoji: '🌀' },
    { value: 'coily', label: '🌪️ Coily', emoji: '🌪️' },
    { value: 'fine', label: '💫 Fine', emoji: '💫' },
    { value: 'thick', label: '💪 Thick', emoji: '💪' }
  ];

  const serviceOptions = [
    { value: 'haircut', label: '✂️ Haircut', emoji: '✂️' },
    { value: 'color', label: '🎨 Color & Dye', emoji: '🎨' },
    { value: 'styling', label: '💇‍♀️ Styling', emoji: '💇‍♀️' },
    { value: 'treatment', label: '💆‍♀️ Treatment', emoji: '💆‍♀️' },
    { value: 'bridal', label: '👰 Bridal', emoji: '👰' },
    { value: 'spa', label: '🧖‍♀️ Spa Services', emoji: '🧖‍♀️' }
  ];

  const handlePreferenceChange = (field, value) => {
    console.log(`🔄 Preference changed: ${field} = ${value}`);
    setPreferences(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleServiceToggle = (service) => {
    console.log(`🔄 Service toggled: ${service}`);
    setPreferences(prev => ({
      ...prev,
      preferredServices: prev.preferredServices.includes(service)
        ? prev.preferredServices.filter(s => s !== service)
        : [...prev.preferredServices, service]
    }));
  };

  const findPerfectSalons = async () => {
    console.log('🔍 Find Perfect Salons triggered');
    console.log('📊 Current preferences:', preferences);
    console.log('✅ Style preference:', preferences.stylePreference);
    console.log('💰 Budget:', preferences.budget);
    
    if (!preferences.stylePreference || !preferences.budget) {
      console.log('❌ Missing required fields');
      showNotification('Please select your style preference and budget!', 'warning');
      return;
    }

    console.log('🚀 Starting AI salon matching...');
    setIsLoading(true);
    
    try {
      const matches = await salonMatcherService.findPerfectSalons(preferences, availableSalons);
      console.log('🎯 AI matches found:', matches);
      setMatchedSalons(matches);
      setShowResults(true);
      
      if (matches.length > 0) {
        showNotification(`✨ Found ${matches.length} perfect salon matches for you!`, 'success');
      } else {
        showNotification('No perfect matches found. Try adjusting your preferences!', 'info');
      }
    } catch (error) {
      console.error('❌ Error finding salon matches:', error);
      showNotification('Failed to find salon matches. Please try again!', 'danger');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSalonSelect = (salon) => {
    navigate(`/salon/${salon.id}`);
  };

  const resetPreferences = () => {
    setPreferences({
      stylePreference: '',
      budget: '',
      location: '',
      hairType: '',
      preferredServices: [],
      specialRequirements: ''
    });
    setShowResults(false);
    setMatchedSalons([]);
  };

  return (
    <div className="ai-salon-matcher-page">
      <div className="matcher-background-effects">
        <div className="matcher-gradient-orb matcher-orb-1"></div>
        <div className="matcher-gradient-orb matcher-orb-2"></div>
        <div className="matcher-gradient-orb matcher-orb-3"></div>
      </div>

      <div className="container">
        {/* Back Button */}
        <div className="back-button-container">
          <Link to="/ai-features" className="back-button-modern">
            <span className="back-icon">←</span>
            <span className="back-text">Back to AI Features</span>
            <div className="button-glow"></div>
          </Link>
        </div>

        {/* Header */}
        <div className="matcher-header-modern">
          <div className="matcher-header-content">
            <div className="matcher-header-badge">
              <span className="badge-icon">🏪</span>
              <span className="badge-text">AI MATCHMAKER</span>
            </div>
            <h1 className="matcher-title-modern">
              <span className="title-gradient">Salon Soulmate</span>
              <span className="title-accent">💫</span>
            </h1>
            <p className="matcher-subtitle-modern">
              AI that finds your salon twin flame
            </p>
          </div>
        </div>

        {!showResults ? (
          /* Preferences Form */
          <div className="preferences-form-modern">
            <div className="form-section-modern">
              <div className="section-header">
                <span className="section-icon">✨</span>
                <h3 className="section-title">What's Your Vibe?</h3>
              </div>
              
              <div className="preference-group">
                <label className="form-label" htmlFor="stylePreference">Style Preference</label>
                <div className="option-grid">
                  {styleOptions.map(option => (
                    <button
                      type="button"
                      key={option.value}
                      className={`option-card ${preferences.stylePreference === option.value ? 'selected' : ''}`}
                      onClick={() => handlePreferenceChange('stylePreference', option.value)}
                    >
                      <span className="option-emoji">{option.emoji}</span>
                      <span className="option-label">{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="preference-group">
                <label className="form-label" htmlFor="budget">Budget Range</label>
                <div className="option-grid">
                  {budgetOptions.map(option => (
                    <button
                      type="button"
                      key={option.value}
                      className={`option-card ${preferences.budget === option.value ? 'selected' : ''}`}
                      onClick={() => handlePreferenceChange('budget', option.value)}
                    >
                      <span className="option-emoji">{option.emoji}</span>
                      <span className="option-label">{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="preference-group">
                <label className="form-label" htmlFor="hairType">Hair Type</label>
                <div className="option-grid">
                  {hairTypeOptions.map(option => (
                    <button
                      type="button"
                      key={option.value}
                      className={`option-card ${preferences.hairType === option.value ? 'selected' : ''}`}
                      onClick={() => handlePreferenceChange('hairType', option.value)}
                    >
                      <span className="option-emoji">{option.emoji}</span>
                      <span className="option-label">{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="preference-group">
                <label className="form-label" htmlFor="preferredServices">Preferred Services</label>
                <div className="service-grid">
                  {serviceOptions.map(option => (
                    <button
                      type="button"
                      key={option.value}
                      className={`service-option ${preferences.preferredServices.includes(option.value) ? 'selected' : ''}`}
                      onClick={() => handleServiceToggle(option.value)}
                    >
                      <span className="service-emoji">{option.emoji}</span>
                      <span className="service-label">{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="preference-group">
                <label className="form-label" htmlFor="location">Location (Optional)</label>
                <input
                  id="location"
                  type="text"
                  className="form-control"
                  placeholder="Enter your preferred area..."
                  value={preferences.location}
                  onChange={(e) => handlePreferenceChange('location', e.target.value)}
                />
              </div>

              <div className="preference-group">
                <label className="form-label" htmlFor="specialRequirements">Special Requirements (Optional)</label>
                <textarea
                  id="specialRequirements"
                  className="form-control"
                  placeholder="Any special needs or preferences..."
                  value={preferences.specialRequirements}
                  onChange={(e) => handlePreferenceChange('specialRequirements', e.target.value)}
                  rows="3"
                />
              </div>
            </div>

            <div className="text-center mt-4">
              <button
                type="button"
                className="btn btn-primary btn-lg matcher-btn"
                onClick={findPerfectSalons}
                disabled={isLoading || !preferences.stylePreference || !preferences.budget}
                style={{
                  minHeight: '60px',
                  minWidth: '280px',
                  fontSize: '1.1rem',
                  fontWeight: '600',
                  borderRadius: '16px',
                  boxShadow: '0 8px 32px rgba(255, 107, 157, 0.3)',
                  transition: 'all 0.2s ease',
                  transform: 'translateY(0)',
                  cursor: (!isLoading && preferences.stylePreference && preferences.budget) ? 'pointer' : 'not-allowed'
                }}
                onMouseDown={(e) => {
                  if (!isLoading && preferences.stylePreference && preferences.budget) {
                    e.target.style.transform = 'translateY(2px)';
                    e.target.style.boxShadow = '0 4px 16px rgba(255, 107, 157, 0.2)';
                  }
                }}
                onMouseUp={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 32px rgba(255, 107, 157, 0.3)';
                }}
                onTouchStart={(e) => {
                  if (!isLoading && preferences.stylePreference && preferences.budget) {
                    e.target.style.transform = 'translateY(2px)';
                    e.target.style.boxShadow = '0 4px 16px rgba(255, 107, 157, 0.2)';
                  }
                }}
                onTouchEnd={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 8px 32px rgba(255, 107, 157, 0.3)';
                }}
              >
                {isLoading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" />
                    Finding Perfect Matches...
                  </>
                ) : (
                  <>
                    <i className="bi bi-magic me-2" />
                    ✨ Find My Perfect Salon
                  </>
                )}
              </button>
              
              {/* Debug info for development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-2 text-muted" style={{ fontSize: '0.8rem' }}>
                  Debug: Style={preferences.stylePreference || 'none'}, Budget={preferences.budget || 'none'}
                </div>
              )}
            </div>
          </div>
        ) : (
          /* Results Section */
          <div className="results-section">
            <div className="results-header">
              <h3 className="section-title">
                <i className="bi bi-star me-2" />
                Your Perfect Salon Matches
              </h3>
              <button
                type="button"
                className="btn btn-outline-light btn-sm"
                onClick={resetPreferences}
              >
                <i className="bi bi-arrow-left me-1" />
                Start Over
              </button>
            </div>

            <div className="matches-grid">
              {matchedSalons.map((match, index) => (
                <div key={match.salon.id} className="match-card">
                  <div className="match-header">
                    <div className="match-rank">
                      #
                      {index + 1}
                    </div>
                    <div className="match-score">
                      <span className="score-number">{match.matchScore}</span>
                      <span className="score-label">/10</span>
                    </div>
                  </div>

                  <div className="salon-info">
                    <h4 className="salon-name">{match.salon.name}</h4>
                    <div className="salon-rating">
                      <span className="stars">{'⭐'.repeat(Math.floor(match.salon.rating))}</span>
                      <span className="rating-text">
                        (
                        {match.salon.rating}
                        )
                      </span>
                    </div>
                    <p className="salon-location">
                      <i className="bi bi-geo-alt me-1" />
                      {match.salon.location}
                    </p>
                  </div>

                  <div className="match-reasoning">
                    <p className="reasoning-text">{match.reasoning}</p>
                  </div>

                  <div className="salon-specialties">
                    <h6>Specialties:</h6>
                    <div className="specialty-tags">
                      {match.salon.specialties.map(specialty => (
                        <span key={specialty} className="specialty-tag">
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="best-for">
                    <h6>Best For:</h6>
                    <div className="best-for-tags">
                      {match.bestFor.map(style => (
                        <span key={style} className="best-for-tag">
                          {style}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="special-considerations">
                    {match.specialConsiderations.map(consideration => (
                      <span key={consideration} className="consideration-badge">
                        {consideration}
                      </span>
                    ))}
                  </div>

                  <button
                    type="button"
                    className="btn btn-primary w-100 mt-3"
                    onClick={() => handleSalonSelect(match.salon)}
                  >
                    <i className="bi bi-eye me-2" />
                    View Salon
                  </button>
                </div>
              ))}
            </div>

            <div className="ai-insights mt-4">
              <h4 className="insights-title">
                <i className="bi bi-lightbulb me-2" />
                AI Insights
              </h4>
              <div className="insights-content">
                <p>
                  💡
                  <strong>Tip:</strong>
                  {' '}
                  Book early for highly-rated salons as they fill up quickly!
                </p>
                <p>
                  🎯
                  <strong>Recommendation:</strong>
                  {' '}
                  Consider your budget and location when making your final choice.
                </p>
                <p>
                  ✨
                  <strong>Pro Tip:</strong>
                  {' '}
                  Check salon reviews and photos before booking your appointment.
                </p>
              </div>
            </div>

            {/* Render AI analysis if available */}
            {matchedSalons[0] && matchedSalons[0].analysis && (
              <div className="ai-analysis mt-4">
                <h4 className="insights-title">
                  <i className="bi bi-lightbulb me-2" />
                  AI Analysis
                </h4>
                <div className="insights-content">
                  {Object.entries(matchedSalons[0].analysis).map(([key, value]) => (
                    <p key={key}>
                      <strong>{key.replace(/([A-Z])/g, ' $1')}: </strong>{value}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AISalonMatcher; 
