/* ===== AI Smart Scheduling - Enterprise Gen Z Design ===== */

.ai-smart-scheduling-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
  /* Mobile-first optimizations */
  will-change: auto;
  contain: layout style paint;
}

.scheduling-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  /* Reduce blur on mobile for performance */
  filter: blur(30px);
}

.scheduling-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: schedulingFloat 12s ease-in-out infinite;
  /* Mobile performance optimizations */
  will-change: transform;
  transform: translateZ(0);
}

.scheduling-orb-1 {
  width: 260px;
  height: 260px;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  top: 25%;
  left: -12%;
  animation-delay: 0s;
}

.scheduling-orb-2 {
  width: 190px;
  height: 190px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  top: 65%;
  right: -10%;
  animation-delay: 5s;
}

.scheduling-orb-3 {
  width: 220px;
  height: 220px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  bottom: 20%;
  left: 55%;
  animation-delay: 8s;
}

@keyframes schedulingFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  20% { transform: translateY(-25px) rotate(72deg) scale(1.1); }
  40% { transform: translateY(15px) rotate(144deg) scale(0.9); }
  60% { transform: translateY(-10px) rotate(216deg) scale(1.05); }
  80% { transform: translateY(20px) rotate(288deg) scale(0.95); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  /* Mobile-first container */
  padding: 16px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(255, 234, 167, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 234, 167, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Modern Header */
.scheduling-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.scheduling-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.scheduling-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 234, 167, 0.1);
  border: 1px solid rgba(255, 234, 167, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #ffeaa7;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.scheduling-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #ffeaa7, #667eea, #ff6b9d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: schedulingGradientShift 6s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: schedulingSparkle 3s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes schedulingGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes schedulingSparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.5) rotate(180deg); opacity: 0.5; }
}

.scheduling-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Modern Container */
.scheduling-container-modern {
  position: relative;
  z-index: 1;
}

/* Modern Sections */
.preferences-section-modern {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.preferences-section-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 234, 167, 0.05), rgba(102, 126, 234, 0.05));
  opacity: 0.5;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.section-icon {
  font-size: 1.5rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #f0f6fc;
  margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .scheduling-orb-1, .scheduling-orb-2, .scheduling-orb-3 {
    /* Reduce animation complexity on mobile */
    animation-duration: 18s;
    filter: blur(40px);
  }

  .container {
    padding: 12px;
  }

  .scheduling-title-modern {
    font-size: 2.5rem;
  }

  .scheduling-subtitle-modern {
    font-size: 1rem;
  }

  .preferences-section-modern {
    padding: 1.5rem;
  }

  .section-header {
    margin-bottom: 1.5rem;
  }

  .section-icon {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .scheduling-title-modern {
    font-size: 2rem;
  }

  .scheduling-header-badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
  }

  .preferences-section-modern {
    padding: 1rem;
    border-radius: 16px;
  }
  
  /* Optimize button for mobile touch */
  .refresh-schedule-btn {
    min-height: 56px !important;
    min-width: 100% !important;
    font-size: 1rem !important;
    border-radius: 12px !important;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Reduce animations on mobile for performance */
  .scheduling-gradient-orb {
    animation: none;
    opacity: 0.4;
  }
  
  .day-checkbox, .time-checkbox {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
}

.back-button:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.scheduling-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.scheduling-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.scheduling-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.scheduling-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Preferences Section */
.preferences-section {
  margin-bottom: 40px;
}

.preferences-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.5rem;
}

.preferences-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.preference-group {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.preference-group h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.day-selector,
.time-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.day-checkbox,
.time-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.day-checkbox:hover,
.time-checkbox:hover {
  background: rgba(102, 126, 234, 0.2);
}

.day-checkbox input,
.time-checkbox input {
  margin: 0;
}

.travel-time-input,
.budget-input {
  width: 80px;
  padding: 8px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  margin-right: 10px;
}

.travel-time-input:focus,
.budget-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.urgency-select,
.service-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
}

.urgency-select:focus,
.service-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Service Section */
.service-section {
  margin-bottom: 40px;
}

/* Action Section */
.action-section {
  text-align: center;
  margin-bottom: 30px;
}

.refresh-schedule-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.refresh-schedule-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.refresh-schedule-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.service-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.service-form {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.form-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  font-weight: 600;
  color: #333;
  min-width: 120px;
}

.service-input,
.duration-input,
.price-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
}

.service-input:focus,
.duration-input:focus,
.price-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Loading Section */
.loading-section {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 3rem;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-section p {
  color: #666;
  font-size: 1.1rem;
}

/* Schedule Results */
.schedule-results {
  margin-bottom: 40px;
}

.schedule-results h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.8rem;
  text-align: center;
}

.optimal-time {
  margin-bottom: 30px;
}

.optimal-time h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.optimal-card {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(76, 175, 80, 0.3);
}

.optimal-date {
  font-size: 1.2rem;
  margin-bottom: 10px;
  opacity: 0.9;
}

.optimal-time-display {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.optimal-reason {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 20px;
}

.book-optimal-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-optimal-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Recommendations Section */
.recommendations-section {
  margin-bottom: 30px;
}

.recommendations-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.recommendation-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.recommendation-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.rec-date {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.rec-time {
  font-size: 1.3rem;
  font-weight: 700;
  color: #667eea;
}

.confidence-badge {
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.rec-details {
  margin-bottom: 15px;
}

.rec-reasoning {
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.rec-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 10px;
}

.availability-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.rec-alternatives {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.rec-alternatives strong {
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 8px;
  display: block;
}

.alternatives-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.alternative-time {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Scheduling Tips */
.scheduling-tips {
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #4CAF50;
  margin-bottom: 30px;
}

.scheduling-tips h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.scheduling-tips ul {
  margin: 0;
  padding-left: 20px;
}

.scheduling-tips li {
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

/* Conflicts Section */
.conflicts-section {
  margin-bottom: 30px;
}

.conflicts-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.conflicts-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.conflict-card {
  background: rgba(244, 67, 54, 0.1);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #F44336;
}

.conflict-type {
  font-weight: 600;
  color: #F44336;
  text-transform: uppercase;
  font-size: 0.8rem;
  margin-bottom: 8px;
}

.conflict-description {
  color: #666;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.conflict-solution {
  color: #333;
  font-size: 0.9rem;
}

.conflict-solution strong {
  color: #F44336;
}

/* Patterns Section */
.patterns-section {
  margin-top: 40px;
  padding-top: 40px;
  border-top: 2px solid #f0f0f0;
}

.patterns-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.5rem;
  text-align: center;
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.pattern-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.pattern-card h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.pattern-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.pattern-item {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.pattern-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-smart-scheduling {
    padding: 15px;
  }
  
  .scheduling-header {
    padding: 20px;
  }
  
  .scheduling-header h2 {
    font-size: 2rem;
  }
  
  .scheduling-container {
    padding: 20px;
  }
  
  .preferences-grid {
    grid-template-columns: 1fr;
  }
  
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .rec-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .rec-meta {
    flex-direction: column;
    gap: 5px;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
  
  .form-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .form-group label {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .scheduling-header h2 {
    font-size: 1.8rem;
  }
  
  .scheduling-header p {
    font-size: 1rem;
  }
  
  .recommendation-card {
    padding: 15px;
  }
  
  .optimal-card {
    padding: 20px;
  }
  
  .optimal-time-display {
    font-size: 2rem;
  }
} 