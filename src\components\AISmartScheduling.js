import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import aiSmartSchedulingService from '../services/aiSmartSchedulingService';
import './AISmartScheduling.css';

const AISmartScheduling = () => {
  const [userPreferences, setUserPreferences] = useState({
    preferredDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    preferredTimes: ['10:00', '11:00', '14:00', '15:00', '16:00'],
    travelTime: 15,
    workSchedule: {
      workDays: [1, 2, 3, 4, 5], // Monday to Friday
      workHours: { start: '09:00', end: '17:00' }
    },
    urgency: 'medium',
    budget: 100,
    serviceType: 'haircut'
  });

  const [serviceDetails, setServiceDetails] = useState({
    name: 'Haircut & Styling',
    duration: 60,
    price: 75,
    category: 'hair'
  });

  const salonAvailability = useMemo(() => ({
    monday: {
      '09:00': 'available',
      '10:00': 'available',
      '11:00': 'limited',
      '12:00': 'available',
      '13:00': 'available',
      '14:00': 'available',
      '15:00': 'limited',
      '16:00': 'available',
      '17:00': 'available'
    },
    tuesday: {
      '09:00': 'available',
      '10:00': 'limited',
      '11:00': 'available',
      '12:00': 'available',
      '13:00': 'limited',
      '14:00': 'available',
      '15:00': 'available',
      '16:00': 'limited',
      '17:00': 'available'
    },
    wednesday: {
      '09:00': 'available',
      '10:00': 'available',
      '11:00': 'available',
      '12:00': 'limited',
      '13:00': 'available',
      '14:00': 'available',
      '15:00': 'available',
      '16:00': 'available',
      '17:00': 'limited'
    },
    thursday: {
      '09:00': 'limited',
      '10:00': 'available',
      '11:00': 'available',
      '12:00': 'available',
      '13:00': 'available',
      '14:00': 'limited',
      '15:00': 'available',
      '16:00': 'available',
      '17:00': 'available'
    },
    friday: {
      '09:00': 'available',
      '10:00': 'available',
      '11:00': 'limited',
      '12:00': 'available',
      '13:00': 'available',
      '14:00': 'available',
      '15:00': 'available',
      '16:00': 'limited',
      '17:00': 'available'
    },
    saturday: {
      '09:00': 'available',
      '10:00': 'available',
      '11:00': 'available',
      '12:00': 'available',
      '13:00': 'limited',
      '14:00': 'available',
      '15:00': 'available',
      '16:00': 'available',
      '17:00': 'unavailable'
    },
    sunday: {
      '09:00': 'unavailable',
      '10:00': 'unavailable',
      '11:00': 'unavailable',
      '12:00': 'unavailable',
      '13:00': 'unavailable',
      '14:00': 'unavailable',
      '15:00': 'unavailable',
      '16:00': 'unavailable',
      '17:00': 'unavailable'
    }
  }), []);

  const [scheduleResult, setScheduleResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);
  const [schedulingPatterns, setSchedulingPatterns] = useState(null);

  const generateSmartSchedule = async () => {
    console.log('📅 Generate Smart Schedule triggered');
    console.log('⚙️ User preferences:', userPreferences);
    console.log('🏪 Salon availability:', salonAvailability);
    console.log('💇‍♀️ Service details:', serviceDetails);
    
    setLoading(true);
    try {
      const result = await aiSmartSchedulingService.generateSmartSchedule(
        userPreferences,
        salonAvailability,
        serviceDetails
      );
      console.log('🎯 Smart schedule result:', result);
      setScheduleResult(result);
    } catch (error) {
      console.error('❌ Error generating smart schedule:', error);
      // Set a fallback result to prevent freezing
      setScheduleResult({
        recommendations: [],
        optimalTime: {
          date: new Date().toISOString().split('T')[0],
          time: '10:00',
          reason: 'Default recommendation due to error'
        },
        schedulingTips: ['Please try again later'],
        conflicts: []
      });
    } finally {
      setLoading(false);
    }
  };

  // Removed unused analyzeUserPatterns function per ESLint
  // Mock user history for pattern analysis
  // (If you want to use this, wrap in useEffect or a function, not at the top level)
  // const userHistory = [ ... ];
  // const patterns = aiSmartSchedulingService.analyzeSchedulingPatterns(userHistory);
  // setSchedulingPatterns(patterns);

  const handlePreferenceUpdate = (field, value) => {
    console.log(`🔄 Preference updated: ${field} = ${value}`);
    setUserPreferences(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleServiceUpdate = (field, value) => {
    console.log(`🔄 Service updated: ${field} = ${value}`);
    setServiceDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return '#4CAF50';
    if (confidence >= 0.6) return '#FF9800';
    return '#F44336';
  };

  const getAvailabilityColor = (availability) => {
    switch (availability) {
      case 'high': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'low': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div className="ai-smart-scheduling-page">
      <div className="scheduling-background-effects">
        <div className="scheduling-gradient-orb scheduling-orb-1"></div>
        <div className="scheduling-gradient-orb scheduling-orb-2"></div>
        <div className="scheduling-gradient-orb scheduling-orb-3"></div>
      </div>

      <div className="container">
        {/* Back Button */}
        <div className="back-button-container">
          <Link to="/ai-features" className="back-button-modern">
            <span className="back-icon">←</span>
            <span className="back-text">Back to AI Features</span>
            <div className="button-glow"></div>
          </Link>
        </div>

        <div className="scheduling-header-modern">
          <div className="scheduling-header-content">
            <div className="scheduling-header-badge">
              <span className="badge-icon">📅</span>
              <span className="badge-text">AI SCHEDULER</span>
            </div>
            <h1 className="scheduling-title-modern">
              <span className="title-gradient">Time Sync</span>
              <span className="title-accent">⚡</span>
            </h1>
            <p className="scheduling-subtitle-modern">
              AI that finds your perfect appointment window
            </p>
          </div>
        </div>

        <div className="scheduling-container-modern">
          <div className="preferences-section-modern">
            <div className="section-header">
              <span className="section-icon">⚙️</span>
              <h3 className="section-title">Your Schedule Vibe</h3>
            </div>
          
          <div className="preferences-grid">
            <div className="preference-group">
              <h4>📅 Preferred Days</h4>
              <div className="day-selector">
                {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                  <label key={day} className="day-checkbox">
                    <input
                      type="checkbox"
                      checked={userPreferences.preferredDays.includes(day)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handlePreferenceUpdate('preferredDays', [...userPreferences.preferredDays, day]);
                        } else {
                          handlePreferenceUpdate('preferredDays', userPreferences.preferredDays.filter(d => d !== day));
                        }
                      }}
                    />
                    <span>{day.slice(0, 3)}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="preference-group">
              <h4>⏰ Preferred Times</h4>
              <div className="time-selector">
                {['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'].map(time => (
                  <label key={time} className="time-checkbox">
                    <input
                      type="checkbox"
                      checked={userPreferences.preferredTimes.includes(time)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handlePreferenceUpdate('preferredTimes', [...userPreferences.preferredTimes, time]);
                        } else {
                          handlePreferenceUpdate('preferredTimes', userPreferences.preferredTimes.filter(t => t !== time));
                        }
                      }}
                    />
                    <span>{time}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="preference-group">
              <h4>🚗 Travel Time</h4>
              <input
                type="number"
                value={userPreferences.travelTime}
                onChange={(e) => handlePreferenceUpdate('travelTime', parseInt(e.target.value, 10))}
                min="5"
                max="60"
                className="travel-time-input"
              />
              <span>minutes</span>
            </div>

            <div className="preference-group">
              <h4>🔥 Urgency</h4>
              <select
                value={userPreferences.urgency}
                onChange={(e) => handlePreferenceUpdate('urgency', e.target.value)}
                className="urgency-select"
              >
                <option value="low">Low - Flexible</option>
                <option value="medium">Medium - Soon</option>
                <option value="high">High - Urgent</option>
              </select>
            </div>

            <div className="preference-group">
              <h4>💰 Budget</h4>
              <input
                type="number"
                value={userPreferences.budget}
                onChange={(e) => handlePreferenceUpdate('budget', parseInt(e.target.value, 10))}
                min="20"
                max="500"
                className="budget-input"
              />
              <span>USD</span>
            </div>

            <div className="preference-group">
              <h4>💇‍♀️ Service Type</h4>
              <select
                value={userPreferences.serviceType}
                onChange={(e) => handlePreferenceUpdate('serviceType', e.target.value)}
                className="service-select"
              >
                <option value="haircut">Haircut</option>
                <option value="hair_coloring">Hair Coloring</option>
                <option value="styling">Styling</option>
                <option value="treatment">Treatment</option>
                <option value="manicure">Manicure</option>
                <option value="pedicure">Pedicure</option>
              </select>
            </div>
          </div>
        </div>

        <div className="service-section">
          <h3>🛠️ Service Details</h3>
          <div className="service-form">
            <div className="form-group">
              <label>Service Name</label>
              <input
                type="text"
                value={serviceDetails.name}
                onChange={(e) => handleServiceUpdate('name', e.target.value)}
                className="service-input"
              />
            </div>
            
            <div className="form-group">
              <label>Duration (minutes)</label>
              <input
                type="number"
                value={serviceDetails.duration}
                onChange={(e) => handleServiceUpdate('duration', parseInt(e.target.value, 10))}
                min="15"
                max="300"
                className="duration-input"
              />
            </div>
            
            <div className="form-group">
              <label>Price (USD)</label>
              <input
                type="number"
                value={serviceDetails.price}
                onChange={(e) => handleServiceUpdate('price', parseInt(e.target.value, 10))}
                min="10"
                max="500"
                className="price-input"
              />
            </div>
          </div>
        </div>

        <div className="action-section">
          <button
            type="button" 
            onClick={generateSmartSchedule}
            disabled={loading}
            className="refresh-schedule-btn"
            style={{
              minHeight: '60px',
              minWidth: '280px',
              fontSize: '1.1rem',
              fontWeight: '600',
              borderRadius: '16px',
              boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
              transition: 'all 0.2s ease',
              transform: 'translateY(0)',
              cursor: loading ? 'not-allowed' : 'pointer',
              touchAction: 'manipulation',
              WebkitTapHighlightColor: 'transparent'
            }}
            onMouseDown={(e) => {
              if (!loading) {
                e.target.style.transform = 'translateY(2px)';
                e.target.style.boxShadow = '0 4px 16px rgba(102, 126, 234, 0.2)';
              }
            }}
            onMouseUp={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.3)';
            }}
            onTouchStart={(e) => {
              if (!loading) {
                e.target.style.transform = 'translateY(2px)';
                e.target.style.boxShadow = '0 4px 16px rgba(102, 126, 234, 0.2)';
              }
            }}
            onTouchEnd={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.3)';
            }}
          >
            {loading ? '🔄 Generating...' : '🔄 Generate Smart Schedule'}
          </button>
          
          {/* Debug info for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-2 text-muted" style={{ fontSize: '0.8rem' }}>
              Debug: Days={userPreferences.preferredDays.length}, Times={userPreferences.preferredTimes.length}, Service={serviceDetails.name}
            </div>
          )}
        </div>

        {loading && (
          <div className="loading-section">
            <div className="loading-spinner">📅</div>
            <p>AI is analyzing your preferences and finding optimal times...</p>
          </div>
        )}

        {scheduleResult && !loading && (
          <div className="schedule-results">
            <h3>🎯 Smart Recommendations</h3>
            
            <div className="optimal-time">
              <h4>⭐ Optimal Time</h4>
              <div className="optimal-card">
                <div className="optimal-date">{formatDate(scheduleResult.optimalTime.date)}</div>
                <div className="optimal-time-display">{scheduleResult.optimalTime.time}</div>
                <div className="optimal-reason">{scheduleResult.optimalTime.reason}</div>
                <button type="button" className="book-optimal-btn">Book This Time</button>
              </div>
            </div>

            <div className="recommendations-section">
              <h4>📋 All Recommendations</h4>
              <div className="recommendations-grid">
                {scheduleResult.recommendations.map((rec, index) => (
                  <div 
                    key={index} 
                    className={`recommendation-card ${selectedRecommendation === index ? 'selected' : ''}`}
                    onClick={() => setSelectedRecommendation(index)}
                  >
                    <div className="rec-header">
                      <div className="rec-date">{formatDate(rec.date)}</div>
                      <div className="rec-time">{rec.time}</div>
                      <span 
                        className="confidence-badge"
                        style={{ backgroundColor: getConfidenceColor(rec.confidence) }}
                      >
                        {Math.round(rec.confidence * 100)}
                        %
                      </span>
                    </div>
                    
                    <div className="rec-details">
                      <div className="rec-reasoning">{rec.reasoning}</div>
                      <div className="rec-meta">
                        <span>
                          ⏱️
                          {rec.totalTime}
                        </span>
                        <span>
                          💰
                          {rec.cost}
                        </span>
                        <span>
                          🚗
                          {rec.travelTime}
                        </span>
                      </div>
                      <div className="rec-availability">
                        <span 
                          className="availability-badge"
                          style={{ backgroundColor: getAvailabilityColor(rec.availability) }}
                        >
                          {rec.availability}
                          {' '}
                          availability
                        </span>
                      </div>
                    </div>

                    <div className="rec-alternatives">
                      <strong>Alternatives:</strong>
                      <div className="alternatives-list">
                        {rec.alternatives.map((alt, i) => (
                          <span key={i} className="alternative-time">{alt}</span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="scheduling-tips">
              <h4>💡 Smart Tips</h4>
              <ul>
                {scheduleResult.schedulingTips.map((tip, index) => (
                  <li key={index}>{tip}</li>
                ))}
              </ul>
            </div>

            {scheduleResult.conflicts.length > 0 && (
              <div className="conflicts-section">
                <h4>⚠️ Potential Conflicts</h4>
                <div className="conflicts-list">
                  {scheduleResult.conflicts.map((conflict, index) => (
                    <div key={index} className="conflict-card">
                      <div className="conflict-type">{conflict.type}</div>
                      <div className="conflict-description">{conflict.description}</div>
                      <div className="conflict-solution">
                        <strong>Solution:</strong> 
                        {' '}
                        {conflict.solution}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {schedulingPatterns && (
          <div className="patterns-section">
            <h3>📊 Your Scheduling Patterns</h3>
            <div className="patterns-grid">
              <div className="pattern-card">
                <h4>📅 Preferred Days</h4>
                <div className="pattern-list">
                  {schedulingPatterns.preferredDays.map((day, index) => (
                    <span key={index} className="pattern-item">{day}</span>
                  ))}
                </div>
              </div>
              
              <div className="pattern-card">
                <h4>⏰ Preferred Times</h4>
                <div className="pattern-list">
                  {schedulingPatterns.preferredTimes.map((time, index) => (
                    <span key={index} className="pattern-item">{time}</span>
                  ))}
                </div>
              </div>
              
              <div className="pattern-card">
                <h4>⏱️ Average Duration</h4>
                <div className="pattern-value">
                  {Math.round(schedulingPatterns.averageDuration)}
                  {' '}
                  minutes
                </div>
              </div>
              
              <div className="pattern-card">
                <h4>💇‍♀️ Common Services</h4>
                <div className="pattern-list">
                  {schedulingPatterns.commonServices.map((service, index) => (
                    <span key={index} className="pattern-item">{service}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};
export default AISmartScheduling;
