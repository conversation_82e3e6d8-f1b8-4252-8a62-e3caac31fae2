/* AI Style Advisor Styles */
.ai-style-advisor {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Back Button Styles */
.back-button-container {
  margin-bottom: 20px;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 107, 157, 0.2);
  color: #ff6b9d;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 107, 157, 0.3);
  font-size: 0.9rem;
}

.back-button:hover {
  background: rgba(255, 107, 157, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #ff6b9d;
  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.advisor-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.advisor-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.advisor-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

/* Tabs */
.advisor-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.tab {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #333;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Content */
.advisor-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Profile Section */
.profile-section {
  max-width: 800px;
  margin: 0 auto;
}

/* Profile Form */
.profile-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  font-size: 0.9rem;
}

.form-group select,
.form-group input {
  padding: 12px 15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* Preferences Grid */
.preferences-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.preference-btn {
  padding: 12px 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  backdrop-filter: blur(5px);
}

.preference-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
}

.preference-btn.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

/* Get Recommendations Button */
.get-recommendations-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
  margin-top: 20px;
  width: 100%;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.get-recommendations-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.get-recommendations-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Recommendations Section */
.recommendations-section {
  max-width: 1000px;
  margin: 0 auto;
}

.no-recommendations {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 107, 157, 0.1);
  border-radius: 15px;
  border: 2px dashed rgba(255, 107, 157, 0.3);
}

.no-recommendations p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 20px;
}

.no-recommendations button {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.no-recommendations button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 157, 0.3);
}

.recommendations-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.recommendations-header {
  text-align: center;
  margin-bottom: 30px;
}

.recommendations-header h3 {
  font-size: 1.8rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.general-advice {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 10px;
  font-style: italic;
}

.trending-notes {
  font-size: 1rem;
  color: #777;
  background: rgba(255, 107, 157, 0.1);
  padding: 15px;
  border-radius: 12px;
  border-left: 4px solid #ff6b9d;
}

/* Recommendations Grid */
.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 30px;
}

.recommendation-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 18px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 120px;
  height: 100%;
}

.recommendation-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.rec-header h4 {
  font-size: 1.3rem;
  margin: 0;
  color: #333;
  font-weight: 700;
}

.rec-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.cost-low {
  background: rgba(46, 204, 113, 0.2);
  color: #27ae60;
  border: 1px solid rgba(46, 204, 113, 0.3);
}

.badge.cost-medium {
  background: rgba(241, 196, 15, 0.2);
  color: #f39c12;
  border: 1px solid rgba(241, 196, 15, 0.3);
}

.badge.cost-high {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.badge.maintenance-low {
  background: rgba(46, 204, 113, 0.2);
  color: #27ae60;
  border: 1px solid rgba(46, 204, 113, 0.3);
}

.badge.maintenance-medium {
  background: rgba(241, 196, 15, 0.2);
  color: #f39c12;
  border: 1px solid rgba(241, 196, 15, 0.3);
}

.badge.maintenance-high {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.rec-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.rec-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item .label {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.detail-item ul {
  margin: 0;
  padding-left: 20px;
  color: #555;
}

.detail-item li {
  margin-bottom: 5px;
  line-height: 1.5;
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  padding: 20px;
}

.pagination-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
  font-size: 0.9rem;
}

.pagination-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
}

.pagination-info {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(5px);
}

/* Analysis Section */
.analysis-section {
  max-width: 800px;
  margin: 0 auto;
}

.analysis-input {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.analysis-input label {
  display: block;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
  font-size: 1rem;
}

.analysis-input textarea {
  width: 100%;
  padding: 15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.analysis-input textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.analysis-input button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
  margin-top: 15px;
  width: 100%;
  max-width: 300px;
}

.analysis-input button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.analysis-input button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.analysis-results {
  display: grid;
  gap: 20px;
}

.analysis-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(10px);
}

.analysis-card h4 {
  font-size: 1.2rem;
  margin: 0 0 15px 0;
  color: #333;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-card p {
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.analysis-card ul {
  margin: 0;
  padding-left: 20px;
  color: #555;
}

.analysis-card li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Trending Section */
.trending-section {
  max-width: 1000px;
  margin: 0 auto;
}

.trending-section h3 {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.trending-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 107, 157, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.trending-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border-color: #ff6b9d;
}

.trending-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.trending-header h4 {
  font-size: 1.3rem;
  margin: 0;
  color: #333;
  font-weight: 700;
}

.trending-badge {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trending-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.trending-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.difficulty {
  font-size: 0.9rem;
  color: #555;
  font-weight: 600;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .ai-style-advisor {
    padding: 15px;
  }

  .advisor-header {
    padding: 20px;
  }

  .advisor-header h2 {
    font-size: 2rem;
  }

  .advisor-tabs {
    gap: 8px;
  }

  .tab {
    padding: 10px 15px;
    font-size: 0.85rem;
  }

  .advisor-content {
    padding: 20px;
  }

  .profile-form {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .trending-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .recommendation-card,
  .trending-card,
  .analysis-card {
    padding: 20px;
  }

  .rec-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .trending-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .advisor-header h2 {
    font-size: 1.8rem;
  }

  .advisor-tabs {
    flex-direction: column;
    align-items: center;
  }

  .tab {
    width: 100%;
    max-width: 200px;
    text-align: center;
  }

  .recommendations-header h3,
  .trending-section h3 {
    font-size: 1.5rem;
  }
}




