import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styleAdvisorService from '../services/styleAdvisorService';
import './AIStyleAdvisor.css';

const AIStyleAdvisor = () => {
  const [userProfile, setUserProfile] = useState({
    hairType: '',
    faceShape: '',
    occasion: '',
    stylePreferences: [],
    budget: '',
    maintenanceLevel: ''
  });

  const [recommendations, setRecommendations] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentStyle, setCurrentStyle] = useState('');
  const [styleAnalysis, setStyleAnalysis] = useState(null);
  const [activeTab, setActiveTab] = useState('profile');
  const [currentPage, setCurrentPage] = useState(0);

  const hairTypes = ['Straight', 'Wavy', 'Curly', 'Coily', 'Fine', 'Thick', 'Mixed'];
  const faceShapes = ['Oval', 'Round', 'Square', 'Heart', 'Diamond', 'Rectangle'];
  const occasions = ['Everyday', 'Work/Professional', 'Date Night', 'Party/Event', 'Wedding', 'Casual'];
  const stylePreferences = ['Natural', 'Edgy', 'Classic', 'Trendy', 'Minimal', 'Bold', 'Low Maintenance', 'High Impact'];
  const budgets = ['Budget-Friendly', 'Mid-Range', 'Premium'];
  const maintenanceLevels = ['Low', 'Medium', 'High'];

  // Pagination logic
  const isMobile = window.innerWidth <= 768;
  const itemsPerPage = isMobile ? 1 : 2;
  const totalPages = recommendations ? Math.ceil(recommendations.recommendations.length / itemsPerPage) : 0;
  const currentRecommendations = recommendations ?
    recommendations.recommendations.slice(currentPage * itemsPerPage, (currentPage + 1) * itemsPerPage) : [];

  const nextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleInputChange = (field, value) => {
    setUserProfile(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePreferenceToggle = (preference) => {
    setUserProfile(prev => ({
      ...prev,
      stylePreferences: prev.stylePreferences.includes(preference)
        ? prev.stylePreferences.filter(p => p !== preference)
        : [...prev.stylePreferences, preference]
    }));
  };

  const getRecommendations = async () => {
    setLoading(true);
    try {
      const result = await styleAdvisorService.getStyleRecommendations(userProfile);
      setRecommendations(result);
      setCurrentPage(0); // Reset to first page when new recommendations are loaded
    } catch (error) {
      console.error('Error getting recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const analyzeCurrentStyle = async () => {
    if (!currentStyle.trim()) return;

    setLoading(true);
    try {
      const analysis = await styleAdvisorService.analyzeCurrentStyle(currentStyle, userProfile);
      setStyleAnalysis(analysis);
    } catch (error) {
      console.error('Error analyzing style:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTrendingStyles = () => styleAdvisorService.getTrendingStyles();

  const isProfileComplete = () => userProfile.hairType && userProfile.faceShape && userProfile.occasion
           && userProfile.stylePreferences.length > 0 && userProfile.budget && userProfile.maintenanceLevel;

  return (
    <div className="ai-style-advisor">
      {/* Back Button */}
      <div className="back-button-container">
        <Link to="/ai-features" className="back-button">
          <span className="back-icon">←</span>
          <span className="back-text">Back to AI Features</span>
        </Link>
      </div>

      <div className="advisor-header">
        <h2>✨ AI Style Advisor</h2>
        <p>Get personalized style recommendations powered by AI</p>
      </div>

      <div className="advisor-tabs">
        <button
          className={`tab ${activeTab === 'profile' ? 'active' : ''}`}
          onClick={() => setActiveTab('profile')}
        >
          📝 Profile
        </button>
        <button
          className={`tab ${activeTab === 'recommendations' ? 'active' : ''}`}
          onClick={() => setActiveTab('recommendations')}
        >
          💡 Recommendations
        </button>
        <button
          className={`tab ${activeTab === 'analysis' ? 'active' : ''}`}
          onClick={() => setActiveTab('analysis')}
        >
          🔍 Style Analysis
        </button>
        <button
          className={`tab ${activeTab === 'trending' ? 'active' : ''}`}
          onClick={() => setActiveTab('trending')}
        >
          🔥 Trending
        </button>
      </div>

      <div className="advisor-content">
        {activeTab === 'profile' && (
          <div className="profile-section">
              <div className="profile-form">
                <div className="form-group">
                  <label>Hair Type</label>
                  <select
                    value={userProfile.hairType}
                    onChange={(e) => handleInputChange('hairType', e.target.value)}
                  >
                    <option value="">Select hair type</option>
                    {hairTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>Face Shape</label>
                  <select
                    value={userProfile.faceShape}
                    onChange={(e) => handleInputChange('faceShape', e.target.value)}
                  >
                    <option value="">Select face shape</option>
                    {faceShapes.map(shape => (
                      <option key={shape} value={shape}>{shape}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>Occasion</label>
                  <select
                    value={userProfile.occasion}
                    onChange={(e) => handleInputChange('occasion', e.target.value)}
                  >
                    <option value="">Select occasion</option>
                    {occasions.map(occasion => (
                      <option key={occasion} value={occasion}>{occasion}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>Budget</label>
                  <select
                    value={userProfile.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                  >
                    <option value="">Select budget</option>
                    {budgets.map(budget => (
                      <option key={budget} value={budget}>{budget}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>Maintenance Level</label>
                  <select
                    value={userProfile.maintenanceLevel}
                    onChange={(e) => handleInputChange('maintenanceLevel', e.target.value)}
                  >
                    <option value="">Select maintenance level</option>
                    {maintenanceLevels.map(level => (
                      <option key={level} value={level}>{level}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label>Style Preferences (select multiple)</label>
                <div className="preferences-grid">
                  {stylePreferences.map(preference => (
                    <button
                      key={preference}
                      className={`preference-btn ${userProfile.stylePreferences.includes(preference) ? 'selected' : ''}`}
                      onClick={() => handlePreferenceToggle(preference)}
                    >
                      {preference}
                    </button>
                  ))}
                </div>
              </div>

            <button
              className="get-recommendations-btn"
              onClick={getRecommendations}
              disabled={!isProfileComplete() || loading}
            >
              {loading ? '✨ AI is thinking...' : '✨ Get AI Recommendations'}
            </button>
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div className="recommendations-section">
            {!recommendations ? (
              <div className="no-recommendations">
                <p>Complete your profile first to get personalized recommendations!</p>
                <button onClick={() => setActiveTab('profile')}>
                  Go to Profile
                </button>
              </div>
            ) : (
              <div className="recommendations-container">
                <div className="recommendations-header">
                  <h3>✨ Your Personalized Recommendations</h3>
                  <p className="general-advice">{recommendations.generalAdvice}</p>
                  <p className="trending-notes">{recommendations.trendingNotes}</p>
                </div>

                <div className="recommendations-grid">
                  {currentRecommendations.map((rec, index) => (
                    <div key={index} className="recommendation-card">
                      <div className="rec-header">
                        <h4>{rec.name}</h4>
                        <div className="rec-badges">
                          <span className={`badge cost-${rec.costRange?.toLowerCase() || 'medium'}`}>
                            {rec.costRange || 'Medium'}
                          </span>
                          <span className={`badge maintenance-${rec.maintenance?.toLowerCase() || 'medium'}`}>
                            {rec.maintenance || 'Medium'}
                          </span>
                        </div>
                      </div>

                      <p className="rec-description">{rec.description}</p>

                      <div className="rec-details">
                        <div className="detail-item">
                          <span className="label">⏱️ Styling Time:</span>
                          <span>{rec.stylingTime}</span>
                        </div>

                        <div className="detail-item">
                          <span className="label">💡 Styling Tips:</span>
                          <ul>
                            {rec.stylingTips.map((tip, i) => (
                              <li key={i}>{tip}</li>
                            ))}
                          </ul>
                        </div>

                        <div className="detail-item">
                          <span className="label">🛍️ Products Needed:</span>
                          <ul>
                            {rec.products.map((product, i) => (
                              <li key={i}>{product}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="pagination-controls">
                    <button
                      onClick={prevPage}
                      disabled={currentPage === 0}
                      className="pagination-btn"
                    >
                      ← Previous
                    </button>
                    <span className="pagination-info">
                      {currentPage + 1} of {totalPages}
                    </span>
                    <button
                      onClick={nextPage}
                      disabled={currentPage === totalPages - 1}
                      className="pagination-btn"
                    >
                      Next →
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === 'analysis' && (
          <div className="analysis-section">
            <div className="analysis-input">
              <label>Describe your current hairstyle:</label>
              <textarea
                value={currentStyle}
                onChange={(e) => setCurrentStyle(e.target.value)}
                placeholder="Describe your current style, what you like/dislike, and what you want to improve..."
                rows="4"
              />
              <button
                onClick={analyzeCurrentStyle}
                disabled={!currentStyle.trim() || loading}
              >
                {loading ? '🔍 Analyzing...' : '🔍 Analyze My Style'}
              </button>
            </div>

            {styleAnalysis && (
              <div className="analysis-results">
                <h3>✨ Style Analysis Results</h3>

                <div className="analysis-card">
                  <h4>📊 Analysis</h4>
                  <p>{styleAnalysis.analysis}</p>
                </div>

                <div className="analysis-card">
                  <h4>🚀 Improvements</h4>
                  <ul>
                    {styleAnalysis.improvements?.map((improvement, i) => (
                      <li key={i}>{improvement}</li>
                    ))}
                  </ul>
                </div>

                <div className="analysis-card">
                  <h4>💡 Styling Tips</h4>
                  <ul>
                    {styleAnalysis.stylingTips?.map((tip, i) => (
                      <li key={i}>{tip}</li>
                    ))}
                  </ul>
                </div>

                <div className="analysis-card">
                  <h4>🛍️ Recommended Products</h4>
                  <ul>
                    {styleAnalysis.products?.map((product, i) => (
                      <li key={i}>{product}</li>
                    ))}
                  </ul>
                </div>

                <div className="analysis-card">
                  <h4>⏰ Maintenance</h4>
                  <p>{styleAnalysis.maintenance}</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'trending' && (
          <div className="trending-section">
            <h3>🔥 Trending Styles for Gen-Z</h3>
            <div className="trending-grid">
              {getTrendingStyles().map((style, index) => (
                <div key={index} className="trending-card">
                  <div className="trending-header">
                    <h4>{style.name}</h4>
                    <span className="trending-badge">🔥 Trending</span>
                  </div>
                  <p>{style.description}</p>
                  <div className="trending-details">
                    <span className="difficulty">Difficulty: {style.difficulty}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIStyleAdvisor;
