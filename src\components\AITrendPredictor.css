/* ===== AI Trend Predictor - Enterprise Gen Z Design ===== */

.ai-trend-predictor-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.predictor-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.predictor-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: predictorFloat 22s ease-in-out infinite;
}

.predictor-orb-1 {
  width: 310px;
  height: 310px;
  background: linear-gradient(135deg, #ff9800, #e91e63);
  top: 5%;
  left: -25%;
  animation-delay: 0s;
}

.predictor-orb-2 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #673ab7, #3f51b5);
  top: 90%;
  right: -22%;
  animation-delay: 10s;
}

.predictor-orb-3 {
  width: 270px;
  height: 270px;
  background: linear-gradient(135deg, #ff5722, #9c27b0);
  bottom: 45%;
  left: 30%;
  animation-delay: 18s;
}

@keyframes predictorFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  9% { transform: translateY(-50px) rotate(32deg) scale(1.35); }
  18% { transform: translateY(40px) rotate(64deg) scale(0.65); }
  27% { transform: translateY(-35px) rotate(96deg) scale(1.2); }
  36% { transform: translateY(45px) rotate(128deg) scale(0.75); }
  45% { transform: translateY(-40px) rotate(160deg) scale(1.25); }
  54% { transform: translateY(30px) rotate(192deg) scale(0.8); }
  63% { transform: translateY(-45px) rotate(224deg) scale(1.15); }
  72% { transform: translateY(35px) rotate(256deg) scale(0.85); }
  81% { transform: translateY(-30px) rotate(288deg) scale(1.1); }
  90% { transform: translateY(25px) rotate(320deg) scale(0.9); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(255, 152, 0, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 152, 0, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

.back-button:hover {
  background: rgba(255, 152, 0, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #ff9800;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.predictor-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.predictor-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.predictor-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 20px 0;
}

.predictor-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.platform-select {
  padding: 10px 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.platform-select option {
  background: #333;
  color: white;
}

.refresh-btn {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Predictor Tabs */
.predictor-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.tab {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #333;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Predictor Content */
.predictor-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Predictions Section */
.predictions-section {
  max-width: 1000px;
  margin: 0 auto;
}

.predictions-summary {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  margin-bottom: 30px;
}

.predictions-summary h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.3rem;
}

.predictions-summary p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
}

.trending-styles {
  margin-bottom: 30px;
}

.trending-styles h3 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.styles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.style-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  text-align: center;
}

.style-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.style-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

.style-card h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.1rem;
}

.platform-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.virality-predictions {
  margin-top: 30px;
}

.virality-predictions h3 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.virality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.virality-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.virality-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.virality-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.virality-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.virality-score {
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.virality-card p {
  margin: 0 0 15px 0;
  color: #666;
  line-height: 1.5;
}

.virality-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
}

/* Emerging Trends Section */
.emerging-trends-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.trends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.trend-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.trend-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.trend-card.emerging {
  border-left: 4px solid #4CAF50;
}

.trend-card.declining {
  border-left: 4px solid #F44336;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.trend-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.trend-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.confidence-badge,
.impact-badge,
.decline-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.trend-description {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.5;
  font-size: 1rem;
}

.trend-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
}

.detail-item .label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.detail-item span:last-child {
  color: #666;
  font-size: 0.9rem;
}

.hype-badge {
  padding: 4px 8px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.influencers {
  margin-bottom: 20px;
}

.influencers .label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 10px;
  display: block;
}

.influencer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.influencer-tag {
  background: rgba(255, 193, 7, 0.1);
  color: #FF9800;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.prediction-reasoning {
  background: rgba(102, 126, 234, 0.1);
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.prediction-reasoning strong {
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 5px;
  display: block;
}

.prediction-reasoning p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Declining Trends Section */
.declining-trends-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.replacement-trend {
  background: rgba(76, 175, 80, 0.1);
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #4CAF50;
  margin-top: 15px;
}

.replacement-trend strong {
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 5px;
  display: block;
}

.replacement-trend p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Seasonal Section */
.seasonal-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.seasons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.season-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.season-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.season-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.season-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  text-transform: capitalize;
}

.season-icon {
  font-size: 2rem;
}

.season-trends {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.season-trend {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
}

.trend-icon {
  font-size: 1.2rem;
}

/* Recommendations Section */
.recommendations-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.recommendation-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  border-left: 4px solid #667eea;
}

.recommendation-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.recommendation-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  line-height: 1.3;
}

.priority-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.recommendation-category {
  margin-bottom: 15px;
  padding: 10px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
}

.recommendation-category strong {
  color: #333;
  font-size: 0.9rem;
}

.expected-roi {
  background: rgba(76, 175, 80, 0.1);
  padding: 10px;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.expected-roi strong {
  color: #333;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-trend-predictor {
    padding: 15px;
  }
  
  .predictor-header {
    padding: 20px;
  }
  
  .predictor-header h2 {
    font-size: 2rem;
  }
  
  .predictor-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .predictor-tabs {
    gap: 8px;
  }
  
  .tab {
    padding: 10px 16px;
    font-size: 0.8rem;
  }
  
  .predictor-content {
    padding: 20px;
  }
  
  .trends-grid {
    grid-template-columns: 1fr;
  }
  
  .virality-grid {
    grid-template-columns: 1fr;
  }
  
  .seasons-grid {
    grid-template-columns: 1fr;
  }
  
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .trend-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .trend-badges {
    justify-content: flex-start;
  }
  
  .virality-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .predictor-header h2 {
    font-size: 1.8rem;
  }
  
  .predictor-header p {
    font-size: 1rem;
  }
  
  .trend-card {
    padding: 20px;
  }
  
  .virality-card {
    padding: 20px;
  }
  
  .season-card {
    padding: 20px;
  }
  
  .recommendation-card {
    padding: 20px;
  }
} 