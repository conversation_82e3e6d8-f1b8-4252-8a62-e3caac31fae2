import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import aiTrendPredictorService from '../services/aiTrendPredictorService';
import './AITrendPredictorProfile.css';

const AITrendPredictor = () => {
  const [trendData, setTrendData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('predictions');
  const [selectedPlatform, setSelectedPlatform] = useState('all');

  // Mock current data for demonstration
  const mockCurrentData = {
    socialMediaTrends: {
      trendingHashtags: ['#HairTok', '#SalonLife', '#GenZBeauty'],
      viralStyles: ['Wolf Cut', 'Butterfly Cut', 'Space Buns'],
      engagement: { tiktok: '8.5%', instagram: '6.2%', pinterest: '4.8%' }
    },
    fashionData: {
      runwayTrends: ['Natural Textures', 'Bold Colors', 'Minimal Styling'],
      celebrityInfluences: ['<PERSON> E<PERSON>', 'Doja Cat', 'Olivia Rodrigo'],
      seasonalColors: {
        spring: ['Lavender', 'Mint', 'Peach'],
        summer: ['Coral', 'Turquoise', 'Sunset Orange']
      }
    },
    salonBookings: [
      { service: 'Hair Color', date: '2024-01-15', customerId: 1 },
      { service: 'Haircut', date: '2024-01-16', customerId: 2 },
      { service: 'Styling', date: '2024-01-17', customerId: 3 }
    ],
    seasonalFactors: {
      currentSeason: 'winter',
      upcomingSeason: 'spring',
      seasonalPreferences: ['Warm Tones', 'Layered Cuts', 'Textured Styles']
    },
    celebrityInfluences: [
      { name: 'Billie Eilish', influence: 'Hair Color', reach: '50M' },
      { name: 'Doja Cat', influence: 'Styling', reach: '45M' },
      { name: 'Olivia Rodrigo', influence: 'Natural Looks', reach: '40M' }
    ]
  };

  const generateTrendPredictions = async () => {
    setLoading(true);
    try {
      const report = await aiTrendPredictorService.generateTrendReport(mockCurrentData);
      setTrendData(report);
    } catch (error) {
      console.error('Error generating trend predictions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return '#4CAF50';
    if (confidence >= 0.6) return '#FF9800';
    return '#F44336';
  };

  const getViralityColor = (score) => {
    if (score >= 0.7) return '#4CAF50';
    if (score >= 0.5) return '#FF9800';
    return '#F44336';
  };

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'high': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'low': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getHypeColor = (hype) => {
    switch (hype) {
      case 'very high': return '#4CAF50';
      case 'high': return '#8BC34A';
      case 'medium': return '#FF9800';
      case 'low': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  return (
    <div className="ai-trend-predictor-profile-container">
      <div className="profile-container">
        <div className="trend-sparkles">
          <span className="trend-sparkle">🔮</span>
          <span className="trend-sparkle">✨</span>
          <span className="trend-sparkle">📈</span>
          <span className="trend-sparkle">🚀</span>
          <span className="trend-sparkle">💫</span>
        </div>

        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">🔮</div>
          </div>
          <h1 className="profile-title">Trend Vision✨</h1>
          <p className="profile-subtitle">See tomorrow's trends today with AI</p>

          {/* Quick Stats */}
          <div className="trend-stats-modern">
            <div className="stat-card">
              <div className="stat-icon">📈</div>
              <div className="stat-content">
                <span className="stat-number">95%</span>
                <span className="stat-label">Accuracy</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🚀</div>
              <div className="stat-content">
                <span className="stat-number">24/7</span>
                <span className="stat-label">Monitoring</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">💫</div>
              <div className="stat-content">
                <span className="stat-number">∞</span>
                <span className="stat-label">Insights</span>
              </div>
            </div>
          </div>
        </div>

        <div className="profile-content">
          {/* Navigation Section */}
          <div className="profile-section">
            <h3 className="section-title">🏠 Navigation</h3>
            <div className="back-button-container">
              <Link to="/ai-features" className="back-button-modern">
                <span className="back-icon">←</span>
                <span className="back-text">Back to AI Features</span>
                <div className="button-glow"></div>
              </Link>
            </div>
          </div>

          {/* AI Controls Section */}
          <div className="profile-section">
            <h3 className="section-title">⚙️ AI Controls</h3>
            <div className="predictor-controls-modern">
              <div className="control-group">
                <label className="control-label">📱 Platform Focus</label>
                <select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                  className="platform-select-modern"
                >
                  <option value="all">All Platforms</option>
                  <option value="tiktok">TikTok</option>
                  <option value="instagram">Instagram</option>
                  <option value="pinterest">Pinterest</option>
                </select>
              </div>

              <button
                className="refresh-btn-modern"
                onClick={generateTrendPredictions}
                disabled={loading}
              >
                {loading ? '🔮 AI is analyzing...' : '🔮 Refresh Predictions'}
              </button>
            </div>
          </div>

          {/* Trend Analysis Menu */}
          <div className="profile-section">
            <h3 className="section-title">📊 Trend Analysis Menu</h3>
            <div className="predictor-tabs">
              <button
                className={`tab ${activeTab === 'predictions' ? 'active' : ''}`}
                onClick={() => setActiveTab('predictions')}
              >
                🔮 Predictions
              </button>
              <button
                className={`tab ${activeTab === 'emerging' ? 'active' : ''}`}
                onClick={() => setActiveTab('emerging')}
              >
                📈 Emerging Trends
              </button>
              <button
                className={`tab ${activeTab === 'declining' ? 'active' : ''}`}
                onClick={() => setActiveTab('declining')}
              >
                📉 Declining Trends
              </button>
              <button
                className={`tab ${activeTab === 'seasonal' ? 'active' : ''}`}
                onClick={() => setActiveTab('seasonal')}
              >
                🌸 Seasonal
              </button>
              <button
                className={`tab ${activeTab === 'recommendations' ? 'active' : ''}`}
                onClick={() => setActiveTab('recommendations')}
              >
                💡 Recommendations
              </button>
            </div>
          </div>

          {/* Content Sections */}
          {activeTab === 'predictions' && (
            <>
              <div className="profile-section">
                <h3 className="section-title">🎯 AI Trend Analysis Summary</h3>
                <div className="predictions-summary">
                  <p>{trendData?.predictions?.summary || 'AI is analyzing current trends across social media platforms, fashion weeks, and celebrity influences to predict the next big beauty movements. Our advanced algorithms process millions of data points to deliver accurate trend forecasts.'}</p>
                </div>
              </div>

              <div className="profile-section">
                <h3 className="section-title">🔥 Trending Styles</h3>
                <div className="styles-grid">
                  {aiTrendPredictorService.getTrendingStyles(selectedPlatform).map((style, index) => (
                    <div key={index} className="style-card">
                      <div className="style-icon">💇‍♀️</div>
                      <h4>{style}</h4>
                      <span className="platform-badge">{selectedPlatform}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="profile-section">
                <h3 className="section-title">🚀 Virality Predictions</h3>
                <div className="virality-grid">
                  {trendData?.predictions?.emergingTrends?.slice(0, 3).map((trend, index) => (
                    <div key={index} className="virality-card">
                      <div className="virality-header">
                        <h4>{trend.name}</h4>
                        <span
                          className="virality-score"
                          style={{ backgroundColor: getViralityColor(trend.virality?.score || 0) }}
                        >
                          {Math.round((trend.virality?.score || 0) * 100)}
                          %
                        </span>
                      </div>
                      <p>{trend.description}</p>
                      <div className="virality-details">
                        <span>
                          Timeframe:
                          {trend.virality?.timeframe}
                        </span>
                        <span>
                          Probability:
                          {Math.round(trend.virality?.probability || 0)}
                          %
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {activeTab === 'emerging' && (
            <div className="profile-section">
              <h3 className="section-title">📈 Emerging Trends</h3>
              <div className="trends-grid">
                {trendData?.predictions?.emergingTrends?.map((trend, index) => (
                  <div key={index} className="trend-card emerging">
                    <div className="trend-header">
                      <h4>{trend.name}</h4>
                      <div className="trend-badges">
                        <span
                          className="confidence-badge"
                          style={{ backgroundColor: getConfidenceColor(trend.confidence) }}
                        >
                          {Math.round(trend.confidence * 100)}
                          % confidence
                        </span>
                        <span
                          className="impact-badge"
                          style={{ backgroundColor: getImpactColor(trend.salonImpact) }}
                        >
                          {trend.salonImpact}
                          {' '}
                          impact
                        </span>
                      </div>
                    </div>

                    <p className="trend-description">{trend.description}</p>

                    <div className="trend-details">
                      <div className="detail-item">
                        <span className="label">⏰ Timeframe:</span>
                        <span>{trend.timeframe}</span>
                      </div>
                      <div className="detail-item">
                        <span className="label">👥 Target:</span>
                        <span>{trend.targetAudience}</span>
                      </div>
                      <div className="detail-item">
                        <span className="label">📱 Social Hype:</span>
                        <span
                          className="hype-badge"
                          style={{ backgroundColor: getHypeColor(trend.socialMediaHype) }}
                        >
                          {trend.socialMediaHype}
                        </span>
                      </div>
                    </div>

                    <div className="influencers">
                      <span className="label">🌟 Influencers:</span>
                      <div className="influencer-tags">
                        {trend.influencers.map((influencer, i) => (
                          <span key={i} className="influencer-tag">{influencer}</span>
                        ))}
                      </div>
                    </div>

                    <div className="prediction-reasoning">
                      <strong>🔮 Why this will trend:</strong>
                      <p>{trend.predictionReasoning}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'declining' && (
            <div className="profile-section">
              <h3 className="section-title">📉 Declining Trends</h3>
              <div className="trends-grid">
                {trendData?.predictions?.decliningTrends?.map((trend, index) => (
                  <div key={index} className="trend-card declining">
                    <div className="trend-header">
                      <h4>{trend.name}</h4>
                      <span
                        className="decline-badge"
                        style={{ backgroundColor: getImpactColor(trend.declineRate) }}
                      >
                        {trend.declineRate}
                        {' '}
                        decline
                      </span>
                    </div>

                    <p className="trend-description">{trend.description}</p>

                    <div className="replacement-trend">
                      <strong>🔄 Replaced by:</strong>
                      <p>{trend.replacementTrend}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'seasonal' && (
            <div className="profile-section">
              <h3 className="section-title">🌸 Seasonal Predictions</h3>
              <div className="seasons-grid">
                {Object.entries(trendData?.predictions?.seasonalPredictions || {}).map(([season, trends]) => (
                  <div key={season} className="season-card">
                    <div className="season-header">
                      <h4>{season.charAt(0).toUpperCase() + season.slice(1)}</h4>
                      <span className="season-icon">
                        {season === 'spring' ? '🌸'
                          : season === 'summer' ? '☀️'
                            : season === 'fall' ? '🍂' : '❄️'}
                      </span>
                    </div>

                    <div className="season-trends">
                      {trends.map((trend, index) => (
                        <div key={index} className="season-trend">
                          <span className="trend-icon">💇‍♀️</span>
                          <span>{trend}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'recommendations' && (
            <div className="profile-section">
              <h3 className="section-title">💡 Business Recommendations</h3>
              <div className="recommendations-grid">
                {trendData?.predictions?.businessRecommendations?.map((rec, index) => (
                  <div key={index} className="recommendation-card">
                    <div className="recommendation-header">
                      <h4>{rec.recommendation}</h4>
                      <span
                        className="priority-badge"
                        style={{ backgroundColor: getImpactColor(rec.priority) }}
                      >
                        {rec.priority}
                        {' '}
                        priority
                      </span>
                    </div>

                    <div className="recommendation-category">
                      <strong>Category:</strong>
                      {' '}
                      {rec.category}
                    </div>

                    <div className="expected-roi">
                      <strong>Expected ROI:</strong>
                      {' '}
                      {rec.expectedROI}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AITrendPredictor; 
