/* AI Trend Predictor - Profile Design Pattern with Dark Theme */

/* Main Container - Profile Pattern with Dark Trend Predictor Theme */
.ai-trend-predictor-profile-container {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Floating Background Effects */
.ai-trend-predictor-profile-container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 152, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(233, 30, 99, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(103, 58, 183, 0.08) 0%, transparent 50%);
  animation: trendBackgroundFloat 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes trendBackgroundFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.ai-trend-predictor-profile-container .profile-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: #161b22;
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 15px 35px rgba(255, 152, 0, 0.1),
    0 5px 15px rgba(233, 30, 99, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px) saturate(180%);
  border: 2px solid;
  border-image: linear-gradient(145deg,
    rgba(255, 152, 0, 0.3) 0%,
    rgba(233, 30, 99, 0.2) 50%,
    rgba(103, 58, 183, 0.3) 100%) 1;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.ai-trend-predictor-profile-container .profile-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 152, 0, 0.4), 
    rgba(233, 30, 99, 0.4), 
    transparent);
  animation: trendBorderGlow 4s ease-in-out infinite;
}

@keyframes trendBorderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Floating Trend Sparkles */
.ai-trend-predictor-profile-container .trend-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.ai-trend-predictor-profile-container .trend-sparkle {
  position: absolute;
  font-size: 1rem;
  color: rgba(255, 152, 0, 0.6);
  animation: trendSparkleFloat 8s ease-in-out infinite;
}

.ai-trend-predictor-profile-container .trend-sparkle:nth-child(1) {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.ai-trend-predictor-profile-container .trend-sparkle:nth-child(2) {
  top: 25%;
  right: 15%;
  animation-delay: 1.5s;
}

.ai-trend-predictor-profile-container .trend-sparkle:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 3s;
}

.ai-trend-predictor-profile-container .trend-sparkle:nth-child(4) {
  top: 40%;
  right: 25%;
  animation-delay: 4.5s;
}

.ai-trend-predictor-profile-container .trend-sparkle:nth-child(5) {
  bottom: 20%;
  right: 10%;
  animation-delay: 6s;
}

@keyframes trendSparkleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
  50% { transform: translateY(-15px) rotate(180deg); opacity: 1; }
}

/* Header - Profile Pattern with Dark Theme */
.ai-trend-predictor-profile-container .profile-header {
  background: linear-gradient(135deg,
    rgba(255, 152, 0, 0.1) 0%,
    rgba(233, 30, 99, 0.05) 50%,
    rgba(103, 58, 183, 0.1) 100%);
  padding: 2.5rem 2rem 1.5rem 2rem;
  text-align: center;
  color: #f0f6fc;
  position: relative;
  z-index: 2;
  border-radius: 24px 24px 0 0;
}

.ai-trend-predictor-profile-container .auth-icon-wrapper {
  margin-bottom: 1rem;
}

.ai-trend-predictor-profile-container .auth-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(255, 152, 0, 0.2) 0%,
    rgba(233, 30, 99, 0.2) 100%);
  border: 3px solid rgba(255, 152, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 12px 25px rgba(255, 152, 0, 0.2);
  animation: trendIconPulse 3s ease-in-out infinite;
}

@keyframes trendIconPulse {
  0%, 100% { transform: scale(1); box-shadow: 0 12px 25px rgba(255, 152, 0, 0.2); }
  50% { transform: scale(1.05); box-shadow: 0 15px 30px rgba(255, 152, 0, 0.3); }
}

.ai-trend-predictor-profile-container .profile-title {
  font-size: 2.5rem;
  font-weight: 900;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, 
    #ff9800 0%,
    #e91e63 50%,
    #9c27b0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-trend-predictor-profile-container .profile-subtitle {
  color: #8b949e;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 2rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Trend Stats Cards */
.ai-trend-predictor-profile-container .trend-stats-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.ai-trend-predictor-profile-container .stat-card {
  background: rgba(22, 27, 34, 0.8);
  border: 1px solid rgba(255, 152, 0, 0.2);
  border-radius: 16px;
  padding: 1.25rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.ai-trend-predictor-profile-container .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.15);
  border-color: rgba(255, 152, 0, 0.3);
}

.ai-trend-predictor-profile-container .stat-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.ai-trend-predictor-profile-container .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 900;
  color: #ff9800;
  margin-bottom: 0.25rem;
}

.ai-trend-predictor-profile-container .stat-label {
  font-size: 0.875rem;
  color: #8b949e;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content Area - Profile Pattern */
.ai-trend-predictor-profile-container .profile-content {
  padding: 0 2rem 2.5rem 2rem;
  position: relative;
  z-index: 2;
  max-height: 70vh;
  overflow-y: auto;
}

/* Custom Scrollbar */
.ai-trend-predictor-profile-container .profile-content::-webkit-scrollbar {
  width: 8px;
}

.ai-trend-predictor-profile-container .profile-content::-webkit-scrollbar-track {
  background: rgba(255, 152, 0, 0.05);
  border-radius: 4px;
}

.ai-trend-predictor-profile-container .profile-content::-webkit-scrollbar-thumb {
  background: rgba(255, 152, 0, 0.3);
  border-radius: 4px;
}

.ai-trend-predictor-profile-container .profile-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 152, 0, 0.5);
}

/* Sections - Profile Pattern with Dark Theme */
.ai-trend-predictor-profile-container .profile-section {
  background: rgba(22, 27, 34, 0.6);
  border: 1px solid rgba(255, 152, 0, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.ai-trend-predictor-profile-container .profile-section:hover {
  background: rgba(22, 27, 34, 0.8);
  border-color: rgba(255, 152, 0, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.1);
}

.ai-trend-predictor-profile-container .section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ff9800;
  margin: 0 0 1.25rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgba(255, 152, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Back Button Styling */
.ai-trend-predictor-profile-container .back-button-container {
  display: flex;
  justify-content: center;
}

.ai-trend-predictor-profile-container .back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, 
    rgba(255, 152, 0, 0.1) 0%,
    rgba(233, 30, 99, 0.1) 100%);
  border: 1px solid rgba(255, 152, 0, 0.2);
  border-radius: 12px;
  color: #ff9800;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-trend-predictor-profile-container .back-button-modern:hover {
  background: linear-gradient(135deg, 
    rgba(255, 152, 0, 0.2) 0%,
    rgba(233, 30, 99, 0.2) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.2);
  text-decoration: none;
  color: #ff9800;
}

.ai-trend-predictor-profile-container .back-icon {
  font-size: 1.2rem;
}

.ai-trend-predictor-profile-container .back-text {
  font-size: 0.95rem;
}

/* AI Controls Section */
.ai-trend-predictor-profile-container .predictor-controls-modern {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.ai-trend-predictor-profile-container .control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.ai-trend-predictor-profile-container .control-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #8b949e;
  text-align: center;
}

.ai-trend-predictor-profile-container .platform-select-modern {
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 152, 0, 0.2);
  border-radius: 12px;
  background: rgba(22, 27, 34, 0.8);
  color: #f0f6fc;
  font-weight: 600;
  min-width: 200px;
  text-align: center;
  transition: all 0.3s ease;
}

.ai-trend-predictor-profile-container .platform-select-modern:focus {
  outline: none;
  border-color: rgba(255, 152, 0, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.1);
}

.ai-trend-predictor-profile-container .refresh-btn-modern {
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #ff9800 0%, #e91e63 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.ai-trend-predictor-profile-container .refresh-btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.3);
}

.ai-trend-predictor-profile-container .refresh-btn-modern:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Tabs Styling */
.ai-trend-predictor-profile-container .predictor-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
}

.ai-trend-predictor-profile-container .tab {
  padding: 0.75rem 1rem;
  background: rgba(22, 27, 34, 0.6);
  border: 1px solid rgba(255, 152, 0, 0.2);
  border-radius: 12px;
  color: #8b949e;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.ai-trend-predictor-profile-container .tab:hover {
  background: rgba(22, 27, 34, 0.8);
  border-color: rgba(255, 152, 0, 0.3);
  transform: translateY(-1px);
  color: #f0f6fc;
}

.ai-trend-predictor-profile-container .tab.active {
  background: linear-gradient(135deg, #ff9800 0%, #e91e63 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .ai-trend-predictor-profile-container {
    padding: 0.75rem 0.25rem;
  }

  .ai-trend-predictor-profile-container .profile-container {
    border-radius: 20px;
    max-width: 100%;
    margin: 0 0.5rem;
  }

  .ai-trend-predictor-profile-container .profile-header {
    padding: 2rem 1.5rem 1.25rem 1.5rem;
  }

  .ai-trend-predictor-profile-container .profile-title {
    font-size: 2rem;
  }

  .ai-trend-predictor-profile-container .profile-content {
    padding: 0 1.5rem 2rem 1.5rem;
    max-height: 65vh;
  }

  .ai-trend-predictor-profile-container .auth-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .ai-trend-predictor-profile-container .profile-section {
    padding: 1.25rem;
  }

  .ai-trend-predictor-profile-container .trend-stats-modern {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .ai-trend-predictor-profile-container .predictor-tabs {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .ai-trend-predictor-profile-container .tab {
    padding: 0.625rem 0.75rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .ai-trend-predictor-profile-container .profile-header {
    padding: 1.5rem 1rem;
  }

  .ai-trend-predictor-profile-container .profile-title {
    font-size: 1.75rem;
  }

  .ai-trend-predictor-profile-container .profile-content {
    padding: 0 1rem 1.5rem 1rem;
    max-height: 60vh;
  }

  .ai-trend-predictor-profile-container .profile-section {
    padding: 1rem;
  }

  .ai-trend-predictor-profile-container .auth-icon {
    width: 50px;
    height: 50px;
    font-size: 1.75rem;
  }

  .ai-trend-predictor-profile-container .section-title {
    font-size: 1.1rem;
  }

  .ai-trend-predictor-profile-container .predictor-tabs {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .ai-trend-predictor-profile-container .platform-select-modern,
  .ai-trend-predictor-profile-container .refresh-btn-modern {
    min-width: 100%;
    font-size: 0.9rem;
  }
}

/* Content Cards and Grids - Optimized Mobile-First */

/* Predictions Summary */
.ai-trend-predictor-profile-container .predictions-summary {
  background: rgba(22, 27, 34, 0.8);
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid rgba(255, 152, 0, 0.2);
}

.ai-trend-predictor-profile-container .predictions-summary p {
  margin: 0;
  color: #c9d1d9;
  line-height: 1.6;
  font-size: 1rem;
}

/* Styles Grid */
.ai-trend-predictor-profile-container .styles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.ai-trend-predictor-profile-container .style-card {
  background: rgba(22, 27, 34, 0.9);
  border-radius: 12px;
  padding: 1.25rem;
  text-align: center;
  border: 1px solid rgba(255, 152, 0, 0.2);
  transition: all 0.3s ease;
}

.ai-trend-predictor-profile-container .style-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.15);
  border-color: rgba(255, 152, 0, 0.3);
}

.ai-trend-predictor-profile-container .style-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  display: block;
}

.ai-trend-predictor-profile-container .style-card h4 {
  margin: 0 0 0.5rem 0;
  color: #f0f6fc;
  font-size: 1.1rem;
  font-weight: 600;
}

.ai-trend-predictor-profile-container .platform-badge {
  background: linear-gradient(135deg, #ff9800 0%, #e91e63 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Virality Grid */
.ai-trend-predictor-profile-container .virality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.25rem;
  margin-top: 1rem;
}

.ai-trend-predictor-profile-container .virality-card {
  background: rgba(22, 27, 34, 0.9);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 152, 0, 0.2);
  transition: all 0.3s ease;
}

.ai-trend-predictor-profile-container .virality-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.15);
}

.ai-trend-predictor-profile-container .virality-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.ai-trend-predictor-profile-container .virality-header h4 {
  margin: 0;
  color: #f0f6fc;
  font-size: 1.2rem;
  font-weight: 600;
}

.ai-trend-predictor-profile-container .virality-score {
  background: linear-gradient(135deg, #ff9800 0%, #e91e63 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 700;
}

.ai-trend-predictor-profile-container .virality-card p {
  margin: 0 0 1rem 0;
  color: #c9d1d9;
  line-height: 1.5;
}

.ai-trend-predictor-profile-container .virality-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #8b949e;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Trends Grid */
.ai-trend-predictor-profile-container .trends-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.ai-trend-predictor-profile-container .trend-card {
  background: rgba(22, 27, 34, 0.9);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 152, 0, 0.2);
  transition: all 0.3s ease;
}

.ai-trend-predictor-profile-container .trend-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.15);
}

.ai-trend-predictor-profile-container .trend-card.emerging {
  border-left: 4px solid #4CAF50;
}

.ai-trend-predictor-profile-container .trend-card.declining {
  border-left: 4px solid #F44336;
}

.ai-trend-predictor-profile-container .trend-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.ai-trend-predictor-profile-container .trend-header h4 {
  margin: 0;
  color: #f0f6fc;
  font-size: 1.2rem;
  font-weight: 600;
}

.ai-trend-predictor-profile-container .trend-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.ai-trend-predictor-profile-container .confidence-badge,
.ai-trend-predictor-profile-container .impact-badge,
.ai-trend-predictor-profile-container .decline-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.ai-trend-predictor-profile-container .trend-description {
  margin: 0 0 1rem 0;
  color: #c9d1d9;
  line-height: 1.5;
}

/* Mobile Optimizations for Content */
@media (max-width: 768px) {
  .ai-trend-predictor-profile-container .styles-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .ai-trend-predictor-profile-container .virality-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .ai-trend-predictor-profile-container .trends-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .ai-trend-predictor-profile-container .virality-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .ai-trend-predictor-profile-container .trend-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .ai-trend-predictor-profile-container .styles-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .ai-trend-predictor-profile-container .style-card,
  .ai-trend-predictor-profile-container .virality-card,
  .ai-trend-predictor-profile-container .trend-card {
    padding: 1rem;
  }

  .ai-trend-predictor-profile-container .virality-details {
    flex-direction: column;
    gap: 0.25rem;
  }
}
