/* ===== AI Virtual Try-On - Enterprise Gen Z Design ===== */

.ai-virtual-tryon-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.tryon-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.tryon-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: tryonFloat 24s ease-in-out infinite;
}

.tryon-orb-1 {
  width: 320px;
  height: 320px;
  background: linear-gradient(135deg, #8a2be2, #ba68c8);
  top: 0%;
  left: -28%;
  animation-delay: 0s;
}

.tryon-orb-2 {
  width: 260px;
  height: 260px;
  background: linear-gradient(135deg, #e91e63, #f06292);
  top: 95%;
  right: -25%;
  animation-delay: 11s;
}

.tryon-orb-3 {
  width: 280px;
  height: 280px;
  background: linear-gradient(135deg, #ff6f00, #ffb74d);
  bottom: 50%;
  left: 25%;
  animation-delay: 20s;
}

@keyframes tryonFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  8% { transform: translateY(-55px) rotate(30deg) scale(1.4); }
  16% { transform: translateY(45px) rotate(60deg) scale(0.6); }
  24% { transform: translateY(-40px) rotate(90deg) scale(1.25); }
  32% { transform: translateY(50px) rotate(120deg) scale(0.7); }
  40% { transform: translateY(-45px) rotate(150deg) scale(1.3); }
  48% { transform: translateY(35px) rotate(180deg) scale(0.75); }
  56% { transform: translateY(-50px) rotate(210deg) scale(1.2); }
  64% { transform: translateY(40px) rotate(240deg) scale(0.8); }
  72% { transform: translateY(-35px) rotate(270deg) scale(1.15); }
  80% { transform: translateY(30px) rotate(300deg) scale(0.85); }
  88% { transform: translateY(-25px) rotate(330deg) scale(1.05); }
  96% { transform: translateY(20px) rotate(360deg) scale(0.95); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(138, 43, 226, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

.back-button:hover {
  background: rgba(138, 43, 226, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #8a2be2;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.tryon-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.tryon-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tryon-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.tryon-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Profile Section */
.profile-section {
  margin-bottom: 40px;
}

.profile-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.profile-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.profile-field label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.profile-field select,
.profile-field input {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.profile-field select:focus,
.profile-field input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Styles Section */
.styles-section {
  margin-bottom: 40px;
}

.styles-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.styles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.style-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  gap: 15px;
  align-items: center;
}

.style-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.style-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.style-image {
  flex-shrink: 0;
}

.style-placeholder {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.style-info {
  flex: 1;
}

.style-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
}

.style-info p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.style-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
}

.difficulty {
  color: #FF9800;
  font-weight: 600;
}

.maintenance {
  color: #4CAF50;
  font-weight: 600;
}

/* Loading Section */
.loading-section {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 3rem;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-section p {
  color: #666;
  font-size: 1.1rem;
}

/* Try-On Result */
.tryon-result {
  margin-bottom: 40px;
}

.tryon-result h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.8rem;
  text-align: center;
}

.result-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  margin-bottom: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.result-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.4rem;
}

.suitability-score {
  display: flex;
  align-items: center;
}

.score-badge {
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
}

.result-description {
  margin-bottom: 25px;
}

.result-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
}

.result-details {
  margin-bottom: 25px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
}

.detail-item .label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.detail-item span:last-child {
  color: #666;
  font-size: 0.9rem;
}

.maintenance-badge,
.compatibility-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.styling-tips {
  margin-bottom: 25px;
}

.styling-tips h5 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.styling-tips ul {
  margin: 0;
  padding-left: 20px;
}

.styling-tips li {
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.recommended-products {
  margin-bottom: 25px;
}

.recommended-products h5 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.product-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Alternatives Section */
.alternatives-section {
  margin-bottom: 40px;
}

.alternatives-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.alternatives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.alternative-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.alternative-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
}

.alternative-card h5 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.1rem;
}

.alternative-card p {
  margin: 0 0 15px 0;
  color: #666;
  line-height: 1.4;
  font-size: 0.9rem;
}

.suitability-badge {
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Recommendations Section */
.recommendations-section {
  margin-bottom: 40px;
}

.recommendations-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.recommendation-category {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.recommendation-category h5 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-tag {
  background: rgba(255, 193, 7, 0.1);
  color: #FF9800;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Comparison Section */
.comparison-section {
  margin-top: 40px;
  padding-top: 40px;
  border-top: 2px solid #f0f0f0;
}

.comparison-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.5rem;
  text-align: center;
}

.comparison-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.comparison-select {
  display: flex;
  align-items: center;
  gap: 10px;
}

.comparison-select label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.comparison-select select {
  padding: 10px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  background: white;
  min-width: 200px;
}

.compare-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.compare-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.compare-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Comparison Result */
.comparison-result {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.comparison-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.winner-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 25px;
}

.comparison-card {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.comparison-card h5 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2rem;
}

.comparison-score {
  margin-bottom: 15px;
}

.comparison-card p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

.comparison-recommendation {
  background: rgba(76, 175, 80, 0.1);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #4CAF50;
}

.comparison-recommendation h5 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.comparison-recommendation p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-virtual-tryon {
    padding: 15px;
  }
  
  .tryon-header {
    padding: 20px;
  }
  
  .tryon-header h2 {
    font-size: 2rem;
  }
  
  .tryon-container {
    padding: 20px;
  }
  
  .profile-grid {
    grid-template-columns: 1fr;
  }
  
  .styles-grid {
    grid-template-columns: 1fr;
  }
  
  .style-card {
    flex-direction: column;
    text-align: center;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .alternatives-grid {
    grid-template-columns: 1fr;
  }
  
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .comparison-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .comparison-select {
    flex-direction: column;
    align-items: stretch;
  }
  
  .comparison-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .comparison-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .tryon-header h2 {
    font-size: 1.8rem;
  }
  
  .tryon-header p {
    font-size: 1rem;
  }
  
  .result-card {
    padding: 20px;
  }
  
  .comparison-result {
    padding: 20px;
  }
} 