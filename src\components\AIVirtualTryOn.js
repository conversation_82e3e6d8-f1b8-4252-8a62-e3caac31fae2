import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import aiVirtualTryOnService from '../services/aiVirtualTryOnService';
import './AIVirtualTryOn.css';

const AIVirtualTryOn = () => {
  const [userProfile, setUserProfile] = useState({
    faceShape: 'Oval',
    hairType: 'Straight',
    hairColor: 'Brown',
    skinTone: 'Medium',
    age: 22,
    gender: 'Female',
    preferences: ['Low maintenance', 'Modern look', 'Professional']
  });
  
  const [selectedStyle, setSelectedStyle] = useState(null);
  const [tryOnResult, setTryOnResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [availableStyles, setAvailableStyles] = useState([]);
  const [currentStyle, setCurrentStyle] = useState('Long layered cut');
  const [comparisonMode, setComparisonMode] = useState(false);
  const [styleToCompare, setStyleToCompare] = useState(null);
  const [comparisonResult, setComparisonResult] = useState(null);

  useEffect(() => {
    setAvailableStyles(aiVirtualTryOnService.getAvailableStyles());
  }, []);

  const handleStyleSelect = async (style) => {
    setSelectedStyle(style);
    setLoading(true);
    
    try {
      const result = await aiVirtualTryOnService.generateVirtualTryOn(userProfile, style);
      setTryOnResult(result);
    } catch (error) {
      console.error('Error generating try-on:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = (field, value) => {
    setUserProfile(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleComparison = async () => {
    if (!selectedStyle || !styleToCompare) return;
    
    setLoading(true);
    try {
      const result = await aiVirtualTryOnService.compareStyles(userProfile, selectedStyle, styleToCompare);
      setComparisonResult(result);
    } catch (error) {
      console.error('Error comparing styles:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSuitabilityColor = (score) => {
    if (score >= 0.8) return '#4CAF50';
    if (score >= 0.6) return '#FF9800';
    return '#F44336';
  };

  const getMaintenanceColor = (level) => {
    switch (level) {
      case 'Low': return '#4CAF50';
      case 'Medium': return '#FF9800';
      case 'High': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getCompatibilityColor = (compatibility) => {
    switch (compatibility) {
      case 'Excellent': return '#4CAF50';
      case 'Good': return '#8BC34A';
      case 'Fair': return '#FF9800';
      case 'Poor': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  return (
    <div className="ai-virtual-tryon-page">
      <div className="tryon-background-effects">
        <div className="tryon-gradient-orb tryon-orb-1"></div>
        <div className="tryon-gradient-orb tryon-orb-2"></div>
        <div className="tryon-gradient-orb tryon-orb-3"></div>
      </div>

      <div className="container">
        {/* Back Button */}
        <div className="back-button-container">
          <Link to="/ai-features" className="back-button-modern">
            <span className="back-icon">←</span>
            <span className="back-text">Back to AI Features</span>
            <div className="button-glow"></div>
          </Link>
        </div>

        <div className="tryon-header-modern">
          <div className="tryon-header-content">
            <div className="tryon-header-badge">
              <span className="badge-icon">🎭</span>
              <span className="badge-text">AI TRY-ON</span>
            </div>
            <h1 className="tryon-title-modern">
              <span className="title-gradient">Virtual Mirror</span>
              <span className="title-accent">💫</span>
            </h1>
            <p className="tryon-subtitle-modern">
              Try on styles before you commit
            </p>
          </div>
        </div>

        <div className="tryon-container-modern">
        <div className="profile-section">
          <h3>👤 Your Profile</h3>
          <div className="profile-grid">
            <div className="profile-field">
              <label>Face Shape</label>
              <select 
                value={userProfile.faceShape}
                onChange={(e) => handleProfileUpdate('faceShape', e.target.value)}
              >
                <option value="Oval">Oval</option>
                <option value="Round">Round</option>
                <option value="Square">Square</option>
                <option value="Heart">Heart</option>
                <option value="Diamond">Diamond</option>
                <option value="Rectangle">Rectangle</option>
              </select>
            </div>

            <div className="profile-field">
              <label>Hair Type</label>
              <select 
                value={userProfile.hairType}
                onChange={(e) => handleProfileUpdate('hairType', e.target.value)}
              >
                <option value="Straight">Straight</option>
                <option value="Wavy">Wavy</option>
                <option value="Curly">Curly</option>
                <option value="Coily">Coily</option>
                <option value="Fine">Fine</option>
                <option value="Thick">Thick</option>
              </select>
            </div>

            <div className="profile-field">
              <label>Hair Color</label>
              <select 
                value={userProfile.hairColor}
                onChange={(e) => handleProfileUpdate('hairColor', e.target.value)}
              >
                <option value="Black">Black</option>
                <option value="Brown">Brown</option>
                <option value="Blonde">Blonde</option>
                <option value="Red">Red</option>
                <option value="Gray">Gray</option>
              </select>
            </div>

            <div className="profile-field">
              <label>Skin Tone</label>
              <select 
                value={userProfile.skinTone}
                onChange={(e) => handleProfileUpdate('skinTone', e.target.value)}
              >
                <option value="Fair">Fair</option>
                <option value="Light">Light</option>
                <option value="Medium">Medium</option>
                <option value="Olive">Olive</option>
                <option value="Dark">Dark</option>
              </select>
            </div>

            <div className="profile-field">
              <label>Age</label>
              <input 
                type="number" 
                value={userProfile.age}
                onChange={(e) => handleProfileUpdate('age', parseInt(e.target.value))}
                min="13" 
                max="80"
              />
            </div>

            <div className="profile-field">
              <label>Gender</label>
              <select 
                value={userProfile.gender}
                onChange={(e) => handleProfileUpdate('gender', e.target.value)}
              >
                <option value="Female">Female</option>
                <option value="Male">Male</option>
                <option value="Non-binary">Non-binary</option>
              </select>
            </div>
          </div>
        </div>

        <div className="styles-section">
          <h3>💇‍♀️ Available Styles</h3>
          <div className="styles-grid">
            {availableStyles.map((style, index) => (
              <div 
                key={index} 
                className={`style-card ${selectedStyle?.name === style.name ? 'selected' : ''}`}
                onClick={() => handleStyleSelect(style)}
              >
                <div className="style-image">
                  <div className="style-placeholder">💇‍♀️</div>
                </div>
                <div className="style-info">
                  <h4>{style.name}</h4>
                  <p>{style.description}</p>
                  <div className="style-meta">
                    <span className="difficulty">
                      Difficulty:
                      {style.difficulty}
                    </span>
                    <span className="maintenance">
                      Maintenance:
                      {style.maintenance}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {loading && (
          <div className="loading-section">
            <div className="loading-spinner">🎭</div>
            <p>AI is creating your virtual try-on...</p>
          </div>
        )}

        {tryOnResult && tryOnResult.simulation && !loading && (
          <div className="tryon-result">
            <h3>🎭 Virtual Try-On Result</h3>
            
            <div className="result-card">
              <div className="result-header">
                <h4>{selectedStyle.name}</h4>
                <div className="suitability-score">
                  <span 
                    className="score-badge"
                    style={{ backgroundColor: getSuitabilityColor(tryOnResult.simulation.suitabilityScore) }}
                  >
                    {Math.round(tryOnResult.simulation.suitabilityScore * 100)}
                    % Match
                  </span>
                </div>
              </div>

              <div className="result-description">
                <p>{tryOnResult.simulation.description}</p>
              </div>

              <div className="result-details">
                <div className="detail-grid">
                  <div className="detail-item">
                    <span className="label">🎯 Confidence:</span>
                    <span>
                      {Math.round(tryOnResult.simulation.confidence * 100)}
                      %
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="label">⏰ Styling Time:</span>
                    <span>{tryOnResult.simulation.timeToStyle}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">🔧 Maintenance:</span>
                    <span 
                      className="maintenance-badge"
                      style={{ backgroundColor: getMaintenanceColor(tryOnResult.simulation.maintenance) }}
                    >
                      {tryOnResult.simulation.maintenance}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="label">👤 Face Shape:</span>
                    <span 
                      className="compatibility-badge"
                      style={{ backgroundColor: getCompatibilityColor(tryOnResult.simulation.faceShapeCompatibility) }}
                    >
                      {tryOnResult.simulation.faceShapeCompatibility}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="label">💇‍♀️ Hair Type:</span>
                    <span 
                      className="compatibility-badge"
                      style={{ backgroundColor: getCompatibilityColor(tryOnResult.simulation.hairTypeCompatibility) }}
                    >
                      {tryOnResult.simulation.hairTypeCompatibility}
                    </span>
                  </div>
                </div>
              </div>

              <div className="styling-tips">
                <h5>💡 Styling Tips</h5>
                <ul>
                  {Array.isArray(tryOnResult.simulation.stylingTips) && tryOnResult.simulation.stylingTips.map((tip, index) => (
                    <li key={index}>{tip}</li>
                  ))}
                </ul>
              </div>

              <div className="recommended-products">
                <h5>🛍️ Recommended Products</h5>
                <div className="products-grid">
                  {Array.isArray(tryOnResult.simulation.products) && tryOnResult.simulation.products.map((product, index) => (
                    <span key={index} className="product-tag">{product}</span>
                  ))}
                </div>
              </div>
            </div>

            <div className="alternatives-section">
              <h4>🔄 Alternative Suggestions</h4>
              <div className="alternatives-grid">
                {Array.isArray(tryOnResult.alternatives) && tryOnResult.alternatives.map((alt, index) => (
                  <div key={index} className="alternative-card">
                    <h5>{alt.name}</h5>
                    <p>{alt.reason}</p>
                    <span 
                      className="suitability-badge"
                      style={{ backgroundColor: getSuitabilityColor(alt.suitabilityScore) }}
                    >
                      {Math.round(alt.suitabilityScore * 100)}
                      % Match
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="recommendations-section">
              <h4>💡 Personalized Recommendations</h4>
              <div className="recommendations-grid">
                <div className="recommendation-category">
                  <h5>🎨 Color Suggestions</h5>
                  <div className="suggestions-list">
                    {tryOnResult.recommendations && Array.isArray(tryOnResult.recommendations.colorSuggestions) && tryOnResult.recommendations.colorSuggestions.map((color, index) => (
                      <span key={index} className="suggestion-tag">{color}</span>
                    ))}
                  </div>
                </div>

                <div className="recommendation-category">
                  <h5>💇‍♀️ Styling Variations</h5>
                  <div className="suggestions-list">
                    {tryOnResult.recommendations && Array.isArray(tryOnResult.recommendations.stylingVariations) && tryOnResult.recommendations.stylingVariations.map((variation, index) => (
                      <span key={index} className="suggestion-tag">{variation}</span>
                    ))}
                  </div>
                </div>

                <div className="recommendation-category">
                  <h5>👑 Accessories</h5>
                  <div className="suggestions-list">
                    {tryOnResult.recommendations && Array.isArray(tryOnResult.recommendations.accessories) && tryOnResult.recommendations.accessories.map((accessory, index) => (
                      <span key={index} className="suggestion-tag">{accessory}</span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="comparison-section">
          <h3>⚖️ Style Comparison</h3>
          <div className="comparison-controls">
            <div className="comparison-select">
              <label>Compare with:</label>
              <select 
                value={styleToCompare?.name || ''}
                onChange={(e) => {
                  const style = availableStyles.find(s => s.name === e.target.value);
                  setStyleToCompare(style);
                }}
              >
                <option value="">Select a style to compare</option>
                {availableStyles.map((style, index) => (
                  <option key={index} value={style.name}>{style.name}</option>
                ))}
              </select>
            </div>
            
            <button 
              className="compare-btn"
              onClick={handleComparison}
              disabled={!selectedStyle || !styleToCompare || loading}
            >
              {loading ? '🔄 Comparing...' : '⚖️ Compare Styles'}
            </button>
          </div>

          {comparisonResult && (
            <div className="comparison-result">
              <div className="comparison-header">
                <h4>Comparison Results</h4>
                <div className="winner-badge">
                  🏆 Winner: 
                  {' '}
                  {comparisonResult.comparison.winner}
                </div>
              </div>

              <div className="comparison-grid">
                <div className="comparison-card">
                  <h5>{comparisonResult.style1.name}</h5>
                  <div className="comparison-score">
                    <span 
                      className="score-badge"
                      style={{ backgroundColor: getSuitabilityColor(comparisonResult.style1.simulation.suitabilityScore) }}
                    >
                      {Math.round(comparisonResult.style1.simulation.suitabilityScore * 100)}
                      %
                    </span>
                  </div>
                  <p>{comparisonResult.style1.simulation.description}</p>
                </div>

                <div className="comparison-card">
                  <h5>{comparisonResult.style2.name}</h5>
                  <div className="comparison-score">
                    <span 
                      className="score-badge"
                      style={{ backgroundColor: getSuitabilityColor(comparisonResult.style2.simulation.suitabilityScore) }}
                    >
                      {Math.round(comparisonResult.style2.simulation.suitabilityScore * 100)}
                      %
                    </span>
                  </div>
                  <p>{comparisonResult.style2.simulation.description}</p>
                </div>
              </div>

              <div className="comparison-recommendation">
                <h5>💡 AI Recommendation</h5>
                <p>{comparisonResult.comparison.recommendation}</p>
              </div>
            </div>
          )}
        </div>
      </div>
      </div>
    </div>
  );
};

export default AIVirtualTryOn; 
