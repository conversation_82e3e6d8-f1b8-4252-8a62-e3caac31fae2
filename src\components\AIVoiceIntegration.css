/* ===== AI Voice Integration - Enterprise Gen Z Design ===== */

.ai-voice-integration-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.voice-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.voice-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: voiceFloat 18s ease-in-out infinite;
}

.voice-orb-1 {
  width: 290px;
  height: 290px;
  background: linear-gradient(135deg, #6495ed, #4169e1);
  top: 15%;
  left: -20%;
  animation-delay: 0s;
}

.voice-orb-2 {
  width: 220px;
  height: 220px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  top: 80%;
  right: -18%;
  animation-delay: 8s;
}

.voice-orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  bottom: 35%;
  left: 40%;
  animation-delay: 14s;
}

@keyframes voiceFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  11% { transform: translateY(-40px) rotate(40deg) scale(1.25); }
  22% { transform: translateY(30px) rotate(80deg) scale(0.75); }
  33% { transform: translateY(-25px) rotate(120deg) scale(1.1); }
  44% { transform: translateY(35px) rotate(160deg) scale(0.85); }
  55% { transform: translateY(-30px) rotate(200deg) scale(1.15); }
  66% { transform: translateY(20px) rotate(240deg) scale(0.9); }
  77% { transform: translateY(-35px) rotate(280deg) scale(1.05); }
  88% { transform: translateY(25px) rotate(320deg) scale(0.95); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(100, 149, 237, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 149, 237, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Modern Header */
.voice-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.voice-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.voice-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(100, 149, 237, 0.1);
  border: 1px solid rgba(100, 149, 237, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #6495ed;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.voice-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #6495ed, #ff6b9d, #ffeaa7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: voiceGradientShift 9s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: voiceSparkle 4.5s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes voiceGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes voiceSparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.8) rotate(180deg); opacity: 0.2; }
}

.voice-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Modern Status */
.voice-status-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.status-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-card.supported {
  border-color: rgba(76, 175, 80, 0.3);
  background: rgba(76, 175, 80, 0.05);
}

.status-card.not-supported {
  border-color: rgba(244, 67, 54, 0.3);
  background: rgba(244, 67, 54, 0.05);
}

.status-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(100, 149, 237, 0.05), rgba(255, 107, 157, 0.05));
  opacity: 0.5;
}

.status-icon {
  font-size: 2rem;
  position: relative;
  z-index: 2;
}

.status-content {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.status-text {
  font-size: 1.1rem;
  font-weight: 700;
  color: #f0f6fc;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.status-detail {
  font-size: 0.9rem;
  color: #8b949e;
  font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .voice-orb-1, .voice-orb-2, .voice-orb-3 {
    width: 170px;
    height: 170px;
    filter: blur(40px);
  }

  .container {
    padding: 16px;
  }

  .voice-title-modern {
    font-size: 2.5rem;
  }

  .voice-subtitle-modern {
    font-size: 1rem;
  }

  .voice-status-modern {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .status-card {
    padding: 1.25rem;
  }

  .status-icon {
    font-size: 1.5rem;
  }

  .status-text {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .voice-title-modern {
    font-size: 2rem;
  }

  .voice-header-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }

  .status-card {
    padding: 1rem;
    gap: 0.75rem;
  }
}

.back-button:hover {
  background: rgba(100, 149, 237, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #6495ed;
  box-shadow: 0 4px 15px rgba(100, 149, 237, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.voice-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.voice-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.voice-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 20px 0;
}

.voice-status {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.status-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-right: 10px;
}

.status-value {
  font-weight: 600;
}

.status-value.supported {
  color: #4CAF50;
}

.status-value.not-supported {
  color: #F44336;
}

/* Voice Tabs */
.voice-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.tab {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #333;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Voice Content */
.voice-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Voice Commands Section */
.voice-commands-section {
  max-width: 800px;
  margin: 0 auto;
}

.voice-controls {
  text-align: center;
  margin-bottom: 40px;
}

.voice-btn {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50px;
  padding: 20px 40px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
  min-width: 200px;
}

.voice-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.voice-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.voice-btn.listening {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  animation: pulse 2s infinite;
}

.voice-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.voice-icon {
  font-size: 1.5rem;
}

.voice-text {
  font-size: 1rem;
}

.listening-animation {
  position: absolute;
  top: -10px;
  right: -10px;
  display: flex;
  gap: 5px;
}

.pulse {
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 50%;
  animation: pulse-animation 1.5s infinite;
}

.pulse:nth-child(2) {
  animation-delay: 0.2s;
}

.pulse:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse-animation {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

.transcript-display {
  margin-top: 20px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 15px;
  border-left: 4px solid #667eea;
}

.transcript-display h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1rem;
}

.transcript-display p {
  margin: 0;
  color: #666;
  font-style: italic;
}

/* Quick Commands */
.quick-commands {
  margin-top: 40px;
}

.quick-commands h3 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.quick-command-btn {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 15px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.quick-command-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.command-icon {
  font-size: 1.5rem;
}

.command-text {
  font-weight: 600;
  color: #333;
  text-align: center;
  font-size: 0.9rem;
}

/* Voice Results Section */
.voice-results-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.result-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.result-info {
  flex: 1;
}

.result-intent {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.result-transcript {
  margin: 0;
  color: #666;
  font-style: italic;
}

.confidence-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.result-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
}

.detail-item .label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.detail-item span:last-child {
  color: #666;
  font-size: 0.9rem;
}

.result-response {
  background: rgba(255, 193, 7, 0.1);
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #FFC107;
}

.response-label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 5px;
  display: block;
}

.result-response p {
  margin: 0;
  color: #666;
  font-style: italic;
}

/* Voice Help Section */
.voice-help-section h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.help-content {
  max-width: 800px;
  margin: 0 auto;
}

.help-section {
  margin-bottom: 30px;
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.help-section h4 {
  color: #667eea;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.help-section ol,
.help-section ul {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.5;
}

.commands-help {
  display: grid;
  gap: 20px;
}

.command-help-item {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #667eea;
}

.command-help-item h5 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.1rem;
}

.command-help-item p {
  margin: 0 0 10px 0;
  color: #666;
}

.examples {
  margin-top: 10px;
}

.examples strong {
  color: #333;
  font-size: 0.9rem;
}

.examples ul {
  margin: 5px 0 0 0;
  padding-left: 20px;
}

.examples li {
  color: #667eea;
  font-style: italic;
  font-size: 0.9rem;
}

.troubleshooting {
  display: grid;
  gap: 15px;
}

.trouble-item {
  background: rgba(255, 193, 7, 0.1);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #FFC107;
}

.trouble-item strong {
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.trouble-item p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-voice-integration {
    padding: 15px;
  }
  
  .voice-header {
    padding: 20px;
  }
  
  .voice-header h2 {
    font-size: 2rem;
  }
  
  .voice-status {
    gap: 15px;
  }
  
  .status-item {
    padding: 8px 15px;
  }
  
  .voice-tabs {
    gap: 8px;
  }
  
  .tab {
    padding: 10px 16px;
    font-size: 0.8rem;
  }
  
  .voice-content {
    padding: 20px;
  }
  
  .voice-btn {
    padding: 15px 30px;
    font-size: 1rem;
    min-width: 180px;
  }
  
  .commands-grid {
    grid-template-columns: 1fr;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .result-details {
    grid-template-columns: 1fr;
  }
  
  .help-section {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .voice-header h2 {
    font-size: 1.8rem;
  }
  
  .voice-header p {
    font-size: 1rem;
  }
  
  .voice-btn {
    padding: 12px 24px;
    font-size: 0.9rem;
  }
  
  .result-card {
    padding: 15px;
  }
  
  .help-section {
    padding: 12px;
  }
} 