import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import aiVoiceService from '../services/aiVoiceService';
import './AIVoiceIntegration.css';

const AIVoiceIntegration = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [voiceResults, setVoiceResults] = useState([]);
  const [voiceStatus, setVoiceStatus] = useState({});
  const [activeTab, setActiveTab] = useState('voice-commands');
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    const status = aiVoiceService.getVoiceStatus();
    setVoiceStatus(status);
    setIsSupported(status.supported);
  }, []);

  const startListening = () => {
    const success = aiVoiceService.startListening((result) => {
      setVoiceResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
      setIsListening(false);
    });

    if (success) {
      setIsListening(true);
      setTranscript('Listening...');
    }
  };

  const stopListening = () => {
    aiVoiceService.stopListening();
    setIsListening(false);
    setTranscript('');
  };

  const handleVoiceCommand = (command) => {
    setTranscript(command);
    aiVoiceService.processVoiceCommand(command);
  };

  const getAvailableCommands = () => aiVoiceService.getAvailableCommands();

  const getIntentColor = (intent) => {
    const colors = {
      book_appointment: '#4CAF50',
      find_salon: '#2196F3',
      style_recommendation: '#9C27B0',
      check_booking: '#FF9800',
      cancel_booking: '#F44336',
      salon_info: '#607D8B',
      unknown: '#9E9E9E'
    };
    return colors[intent] || colors.unknown;
  };

  const getIntentIcon = (intent) => {
    const icons = {
      book_appointment: '📅',
      find_salon: '🏪',
      style_recommendation: '💇‍♀️',
      check_booking: '📋',
      cancel_booking: '❌',
      salon_info: 'ℹ️',
      unknown: '❓'
    };
    return icons[intent] || icons.unknown;
  };

  return (
    <div className="ai-voice-integration-page">
      <div className="voice-background-effects">
        <div className="voice-gradient-orb voice-orb-1"></div>
        <div className="voice-gradient-orb voice-orb-2"></div>
        <div className="voice-gradient-orb voice-orb-3"></div>
      </div>

      <div className="container">
        {/* Back Button */}
        <div className="back-button-container">
          <Link to="/ai-features" className="back-button-modern">
            <span className="back-icon">←</span>
            <span className="back-text">Back to AI Features</span>
            <div className="button-glow"></div>
          </Link>
        </div>

        <div className="voice-header-modern">
          <div className="voice-header-content">
            <div className="voice-header-badge">
              <span className="badge-icon">🎤</span>
              <span className="badge-text">AI VOICE</span>
            </div>
            <h1 className="voice-title-modern">
              <span className="title-gradient">Voice Command</span>
              <span className="title-accent">🗣️</span>
            </h1>
            <p className="voice-subtitle-modern">
              Speak your salon needs into reality
            </p>

            <div className="voice-status-modern">
              <div className={`status-card ${voiceStatus.recognition ? 'supported' : 'not-supported'}`}>
                <div className="status-icon">
                  {voiceStatus.recognition ? '🎤' : '❌'}
                </div>
                <div className="status-content">
                  <span className="status-text">Speech Recognition</span>
                  <span className="status-detail">
                    {voiceStatus.recognition ? 'Ready to listen' : 'Not available'}
                  </span>
                </div>
              </div>
              <div className={`status-card ${voiceStatus.synthesis ? 'supported' : 'not-supported'}`}>
                <div className="status-icon">
                  {voiceStatus.synthesis ? '🔊' : '❌'}
                </div>
                <div className="status-content">
                  <span className="status-text">Speech Synthesis</span>
                  <span className="status-detail">
                    {voiceStatus.synthesis ? 'Ready to speak' : 'Not available'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

      <div className="voice-tabs">
        <button 
          className={`tab ${activeTab === 'voice-commands' ? 'active' : ''}`}
          onClick={() => setActiveTab('voice-commands')}
        >
          🎤 Voice Commands
        </button>
        <button 
          className={`tab ${activeTab === 'results' ? 'active' : ''}`}
          onClick={() => setActiveTab('results')}
        >
          📊 Voice Results
        </button>
        <button 
          className={`tab ${activeTab === 'help' ? 'active' : ''}`}
          onClick={() => setActiveTab('help')}
        >
          ❓ Help
        </button>
      </div>

      <div className="voice-content">
        {activeTab === 'voice-commands' && (
          <div className="voice-commands-section">
            <div className="voice-controls">
              <button 
                className={`voice-btn ${isListening ? 'listening' : ''}`}
                onClick={isListening ? stopListening : startListening}
                disabled={!isSupported}
              >
                <div className="voice-btn-content">
                  <span className="voice-icon">
                    {isListening ? '🔴' : '🎤'}
                  </span>
                  <span className="voice-text">
                    {isListening ? 'Stop Listening' : 'Start Voice Command'}
                  </span>
                </div>
                {isListening && (
                  <div className="listening-animation">
                    <div className="pulse" />
                    <div className="pulse" />
                    <div className="pulse" />
                  </div>
                )}
              </button>

              {transcript && (
                <div className="transcript-display">
                  <h4>You said:</h4>
                  <p>
                    "
                    {transcript}
                    "
                  </p>
                </div>
              )}
            </div>

            <div className="quick-commands">
              <h3>Quick Voice Commands</h3>
              <div className="commands-grid">
                {getAvailableCommands().map((cmd, index) => (
                  <button
                    key={index}
                    className="quick-command-btn"
                    onClick={() => handleVoiceCommand(cmd.examples[0])}
                  >
                    <span className="command-icon">🎤</span>
                    <span className="command-text">{cmd.command}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'results' && (
          <div className="voice-results-section">
            <h3>Recent Voice Commands</h3>
            {voiceResults.length === 0 ? (
              <div className="no-results">
                <div className="no-results-icon">🎤</div>
                <p>No voice commands yet. Try saying something!</p>
              </div>
            ) : (
              <div className="results-list">
                {voiceResults.map((result, index) => (
                  <div key={index} className="result-card">
                    <div className="result-header">
                      <span className="result-icon" style={{ color: getIntentColor(result.intent) }}>
                        {getIntentIcon(result.intent)}
                      </span>
                      <div className="result-info">
                        <h4 className="result-intent">{result.intent.replace('_', ' ').toUpperCase()}</h4>
                        <p className="result-transcript">
                          "
                          {result.originalTranscript}
                          "
                        </p>
                      </div>
                      <span className="confidence-badge">
                        {Math.round(result.confidence * 100)}
                        %
                      </span>
                    </div>
                    
                    <div className="result-details">
                      <div className="detail-item">
                        <span className="label">Service:</span>
                        <span>{result.service}</span>
                      </div>
                      {result.dateTime && (
                        <div className="detail-item">
                          <span className="label">Date/Time:</span>
                          <span>{result.dateTime}</span>
                        </div>
                      )}
                      {result.location && (
                        <div className="detail-item">
                          <span className="label">Location:</span>
                          <span>{result.location}</span>
                        </div>
                      )}
                      <div className="detail-item">
                        <span className="label">Budget:</span>
                        <span>{result.budget}</span>
                      </div>
                      <div className="detail-item">
                        <span className="label">Urgency:</span>
                        <span>{result.urgency}</span>
                      </div>
                    </div>

                    <div className="result-response">
                      <span className="response-label">AI Response:</span>
                      <p>
                        "
                        {result.response}
                        "
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'help' && (
          <div className="voice-help-section">
            <h3>Voice Command Help</h3>
            
            <div className="help-content">
              <div className="help-section">
                <h4>🎤 How to Use Voice Commands</h4>
                <ol>
                  <li>Click the "Start Voice Command" button</li>
                  <li>Speak your command clearly</li>
                  <li>Wait for AI to process and respond</li>
                  <li>Follow the voice guidance</li>
                </ol>
              </div>

              <div className="help-section">
                <h4>📝 Available Commands</h4>
                <div className="commands-help">
                  {getAvailableCommands().map((cmd, index) => (
                    <div key={index} className="command-help-item">
                      <h5>{cmd.command}</h5>
                      <p>{cmd.description}</p>
                      <div className="examples">
                        <strong>Examples:</strong>
                        <ul>
                          {cmd.examples.map((example, i) => (
                            <li key={i}>
                              "
                              {example}
                              "
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="help-section">
                <h4>💡 Tips for Better Voice Recognition</h4>
                <ul>
                  <li>Speak clearly and at a normal pace</li>
                  <li>Use a quiet environment</li>
                  <li>Include specific details (service, date, time)</li>
                  <li>Try different phrasings if not understood</li>
                </ul>
              </div>

              <div className="help-section">
                <h4>🔧 Troubleshooting</h4>
                <div className="troubleshooting">
                  <div className="trouble-item">
                    <strong>Voice not working?</strong>
                    <p>Make sure your browser supports speech recognition and you've granted microphone permissions.</p>
                  </div>
                  <div className="trouble-item">
                    <strong>Not understanding commands?</strong>
                    <p>Try speaking more clearly or use the quick command buttons as examples.</p>
                  </div>
                  <div className="trouble-item">
                    <strong>No voice response?</strong>
                    <p>Check if speech synthesis is supported in your browser settings.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default AIVoiceIntegration; 
