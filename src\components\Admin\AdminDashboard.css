.admin-dashboard {
  background: #fff;
  border: none;
  width: 100%;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.admin-dashboard .dashboard-header {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 24px;
  padding: 20px 12px;
  color: #222;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

@media (min-width: 600px) {
  .admin-dashboard .dashboard-header {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 32px 24px;
  }
}

/* Analytics-specific styles removed - now handled by AnalyticsDashboard.css */

.admin-dashboard .metric-icon {
  font-size: 2rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f3f3;
  border-radius: 8px;
  color: #FFD700;
  margin-bottom: 8px;
}

.admin-dashboard .dashboard-container {
  width: 100%;
  min-width: 0;
  max-width: 1950px;
  margin-left: auto;
  margin-right: auto;
  padding: 8px;
}

@media (max-width: 600px) {
  .admin-dashboard .dashboard-container {
    padding: 2px;
  }
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 16px;
  background: #fff;
  border-radius: 12px;
  /* box-shadow: none; */
  color: #222;
}

.dashboard-header h1 {
  color: #222;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-range-selector label {
  font-weight: 600;
  color: #555;
}

.time-select {
  padding: 8px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.time-select:focus {
  outline: none;
  border-color: #007bff;
}

/* Legacy metrics grid styles removed - analytics now uses profile design pattern */

.glam-card.metric-card {
  background: rgba(255,255,255,0.85);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(44,62,80,0.08);
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(44,62,80,0.10);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.4s cubic-bezier(0.25,0.8,0.25,1);
  padding: 32px 24px;
}

.glam-card.metric-card:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: 0 18px 45px rgba(44,62,80,0.18);
  border-color: var(--accent-color);
}

.metric-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  border-radius: 12px;
  color: var(--accent-color);
}

.metric-content h3 {
  color: var(--accent-color);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
}

.metric-value {
  color: var(--text-dark);
  font-size: 2.2rem;
  font-weight: 700;
}

.metric-change {
  font-size: 0.85rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

.metric-change.positive {
  background: rgba(255, 215, 0, 0.15);
  color: var(--accent-color);
}

.metric-change.negative {
  background: rgba(255, 99, 71, 0.15);
  color: #FF6347;
}

/* Chart container styles removed - analytics now uses profile design pattern */

/* Performance Section */
.performance-section {
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(44,62,80,0.10);
  padding: 40px 32px;
  margin-bottom: 48px;
  color: var(--text-dark);
}

.performance-section h2 {
  color: var(--accent-color);
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  font-weight: 700;
}

.performance-card h4 {
  color: var(--accent-color);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
}

.performance-value {
  color: var(--text-dark);
  font-size: 1.5rem;
  font-weight: 700;
}

.performance-bar {
  width: 100%;
  height: 8px;
  background: rgba(44,62,80,0.08);
  border-radius: 4px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  background: var(--accent-color);
  border-radius: 4px;
}

/* Recent Activity */
.recent-activity {
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(44,62,80,0.10);
  padding: 40px 32px;
  color: var(--text-dark);
}

.recent-activity h2 {
  color: var(--accent-color);
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  font-weight: 700;
}

.activity-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  gap: 32px;
}

.activity-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  border-radius: 12px;
  color: var(--accent-color);
}

.activity-content p {
  color: var(--text-dark);
}

.activity-time {
  color: var(--accent-color);
  font-size: 0.9rem;
}

/* Tables (Services, Staff, Users) */
.table thead th, .table thead td {
  color: #222;
  font-weight: 700;
  font-size: 1rem;
  background: transparent;
  border-bottom: 2px solid #eee;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f3f3f3;
}

.table-bordered {
  border: 1px solid #eee;
}

.table td, .table th {
  color: #222;
  font-size: 1rem;
  vertical-align: middle;
}

/* Loading and Error States */
.loading-spinner, .error-message {
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(44,62,80,0.10);
  color: var(--text-dark);
  padding: 40px 32px;
  margin: 48px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e1e8ed;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message h3 {
  margin-bottom: 15px;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 15px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  /* Analytics responsive styles removed - handled by AnalyticsDashboard.css */

  .performance-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .activity-item {
    padding: 12px;
  }

  .activity-icon {
    font-size: 1.2rem;
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .admin-dashboard {
    padding: 10px;
  }

  .dashboard-header {
    padding: 15px;
  }

  /* Analytics mobile styles removed - handled by AnalyticsDashboard.css */

  .performance-section,
  .recent-activity {
    padding: 20px;
  }
}

/* --- Sidebar Navigation --- */
.admin-dashboard-layout {
  display: flex;
  width: 100%;
  min-height: 100vh;
  position: relative;
}
.admin-sidebar {
  /* position: static; */
  width: 72px;
  background: #24292f;
  border-right: 1px solid #30363d;
  display: flex;
  flex-direction: column;
  transition: none;
  box-shadow: none;
  z-index: 1;
}
.admin-sidebar .sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px 24px 16px 24px;
}
.sidebar-logo {
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  font-weight: 800;
  color: var(--accent-color);
  letter-spacing: 1px;
}
.sidebar-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-dark);
  cursor: pointer;
  display: none;
}
.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 16px;
}
.sidebar-link {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  color: var(--text-dark);
  font-size: 1.1rem;
  font-weight: 600;
  padding: 12px 18px;
  border-radius: 12px;
  margin-bottom: 2px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
}
.sidebar-link.active {
  color: var(--accent-color);
  background: rgba(255,215,0,0.12);
  font-weight: 800;
  box-shadow: 0 2px 8px rgba(255,215,0,0.08);
}
.sidebar-link:hover {
  color: var(--accent-color);
  transform: scale(1.05);
  background: rgba(255,215,0,0.18);
}

/* Hamburger for mobile */
.sidebar-hamburger {
  position: fixed;
  top: 24px;
  left: 24px;
  z-index: 2100;
  background: rgba(255,255,255,0.85);
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(44,62,80,0.10);
  width: 48px;
  height: 48px;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--text-dark);
  cursor: pointer;
  transition: box-shadow 0.3s;
}
.sidebar-hamburger:active {
  transform: scale(0.95);
  box-shadow: none;
}

/* Main content shifts right on desktop */
.admin-main {
  flex: 1;
  margin-left: 0;
  min-width: 0;
}

/* Sidebar overlay on mobile */
@media (max-width: 992px) {
  .admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 240px;
    z-index: 1050;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    background: #24292f;
  }
  .admin-sidebar.open,
  .admin-sidebar.sidebar-open {
    transform: translateX(0);
  }
  .admin-main.sidebar-open {
    filter: blur(3px);
    pointer-events: none;
  }
  .admin-main {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: none !important;
  }
}

/* Hide hamburger on desktop */
@media (min-width: 992px) {
  .sidebar-hamburger {
    display: none;
  }
  .sidebar-close-btn {
    display: none;
  }

  /* Desktop sidebar styles */
  .admin-sidebar {
    width: 72px !important;
    position: relative !important;
    transform: none !important;
    background: #24292f !important;
  }

  /* Desktop main content */
  .admin-main {
    margin-left: 72px !important;
    width: calc(100% - 72px) !important;
    max-width: none !important;
    padding: 24px 32px !important;
  }

  /* Ensure full width utilization on desktop */
  .admin-dashboard-layout {
    width: 100vw !important;
    max-width: none !important;
  }

  .analytics-dashboard-container,
  .dashboard-container {
    width: 100% !important;
    max-width: none !important;
  }
}

/* --- Dashboard Container --- */
.dashboard-container {
  width: 100%;
  min-width: 0;
  max-width: none;
}

/* --- Admin Main Content --- */
.admin-main {
  flex: 1;
  width: 100%;
  min-width: 0;
  max-width: none;
  overflow: visible;
}

/* --- Analytics Dashboard Container --- */
.analytics-dashboard-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

.analytics-dashboard-container .profile-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

.analytics-dashboard-container .profile-content {
  width: 100%;
  max-width: none;
}

/* --- Ensure all admin sections stretch properly --- */
.admin-main > div,
.admin-main .profile-container,
.admin-main .dashboard-container,
.admin-main .services-container,
.admin-main .staff-container,
.admin-main .users-container,
.admin-main .customers-container,
.admin-main .bookings-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* --- Fix grid layouts to use full width --- */
.metrics-grid,
.charts-grid,
.performance-grid,
.services-grid,
.staff-grid,
.users-grid,
.customers-grid {
  width: 100% !important;
  max-width: none !important;
}

/* --- Ensure profile sections stretch --- */
.profile-section {
  width: 100% !important;
  max-width: none !important;
  margin-bottom: 2rem !important;
}

/* --- Fix table containers --- */
.table-container,
.table-responsive {
  width: 100% !important;
  max-width: none !important;
  overflow-x: auto !important;
}

/* --- Admin Table Styles --- */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.admin-table th {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.admin-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  color: #495057;
}

.admin-table tbody tr:hover {
  background: #f8f9fa;
}

.admin-table .btn {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}

/* Duplicate analytics styles removed - analytics now uses profile design pattern with namespaced classes */

.metrics-grid,
.performance-grid,
.recent-activity-grid,
.dashboard-container,
.admin-main {
  width: 100%;
  min-width: 0;
}
.glam-card {
  width: 100%;
  min-width: 0;
} 