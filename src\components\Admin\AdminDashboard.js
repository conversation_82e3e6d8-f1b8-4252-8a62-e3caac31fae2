import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import Modal from '../Modal';
import { useNotification } from '../../context/NotificationContext';
import AnalyticsDashboard from './AnalyticsDashboard';
import ManageBookings from './ManageBookings';
import './AdminDashboard.css';
import { staff as initialStaff } from '../../data/mockData';
import UserManagement from './UserManagement';

const SIDEBAR_LINKS = [
  { key: 'analytics', label: 'Analytics', icon: 'bi-graph-up' },
  { key: 'services', label: 'Services', icon: 'bi-scissors' },
  { key: 'staff', label: 'Staff', icon: 'bi-people' },
  { key: 'users', label: 'Users', icon: 'bi-person-gear' },
  { key: 'customers', label: 'Customers', icon: 'bi-person-lines-fill' },
  { key: 'bookings', label: 'All Bookings', icon: 'bi-calendar-event' },
];

const AdminDashboard = () => {
  const { showNotification } = useNotification();
  const [activeTab, setActiveTab] = useState('analytics');
  const [services, setServices] = useState([
    { id: 1, name: 'Haircut', price: 25.00, duration: 30 },
    { id: 2, name: 'Hair Styling', price: 35.00, duration: 45 },
    { id: 3, name: 'Hair Coloring', price: 80.00, duration: 120 },
    { id: 4, name: 'Manicure', price: 20.00, duration: 30 },
    { id: 5, name: 'Pedicure', price: 30.00, duration: 45 },
  ]);
  const [staff, setStaff] = useState(initialStaff);
  const [users, setUsers] = useState([]);
  const [showUserManagement, setShowUserManagement] = useState(false);

  // State for modals
  const [isServiceModalOpen, setIsServiceModalOpen] = useState(false);
  const [isStaffModalOpen, setIsStaffModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);

  // State for selected items
  const [selectedService, setSelectedService] = useState(null);
  const [selectedStaff, setSelectedStaff] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [serviceSearchTerm, setServiceSearchTerm] = useState('');
  const [staffSearchTerm, setStaffSearchTerm] = useState('');
  const [userSearchTerm, setUserSearchTerm] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // Show 10 items per page

  const navigate = useNavigate();

  const [sidebarOpen, setSidebarOpen] = useState(false);

  const [customers, setCustomers] = useState([]);
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Fetch customers on component mount
  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users/');
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        // If API doesn't exist, use mock data
        setUsers([
          { id: 1, username: 'john_doe', email: '<EMAIL>', role: 'customer', isActive: true, dateJoined: '2024-01-15' },
          { id: 2, username: 'jane_smith', email: '<EMAIL>', role: 'vendor', isActive: true, dateJoined: '2024-01-20' },
          { id: 3, username: 'admin_user', email: '<EMAIL>', role: 'admin', isActive: true, dateJoined: '2024-01-10' },
          { id: 4, username: 'inactive_user', email: '<EMAIL>', role: 'customer', isActive: false, dateJoined: '2024-01-05' },
        ]);
      }
    } catch (err) {
      // Use mock data if API fails
      setUsers([
        { id: 1, username: 'john_doe', email: '<EMAIL>', role: 'customer', isActive: true, dateJoined: '2024-01-15' },
        { id: 2, username: 'jane_smith', email: '<EMAIL>', role: 'vendor', isActive: true, dateJoined: '2024-01-20' },
        { id: 3, username: 'admin_user', email: '<EMAIL>', role: 'admin', isActive: true, dateJoined: '2024-01-10' },
        { id: 4, username: 'inactive_user', email: '<EMAIL>', role: 'customer', isActive: false, dateJoined: '2024-01-05' },
      ]);
    }
  };

  // Fetch services from backend
  const fetchServices = async () => {
    try {
      const res = await fetch('/api/services/');
      if (!res.ok) throw new Error('Failed to fetch services');
      const data = await res.json();
      setServices(data);
    } catch (err) {
      // Optionally show notification
    }
  };

  React.useEffect(() => {
    fetchServices();
  }, []);

  // Service Handlers
  const handleAddService = () => {
    setSelectedService(null);
    setIsServiceModalOpen(true);
  };

  const handleEditService = (service) => {
    setSelectedService(service);
    setIsServiceModalOpen(true);
  };

  const handleDeleteService = async (service) => {
    try {
      const res = await fetch(`/api/services/${service.id}/`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete service');
      fetchServices();
      showNotification('Service deleted successfully!', 'success');
    } catch (err) {
      showNotification('Failed to delete service', 'error');
    }
  };

  const handleSaveService = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const serviceData = {
      name: formData.get('name'),
      price: parseFloat(formData.get('price')),
      duration: parseInt(formData.get('duration')),
    };

    try {
      if (selectedService) {
        // Edit
        const res = await fetch(`/api/services/${selectedService.id}/`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(serviceData),
        });
        if (!res.ok) throw new Error('Failed to update service');
      } else {
        // Add
        const res = await fetch('/api/services/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(serviceData),
        });
        if (!res.ok) throw new Error('Failed to add service');
      }
      setIsServiceModalOpen(false);
      fetchServices();
    } catch (err) {
      // Optionally show notification
    }
  };

  // Staff Handlers
  const handleAddStaff = () => {
    setSelectedStaff(null);
    setIsStaffModalOpen(true);
  };

  const handleEditStaff = (staffMember) => {
    setSelectedStaff(staffMember);
    setIsStaffModalOpen(true);
  };

  const handleDeleteStaff = (staffMember) => {
    setStaff(staff.filter(s => s.id !== staffMember.id));
    showNotification('Staff member deleted successfully!', 'success');
  };

  const handleSaveStaff = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const staffData = {
      id: selectedStaff ? selectedStaff.id : Date.now(),
      name: formData.get('name'),
      role: formData.get('role'),
      services: formData.get('services').split(',').map(s => s.trim()),
    };

    if (selectedStaff) {
      setStaff(staff.map(s => (s.id === staffData.id ? staffData : s)));
      showNotification('Staff member updated successfully!', 'success');
    } else {
      setStaff([...staff, staffData]);
      showNotification('Staff member added successfully!', 'success');
    }
    setIsStaffModalOpen(false);
  };

  // Customer Handlers
  const fetchCustomers = async () => {
    try {
      const res = await fetch('/api/customers/');
      if (!res.ok) throw new Error('Failed to fetch customers');
      const data = await res.json();
      setCustomers(data);
    } catch (err) {
      setCustomers([]);
    }
  };

  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setIsCustomerModalOpen(true);
  };

  const handleEditCustomer = (customer) => {
    setSelectedCustomer(customer);
    setIsCustomerModalOpen(true);
  };

  const handleDeleteCustomer = async (customer) => {
    if (!window.confirm('Delete this customer?')) return;
    await fetch(`/api/customers/${customer.id}/`, { method: 'DELETE' });
    fetchCustomers();
  };

  const handleSaveCustomer = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const customerData = {
      name: formData.get('name'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      notes: formData.get('notes'),
      salon: formData.get('salon'),
      user: formData.get('user') || null,
    };
    if (selectedCustomer) {
      await fetch(`/api/customers/${selectedCustomer.id}/`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
    } else {
      await fetch('/api/customers/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
    }
    setIsCustomerModalOpen(false);
    fetchCustomers();
  };

  // User Handlers
  const handleAddUser = () => {
    setSelectedUser(null);
    setIsUserModalOpen(true);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setIsUserModalOpen(true);
  };

  const handleDeleteUser = (user) => {
    setUsers(users.filter(u => u.id !== user.id));
    showNotification('User deleted successfully!', 'success');
  };

  const handleBookForUser = (user) => {
    // Navigate to booking form with user data
    navigate(`/booking-form?userId=${user.id}&userName=${user.username}&userEmail=${user.email}`);
  };

  const handleSaveUser = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const userData = {
      id: selectedUser ? selectedUser.id : Date.now(),
      username: formData.get('username'),
      email: formData.get('email'),
      role: formData.get('role'),
      isActive: formData.get('isActive') === 'true',
      dateJoined: selectedUser ? selectedUser.dateJoined : new Date().toISOString().split('T')[0],
    };

    if (selectedUser) {
      setUsers(users.map(u => (u.id === userData.id ? userData : u)));
      showNotification('User updated successfully!', 'success');
    } else {
      setUsers([...users, userData]);
      showNotification('User added successfully!', 'success');
    }
    setIsUserModalOpen(false);
  };

  const handleToggleUserStatus = (user) => {
    const updatedUser = { ...user, isActive: !user.isActive };
    setUsers(users.map(u => (u.id === user.id ? updatedUser : u)));
    showNotification(`User ${updatedUser.isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
  };

  const filteredServices = useMemo(() => services.filter(service => Object.values(service).some(value => String(value).toLowerCase().includes(serviceSearchTerm.toLowerCase()))), [services, serviceSearchTerm]);

  const filteredStaff = useMemo(() => staff.filter(member => Object.values(member).some(value => String(value).toLowerCase().includes(staffSearchTerm.toLowerCase()))), [staff, staffSearchTerm]);

  const filteredUsers = useMemo(() => users.filter(user => Object.values(user).some(value => String(value).toLowerCase().includes(userSearchTerm.toLowerCase()))), [users, userSearchTerm]);

  // Pagination helpers
  const getCurrentItems = (items) => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return items.slice(indexOfFirstItem, indexOfLastItem);
  };

  const totalPages = (items) => Math.ceil(items.length / itemsPerPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const resetPagination = () => {
    setCurrentPage(1);
  };

  // Reset pagination when search terms change
  useEffect(() => {
    resetPagination();
  }, [serviceSearchTerm, staffSearchTerm, userSearchTerm, customerSearchTerm]);

  // Pagination Component
  const Pagination = ({ items, currentPage, onPageChange }) => {
    const totalPagesCount = totalPages(items);
    
    if (totalPagesCount <= 1) return null;

    const pageNumbers = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPagesCount, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <nav aria-label="Page navigation" className="mt-4">
        <ul className="pagination justify-content-center">
          <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
            <button
              className="page-link bg-transparent text-light border-light"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <i className="bi bi-chevron-left" />
            </button>
          </li>
          
          {startPage > 1 && (
          <>
            <li className="page-item">
              <button
                className="page-link bg-transparent text-light border-light"
                onClick={() => onPageChange(1)}
              >
                1
              </button>
            </li>
            {startPage > 2 && (
            <li className="page-item disabled">
              <span className="page-link bg-transparent text-light border-light">...</span>
            </li>
            )}
          </>
          )}
          
          {pageNumbers.map(number => (
            <li key={number} className={`page-item ${currentPage === number ? 'active' : ''}`}>
              <button
                className={`page-link ${currentPage === number ? 'bg-primary border-primary' : 'bg-transparent text-light border-light'}`}
                onClick={() => onPageChange(number)}
              >
                {number}
              </button>
            </li>
          ))}
          
          {endPage < totalPagesCount && (
          <>
            {endPage < totalPagesCount - 1 && (
            <li className="page-item disabled">
              <span className="page-link bg-transparent text-light border-light">...</span>
            </li>
            )}
            <li className="page-item">
              <button
                className="page-link bg-transparent text-light border-light"
                onClick={() => onPageChange(totalPagesCount)}
              >
                {totalPagesCount}
              </button>
            </li>
          </>
          )}
          
          <li className={`page-item ${currentPage === totalPagesCount ? 'disabled' : ''}`}>
            <button
              className="page-link bg-transparent text-light border-light"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPagesCount}
            >
              <i className="bi bi-chevron-right" />
            </button>
          </li>
          <li className="nav-item">
            <button
              className={`nav-link ${activeTab === 'users' ? 'active glam-btn' : 'text-light'}`}
              onClick={() => setActiveTab('users')}
            >
              Manage Users
            </button>
          </li>
        </ul>
      </nav>
    );
  };

  const renderAnalyticsTab = () => <AnalyticsDashboard />;

  const renderServicesTab = () => (
    <div className="analytics-dashboard-container">
      <div className="profile-container">
        <div className="profile-header">
          <h1 className="profile-title">✂️ Services Management</h1>
          <p className="profile-subtitle">Manage your salon services and pricing</p>
        </div>

        <div className="profile-content">
          <div className="profile-section">
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h3 className="section-title">📋 Services List</h3>
              <button onClick={handleAddService} className="btn btn-primary">
                <i className="bi bi-plus-circle me-2" />
                Add Service
              </button>
            </div>

            <div className="mb-3">
              <input
                type="text"
                className="form-input"
                placeholder="Search services..."
                value={serviceSearchTerm}
                onChange={(e) => setServiceSearchTerm(e.target.value)}
              />
            </div>

            <div className="table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Service Name</th>
                    <th>Price</th>
                    <th>Duration</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getCurrentItems(filteredServices).map((service) => (
                    <tr key={service.id}>
                      <td>{service.name}</td>
                      <td>${service.price}</td>
                      <td>{service.duration} min</td>
                      <td>
                        <button
                          className="btn btn-sm btn-warning me-2"
                          onClick={() => handleEditService(service)}
                        >
                          <i className="bi bi-pencil" />
                        </button>
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => {
                            setItemToDelete({ item: service, type: 'service' });
                            setIsDeleteModalOpen(true);
                          }}
                        >
                          <i className="bi bi-trash" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="d-flex justify-content-between align-items-center mt-3">
              <div className="text-muted">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredServices.length)} to {Math.min(currentPage * itemsPerPage, filteredServices.length)} of {filteredServices.length} services
              </div>
              <nav>
                <ul className="pagination pagination-sm">
                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1}>
                      Previous
                    </button>
                  </li>
                  {Array.from({ length: Math.ceil(filteredServices.length / itemsPerPage) }, (_, i) => (
                    <li key={i + 1} className={`page-item ${currentPage === i + 1 ? 'active' : ''}`}>
                      <button className="page-link" onClick={() => handlePageChange(i + 1)}>
                        {i + 1}
                      </button>
                    </li>
                  ))}
                  <li className={`page-item ${currentPage === Math.ceil(filteredServices.length / itemsPerPage) ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === Math.ceil(filteredServices.length / itemsPerPage)}>
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Service Modal */}
      {isServiceModalOpen && (
        <div className="modal show d-block" tabIndex="-1" style={{ background: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content bg-dark text-light">
              <form onSubmit={handleSaveService}>
                <div className="modal-header">
                  <h5 className="modal-title">
                    {selectedService ? 'Edit' : 'Add'} Service
                  </h5>
                  <button type="button" className="btn-close" onClick={() => setIsServiceModalOpen(false)} />
                </div>
                <div className="modal-body">
                  <div className="mb-3">
                    <label className="form-label">Service Name</label>
                    <input type="text" className="form-control" name="name" defaultValue={selectedService?.name || ''} required />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Price ($)</label>
                    <input type="number" step="0.01" className="form-control" name="price" defaultValue={selectedService?.price || ''} required />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Duration (minutes)</label>
                    <input type="number" className="form-control" name="duration" defaultValue={selectedService?.duration || ''} required />
                  </div>
                </div>
                <div className="modal-footer">
                  <button type="button" className="btn btn-secondary" onClick={() => setIsServiceModalOpen(false)}>Cancel</button>
                  <button type="submit" className="btn btn-primary">Save</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderStaffTab = () => (
    <div className="analytics-dashboard-container">
      <div className="profile-container">
        <div className="profile-header">
          <h1 className="profile-title">👥 Staff Management</h1>
          <p className="profile-subtitle">Manage your salon team and their specialties</p>
        </div>

        <div className="profile-content">
          <div className="profile-section">
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h3 className="section-title">👨‍💼 Staff Members</h3>
              <button onClick={handleAddStaff} className="btn btn-primary">
                <i className="bi bi-plus-circle me-2" />
                Add Staff
              </button>
            </div>

            <div className="mb-3">
              <input
                type="text"
                className="form-input"
                placeholder="Search staff..."
                value={staffSearchTerm}
                onChange={(e) => setStaffSearchTerm(e.target.value)}
              />
            </div>

            <div className="table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Role</th>
                    <th>Services</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getCurrentItems(filteredStaff).map((member) => (
                    <tr key={member.id}>
                      <td>{member.name}</td>
                      <td>{member.role}</td>
                      <td>{member.services.join(', ')}</td>
                      <td>
                        <button
                          className="btn btn-sm btn-warning me-2"
                          onClick={() => handleEditStaff(member)}
                        >
                          <i className="bi bi-pencil" />
                        </button>
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => {
                            setItemToDelete({ item: member, type: 'staff' });
                            setIsDeleteModalOpen(true);
                          }}
                        >
                          <i className="bi bi-trash" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="d-flex justify-content-between align-items-center mt-3">
              <div className="text-muted">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredStaff.length)} to {Math.min(currentPage * itemsPerPage, filteredStaff.length)} of {filteredStaff.length} staff
              </div>
              <nav>
                <ul className="pagination pagination-sm">
                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1}>
                      Previous
                    </button>
                  </li>
                  {Array.from({ length: Math.ceil(filteredStaff.length / itemsPerPage) }, (_, i) => (
                    <li key={i + 1} className={`page-item ${currentPage === i + 1 ? 'active' : ''}`}>
                      <button className="page-link" onClick={() => handlePageChange(i + 1)}>
                        {i + 1}
                      </button>
                    </li>
                  ))}
                  <li className={`page-item ${currentPage === Math.ceil(filteredStaff.length / itemsPerPage) ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === Math.ceil(filteredStaff.length / itemsPerPage)}>
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Staff Modal */}
      {isStaffModalOpen && (
        <div className="modal show d-block" tabIndex="-1" style={{ background: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content bg-dark text-light">
              <form onSubmit={handleSaveStaff}>
                <div className="modal-header">
                  <h5 className="modal-title">
                    {selectedStaff ? 'Edit' : 'Add'} Staff Member
                  </h5>
                  <button type="button" className="btn-close" onClick={() => setIsStaffModalOpen(false)} />
                </div>
                <div className="modal-body">
                  <div className="mb-3">
                    <label className="form-label">Name</label>
                    <input type="text" className="form-control" name="name" defaultValue={selectedStaff?.name || ''} required />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Role</label>
                    <input type="text" className="form-control" name="role" defaultValue={selectedStaff?.role || ''} required />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Services (comma-separated)</label>
                    <input type="text" className="form-control" name="services" defaultValue={selectedStaff?.services?.join(', ') || ''} placeholder="e.g., Haircut, Styling, Color" required />
                  </div>
                </div>
                <div className="modal-footer">
                  <button type="button" className="btn btn-secondary" onClick={() => setIsStaffModalOpen(false)}>Cancel</button>
                  <button type="submit" className="btn btn-primary">Save</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderUsersTab = () => (
    <div className="analytics-dashboard-container">
      <div className="profile-container">
        <div className="profile-header">
          <h1 className="profile-title">⚙️ User Management</h1>
          <p className="profile-subtitle">Manage platform users and permissions</p>
        </div>

        <div className="profile-content">
          <div className="profile-section">
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h3 className="section-title">👤 Platform Users</h3>
              <button onClick={handleAddUser} className="btn btn-primary">
                <i className="bi bi-plus-circle me-2" />
                Add User
              </button>
            </div>

            <div className="mb-3">
              <input
                type="text"
                className="form-input"
                placeholder="Search users..."
                value={userSearchTerm}
                onChange={(e) => setUserSearchTerm(e.target.value)}
              />
            </div>

            <div className="table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Date Joined</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getCurrentItems(users.filter(u => 
                    [u.username, u.email, u.role].some(val => 
                      val && val.toLowerCase().includes(userSearchTerm.toLowerCase())
                    )
                  )).map((user) => (
                    <tr key={user.id}>
                      <td>{user.username}</td>
                      <td>{user.email}</td>
                      <td>{user.role}</td>
                      <td>
                        <span className={`badge ${user.isActive ? 'bg-success' : 'bg-danger'}`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td>{user.dateJoined}</td>
                      <td>
                        <button
                          className="btn btn-sm btn-warning me-2"
                          onClick={() => handleEditUser(user)}
                        >
                          <i className="bi bi-pencil" />
                        </button>
                        <button
                          className="btn btn-sm btn-danger me-2"
                          onClick={() => {
                            setItemToDelete({ item: user, type: 'user' });
                            setIsDeleteModalOpen(true);
                          }}
                        >
                          <i className="bi bi-trash" />
                        </button>
                        <button
                          className="btn btn-sm btn-info"
                          onClick={() => handleToggleUserStatus(user)}
                        >
                          <i className="bi bi-toggle-on" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="d-flex justify-content-between align-items-center mt-3">
              <div className="text-muted">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, users.length)} to {Math.min(currentPage * itemsPerPage, users.length)} of {users.length} users
              </div>
              <nav>
                <ul className="pagination pagination-sm">
                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1}>
                      Previous
                    </button>
                  </li>
                  {Array.from({ length: Math.ceil(users.length / itemsPerPage) }, (_, i) => (
                    <li key={i + 1} className={`page-item ${currentPage === i + 1 ? 'active' : ''}`}>
                      <button className="page-link" onClick={() => handlePageChange(i + 1)}>
                        {i + 1}
                      </button>
                    </li>
                  ))}
                  <li className={`page-item ${currentPage === Math.ceil(users.length / itemsPerPage) ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === Math.ceil(users.length / itemsPerPage)}>
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const filteredCustomers = useMemo(() => customers.filter(c => [c.name, c.email, c.phone].some(val => val && val.toLowerCase().includes(customerSearchTerm.toLowerCase()))), [customers, customerSearchTerm]);

  const renderCustomersTab = () => (
    <div className="container mt-5">
      <div className="glam-card p-4">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2 className="card-title text-light mb-0">Manage Customers</h2>
          <button onClick={handleAddCustomer} className="btn btn-primary glam-btn">
            <i className="bi bi-plus-circle me-2" />
            Add Customer
          </button>
        </div>
        <input
          type="text"
          className="form-control mb-3"
          placeholder="Search by name, email, or phone..."
          value={customerSearchTerm}
          onChange={e => setCustomerSearchTerm(e.target.value)}
        />
        <div className="table-responsive-sm">
          <table className="table table-striped table-bordered text-light">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Salon</th>
                <th>User</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {getCurrentItems(filteredCustomers).map(c => (
                <tr key={c.id}>
                  <td>{c.name}</td>
                  <td>{c.email}</td>
                  <td>{c.phone}</td>
                  <td>{c.salon}</td>
                  <td>{c.user}</td>
                  <td>
                    <button className="btn btn-warning btn-sm glam-btn me-2" onClick={() => handleEditCustomer(c)}><i className="bi bi-pencil" /></button>
                    <button className="btn btn-danger btn-sm glam-btn" onClick={() => handleDeleteCustomer(c)}><i className="bi bi-trash" /></button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredCustomers.length === 0 && <div className="text-light">No customers found.</div>}
        </div>
        
        {/* Pagination Info */}
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="text-light">
            Showing 
            {' '}
            {Math.min((currentPage - 1) * itemsPerPage + 1, filteredCustomers.length)}
            {' '}
            to 
            {' '}
            {Math.min(currentPage * itemsPerPage, filteredCustomers.length)}
            {' '}
            of 
            {' '}
            {filteredCustomers.length}
            {' '}
            customers
          </div>
        </div>
        
        {/* Pagination */}
        <Pagination 
          items={filteredCustomers} 
          currentPage={currentPage} 
          onPageChange={handlePageChange} 
        />
        {/* Modal for add/edit customer */}
        {isCustomerModalOpen && (
          <div className="modal show d-block" tabIndex="-1" style={{ background: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog">
              <div className="modal-content bg-dark text-light">
                <form onSubmit={handleSaveCustomer}>
                  <div className="modal-header">
                    <h5 className="modal-title">
                      {selectedCustomer ? 'Edit' : 'Add'}
                      {' '}
                      Customer
                    </h5>
                    <button type="button" className="btn-close" onClick={() => setIsCustomerModalOpen(false)} />
                  </div>
                  <div className="modal-body">
                    <div className="mb-3">
                      <label className="form-label">Name</label>
                      <input type="text" className="form-control" name="name" defaultValue={selectedCustomer?.name || ''} required />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Email</label>
                      <input type="email" className="form-control" name="email" defaultValue={selectedCustomer?.email || ''} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Phone</label>
                      <input type="text" className="form-control" name="phone" defaultValue={selectedCustomer?.phone || ''} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Notes</label>
                      <textarea className="form-control" name="notes" defaultValue={selectedCustomer?.notes || ''} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Salon ID</label>
                      <input type="number" className="form-control" name="salon" defaultValue={selectedCustomer?.salon || ''} required />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">User ID (optional)</label>
                      <input type="number" className="form-control" name="user" defaultValue={selectedCustomer?.user || ''} />
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button type="button" className="btn btn-secondary" onClick={() => setIsCustomerModalOpen(false)}>Cancel</button>
                    <button type="submit" className="btn btn-primary">Save</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Responsive sidebar visibility
  const isDesktop = typeof window !== 'undefined' && window.innerWidth >= 992;

  return (
    <div className="admin-dashboard-layout" style={{ display: 'flex', minHeight: '100vh', background: '#f8f9fa' }}>
      {/* Sidebar - GitHub-style slim with icons only */}
      <nav
        className={`admin-sidebar${sidebarOpen || isDesktop ? ' open' : ''}`}
        aria-label="Admin sidebar navigation"
        style={{
          position: isDesktop ? 'relative' : 'fixed',
          top: 0,
          left: isDesktop ? 0 : (sidebarOpen ? 0 : '-72px'),
          width: isDesktop ? 72 : 240,
          height: '100%',
          background: '#24292f',
          boxShadow: isDesktop ? 'none' : '2px 0 16px rgba(44,62,80,0.08)',
          zIndex: 1000,
          transition: 'left 0.3s cubic-bezier(0.4,0,0.2,1)',
          borderRight: '1px solid #30363d',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div className="sidebar-header" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: isDesktop ? 'center' : 'space-between',
          padding: isDesktop ? '16px 0' : '18px 18px 8px 18px'
        }}>
          <span className="sidebar-logo" style={{
            fontWeight: 800,
            fontSize: isDesktop ? 18 : 22,
            color: '#f0f6fc',
            letterSpacing: 1
          }}>SG</span>
          {!isDesktop && (
            <button
              className="sidebar-close-btn"
              aria-label="Close sidebar"
              style={{ background: 'none', border: 'none', fontSize: 24, cursor: 'pointer', color: '#f0f6fc', display: 'block' }}
              onClick={() => setSidebarOpen(false)}
            >
              <i className="bi bi-x-lg" />
            </button>
          )}
        </div>
        <ul className="sidebar-nav" style={{ listStyle: 'none', padding: 0, margin: 0, flex: 1 }}>
          {SIDEBAR_LINKS.map(link => (
            <li key={link.key}>
              <button
                className={`sidebar-link${activeTab === link.key ? ' active' : ''}`}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: isDesktop ? 'center' : 'flex-start',
                  width: '100%',
                  border: 'none',
                  background: 'none',
                  padding: isDesktop ? '12px 0' : '14px 24px',
                  fontSize: 16,
                  color: activeTab === link.key ? '#58a6ff' : '#8b949e',
                  fontWeight: 500,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  borderLeft: activeTab === link.key ? (isDesktop ? 'none' : '4px solid #58a6ff') : (isDesktop ? 'none' : '4px solid transparent'),
                  background: activeTab === link.key ? (isDesktop ? '#1f2937' : 'rgba(88,166,255,0.1)') : 'none',
                  textAlign: 'left',
                  position: 'relative'
                }}
                onClick={() => { setActiveTab(link.key); setSidebarOpen(false); }}
                aria-current={activeTab === link.key ? 'page' : undefined}
                title={isDesktop ? link.label : undefined}
              >
                <i className={`bi ${link.icon}`} style={{
                  marginRight: isDesktop ? 0 : 14,
                  fontSize: isDesktop ? 18 : 20,
                  color: activeTab === link.key ? '#58a6ff' : '#8b949e'
                }} />
                {!isDesktop && <span style={{ color: activeTab === link.key ? '#58a6ff' : '#f0f6fc', fontWeight: 500 }}>{link.label}</span>}
                {isDesktop && activeTab === link.key && (
                  <div style={{
                    position: 'absolute',
                    right: '-2px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    width: '2px',
                    height: '20px',
                    background: '#58a6ff',
                    borderRadius: '1px'
                  }} />
                )}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Hamburger for mobile */}
      {!isDesktop && (
        <button
          className="sidebar-hamburger"
          aria-label="Open sidebar"
          style={{
            position: 'fixed',
            top: 18,
            left: 18,
            zIndex: 1100,
            background: '#24292f',
            border: '1px solid #30363d',
            borderRadius: 8,
            padding: 8,
            fontSize: 24,
            color: '#f0f6fc',
            display: sidebarOpen ? 'none' : 'block',
            boxShadow: '0 2px 8px rgba(44,62,80,0.08)',
          }}
          onClick={() => setSidebarOpen(true)}
        >
          <i className="bi bi-list" />
        </button>
      )}

      {/* Main content */}
      <main
        className="admin-main"
        style={{
          flex: 1,
          marginLeft: isDesktop ? 72 : 0,
          padding: window.innerWidth < 600 ? '16px 8px' : '24px 32px',
          transition: 'margin-left 0.3s cubic-bezier(0.4,0,0.2,1)',
          minHeight: '100vh',
          background: '#f8f9fa',
          width: '100%',
          maxWidth: 'none',
          overflow: 'visible'
        }}
      >
        {activeTab === 'analytics' && renderAnalyticsTab()}
        {activeTab === 'services' && renderServicesTab()}
        {activeTab === 'staff' && renderStaffTab()}
        {activeTab === 'users' && renderUsersTab()}
        {activeTab === 'customers' && renderCustomersTab()}
        {activeTab === 'bookings' && <ManageBookings />}
        
        {/* Delete Confirmation Modal */}
        {isDeleteModalOpen && itemToDelete && (
          <div className="modal show d-block" tabIndex="-1" style={{ background: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog">
              <div className="modal-content bg-dark text-light">
                <div className="modal-header">
                  <h5 className="modal-title">Confirm Delete</h5>
                  <button type="button" className="btn-close" onClick={() => setIsDeleteModalOpen(false)} />
                </div>
                <div className="modal-body">
                  <p>Are you sure you want to delete this {itemToDelete.type}?</p>
                  <p><strong>{itemToDelete.item.name || itemToDelete.item.username || itemToDelete.item.email}</strong></p>
                </div>
                <div className="modal-footer">
                  <button type="button" className="btn btn-secondary" onClick={() => setIsDeleteModalOpen(false)}>Cancel</button>
                  <button 
                    type="button" 
                    className="btn btn-danger" 
                    onClick={() => {
                      if (itemToDelete.type === 'service') {
                        handleDeleteService(itemToDelete.item);
                      } else if (itemToDelete.type === 'staff') {
                        handleDeleteStaff(itemToDelete.item);
                      } else if (itemToDelete.type === 'user') {
                        handleDeleteUser(itemToDelete.item);
                      }
                      setIsDeleteModalOpen(false);
                      setItemToDelete(null);
                    }}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default AdminDashboard;
