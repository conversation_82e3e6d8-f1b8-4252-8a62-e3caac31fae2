/* Analytics Dashboard - Profile Design Pattern */

/* Main Container - <PERSON> Pattern */
.analytics-dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.analytics-dashboard-container .profile-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header - Profile Pattern */
.analytics-dashboard-container .profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem 1rem;
  text-align: center;
  color: white;
}

.analytics-dashboard-container .profile-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.analytics-dashboard-container .profile-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* Content Area - Profile Pattern */
.analytics-dashboard-container .profile-content {
  padding: 1.5rem 1rem;
}

/* Sections - Profile Pattern */
.analytics-dashboard-container .profile-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.analytics-dashboard-container .section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Form Elements - Profile Pattern */
.analytics-dashboard-container .form-group {
  margin-bottom: 1rem;
}

.analytics-dashboard-container .form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.analytics-dashboard-container .form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.analytics-dashboard-container .form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Metrics Grid */
.analytics-dashboard-container .metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.analytics-dashboard-container .metric-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analytics-dashboard-container .metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.analytics-dashboard-container .metric-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.analytics-dashboard-container .metric-content h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.25rem 0;
}

.analytics-dashboard-container .metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.analytics-dashboard-container .metric-change {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.analytics-dashboard-container .metric-change.positive {
  background: #d1fae5;
  color: #065f46;
}

/* Charts Grid */
.analytics-dashboard-container .charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.analytics-dashboard-container .chart-container {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.analytics-dashboard-container .chart-container h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.analytics-dashboard-container .chart-wrapper {
  position: relative;
  height: 300px;
}

/* Performance Grid */
.analytics-dashboard-container .performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.analytics-dashboard-container .performance-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.analytics-dashboard-container .performance-card h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
}

.analytics-dashboard-container .performance-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
}

.analytics-dashboard-container .performance-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.analytics-dashboard-container .performance-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Activity List */
.analytics-dashboard-container .activity-list {
  margin-top: 1rem;
}

.analytics-dashboard-container .activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 0.75rem;
}

.analytics-dashboard-container .activity-item:last-child {
  margin-bottom: 0;
}

.analytics-dashboard-container .activity-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.analytics-dashboard-container .activity-content {
  flex: 1;
}

.analytics-dashboard-container .activity-content p {
  font-size: 0.875rem;
  color: #374151;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.analytics-dashboard-container .activity-time {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .analytics-dashboard-container {
    padding: 0.75rem 0.25rem;
  }

  .analytics-dashboard-container .profile-container {
    border-radius: 12px;
  }

  .analytics-dashboard-container .profile-header {
    padding: 1rem 0.75rem;
  }

  .analytics-dashboard-container .profile-title {
    font-size: 1.25rem;
  }

  .analytics-dashboard-container .profile-content {
    padding: 1rem 0.75rem;
  }

  .analytics-dashboard-container .metrics-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .analytics-dashboard-container .charts-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .analytics-dashboard-container .performance-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .analytics-dashboard-container .chart-wrapper {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .analytics-dashboard-container .performance-grid {
    grid-template-columns: 1fr;
  }

  .analytics-dashboard-container .metric-card {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .analytics-dashboard-container .activity-item {
    gap: 0.75rem;
    padding: 0.75rem;
  }
}
