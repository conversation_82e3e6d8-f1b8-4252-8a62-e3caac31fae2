import React, { useState, useEffect, useCallback } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import './AnalyticsDashboard.css';
import './AdminDashboard.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const AnalyticsDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  const [timeRange, setTimeRange] = useState('30'); // days

  const fetchAnalytics = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/analytics/?time_range=${timeRange}`);
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      } else {
        // Use mock data if API doesn't exist
        setAnalytics({
          total_revenue: 123450,
          total_bookings: 1234,
          active_users: 5678,
          registered_salons: 89,
        });
      }
    } catch (err) {
      setError('Failed to load analytics data');
      // Use mock data on error
      setAnalytics({
        total_revenue: 123450,
        total_bookings: 1234,
        active_users: 5678,
        registered_salons: 89,
      });
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Mock data for charts (replace with real data from API)
  const revenueData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Revenue',
        data: [12000, 19000, 15000, 25000, 22000, 30000],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const bookingData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Bookings',
        data: [65, 59, 80, 81, 56, 95, 70],
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgb(54, 162, 235)',
        borderWidth: 1,
      },
    ],
  };

  const serviceDistribution = {
    labels: ['Haircut', 'Styling', 'Color', 'Treatment', 'Extensions'],
    datasets: [
      {
        data: [30, 25, 20, 15, 10],
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
        ],
        borderWidth: 2,
        borderColor: '#fff',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
  };

  const lineChartOptions = {
    ...chartOptions,
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
  };

  if (loading) {
    return (
      <div className="admin-dashboard">
        <div className="loading-spinner">
          <div className="spinner" />
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="admin-dashboard">
        <div className="error-message">
          <h3>Error Loading Dashboard</h3>
          <p>{error}</p>
          <button onClick={fetchAnalytics} className="retry-btn">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-dashboard-container">
      <div className="profile-container">
        {/* Header */}
        <div className="profile-header">
          <h1 className="profile-title">📊 Analytics Dashboard</h1>
          <p className="profile-subtitle">Track your platform's performance and growth</p>
        </div>

        <div className="profile-content">
          {/* Time Range Selector */}
          <div className="profile-section">
            <h3 className="section-title">📅 Time Range</h3>
            <div className="form-group">
              <label className="form-label">Select Time Period:</label>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="form-input"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
            </div>
          </div>

          {/* Key Metrics Cards */}
          <div className="profile-section">
            <h3 className="section-title">📊 Key Metrics</h3>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-icon">💰</div>
                <div className="metric-content">
                  <h4>Total Revenue</h4>
                  <p className="metric-value">
                    $
                    {analytics?.total_revenue || '123,450'}
                  </p>
                  <span className="metric-change positive">+12.5%</span>
                </div>
              </div>

              <div className="metric-card">
                <div className="metric-icon">📅</div>
                <div className="metric-content">
                  <h4>Total Bookings</h4>
                  <p className="metric-value">{analytics?.total_bookings || '1,234'}</p>
                  <span className="metric-change positive">+8.3%</span>
                </div>
              </div>

              <div className="metric-card">
                <div className="metric-icon">👥</div>
                <div className="metric-content">
                  <h4>Active Users</h4>
                  <p className="metric-value">{analytics?.active_users || '5,678'}</p>
                  <span className="metric-change positive">+15.2%</span>
                </div>
              </div>

              <div className="metric-card">
                <div className="metric-icon">🏪</div>
                <div className="metric-content">
                  <h4>Registered Salons</h4>
                  <p className="metric-value">{analytics?.registered_salons || '89'}</p>
                  <span className="metric-change positive">+5.7%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="profile-section">
            <h3 className="section-title">📈 Analytics Charts</h3>
            <div className="charts-grid">
              <div className="chart-container">
                <h4>Revenue Trend</h4>
                <div className="chart-wrapper">
                  <Line data={revenueData} options={lineChartOptions} />
                </div>
              </div>

              <div className="chart-container">
                <h4>Weekly Bookings</h4>
                <div className="chart-wrapper">
                  <Bar data={bookingData} options={chartOptions} />
                </div>
              </div>

              <div className="chart-container">
                <h4>Service Distribution</h4>
                <div className="chart-wrapper">
                  <Doughnut data={serviceDistribution} options={chartOptions} />
                </div>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="profile-section">
            <h3 className="section-title">⚡ Performance Metrics</h3>
            <div className="performance-grid">
              <div className="performance-card">
                <h4>Average Booking Value</h4>
                <p className="performance-value">$85.50</p>
                <div className="performance-bar">
                  <div className="performance-fill" style={{ width: '75%' }} />
                </div>
              </div>

              <div className="performance-card">
                <h4>Customer Satisfaction</h4>
                <p className="performance-value">4.8/5.0</p>
                <div className="performance-bar">
                  <div className="performance-fill" style={{ width: '96%' }} />
                </div>
              </div>

              <div className="performance-card">
                <h4>Repeat Customer Rate</h4>
                <p className="performance-value">68%</p>
                <div className="performance-bar">
                  <div className="performance-fill" style={{ width: '68%' }} />
                </div>
              </div>

              <div className="performance-card">
                <h4>Platform Uptime</h4>
                <p className="performance-value">99.9%</p>
                <div className="performance-bar">
                  <div className="performance-fill" style={{ width: '99.9%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="profile-section">
            <h3 className="section-title">🔔 Recent Activity</h3>
            <div className="activity-list">
              <div className="activity-item">
                <div className="activity-icon">🎉</div>
                <div className="activity-content">
                  <p>
                    <strong>New salon registered:</strong>
                    {' '}
                    Glamour Hair Studio
                  </p>
                  <span className="activity-time">2 hours ago</span>
                </div>
              </div>

              <div className="activity-item">
                <div className="activity-icon">💳</div>
                <div className="activity-content">
                  <p>
                    <strong>High-value booking:</strong>
                    {' '}
                    $150 styling appointment
                  </p>
                  <span className="activity-time">4 hours ago</span>
                </div>
              </div>

              <div className="activity-item">
                <div className="activity-icon">⭐</div>
                <div className="activity-content">
                  <p>
                    <strong>5-star review:</strong>
                    {' '}
                    Excellent service at Beauty Haven
                  </p>
                  <span className="activity-time">6 hours ago</span>
                </div>
              </div>

              <div className="activity-item">
                <div className="activity-icon">👤</div>
                <div className="activity-content">
                  <p>
                    <strong>New user signup:</strong>
                    {' '}
                    25 new customers today
                  </p>
                  <span className="activity-time">8 hours ago</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard; 
