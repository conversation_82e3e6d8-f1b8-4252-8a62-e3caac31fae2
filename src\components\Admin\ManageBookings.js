import React, { useState, useEffect, useMemo } from 'react';
import Modal from '../Modal';
import { useNotification } from '../../context/NotificationContext';

// Mock user list for admin booking assignment
const mockUsers = [
  { id: 1, username: 'admin', name: 'Admin', email: '<EMAIL>' },
  { id: 2, username: 'vendor1', name: 'Vendor One', email: '<EMAIL>' },
  { id: 3, username: 'apollo', name: 'Apollo', email: '<EMAIL>' },
  { id: 4, username: 'testuser', name: 'Test User', email: '<EMAIL>' },
];

const ManageBookings = () => {
  const { showNotification } = useNotification();
  const [allBookings, setAllBookings] = useState([]);
  const [salons, setSalons] = useState({});
  const [services, setServices] = useState({});
  const [staff, setStaff] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCancelConfirmModalOpen, setIsCancelConfirmModalOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [bookingToCancel, setBookingToCancel] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isNewModalOpen, setIsNewModalOpen] = useState(false);
  const [newBooking, setNewBooking] = useState({
    userId: '',
    userName: '',
    salon: '',
    service: '',
    staff: '',
    date: '',
    time: '',
    status: 'Confirmed',
    notes: '',
  });

  const fetchBookings = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/bookings/');
      if (!res.ok) throw new Error('Failed to fetch bookings');
      const data = await res.json();
      setAllBookings(data);

      // Fetch related data (salons, services, staff) for display
      const salonIds = [...new Set(data.map(b => b.salon))];
      const serviceIds = [...new Set(data.map(b => b.service))];
      const staffIds = [...new Set(data.map(b => b.staff))];

      const [salonsData, servicesData, staffData] = await Promise.all([
        Promise.all(salonIds.map(id => fetch(`/api/salons/${id}/`).then(res => res.json()))),
        Promise.all(serviceIds.map(id => fetch(`/api/services/${id}/`).then(res => res.json()))),
        Promise.all(staffIds.map(id => fetch(`/api/staff/${id}/`).then(res => res.json()))),
      ]);

      setSalons(salonsData.reduce((acc, s) => ({ ...acc, [s.id]: s }), {}));
      setServices(servicesData.reduce((acc, s) => ({ ...acc, [s.id]: s }), {}));
      setStaff(staffData.reduce((acc, s) => ({ ...acc, [s.id]: s }), {}));
    } catch (err) {
      setError('Failed to load bookings');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setIsViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedBooking(null);
  };

  const handleEdit = (booking) => {
    setSelectedBooking(booking);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const updatedBooking = {
      ...selectedBooking,
      status: formData.get('status'),
      date: formData.get('date'),
      time: formData.get('time'),
      notes: formData.get('notes'),
    };

    try {
      const res = await fetch(`/api/bookings/${updatedBooking.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken'), // Assuming CSRF token is available
        },
        body: JSON.stringify(updatedBooking),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.detail || 'Failed to update booking');
      }

      showNotification('Booking updated successfully!', 'success');
      setIsEditModalOpen(false);
      setSelectedBooking(null);
      fetchBookings(); // Refresh the list
    } catch (err) {
      showNotification(`Update failed: ${err.message}`, 'danger');
    }
  };

  const handleCancel = (booking) => {
    setBookingToCancel(booking);
    setIsCancelConfirmModalOpen(true);
  };

  const confirmCancel = async () => {
    try {
      const res = await fetch(`/api/bookings/${bookingToCancel.id}/`, {
        method: 'PATCH', // Use PATCH for partial update
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify({ status: 'Cancelled' }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.detail || 'Failed to cancel booking');
      }

      showNotification('Booking cancelled successfully!', 'success');
      setIsCancelConfirmModalOpen(false);
      setBookingToCancel(null);
      fetchBookings(); // Refresh the list
    } catch (err) {
      showNotification(`Cancellation failed: ${err.message}`, 'danger');
    }
  };

  // Helper to get CSRF token
  const getCookie = (name) => {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (`${name}=`)) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  };

  const filteredBookings = useMemo(() => allBookings.filter(booking => Object.values({
    id: booking.id,
    userName: booking.userName,
    salonName: salons[booking.salon]?.name || '',
    serviceName: services[booking.service]?.name || '',
    staffName: staff[booking.staff]?.name || '',
    date: booking.date,
    time: booking.time,
    status: booking.status,
  }).some(value => String(value).toLowerCase().includes(searchTerm.toLowerCase()))), [allBookings, searchTerm, salons, services, staff]);

  // Add New Booking handler
  const handleOpenNewModal = () => {
    setNewBooking({
      userId: '', userName: '', salon: '', service: '', staff: '', date: '', time: '', status: 'Confirmed', notes: ''
    });
    setIsNewModalOpen(true);
  };
  const handleCloseNewModal = () => setIsNewModalOpen(false);
  const handleNewBookingChange = (e) => {
    const { name, value } = e.target;
    setNewBooking(prev => ({ ...prev, [name]: value }));
    // If userId changes, update userName
    if (name === 'userId') {
      const user = mockUsers.find(u => String(u.id) === value);
      setNewBooking(prev => ({ ...prev, userName: user ? user.name || user.username : '' }));
    }
  };
  const handleCreateBooking = async (e) => {
    e.preventDefault();
    try {
      const bookingData = {
        userId: mockUsers.find(u => String(u.id) === newBooking.userId)?.username || '',
        userName: newBooking.userName,
        salon: newBooking.salon,
        service: newBooking.service,
        staff: newBooking.staff,
        date: newBooking.date,
        time: newBooking.time,
        status: newBooking.status,
        notes: newBooking.notes,
      };
      const res = await fetch('/api/bookings/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify(bookingData),
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.detail || 'Failed to create booking');
      }
      showNotification('Booking created successfully!', 'success');
      setIsNewModalOpen(false);
      fetchBookings();
    } catch (err) {
      showNotification(`Booking creation failed: ${err.message}`, 'danger');
    }
  };

  if (loading) {
    return <div className="container mt-5 text-light text-center">Loading bookings...</div>;
  }

  if (error) {
    return (
      <div className="container mt-5 text-danger text-center">
        Error:
        {error}
      </div>
    );
  }

  return (
    <div className="container mt-5">
      <div className="glam-card p-4">
        <h2 className="card-title text-center mb-4 text-light">Manage All Bookings</h2>
        <div className="mb-3 d-flex justify-content-between align-items-center">
          <input
            type="text"
            className="form-control bg-transparent text-light border-light w-75"
            placeholder="Search bookings..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="btn btn-success glam-btn ms-3" onClick={handleOpenNewModal}>
            + New Booking
          </button>
        </div>
        {filteredBookings.length > 0 ? (
          <div className="table-responsive-sm">
            <table className="table table-striped table-bordered text-light">
              <thead>
                <tr>
                  <th className="text-light">Booking ID</th>
                  <th className="text-light">User</th>
                  <th className="text-light">Salon</th>
                  <th className="text-light">Service</th>
                  <th className="text-light">Staff</th>
                  <th className="text-light">Date</th>
                  <th className="text-light">Time</th>
                  <th className="text-light">Status</th>
                  <th className="text-light">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredBookings.map((booking) => (
                  <tr key={booking.id}>
                    <td>{booking.id}</td>
                    <td>{booking.userName}</td>
                    <td>{salons[booking.salon]?.name || 'N/A'}</td>
                    <td>{services[booking.service]?.name || 'N/A'}</td>
                    <td>{staff[booking.staff]?.name || 'N/A'}</td>
                    <td>{booking.date}</td>
                    <td>{booking.time}</td>
                    <td>{booking.status}</td>
                    <td>
                      <button
                        className="btn btn-sm btn-info me-2 glam-btn"
                        onClick={() => handleViewDetails(booking)}
                      >
                        View
                      </button>
                      <button
                        className="btn btn-sm btn-warning me-2 glam-btn"
                        onClick={() => handleEdit(booking)}
                      >
                        Edit
                      </button>
                      {booking.status !== 'Cancelled' && (
                      <button
                        className="btn btn-sm btn-danger glam-btn"
                        onClick={() => handleCancel(booking)}
                      >
                        Cancel
                      </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-center text-light">No bookings found.</p>
        )}
      </div>

      {selectedBooking && (
        <Modal
          isOpen={isViewModalOpen}
          onClose={handleCloseViewModal}
          title="Booking Details"
        >
          <p>
            <strong>User:</strong> 
            {' '}
            {selectedBooking.userName}
          </p>
          <p>
            <strong>Salon:</strong> 
            {' '}
            {salons[selectedBooking.salon]?.name || 'N/A'}
          </p>
          <p>
            <strong>Service:</strong> 
            {' '}
            {services[selectedBooking.service]?.name || 'N/A'}
          </p>
          <p>
            <strong>Staff:</strong> 
            {' '}
            {staff[selectedBooking.staff]?.name || 'N/A'}
          </p>
          <p>
            <strong>Date:</strong> 
            {' '}
            {selectedBooking.date}
          </p>
          <p>
            <strong>Time:</strong> 
            {' '}
            {selectedBooking.time}
          </p>
          <p>
            <strong>Status:</strong> 
            {' '}
            {selectedBooking.status}
          </p>
          <p>
            <strong>Notes:</strong> 
            {' '}
            {selectedBooking.notes || 'N/A'}
          </p>
        </Modal>
      )}

      {selectedBooking && (
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Edit Booking"
        >
          <form onSubmit={handleSaveEdit}>
            <div className="mb-3">
              <label htmlFor="editStatus" className="form-label">Status</label>
              <select
                id="editStatus"
                name="status"
                className="form-select"
                defaultValue={selectedBooking.status}
                required
              >
                <option value="Confirmed">Confirmed</option>
                <option value="Pending">Pending</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
            <div className="mb-3">
              <label htmlFor="editDate" className="form-label">Date</label>
              <input
                type="date"
                id="editDate"
                name="date"
                className="form-control"
                defaultValue={selectedBooking.date}
                required
              />
            </div>
            <div className="mb-3">
              <label htmlFor="editTime" className="form-label">Time</label>
              <input
                type="time"
                id="editTime"
                name="time"
                className="form-control"
                defaultValue={selectedBooking.time}
                required
              />
            </div>
            <div className="mb-3">
              <label htmlFor="editNotes" className="form-label">Notes</label>
              <textarea
                id="editNotes"
                name="notes"
                className="form-control"
                defaultValue={selectedBooking.notes || ''}
                rows="3"
              />
            </div>
            <button type="submit" className="btn btn-primary glam-btn">Save Changes</button>
          </form>
        </Modal>
      )}

      {bookingToCancel && (
        <Modal
          isOpen={isCancelConfirmModalOpen}
          onClose={() => setIsCancelConfirmModalOpen(false)}
          title="Confirm Cancellation"
        >
          <p>
            Are you sure you want to cancel the booking for
            <strong>{bookingToCancel.userName}</strong>
            {' '}
            on
            <strong>{bookingToCancel.date}</strong>
            {' '}
            at
            <strong>{bookingToCancel.time}</strong>
            ?
          </p>
          <div className="d-flex justify-content-end mt-4">
            <button className="btn btn-secondary me-2" onClick={() => setIsCancelConfirmModalOpen(false)}>No, Keep Booking</button>
            <button className="btn btn-danger glam-btn" onClick={confirmCancel}>Yes, Cancel Booking</button>
          </div>
        </Modal>
      )}

      {/* New Booking Modal */}
      <Modal
        isOpen={isNewModalOpen}
        onClose={handleCloseNewModal}
        title="Create New Booking"
      >
        <form onSubmit={handleCreateBooking}>
          <div className="mb-3">
            <label className="form-label">User</label>
            <select
              name="userId"
              value={newBooking.userId}
              onChange={handleNewBookingChange}
              className="form-select"
              required
            >
              <option value="" disabled>Select user</option>
              {mockUsers.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name || user.username}
                  {' '}
                  (
                  {user.username}
                  )
                </option>
              ))}
            </select>
          </div>
          <div className="mb-3">
            <label className="form-label">Salon</label>
            <select
              name="salon"
              value={newBooking.salon}
              onChange={handleNewBookingChange}
              className="form-select"
              required
            >
              <option value="" disabled>Select salon</option>
              {Object.values(salons).map(salon => (
                <option key={salon.id} value={salon.id}>{salon.name}</option>
              ))}
            </select>
          </div>
          <div className="mb-3">
            <label className="form-label">Service</label>
            <select
              name="service"
              value={newBooking.service}
              onChange={handleNewBookingChange}
              className="form-select"
              required
              disabled={!newBooking.salon}
            >
              <option value="" disabled>Select service</option>
              {Object.values(services)
                .filter(service => String(service.salon) === String(newBooking.salon))
                .map(service => (
                  <option key={service.id} value={service.id}>{service.name}</option>
                ))}
            </select>
          </div>
          <div className="mb-3">
            <label className="form-label">Staff</label>
            <select
              name="staff"
              value={newBooking.staff}
              onChange={handleNewBookingChange}
              className="form-select"
              required
              disabled={!newBooking.salon}
            >
              <option value="" disabled>Select staff</option>
              {Object.values(staff)
                .filter(member => String(member.salon) === String(newBooking.salon))
                .map(member => (
                  <option key={member.id} value={member.id}>
                    {member.name}
                    {' '}
                    (
                    {member.role}
                    )
                  </option>
                ))}
            </select>
          </div>
          <div className="mb-3">
            <label className="form-label">Date</label>
            <input
              type="date"
              name="date"
              value={newBooking.date}
              onChange={handleNewBookingChange}
              className="form-control"
              required
            />
          </div>
          <div className="mb-3">
            <label className="form-label">Time</label>
            <input
              type="time"
              name="time"
              value={newBooking.time}
              onChange={handleNewBookingChange}
              className="form-control"
              required
            />
          </div>
          <div className="mb-3">
            <label className="form-label">Status</label>
            <select
              name="status"
              value={newBooking.status}
              onChange={handleNewBookingChange}
              className="form-select"
              required
            >
              <option value="Confirmed">Confirmed</option>
              <option value="Pending">Pending</option>
              <option value="Cancelled">Cancelled</option>
              <option value="Completed">Completed</option>
            </select>
          </div>
          <div className="mb-3">
            <label className="form-label">Notes</label>
            <textarea
              name="notes"
              value={newBooking.notes}
              onChange={handleNewBookingChange}
              className="form-control"
              rows="2"
            />
          </div>
          <button type="submit" className="btn btn-primary glam-btn w-100">Create Booking</button>
        </form>
      </Modal>
    </div>
  );
};

export default ManageBookings;
