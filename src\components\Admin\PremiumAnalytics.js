import React, { useState, useEffect } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import './AdminDashboard.css';

const PremiumAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');

  // Mock premium analytics data
  const mockAnalytics = {
    totalRevenue: 15750,
    activeSubscriptions: 12,
    totalClicks: 2847,
    totalImpressions: 45230,
    averageCTR: 6.3,
    conversionRate: 12.8,
    topPerformers: [
      { name: "Elite Beauty Lounge", clicks: 456, revenue: 3200, tier: "elite" },
      { name: "Glamour Studio", clicks: 389, revenue: 2800, tier: "spotlight" },
      { name: "Royal Hair Salon", clicks: 334, revenue: 2100, tier: "featured" }
    ],
    tierDistribution: {
      elite: 3,
      spotlight: 4,
      featured: 5
    },
    revenueByTier: {
      elite: 8500,
      spotlight: 4750,
      featured: 2500
    },
    clickTrends: [
      { date: '2024-01-01', clicks: 145 },
      { date: '2024-01-02', clicks: 167 },
      { date: '2024-01-03', clicks: 189 },
      { date: '2024-01-04', clicks: 203 },
      { date: '2024-01-05', clicks: 178 },
      { date: '2024-01-06', clicks: 234 },
      { date: '2024-01-07', clicks: 267 }
    ]
  };

  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        // Replace with actual API call
        // const response = await fetch(`/api/premium-analytics/?time_range=${timeRange}`);
        // const data = await response.json();
        
        // Using mock data for now
        setTimeout(() => {
          setAnalytics(mockAnalytics);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching premium analytics:', error);
        setAnalytics(mockAnalytics);
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [timeRange]);

  const clickTrendData = {
    labels: analytics?.clickTrends.map(item => 
      new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    ) || [],
    datasets: [
      {
        label: 'Ticker Clicks',
        data: analytics?.clickTrends.map(item => item.clicks) || [],
        borderColor: '#FFD700',
        backgroundColor: 'rgba(255, 215, 0, 0.1)',
        fill: true,
        tension: 0.4,
      }
    ]
  };

  const tierRevenueData = {
    labels: ['Elite', 'Spotlight', 'Featured'],
    datasets: [
      {
        data: [
          analytics?.revenueByTier.elite || 0,
          analytics?.revenueByTier.spotlight || 0,
          analytics?.revenueByTier.featured || 0
        ],
        backgroundColor: [
          'rgba(255, 215, 0, 0.8)',
          'rgba(255, 107, 157, 0.8)',
          'rgba(52, 152, 219, 0.8)'
        ],
        borderColor: [
          '#FFD700',
          '#FF6B9D',
          '#3498DB'
        ],
        borderWidth: 2
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#ffffff'
        }
      }
    },
    scales: {
      x: {
        ticks: { color: '#ffffff' },
        grid: { color: 'rgba(255, 255, 255, 0.1)' }
      },
      y: {
        ticks: { color: '#ffffff' },
        grid: { color: 'rgba(255, 255, 255, 0.1)' }
      }
    }
  };

  if (loading) {
    return (
      <div className="analytics-loading">
        <div className="loading-spinner"></div>
        <p>Loading premium analytics...</p>
      </div>
    );
  }

  return (
    <div className="premium-analytics-dashboard">
      <div className="analytics-header">
        <h2>💎 Premium Salon Analytics</h2>
        <div className="time-range-selector">
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className="form-select"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="metrics-grid">
        <div className="glam-card metric-card premium-metric">
          <div className="metric-icon">💰</div>
          <div className="metric-content">
            <h3>Premium Revenue</h3>
            <p className="metric-value">${analytics?.totalRevenue?.toLocaleString() || '0'}</p>
            <span className="metric-change positive">+18.5%</span>
          </div>
        </div>

        <div className="glam-card metric-card premium-metric">
          <div className="metric-icon">🏪</div>
          <div className="metric-content">
            <h3>Active Subscriptions</h3>
            <p className="metric-value">{analytics?.activeSubscriptions || '0'}</p>
            <span className="metric-change positive">+3 this month</span>
          </div>
        </div>

        <div className="glam-card metric-card premium-metric">
          <div className="metric-icon">👆</div>
          <div className="metric-content">
            <h3>Total Clicks</h3>
            <p className="metric-value">{analytics?.totalClicks?.toLocaleString() || '0'}</p>
            <span className="metric-change positive">+{analytics?.averageCTR || 0}% CTR</span>
          </div>
        </div>

        <div className="glam-card metric-card premium-metric">
          <div className="metric-icon">📊</div>
          <div className="metric-content">
            <h3>Conversion Rate</h3>
            <p className="metric-value">{analytics?.conversionRate || 0}%</p>
            <span className="metric-change positive">+2.3% vs last month</span>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="charts-grid">
        <div className="chart-container">
          <h3>Click Trends</h3>
          <div className="chart-wrapper">
            <Line data={clickTrendData} options={chartOptions} />
          </div>
        </div>

        <div className="chart-container">
          <h3>Revenue by Tier</h3>
          <div className="chart-wrapper">
            <Doughnut data={tierRevenueData} options={chartOptions} />
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="top-performers">
        <h3>🏆 Top Performing Salons</h3>
        <div className="performers-list">
          {analytics?.topPerformers?.map((salon, index) => (
            <div key={salon.name} className="performer-item glam-card">
              <div className="performer-rank">#{index + 1}</div>
              <div className="performer-info">
                <h4>{salon.name}</h4>
                <span className={`tier-badge ${salon.tier}`}>
                  {salon.tier === 'elite' ? '💎' : salon.tier === 'spotlight' ? '⭐' : '🏆'} 
                  {salon.tier.charAt(0).toUpperCase() + salon.tier.slice(1)}
                </span>
              </div>
              <div className="performer-stats">
                <div className="stat">
                  <span className="stat-value">{salon.clicks}</span>
                  <span className="stat-label">Clicks</span>
                </div>
                <div className="stat">
                  <span className="stat-value">${salon.revenue}</span>
                  <span className="stat-label">Revenue</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PremiumAnalytics;
