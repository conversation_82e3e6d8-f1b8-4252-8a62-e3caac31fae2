import React, { useState, useEffect } from 'react';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [form, setForm] = useState({ username: '', email: '', password: '', first_name: '', last_name: '', role: 'Customer' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch users from backend
  const fetchUsers = async () => {
    setLoading(true);
    setError('');
    try {
      const res = await fetch('/api/users/');
      if (!res.ok) throw new Error('Failed to fetch users');
      const data = await res.json();
      setUsers(data);
    } catch (err) {
      setError(err.message);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleAddUser = async (e) => {
    e.preventDefault();
    setError('');
    if (!form.username || !form.email || !form.password) {
      setError('Username, email, and password are required.');
      return;
    }
    try {
      const res = await fetch('/api/users/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: form.username,
          email: form.email,
          password: form.password,
          first_name: form.first_name,
          last_name: form.last_name,
          is_staff: form.role === 'Super Admin' || form.role === 'Vendor',
          is_superuser: form.role === 'Super Admin',
          is_active: true,
        }),
      });
      if (!res.ok) {
        const errData = await res.json();
        setError(JSON.stringify(errData));
        return;
      }
      setForm({ username: '', email: '', password: '', first_name: '', last_name: '', role: 'Customer' });
      fetchUsers();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleDelete = async (id) => {
    setError('');
    try {
      const res = await fetch(`/api/users/${id}/`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete user');
      fetchUsers();
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div className="glam-card p-4 mt-4">
      <h3 className="mb-4 text-light">User Management</h3>
      {error && <div className="alert alert-danger">{error}</div>}
      <form className="mb-4" onSubmit={handleAddUser}>
        <div className="row g-2 align-items-end">
          <div className="col-md-2">
            <input
              type="text"
              name="username"
              value={form.username}
              onChange={handleChange}
              className="form-control bg-transparent text-light border-light"
              placeholder="Username"
              required
            />
          </div>
          <div className="col-md-2">
            <input
              type="email"
              name="email"
              value={form.email}
              onChange={handleChange}
              className="form-control bg-transparent text-light border-light"
              placeholder="Email"
              required
            />
          </div>
          <div className="col-md-2">
            <input
              type="password"
              name="password"
              value={form.password}
              onChange={handleChange}
              className="form-control bg-transparent text-light border-light"
              placeholder="Password"
              required
            />
          </div>
          <div className="col-md-2">
            <input
              type="text"
              name="first_name"
              value={form.first_name}
              onChange={handleChange}
              className="form-control bg-transparent text-light border-light"
              placeholder="First Name"
            />
          </div>
          <div className="col-md-2">
            <input
              type="text"
              name="last_name"
              value={form.last_name}
              onChange={handleChange}
              className="form-control bg-transparent text-light border-light"
              placeholder="Last Name"
            />
          </div>
          <div className="col-md-1">
            <select
              name="role"
              value={form.role}
              onChange={handleChange}
              className="form-select bg-transparent text-light border-light"
            >
              <option value="Super Admin">Super Admin</option>
              <option value="Vendor">Vendor</option>
              <option value="Customer">Customer</option>
            </select>
          </div>
          <div className="col-md-1">
            <button type="submit" className="btn btn-success glam-btn w-100">Add</button>
          </div>
        </div>
      </form>
      {loading ? (
        <div className="text-light">Loading users...</div>
      ) : (
        <div className="table-responsive-sm">
          <table className="table table-striped table-bordered text-light">
            <thead>
              <tr>
                <th>Username</th>
                <th>Email</th>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Role</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user => (
                <tr key={user.id}>
                  <td>{user.username}</td>
                  <td>{user.email}</td>
                  <td>{user.first_name}</td>
                  <td>{user.last_name}</td>
                  <td>{user.is_superuser ? 'Super Admin' : user.is_staff ? 'Vendor' : 'Customer'}</td>
                  <td>
                    <button className="btn btn-danger btn-sm glam-btn" onClick={() => handleDelete(user.id)}>
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default UserManagement; 
