/* Luxury Gen Z Auth Forms - Same Vibes as Modal */

/* Auth Container */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

/* Floating Background Elements */
.auth-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes backgroundFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

/* Luxury Form Card */
.luxury-auth-card {
  background: linear-gradient(145deg, 
    rgba(20, 20, 30, 0.95) 0%,
    rgba(30, 25, 40, 0.95) 30%,
    rgba(40, 20, 50, 0.95) 70%,
    rgba(25, 15, 35, 0.95) 100%);
  border-radius: 24px;
  padding: 3rem 2.5rem;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 15px 35px rgba(255, 20, 147, 0.2),
    0 5px 15px rgba(138, 43, 226, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(180%);
  border: 2px solid;
  border-image: linear-gradient(145deg, 
    rgba(255, 215, 0, 0.6) 0%,
    rgba(255, 20, 147, 0.4) 50%,
    rgba(138, 43, 226, 0.6) 100%) 1;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 420px;
}

/* Glow Effect */
.luxury-auth-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(145deg, 
    rgba(255, 215, 0, 0.3) 0%,
    rgba(255, 20, 147, 0.2) 50%,
    rgba(138, 43, 226, 0.3) 100%);
  border-radius: 26px;
  filter: blur(8px);
  opacity: 0.7;
  z-index: -1;
}

/* Floating Sparkles */
.auth-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: 24px;
}

.auth-sparkle {
  position: absolute;
  font-size: 0.8rem;
  opacity: 0.6;
  animation: sparkleFloat 4s ease-in-out infinite;
}

.auth-sparkle:nth-child(1) {
  top: 15%;
  left: 85%;
  animation-delay: 0s;
}

.auth-sparkle:nth-child(2) {
  top: 70%;
  left: 10%;
  animation-delay: 1.5s;
}

.auth-sparkle:nth-child(3) {
  top: 40%;
  left: 90%;
  animation-delay: 3s;
}

@keyframes sparkleFloat {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% { 
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

/* Form Header */
.auth-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.auth-icon-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.auth-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(145deg, 
    rgba(255, 215, 0, 0.2) 0%,
    rgba(255, 20, 147, 0.15) 100%);
  border-radius: 50%;
  border: 2px solid rgba(255, 215, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  box-shadow: 
    0 4px 12px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.auth-title {
  font-size: 1.8rem;
  font-weight: 900;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, 
    #ffffff 0%,
    rgba(255, 215, 0, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.auth-subtitle {
  color: rgba(255, 215, 0, 0.8);
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Divider Line */
.auth-divider {
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%,
    rgba(255, 215, 0, 0.6) 50%,
    transparent 100%);
  margin: 1.5rem 0;
  position: relative;
}

.auth-divider::after {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent 0%,
    rgba(255, 215, 0, 0.8) 50%,
    transparent 100%);
}

/* Form Groups */
.luxury-form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.luxury-form-label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Luxury Form Controls */
.luxury-form-control {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
}

.luxury-form-control:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 
    0 0 0 3px rgba(255, 215, 0, 0.2),
    0 4px 12px rgba(255, 215, 0, 0.1);
  transform: translateY(-1px);
}

.luxury-form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

/* Select Dropdown */
.luxury-form-select {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.luxury-form-select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 
    0 0 0 3px rgba(255, 215, 0, 0.2),
    0 4px 12px rgba(255, 215, 0, 0.1);
}

.luxury-form-select option {
  background: rgba(20, 20, 30, 0.95);
  color: white;
  padding: 0.5rem;
}

/* Luxury Submit Button */
.luxury-submit-btn {
  width: 100%;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, 
    var(--genz-accent) 0%,
    #ffb347 100%);
  border: none;
  border-radius: 20px;
  color: var(--genz-dark);
  font-size: 1.1rem;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
  cursor: pointer;
  margin-top: 1rem;
}

.luxury-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.luxury-submit-btn:hover::before {
  left: 100%;
}

.luxury-submit-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 12px 30px rgba(255, 215, 0, 0.5);
}

.luxury-submit-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Auth Link */
.auth-link-section {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-link-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.auth-link {
  color: rgba(255, 215, 0, 0.9);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(255, 215, 0, 0.8) 0%,
    rgba(255, 20, 147, 0.6) 100%);
  transition: width 0.3s ease;
}

.auth-link:hover {
  color: rgba(255, 215, 0, 1);
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

.auth-link:hover::after {
  width: 100%;
}

/* Error Alert */
.luxury-alert {
  background: rgba(220, 53, 69, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: #ff6b6b;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* Success Alert */
.luxury-alert.success {
  background: rgba(40, 167, 69, 0.15);
  border-color: rgba(40, 167, 69, 0.3);
  color: #51cf66;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 1rem 0.5rem;
  }

  .luxury-auth-card {
    padding: 2rem 1.5rem;
    max-width: 100%;
    margin: 0 0.5rem;
  }

  .auth-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .auth-title {
    font-size: 1.5rem;
  }

  .auth-subtitle {
    font-size: 0.8rem;
  }

  .luxury-form-control,
  .luxury-form-select {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }

  .luxury-submit-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 0.5rem 0.25rem;
  }

  .luxury-auth-card {
    padding: 1.5rem 1rem;
    border-radius: 20px;
  }

  .auth-icon {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .auth-title {
    font-size: 1.3rem;
  }

  .auth-subtitle {
    font-size: 0.75rem;
  }

  .luxury-form-group {
    margin-bottom: 1.25rem;
  }

  .luxury-form-control,
  .luxury-form-select {
    padding: 0.75rem 0.875rem;
    font-size: 0.9rem;
    border-radius: 14px;
  }

  .luxury-submit-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
    border-radius: 16px;
  }

  .auth-sparkle {
    font-size: 0.7rem;
  }
}

@media (max-width: 360px) {
  .luxury-auth-card {
    padding: 1.25rem 0.75rem;
  }

  .auth-title {
    font-size: 1.2rem;
  }

  .luxury-form-control,
  .luxury-form-select {
    padding: 0.625rem 0.75rem;
    font-size: 0.85rem;
  }

  .luxury-submit-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}
