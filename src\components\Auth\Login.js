import React, { useState } from 'react';
import { Link, useNavigate, Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import './AuthForms.css';
import './LoginProfile.css';

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [error, setError] = useState('');
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Placeholder: In real app, replace with logic to check if user has completed onboarding
  const needsOnboarding = () => {
    // If you have user info in context, use that instead
    const onboarding = localStorage.getItem('onboardingComplete');
    return onboarding !== 'true';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(''); // Clear previous errors
    const result = await login(formData.username, formData.password);
    if (result.success) {
      navigate('/'); // Always go to landing page after login
    } else {
      setError(result.message);
    }
  };

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="login-profile-container">
      <div className="profile-container">
        {/* Floating Sparkles */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">✨</span>
          <span className="auth-sparkle">💫</span>
          <span className="auth-sparkle">⭐</span>
        </div>

        {/* Header - Profile Pattern */}
        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">🔐</div>
          </div>
          <h1 className="profile-title">Welcome Back</h1>
          <p className="profile-subtitle">Sign in to your account</p>
        </div>

        {/* Content Area - Profile Pattern */}
        <div className="profile-content">

          {/* Login Form Section */}
          <div className="profile-section">
            <h3 className="section-title">🔐 Account Login</h3>

            <form onSubmit={handleSubmit}>
              {error && <div className="error-alert">{error}</div>}

              <div className="form-group">
                <label htmlFor="username" className="form-label">Username</label>
                <input
                  type="text"
                  className="form-input"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  placeholder="Enter your username"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">Password</label>
                <input
                  type="password"
                  className="form-input"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Enter your password"
                  required
                />
              </div>

              <button type="submit" className="btn-primary">
                Sign In
              </button>
            </form>
          </div>

          {/* Account Creation Section */}
          <div className="profile-section">
            <h3 className="section-title">👤 New to SalonGenz?</h3>
            <div className="auth-link-section">
              <p className="auth-link-text">Don't have an account?</p>
              <Link to="/signup" className="auth-link">Create Account</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
