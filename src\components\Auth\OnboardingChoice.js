import React from 'react';
import { useNavigate } from 'react-router-dom';

const OnboardingChoice = () => {
  const navigate = useNavigate();
  return (
    <div className="container mt-5">
      <div className="glam-card p-4 mx-auto text-center" style={{ maxWidth: '400px' }}>
        <h2 className="card-title mb-4 text-light">Welcome! Who are you?</h2>
        <button
          className="btn btn-primary w-100 mb-3 glam-btn"
          onClick={() => navigate('/register-customer')}
        >
          I am a Customer
        </button>
        <button
          className="btn btn-secondary w-100 glam-btn"
          onClick={() => navigate('/register-vendor')}
        >
          I am a Salon Vendor
        </button>
      </div>
    </div>
  );
};

export default OnboardingChoice;
