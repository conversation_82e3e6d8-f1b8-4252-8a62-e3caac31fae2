import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const PrivateRoute = ({ children, adminOnly = false }) => {
  const { user } = useAuth();

  if (!user) {
    // User not authenticated, redirect to login page
    return <Navigate to="/login" replace />;
  }

  if (adminOnly && !user.isAdmin) {
    // User is not an admin, redirect to home or an unauthorized page
    return <Navigate to="/" replace />;
  }

  return children;
};

export default PrivateRoute;
