import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const RegisterCustomer = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    email: '',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  // Placeholder: Replace this with a real API or context check for registration status
  const isRegistered = localStorage.getItem('isRegistered') === 'true';
  const isAuthenticated = localStorage.getItem('token') !== null; // Example, adjust if you use context

  React.useEffect(() => {
    if (isAuthenticated && isRegistered) {
      navigate('/account');
    }
  }, [isAuthenticated, isRegistered, navigate]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess(false);
    // TODO: Integrate with backend customer profile API
    setSuccess(true);
    setTimeout(() => navigate('/'), 1500);
  };

  return (
    <div className="container mt-5">
      <div className="glam-card p-4 mx-auto" style={{ maxWidth: '500px', background: '#6C2EB5', color: '#fff' }}>
        <h2 className="card-title text-center mb-4 text-light">Customer Registration</h2>
        <form onSubmit={handleSubmit}>
          {error && <div className="alert alert-danger">{error}</div>}
          {success && <div className="alert alert-success">Profile created! Redirecting...</div>}
          <div className="mb-3">
            <label className="form-label text-light">Full Name</label>
            <input type="text" className="form-control bg-transparent text-light border-light" name="fullName" value={formData.fullName} onChange={handleChange} required />
          </div>
          <div className="mb-3">
            <label className="form-label text-light">Phone</label>
            <input type="tel" className="form-control bg-transparent text-light border-light" name="phone" value={formData.phone} onChange={handleChange} required />
          </div>
          <div className="mb-3">
            <label className="form-label text-light">Email</label>
            <input type="email" className="form-control bg-transparent text-light border-light" name="email" value={formData.email} onChange={handleChange} required />
          </div>
          <button type="submit" className="btn btn-primary w-100 glam-btn">Register as Customer</button>
        </form>
      </div>
    </div>
  );
};

export default RegisterCustomer;
