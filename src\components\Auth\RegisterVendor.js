import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import './VendorRegistration.css';

const RegisterVendor = () => {
  const [formData, setFormData] = useState({
    salonName: '',
    town: '',
    address: '',
    phone: '',
    email: '',
    description: '',
    imageUrl: '',
  });

  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess(false);

    // Validate basic salon info
    if (!formData.salonName || !formData.email || !formData.phone) {
      setError('Please fill in all required fields');
      return;
    }

    // Navigate to unified subscription page with vendor data
    navigate('/vendor/subscription', {
      state: { vendorData: formData }
    });
  };

  return (
    <div className="vendor-registration">
      <div className="registration-container">
        <form onSubmit={handleSubmit}>
          {error && <div className="error-message">{error}</div>}
          <div className="registration-header">
            <h1 className="registration-title">Salon Vendor Registration</h1>
            <p className="registration-subtitle">Join our premium platform</p>
          </div>
          <div className="registration-form">
                <div className="form-group">
                  <label className="form-label">Salon Name</label>
                  <input
                    type="text"
                    className="form-input"
                    name="salonName"
                    value={formData.salonName}
                    onChange={handleChange}
                    required
                    placeholder="Enter your salon name"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Town</label>
                  <input
                    type="text"
                    className="form-input"
                    name="town"
                    value={formData.town}
                    onChange={handleChange}
                    required
                    placeholder="e.g., Nairobi, Mombasa"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Address</label>
                  <input
                    type="text"
                    className="form-input"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    required
                    placeholder="Street address"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Phone</label>
                  <input
                    type="tel"
                    className="form-input"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    placeholder="+254 700 000 000"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-input"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Description</label>
                  <textarea
                    className="form-input form-textarea"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    required
                    placeholder="Describe your salon services and specialties"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Image URL (Optional)</label>
                  <input
                    type="text"
                    className="form-input"
                    name="imageUrl"
                    value={formData.imageUrl}
                    onChange={handleChange}
                    placeholder="https://example.com/salon-image.jpg"
                  />
                </div>
                <button type="submit" className="continue-button">
                  Continue to Subscription Plans
                </button>
              </div>
        </form>
      </div>
    </div>
  );
};

export default RegisterVendor;
