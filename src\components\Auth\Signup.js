import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import './AuthForms.css';
import './SignupProfile.css';

const Signup = () => {
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    password: '',
    confirmPassword: '',
    role: 'customer',
    vendorPreference: '', // New field for vendor matching
  });
  const [error, setError] = useState('');
  const { signup, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Placeholder: Replace this with a real API or context check for registration status
  const isRegistered = localStorage.getItem('isRegistered') === 'true';

  useEffect(() => {
    if (isAuthenticated && isRegistered) {
      navigate('/account');
    }
  }, [isAuthenticated, isRegistered, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleRoleChange = (e) => {
    const selectedRole = e.target.value;
    setFormData({ ...formData, role: selectedRole });

    // Redirect vendors to dedicated vendor signup page
    if (selectedRole === 'vendor') {
      navigate('/register-vendor');
    }
  };

  // Vendor preference options for customer matching
  const vendorPreferences = [
    { value: '', label: 'No preference - I\'ll browse all vendors' },
    { value: 'nail_technician', label: 'Hook me up with a Nail Technician' },
    { value: 'beautician', label: 'Hook me up with a Beautician' },
    { value: 'hair_stylist', label: 'Hook me up with a Hair Stylist' },
    { value: 'makeup_artist', label: 'Hook me up with a Makeup Artist' },
    { value: 'barber', label: 'Hook me up with a Barber' },
    { value: 'spa_therapist', label: 'Hook me up with a Spa Therapist' },
    { value: 'lash_technician', label: 'Hook me up with a Lash Technician' },
    { value: 'brow_specialist', label: 'Hook me up with a Brow Specialist' }
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(''); // Clear previous errors

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    // Add role and vendor preference to signup logic
    const result = await signup(
      formData.username,
      formData.password,
      formData.name,
      formData.role,
      formData.salonName || '',
      formData.vendorPreference || ''
    );
    if (result.success) {
      navigate('/'); // Always go to landing page after signup
    } else {
      setError(result.message);
    }
  };

  return (
    <div className="signup-profile-container">
      <div className="profile-container">
        {/* Floating Sparkles */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">✨</span>
          <span className="auth-sparkle">💫</span>
          <span className="auth-sparkle">⭐</span>
        </div>

        {/* Header - Profile Pattern */}
        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">🌟</div>
          </div>
          <h1 className="profile-title">Join SalonG</h1>
          <p className="profile-subtitle">Create your account</p>
        </div>

        {/* Content Area - Profile Pattern */}
        <div className="profile-content">

          <form onSubmit={handleSubmit}>
            {error && <div className="error-alert">{error}</div>}

            {/* Account Type Section */}
            <div className="profile-section">
              <h3 className="section-title">👤 Account Type</h3>

              <div className="form-group">
                <label htmlFor="role" className="form-label">I am a</label>
                <select
                  id="role"
                  name="role"
                  className="form-select"
                  value={formData.role}
                  onChange={handleRoleChange}
                  required
                >
                  <option value="customer">Customer</option>
                  <option value="vendor">Salon Vendor</option>
                </select>
                {formData.role === 'vendor' && (
                  <div className="vendor-notice">
                    <div className="vendor-notice-content">
                      <span>🏪</span>
                      <span>
                        <strong>Vendors:</strong> You'll be redirected to our dedicated vendor registration with subscription plans!
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

          {/* Personal Information Section */}
          <div className="profile-section">
            <h3 className="section-title">📝 Personal Information</h3>

            <div className="form-group">
              <label htmlFor="name" className="form-label">Full Name</label>
              <input
                type="text"
                className="form-input"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Enter your full name"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="username" className="form-label">Username</label>
              <input
                type="text"
                className="form-input"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleChange}
                placeholder="Choose a username"
                required
              />
            </div>
            {formData.role === 'vendor' && (
              <div className="form-group">
                <label htmlFor="salonName" className="form-label">Salon Name</label>
                <input
                  type="text"
                  className="form-input"
                  id="salonName"
                  name="salonName"
                  value={formData.salonName || ''}
                  onChange={handleChange}
                  placeholder="Enter your salon name"
                  required={formData.role === 'vendor'}
                />
              </div>
            )}
          </div>

          {formData.role === 'customer' && (
            <div className="profile-section">
              <h3 className="section-title">🎯 Vendor Matching Preference</h3>
              <div className="form-group">
                <label htmlFor="vendorPreference" className="form-label">Preference Type</label>
                <select
                  className="form-select"
                  id="vendorPreference"
                  name="vendorPreference"
                  value={formData.vendorPreference}
                  onChange={handleChange}
                >
                  {vendorPreferences.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <small className="form-help-text">
                  We'll help you find the perfect vendor for your beauty needs!
                </small>
              </div>
            </div>
          )}

          {/* Security Section */}
          <div className="profile-section">
            <h3 className="section-title">🔒 Account Security</h3>
            <div className="form-group">
              <label htmlFor="password" className="form-label">Password</label>
              <input
                type="password"
                className="form-input"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Create a password"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
              <input
                type="password"
                className="form-input"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm your password"
                required
              />
            </div>

            <button type="submit" className="btn-primary">
              Create Account
            </button>
          </div>
          </form>

          {/* Login Link Section */}
          <div className="profile-section">
            <h3 className="section-title">🔑 Already a Member?</h3>
            <div className="auth-link-section">
              <p className="auth-link-text">Already have an account?</p>
              <Link to="/login" className="auth-link">Sign In</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;
