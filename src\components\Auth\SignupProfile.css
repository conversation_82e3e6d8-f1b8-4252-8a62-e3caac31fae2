/* Signup Page - Profile Design Pattern with Dark Luxury Theme */

/* Main Container - Profile Pattern with Dark Luxury Theme */
.signup-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Floating Background Effects */
.signup-profile-container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
  animation: backgroundFloat 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes backgroundFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.signup-profile-container .profile-container {
  max-width: 480px;
  width: 100%;
  margin: 0 auto;
  background: linear-gradient(145deg, 
    rgba(20, 20, 30, 0.95) 0%,
    rgba(30, 25, 40, 0.95) 30%,
    rgba(40, 20, 50, 0.95) 70%,
    rgba(25, 15, 35, 0.95) 100%);
  border-radius: 24px;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 15px 35px rgba(255, 20, 147, 0.2),
    0 5px 15px rgba(138, 43, 226, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(180%);
  border: 2px solid;
  border-image: linear-gradient(145deg, 
    rgba(255, 215, 0, 0.6) 0%,
    rgba(255, 20, 147, 0.4) 50%,
    rgba(138, 43, 226, 0.6) 100%) 1;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.signup-profile-container .profile-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 215, 0, 0.6), 
    rgba(255, 20, 147, 0.6), 
    transparent);
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Floating Sparkles */
.signup-profile-container .auth-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.signup-profile-container .auth-sparkle {
  position: absolute;
  font-size: 0.8rem;
  color: rgba(255, 215, 0, 0.6);
  animation: sparkleFloat 6s ease-in-out infinite;
}

.signup-profile-container .auth-sparkle:nth-child(1) {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.signup-profile-container .auth-sparkle:nth-child(2) {
  top: 25%;
  right: 15%;
  animation-delay: 2s;
}

.signup-profile-container .auth-sparkle:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes sparkleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
  50% { transform: translateY(-10px) rotate(180deg); opacity: 1; }
}

/* Header - Profile Pattern with Dark Theme */
.signup-profile-container .profile-header {
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.9) 0%,
    rgba(25, 25, 35, 0.8) 50%,
    rgba(30, 30, 40, 0.9) 100%);
  padding: 2.5rem 2rem 1.5rem 2rem;
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.signup-profile-container .auth-icon-wrapper {
  margin-bottom: 1rem;
}

.signup-profile-container .auth-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.2) 0%,
    rgba(255, 20, 147, 0.2) 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  margin: 0 auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.2);
}

.signup-profile-container .profile-title {
  font-size: 1.8rem;
  font-weight: 900;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, 
    #ffffff 0%,
    rgba(255, 215, 0, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.signup-profile-container .profile-subtitle {
  color: rgba(255, 215, 0, 0.8);
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Content Area - Profile Pattern */
.signup-profile-container .profile-content {
  padding: 0 2rem 2.5rem 2rem;
  position: relative;
  z-index: 2;
  max-height: 70vh;
  overflow-y: auto;
}

/* Custom Scrollbar */
.signup-profile-container .profile-content::-webkit-scrollbar {
  width: 6px;
}

.signup-profile-container .profile-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.signup-profile-container .profile-content::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.3);
  border-radius: 3px;
}

.signup-profile-container .profile-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.5);
}

/* Sections - Profile Pattern with Dark Theme */
.signup-profile-container .profile-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.signup-profile-container .section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 1.25rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Form Elements */
.signup-profile-container .form-group {
  margin-bottom: 1.25rem;
}

.signup-profile-container .form-label {
  display: block;
  font-weight: 600;
  color: rgba(255, 215, 0, 0.9);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.signup-profile-container .form-input,
.signup-profile-container .form-select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 0.9rem;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.signup-profile-container .form-input:focus,
.signup-profile-container .form-select:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 
    0 0 0 3px rgba(255, 215, 0, 0.1),
    0 8px 20px rgba(255, 215, 0, 0.2);
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.signup-profile-container .form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

.signup-profile-container .form-select option {
  background: rgba(20, 20, 30, 0.95);
  color: #ffffff;
}

/* Form Help Text */
.signup-profile-container .form-help-text {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

/* Vendor Notice */
.signup-profile-container .vendor-notice {
  margin-top: 0.75rem;
  padding: 0.875rem;
  background: rgba(0, 123, 255, 0.15);
  border: 1px solid rgba(0, 123, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.signup-profile-container .vendor-notice-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #66b3ff;
  font-size: 0.85rem;
}

/* Primary Button */
.signup-profile-container .btn-primary {
  width: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.9) 0%,
    rgba(255, 165, 0, 0.9) 100%);
  border: none;
  color: #1a1a1a;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 
    0 8px 20px rgba(255, 215, 0, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2);
  margin-top: 1rem;
}

.signup-profile-container .btn-primary:hover {
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 1) 0%,
    rgba(255, 165, 0, 1) 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 12px 25px rgba(255, 215, 0, 0.4),
    0 6px 15px rgba(0, 0, 0, 0.3);
}

.signup-profile-container .btn-primary:active {
  transform: translateY(0);
}

/* Error Alert */
.signup-profile-container .error-alert {
  background: rgba(220, 53, 69, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.25rem;
  color: #ff6b6b;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* Auth Link Section */
.signup-profile-container .auth-link-section {
  text-align: center;
}

.signup-profile-container .auth-link-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.signup-profile-container .auth-link {
  color: rgba(255, 215, 0, 0.9);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  position: relative;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.signup-profile-container .auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(255, 215, 0, 0.8) 0%,
    rgba(255, 20, 147, 0.6) 100%);
  transition: width 0.3s ease;
}

.signup-profile-container .auth-link:hover {
  color: rgba(255, 215, 0, 1);
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

.signup-profile-container .auth-link:hover::after {
  width: 100%;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .signup-profile-container {
    padding: 0.75rem 0.25rem;
  }

  .signup-profile-container .profile-container {
    border-radius: 20px;
    max-width: 100%;
    margin: 0 0.5rem;
  }

  .signup-profile-container .profile-header {
    padding: 2rem 1.5rem 1.25rem 1.5rem;
  }

  .signup-profile-container .profile-title {
    font-size: 1.5rem;
  }

  .signup-profile-container .profile-content {
    padding: 0 1.5rem 2rem 1.5rem;
    max-height: 65vh;
  }

  .signup-profile-container .auth-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .signup-profile-container .profile-section {
    padding: 1.25rem;
  }
}

@media (max-width: 480px) {
  .signup-profile-container .profile-header {
    padding: 1.5rem 1rem;
  }

  .signup-profile-container .profile-title {
    font-size: 1.3rem;
  }

  .signup-profile-container .profile-content {
    padding: 0 1rem 1.5rem 1rem;
    max-height: 60vh;
  }

  .signup-profile-container .profile-section {
    padding: 1rem;
  }

  .signup-profile-container .auth-icon {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .signup-profile-container .form-input,
  .signup-profile-container .form-select {
    padding: 0.75rem 0.875rem;
    font-size: 0.85rem;
  }

  .signup-profile-container .btn-primary {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }
}
