/* Mobile-First User Profile Styles - Namespaced and Clean */
.user-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: relative;
  z-index: 1;
}

.user-profile-container .profile-container {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  box-sizing: border-box;
}

.user-profile-container .profile-header {
  background: linear-gradient(135deg, #ff6b9d 0%, #a259ff 100%);
  padding: 1.5rem 1rem;
  text-align: center;
  color: white;
}

.user-profile-container .profile-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.user-profile-container .profile-subtitle {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.user-profile-container .profile-content {
  padding: 1.5rem 1rem;
}

.user-profile-container .profile-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  width: 100%;
  box-sizing: border-box;
}

.user-profile-container .section-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-profile-container .form-group {
  margin-bottom: 1rem;
}

.user-profile-container .form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.user-profile-container .form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  color: #111827;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
}

.user-profile-container .form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-profile-container .save-button {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  min-height: 48px;
  touch-action: manipulation;
  margin: 1rem 0;
  box-sizing: border-box;
}

.user-profile-container .save-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.user-profile-container .save-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.user-profile-container .action-buttons {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  margin-top: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.user-profile-container .action-button {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 44px;
  touch-action: manipulation;
  transition: all 0.2s ease;
  box-sizing: border-box;
  width: 100%;
  background: #ffffff;
  color: #374151;
}

.user-profile-container .action-button.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-color: #3b82f6;
}

.user-profile-container .action-button.secondary {
  background: #ffffff;
  color: #374151;
  border-color: #d1d5db;
}

.user-profile-container .action-button:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

.user-profile-container .action-button.primary:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  color: white;
}

.user-profile-container .action-button.secondary:hover {
  border-color: #9ca3af;
  color: #111827;
}

.user-profile-container .status-alert {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.user-profile-container .status-success {
  background: #ecfdf5;
  border: 1px solid #10b981;
  color: #065f46;
}

.user-profile-container .status-error {
  background: #fef2f2;
  border: 1px solid #ef4444;
  color: #dc2626;
}

.user-profile-container .loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #64748b;
}

/* Desktop enhancements */
@media (min-width: 768px) {
  .user-profile-container {
    padding: 2rem 1rem;
  }
  
  .user-profile-container .profile-container {
    max-width: 600px;
    border-radius: 24px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
  
  .user-profile-container .profile-header {
    padding: 2rem;
  }
  
  .user-profile-container .profile-content {
    padding: 2rem;
  }
  
  .user-profile-container .profile-title {
    font-size: 1.75rem;
  }
  
  .user-profile-container .profile-section {
    padding: 1.5rem;
    border-radius: 16px;
    border-width: 2px;
  }
  
  .user-profile-container .form-input {
    border-radius: 12px;
    border-width: 2px;
  }
  
  .user-profile-container .save-button,
  .user-profile-container .action-button {
    border-radius: 12px;
  }
  
  .user-profile-container .action-buttons {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .user-profile-container .profile-container {
 
    max-width: 700px;
  }
}
