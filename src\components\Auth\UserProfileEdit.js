import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useNotification } from '../../context/NotificationContext';
import './UserProfile.css';

const UserProfileEdit = () => {
  const { authFetch, user } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [profileData, setProfileData] = useState({
    email: '',
    first_name: '',
    last_name: '',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const fetchProfile = useCallback(async () => {
    if (!user) return;
    
    setLoading(true);
    setError('');
    try {
      const res = await authFetch('/api/user/profile/');
      if (!res.ok) {
        throw new Error(`Failed to fetch profile: ${res.status}`);
      }
      const data = await res.json();
      setProfileData({
        email: data.email || '',
        first_name: data.first_name || '',
        last_name: data.last_name || '',
      });
    } catch (err) {
      console.error('User profile fetch error:', err);
      setError('Failed to load profile. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [authFetch, user]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  const handleChange = (e) => {
    setProfileData(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (saving) return;
    
    setError('');
    setSuccess(false);
    setSaving(true);
    
    try {
      const res = await authFetch('/api/user/profile/', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData),
      });
      
      if (!res.ok) {
        throw new Error('Failed to update profile');
      }
      
      setSuccess(true);
      showNotification('Profile updated successfully!', 'success');
      
      // Fetch updated profile data
      await fetchProfile();
    } catch (err) {
      console.error('Profile update error:', err);
      setError('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="user-profile-container">
        <div className="loading-spinner">Loading profile...</div>
      </div>
    );
  }

  return (
    <div className="user-profile-container">
      <div className="profile-container">
        <div className="profile-header">
          <h1 className="profile-title">User Profile</h1>
          <p className="profile-subtitle">Manage your account information</p>
        </div>
        <div className="profile-content">
          <form onSubmit={handleSubmit}>
            {error && <div className="status-alert status-error">{error}</div>}
            {success && <div className="status-alert status-success">Profile updated successfully!</div>}

            <div className="profile-section">
              <h3 className="section-title">
                <span>👤</span>
                Account Information
              </h3>
              <div className="form-group">
                <label className="form-label">Email Address</label>
                <input
                  type="email"
                  className="form-input"
                  name="email"
                  value={profileData.email}
                  onChange={handleChange}
                  required
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="form-group">
                <label className="form-label">First Name</label>
                <input
                  type="text"
                  className="form-input"
                  name="first_name"
                  value={profileData.first_name}
                  onChange={handleChange}
                  placeholder="Your first name"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Last Name</label>
                <input
                  type="text"
                  className="form-input"
                  name="last_name"
                  value={profileData.last_name}
                  onChange={handleChange}
                  placeholder="Your last name"
                />
              </div>
            </div>

            <button type="submit" className="save-button" disabled={saving}>
              {saving ? 'Saving...' : 'Save Changes'}
            </button>

            <div className="action-buttons">
              <button
                type="button"
                className="action-button secondary"
                onClick={() => navigate(-1)}
              >
                <span>←</span>
                Back
              </button>
              <button
                type="button"
                className="action-button primary"
                onClick={() => navigate('/')}
              >
                <span>🏠</span>
                Dashboard
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserProfileEdit; 
