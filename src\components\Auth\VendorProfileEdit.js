import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useNotification } from '../../context/NotificationContext';
import TrialTimer from '../TrialTimer';
import './VendorProfile.css';

const VendorProfileEdit = () => {
  const { authFetch, user } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [accountData, setAccountData] = useState({
    email: '',
    first_name: '',
    last_name: '',
  });
  const [salonData, setSalonData] = useState({
    name: '',
    town: '',
    address: '',
    phone: '',
    email: '',
    description: '',
    imageUrl: '',
    id: '',
  });
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });
  const [services, setServices] = useState([]);
  const [staff, setStaff] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState('account');
  const [customerPage, setCustomerPage] = useState(1);
  const [customerList, setCustomerList] = useState([]);
  const [customerTotal, setCustomerTotal] = useState(0);
  const customersPerPage = 5;

  const fetchProfile = useCallback(async () => {
    if (!user) return;
    
    setLoading(true);
    setError('');
    
    try {
      // Get account info
        const accRes = await authFetch('/api/vendor/account/');
        if (accRes.ok) {
          const acc = await accRes.json();
          setAccountData({
            email: acc.email || '',
            first_name: acc.first_name || '',
            last_name: acc.last_name || '',
          });
      }

      // Get salon info
      const salonRes = await authFetch(`/api/salons/?vendor=${user.username}`);
        if (salonRes.ok) {
          const salons = await salonRes.json();
          if (salons.length > 0) {
          const salon = salons[0];
          setSalonData(salon);

          // Fetch services and staff in parallel
          const [servicesRes, staffRes] = await Promise.allSettled([
            authFetch(`/api/services/?salon=${salon.id}`),
            authFetch(`/api/staff/?salon=${salon.id}`)
          ]);

          if (servicesRes.status === 'fulfilled' && servicesRes.value.ok) {
            const servicesData = await servicesRes.value.json();
                setServices(servicesData);
              }

          if (staffRes.status === 'fulfilled' && staffRes.value.ok) {
            const staffData = await staffRes.value.json();
                setStaff(staffData);
          }
        }
      }
    } catch (err) {
      console.error('Vendor profile fetch error:', err);
      setError('Failed to load profile. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [authFetch, user]);

  const fetchCustomers = useCallback(async (page) => {
    if (!user) return;
    
    try {
      const res = await authFetch(`/api/vendor/customers/?page=${page}&page_size=${customersPerPage}`);
      if (res.ok) {
        const data = await res.json();
        setCustomerList(data.results || []);
        setCustomerTotal(data.count || 0);
      }
    } catch (err) {
      console.error('Failed to fetch customers:', err);
    }
  }, [authFetch, user, customersPerPage]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  useEffect(() => {
    if (activeTab === 'customers') {
      fetchCustomers(customerPage);
    }
  }, [activeTab, customerPage, fetchCustomers]);

  const handleAccountChange = (e) => {
    setAccountData(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSalonChange = (e) => {
    setSalonData(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handlePasswordChange = (e) => {
    setPasswordData(prev => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    if (saving) return;
    
    if (passwordData.new_password !== passwordData.confirm_password) {
      setError('New passwords do not match.');
      return;
    }

    setSaving(true);
    setError('');
    
    try {
      const res = await authFetch('/api/vendor/password/', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(passwordData),
      });

      if (!res.ok) {
        throw new Error('Failed to update password');
      }

      setSuccess(true);
      showNotification('Password updated successfully!', 'success');
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    } catch (err) {
      console.error('Password update error:', err);
      setError('Failed to update password. Please check your current password.');
    } finally {
      setSaving(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (saving) return;
    
    setSaving(true);
    setError('');
    setSuccess(false);

    try {
      // Update account info
      const accountRes = await authFetch('/api/vendor/account/', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(accountData),
      });

      if (!accountRes.ok) {
        throw new Error('Failed to update account information');
      }

      // Update salon info if salon exists
      if (salonData.id) {
        const salonRes = await authFetch(`/api/salons/${salonData.id}/`, {
        method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(salonData),
      });

        if (!salonRes.ok) {
          throw new Error('Failed to update salon information');
        }
      }

      setSuccess(true);
      showNotification('Profile updated successfully!', 'success');
      
      // Refresh profile data
      await fetchProfile();
    } catch (err) {
      console.error('Profile update error:', err);
      setError('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="vendor-profile-container">
      <div className="loading-spinner">Loading profile...</div>
    </div>
  );
  }

  return (
    <div className="vendor-profile-container">
      <TrialTimer />
      <div className="profile-container">
        <div className="profile-header">
          <h1 className="profile-title">Vendor Profile</h1>
          <p className="profile-subtitle">Manage your salon and account</p>
        </div>

        <div className="profile-tabs">
          <button
            className={`tab-button ${activeTab === 'account' ? 'active' : ''}`}
            onClick={() => setActiveTab('account')}
          >
            <span>👤</span>
            Account
          </button>
          <button
            className={`tab-button ${activeTab === 'salon' ? 'active' : ''}`}
            onClick={() => setActiveTab('salon')}
          >
            <span>🏪</span>
            Salon
          </button>
          <button
            className={`tab-button ${activeTab === 'password' ? 'active' : ''}`}
            onClick={() => setActiveTab('password')}
          >
            <span>🔒</span>
            Password
          </button>
          <button
            className={`tab-button ${activeTab === 'customers' ? 'active' : ''}`}
            onClick={() => setActiveTab('customers')}
          >
            <span>👥</span>
            Customers
          </button>
        </div>

        <div className="profile-content">
          {error && <div className="status-alert status-error">{error}</div>}
          {success && <div className="status-alert status-success">Profile updated successfully!</div>}

          {activeTab === 'account' && (
            <form onSubmit={handleSubmit}>
              <div className="profile-section">
                <h3 className="section-title">
                  <span>👤</span>
                  Account Information
                </h3>
                <div className="form-group">
                  <label className="form-label">Email Address</label>
                  <input
                    type="email"
                    className="form-input"
                    name="email"
                    value={accountData.email}
                    onChange={handleAccountChange}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">First Name</label>
                  <input
                    type="text"
                    className="form-input"
                    name="first_name"
                    value={accountData.first_name}
                    onChange={handleAccountChange}
                    placeholder="Your first name"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Last Name</label>
                  <input
                    type="text"
                    className="form-input"
                    name="last_name"
                    value={accountData.last_name}
                    onChange={handleAccountChange}
                    placeholder="Your last name"
                  />
                </div>
              </div>

              <button type="submit" className="save-button" disabled={saving}>
                {saving ? 'Saving...' : 'Save Account Changes'}
              </button>
            </form>
          )}

          {activeTab === 'salon' && (
            <form onSubmit={handleSubmit}>
              <div className="profile-sections">
              <div className="profile-section">
                <h3 className="section-title">
                  <span>🏪</span>
                  Salon Information
                </h3>
                <div className="form-group">
                  <label className="form-label">Salon Name</label>
                  <input
                    type="text"
                    className="form-input"
                    name="name"
                    value={salonData.name}
                    onChange={handleSalonChange}
                    placeholder="Salon name"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Town</label>
                  <input
                    type="text"
                    className="form-input"
                    name="town"
                    value={salonData.town}
                    onChange={handleSalonChange}
                    placeholder="Town"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Address</label>
                  <input
                    type="text"
                    className="form-input"
                    name="address"
                    value={salonData.address}
                    onChange={handleSalonChange}
                    placeholder="Full address"
                  />
                  </div>
                </div>

                <div className="profile-section">
                  <h3 className="section-title">
                    <span>📞</span>
                    Contact Information
                  </h3>
                <div className="form-group">
                    <label className="form-label">Phone Number</label>
                  <input
                    type="tel"
                    className="form-input"
                    name="phone"
                    value={salonData.phone}
                    onChange={handleSalonChange}
                    placeholder="Phone number"
                  />
                </div>
                <div className="form-group">
                    <label className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-input"
                    name="email"
                    value={salonData.email}
                    onChange={handleSalonChange}
                    placeholder="Salon email"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Description</label>
                  <textarea
                    className="form-input form-textarea"
                    name="description"
                    value={salonData.description}
                    onChange={handleSalonChange}
                    placeholder="Describe your salon"
                  />
                </div>
                </div>
              </div>

              <button type="submit" className="save-button" disabled={saving}>
                {saving ? 'Saving...' : 'Save Salon Changes'}
              </button>
            </form>
          )}

          {activeTab === 'password' && (
            <form onSubmit={handlePasswordSubmit}>
              <div className="profile-section">
                <h3 className="section-title">
                  <span>🔒</span>
                  Change Password
                </h3>
                <div className="form-group">
                  <label className="form-label">Current Password</label>
                  <input
                    type="password"
                    className="form-input"
                    name="current_password"
                    value={passwordData.current_password}
                    onChange={handlePasswordChange}
                    required
                    placeholder="Current password"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">New Password</label>
                  <input
                    type="password"
                    className="form-input"
                    name="new_password"
                    value={passwordData.new_password}
                    onChange={handlePasswordChange}
                    required
                    placeholder="New password"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Confirm New Password</label>
                  <input
                    type="password"
                    className="form-input"
                    name="confirm_password"
                    value={passwordData.confirm_password}
                    onChange={handlePasswordChange}
                    required
                    placeholder="Confirm new password"
                  />
                </div>
              </div>

              <button type="submit" className="save-button" disabled={saving}>
                {saving ? 'Updating...' : 'Update Password'}
              </button>
            </form>
          )}

          {activeTab === 'customers' && (
            <div className="profile-section">
              <h3 className="section-title">
                <span>👥</span>
                Your Customers
              </h3>
              {customerList.length > 0 ? (
                <div>
                  {customerList.map((customer) => (
                    <div key={customer.id} className="customer-item">
                      <h4>{customer.first_name} {customer.last_name}</h4>
                      <p>{customer.email}</p>
                    </div>
                  ))}
                  <div className="pagination">
                    <button
                      onClick={() => setCustomerPage(prev => Math.max(1, prev - 1))}
                      disabled={customerPage === 1}
                      className="action-button secondary"
                    >
                      Previous
                    </button>
                    <span>Page {customerPage}</span>
                    <button
                      onClick={() => setCustomerPage(prev => prev + 1)}
                      disabled={customerPage * customersPerPage >= customerTotal}
                      className="action-button secondary"
                    >
                      Next
                    </button>
                  </div>
                </div>
              ) : (
                <p>No customers found.</p>
              )}
            </div>
          )}

          <div className="action-buttons">
            <button
              type="button"
              className="action-button secondary"
              onClick={() => navigate(-1)}
            >
              <span>←</span>
              Back
            </button>
            <button
              type="button"
              className="action-button primary"
              onClick={() => navigate('/')}
            >
              <span>🏠</span>
              Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VendorProfileEdit; 
