/* Booking Confirm Component Styles */
.booking-confirm-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%);
  padding: 2rem 1rem;
  position: relative;
  overflow-x: hidden;
}

.booking-confirm-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(255, 20, 147, 0.1);
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.booking-confirm-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 215, 0, 0.6), 
    rgba(255, 20, 147, 0.6), 
    transparent);
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.booking-confirm-title {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Booking Details Section */
.booking-details {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.details-title {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.details-grid {
  display: grid;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
}

.detail-value {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: right;
}

/* Pricing Section */
.pricing-section {
  margin-bottom: 2rem;
}

.pricing-title {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

/* Action Buttons */
.booking-actions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.btn-back {
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-back:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.4);
  color: rgba(255, 215, 0, 0.9);
  transform: translateY(-1px);
  text-decoration: none;
}

.btn-confirm {
  background: linear-gradient(135deg, #ffd700, #ff1493);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.btn-confirm::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.5s ease;
}

.btn-confirm:hover::before {
  left: 100%;
}

.btn-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
}

.btn-confirm:active {
  transform: translateY(0);
}

.btn-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading State */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spinnerRotate 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

@keyframes spinnerRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  background: rgba(255, 20, 147, 0.1);
  border: 1px solid rgba(255, 20, 147, 0.3);
  border-radius: 12px;
  padding: 1rem;
  color: #ff1493;
  font-weight: 600;
  text-align: center;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Success Message */
.success-message {
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
  padding: 1rem;
  color: #00ff88;
  font-weight: 600;
  text-align: center;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(0, 255, 136, 0); }
}

/* Payment Method Selection Styles */
.payment-method-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.payment-title {
  color: rgba(255, 215, 0, 0.9);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.payment-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.payment-method-option {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  backdrop-filter: blur(5px);
}

.payment-method-option:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.15);
}

.payment-method-option.selected {
  background: rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 
    0 8px 20px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.payment-method-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.payment-method-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.payment-method-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.payment-method-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.95rem;
  flex-grow: 1;
}

.payment-method-option.selected .payment-method-label {
  color: rgba(255, 215, 0, 0.9);
  font-weight: 600;
}

/* Form styles for dropdown */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.form-label.required::after {
  content: ' *';
  color: #ff6b6b;
}

.form-select {
  width: 100%;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  padding: 0.75rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-select:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.5);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
  background: rgba(255, 255, 255, 0.12);
}

.form-select option {
  background: rgba(15, 15, 23, 0.95);
  color: rgba(255, 255, 255, 0.9);
  padding: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-confirm-container {
    padding: 1rem 0.5rem;
  }

  .booking-confirm-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .booking-confirm-title {
    font-size: 1.8rem;
  }

  .booking-details {
    padding: 1rem;
  }

  .details-title {
    font-size: 1.1rem;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .detail-value {
    text-align: left;
  }

  .booking-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-back,
  .btn-confirm {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .booking-confirm-container {
    padding: 0.5rem 0.25rem;
  }

  .booking-confirm-card {
    padding: 1rem;
    border-radius: 12px;
  }

  .booking-confirm-title {
    font-size: 1.6rem;
  }

  .booking-details {
    padding: 0.75rem;
  }

  .details-title {
    font-size: 1rem;
  }

  .detail-item {
    padding: 0.5rem 0;
  }

  .detail-label,
  .detail-value {
    font-size: 0.85rem;
  }

  .btn-back,
  .btn-confirm {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (min-width: 768px) {
  .booking-confirm-card {
    padding: 2.5rem;
  }

  .booking-actions {
    justify-content: space-between;
  }

  .btn-back,
  .btn-confirm {
    padding: 1.25rem 2.5rem;
  }
}

@media (min-width: 1024px) {
  .booking-confirm-card {
    padding: 3rem;
  }
} 