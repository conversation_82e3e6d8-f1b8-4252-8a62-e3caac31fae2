import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';

import './BookingConfirm.css';

const BookingConfirm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [detailsLoading, setDetailsLoading] = useState(true);
  const [error, setError] = useState('');
  const [details, setDetails] = useState({});


  const bookingData = location.state?.bookingData;

  // Payment methods array


  useEffect(() => {
    if (!bookingData) {
      navigate('/book');
      return;
    }

    // Set initial payment method


    const fetchDetails = async () => {
      try {
        // Fetch salon, service, and staff details
        const [salonRes, serviceRes, staffRes] = await Promise.all([
          fetch(`http://127.0.0.1:8000/api/salons/${bookingData.salon}/`),
          fetch(`http://127.0.0.1:8000/api/services/${bookingData.service}/`),
          bookingData.staff ? fetch(`http://127.0.0.1:8000/api/staff/${bookingData.staff}/`) : Promise.resolve(null)
        ]);

        const salonData = salonRes.ok ? await salonRes.json() : null;
        const serviceData = serviceRes.ok ? await serviceRes.json() : null;
        const staffData = staffRes && staffRes.ok ? await staffRes.json() : null;

        setDetails({
          salon: salonData,
          service: serviceData,
          staff: staffData
        });
      } catch (error) {
        console.error('Error fetching details:', error);
      } finally {
        setDetailsLoading(false);
      }
    };

    fetchDetails();
  }, [bookingData, navigate]);

  const handleConfirm = async () => {


    setLoading(true);
    try {
      // Update booking data with pending payment status
      const updatedBookingData = {
        ...bookingData,
        paymentMethod: 'pending',
        paymentStatus: 'pending',
        status: 'confirmed'
      };

      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      
      const response = await fetch('http://127.0.0.1:8000/api/bookings/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(updatedBookingData),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Booking confirmed successfully! 🎉');
        
        // Navigate directly to success page without payment processing
        navigate('/booking-success', {
          state: {
            bookingData: {
              ...result,
              pricingData: bookingData.pricingData,
              paymentStatus: 'pending',
              paymentMethod: 'Pending'
            }
          }
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to confirm booking');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="booking-confirm-container">
      <div className="booking-confirm-card">
        <h2 className="booking-confirm-title">Confirm Your Booking</h2>
        {detailsLoading ? (
          <div className="text-center my-4">
            <div className="loading-spinner" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <div>Loading booking details...</div>
          </div>
        ) : (
          <>
            {/* Booking Details */}
            <div className="booking-details mb-4">
              <h4 className="details-title">📋 Booking Details</h4>
              <div className="details-grid">
                <div className="detail-item">
                  <span className="detail-label">Name:</span>
                  <span className="detail-value">{bookingData.userName}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Salon:</span>
                  <span className="detail-value">{details.salon?.name || bookingData.salon}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Service:</span>
                  <span className="detail-value">{details.service?.name || bookingData.service}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Staff:</span>
                  <span className="detail-value">{details.staff?.name || (!bookingData.staff ? 'Any available staff' : bookingData.staff)}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Date:</span>
                  <span className="detail-value">{bookingData.date}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Time:</span>
                  <span className="detail-value">{bookingData.time}</span>
                </div>
              </div>
            </div>


          </>
        )}
        {error && <div className="error-message">{error}</div>}
        <div className="booking-actions">
          <button className="btn-back" onClick={() => navigate(-1)} disabled={loading}>
            ← Back
          </button>
          <button className="btn-confirm" onClick={handleConfirm} disabled={loading || detailsLoading}>
            {loading ? (
              <>
                <div className="loading-spinner"></div>
                Confirming...
              </>
            ) : (
              'Confirm Booking'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirm; 
