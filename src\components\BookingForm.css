/* GitHub-Inspired Gen Z Booking Form */

.enterprise-booking-container {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  padding: 16px;
  position: relative;
}

.enterprise-booking-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at top, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(236, 72, 153, 0.15) 0%, transparent 50%);
  pointer-events: none;
}

.booking-form-wrapper {
  max-width: 768px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.booking-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 0 16px;
}

.booking-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #f0f6fc;
  line-height: 1.25;
}

.booking-subtitle {
  font-size: 0.875rem;
  color: #8b949e;
  font-weight: 400;
  margin-bottom: 0;
}

.enterprise-booking-form {
  background: #161b22;
  border: 1px solid #30363d;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  position: relative;
}

.enterprise-booking-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #8b5cf6, #ec4899, #06b6d4);
  border-radius: 12px 12px 0 0;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f0f6fc;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #21262d;
}

.section-icon {
  font-size: 1.125rem;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #f0f6fc;
  margin-bottom: 6px;
  font-size: 0.875rem;
}

.form-control-enterprise {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #30363d;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  background: #0d1117;
  color: #f0f6fc;
  transition: all 0.2s ease;
  line-height: 20px;
  min-height: 32px;
}

.form-control-enterprise:focus {
  outline: none;
  border-color: #1f6feb;
  box-shadow: 0 0 0 3px rgba(31, 111, 235, 0.3);
}

.form-control-enterprise.error {
  border-color: #f85149;
  box-shadow: 0 0 0 3px rgba(248, 81, 73, 0.3);
}

.form-control-enterprise::placeholder {
  color: #8b949e;
}

.error-message {
  color: #ff4757;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #667eea;
  font-weight: 600;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(102, 126, 234, 0.3);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

.enterprise-submit-btn {
  background: #238636;
  color: #ffffff;
  border: 1px solid rgba(240, 246, 252, 0.1);
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.enterprise-submit-btn:hover:not(:disabled) {
  background: #2ea043;
  border-color: rgba(240, 246, 252, 0.15);
}

.enterprise-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #21262d;
  color: #8b949e;
}

.back-button {
  background: transparent;
  border: 1px solid #30363d;
  color: #f0f6fc;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.back-button:hover {
  background: #21262d;
  border-color: #8b949e;
}

.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  padding: 0 16px;
}

.progress-step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #21262d;
  border: 1px solid #30363d;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b949e;
  font-weight: 500;
  font-size: 0.75rem;
  margin: 0 6px;
  position: relative;
}

.progress-step.active {
  background: #1f6feb;
  border-color: #1f6feb;
  color: #ffffff;
}

.progress-step.completed {
  background: #238636;
  border-color: #238636;
  color: #ffffff;
}

.progress-step::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  width: 12px;
  height: 1px;
  background: #30363d;
  transform: translateY(-50%);
}

.progress-step:last-child::after {
  display: none;
}

.progress-step.completed::after {
  background: #238636;
}

.error-message {
  color: #f85149;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #8b949e;
  font-size: 0.875rem;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #30363d;
  border-top: 2px solid #1f6feb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .enterprise-booking-container {
    padding: 12px;
  }

  .booking-title {
    font-size: 1.5rem;
  }

  .enterprise-booking-form {
    padding: 16px;
  }

  .form-control-enterprise {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .progress-step {
    width: 28px;
    height: 28px;
    margin: 0 4px;
  }
}

@media (max-width: 480px) {
  .booking-form-wrapper {
    max-width: 100%;
  }

  .section-title {
    font-size: 1rem;
  }

  .progress-indicator {
    padding: 0 8px;
  }
}
