import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import AsyncSelect from 'react-select/async';
import { toast } from 'react-toastify';
import { useAuth } from '../context/AuthContext';
import PricingDisplay from './PricingDisplay';
import { calculateBookingCost } from '../utils/pricingCalculator';
import './LuxuryBookingForm.css';
import './BookingFormProfile.css';

const BookingForm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { authFetch, user } = useAuth();

  // State for form data and selections
  const [formData, setFormData] = useState({
    countyId: '',
    townId: '',
    salonId: '',
    serviceId: '',
    staffId: '',
    date: '',
    time: '',
    name: user?.name || user?.username || '',
    email: user?.email || '',
    phone: user?.phone || '',
    paymentMethod: 'mpesa',
    specialRequests: ''
  });

  const [selectedCounty, setSelectedCounty] = useState(null);
  const [selectedTown, setSelectedTown] = useState(null);
  const [selectedSalon, setSelectedSalon] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [selectedStaff, setSelectedStaff] = useState(null);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);

  // UI state
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // Helper function to check if booking is rush (within 24 hours)
  const isRushBooking = (date) => {
    if (!date) return false;
    const bookingDate = new Date(date);
    const now = new Date();
    const timeDiff = bookingDate.getTime() - now.getTime();
    const hoursDiff = timeDiff / (1000 * 3600);
    return hoursDiff <= 24;
  };

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        name: user.name || user.username || prev.name,
        email: user.email || prev.email,
        phone: user.phone || prev.phone
      }));
    }
  }, [user]);

  // Fetch counties for location selection
  const fetchCounties = useCallback(async (inputValue) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/counties/?search=${encodeURIComponent(inputValue || '')}`);
      if (response.ok) {
        const data = await response.json();
        return data.slice(0, 5).map(county => ({
          label: county.name,
          value: county.id,
          raw: county
        }));
      }
    } catch (error) {
      console.warn('Failed to fetch counties from API');
    }

    return [];
  }, []);

  // Fetch towns for selected county
  const fetchTowns = useCallback(async (inputValue) => {
    if (!selectedCounty) return [];

    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/towns/?county=${encodeURIComponent(selectedCounty.label)}&search=${encodeURIComponent(inputValue || '')}`);
      if (response.ok) {
        const data = await response.json();
        return data.slice(0, 5).map(town => ({
          label: town.name,
          value: town.id,
          raw: town
        }));
      }
    } catch (error) {
      console.warn('Failed to fetch towns from API');
    }

    return [];
  }, [selectedCounty]);

  // Fetch salons for selected town
  const fetchSalons = useCallback(async (inputValue) => {
    if (!selectedTown) return [];

    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/salons/search/?town=${encodeURIComponent(selectedTown.label)}&q=${encodeURIComponent(inputValue || '')}`);
      if (response.ok) {
        const data = await response.json();
        return data.slice(0, 5).map(salon => ({
          label: salon.name,
          value: salon.id,
          raw: salon
        }));
      }
    } catch (error) {
      console.warn('Failed to fetch salons from API');
    }

    return [];
  }, [selectedTown]);

  // Fetch services for selected salon
  const fetchServices = useCallback(async (inputValue) => {
    if (!formData.salonId) return [];

    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/services/search/?salon=${formData.salonId}&search=${encodeURIComponent(inputValue || '')}`);
      if (response.ok) {
        const data = await response.json();
        return data.slice(0, 5).map(service => ({
          label: service.name,
          value: service.id,
          raw: service
        }));
      }
    } catch (error) {
      console.warn('Failed to fetch services from API');
    }

    return [];
  }, [formData.salonId]);

  // Fetch staff for selected service
  const fetchStaff = useCallback(async (inputValue) => {
    if (!formData.serviceId) return [];

    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/staff/search/?service=${formData.serviceId}&salon=${formData.salonId}&search=${encodeURIComponent(inputValue || '')}`);
      if (response.ok) {
        const data = await response.json();
        return data.slice(0, 5).map(member => ({
          label: member.name,
          value: member.id,
          raw: member
        }));
      }
    } catch (error) {
      console.warn('Failed to fetch staff from API');
    }

    return [];
  }, [formData.serviceId, formData.salonId]);

  // Fetch available time slots
  const fetchAvailableSlots = useCallback(async (date) => {
    if (!date || !formData.salonId) return [];

    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/available-slots/?salon=${formData.salonId}&date=${date}`);
      if (response.ok) {
        const data = await response.json();
        return data;
      }
    } catch (error) {
      console.warn('Failed to fetch available slots from API, using mock data');
    }

    // Mock available slots (9 AM to 6 PM)
    const slots = [];
    for (let hour = 9; hour <= 18; hour += 1) {
      slots.push({
        time: `${hour.toString().padStart(2, '0')}:00`,
        available: true
      });
    }
    return slots;
  }, [formData.salonId]);

  // Effects for real-time data fetching
  useEffect(() => {
    if (formData.date && formData.salonId) {
      fetchAvailableSlots(formData.date).then(slots => {
        setAvailableSlots(slots);
      });
    } else {
      setAvailableSlots([]);
    }
  }, [formData.date, formData.salonId, fetchAvailableSlots]);

  // Clear time selection when date changes
  useEffect(() => {
    if (formData.date) {
      setFormData(prev => ({ ...prev, time: '' }));
    }
  }, [formData.date]);

  useEffect(() => {
    if (selectedCounty) {
      setSelectedTown(null);
      // Don't clear salon selection when county changes
      setFormData(prev => ({ ...prev, serviceId: '', staffId: '' }));
    }
  }, [selectedCounty]);

  useEffect(() => {
    if (selectedTown) {
      // Don't clear salon selection when town changes
      setFormData(prev => ({ ...prev, serviceId: '', staffId: '' }));
    }
  }, [selectedTown]);

  // Clear dependent fields when salon changes
  useEffect(() => {
    if (selectedSalon) {
      setSelectedService(null);
      setSelectedStaff(null);
      setFormData(prev => ({ ...prev, serviceId: '', staffId: '' }));
    }
  }, [selectedSalon]);

  // Form validation
  const validateStep = useCallback((step) => {
    const newErrors = {};

    if (step >= 1) {
      if (!formData.name.trim()) newErrors.name = 'Name is required';
      if (!formData.email.trim()) newErrors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Invalid email format';
      if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    }

    if (step >= 2) {
      if (!formData.salonId) newErrors.salonId = 'Please select a salon';
      if (!formData.serviceId) newErrors.serviceId = 'Please select a service';
    }

    if (step >= 3) {
      if (!formData.date) newErrors.date = 'Please select a date';
      if (!formData.time) newErrors.time = 'Please select a time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Form handlers
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear related errors
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);

  const handleNextStep = useCallback(() => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    }
  }, [currentStep, validateStep]);

  const handlePrevStep = useCallback(() => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  }, []);

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!validateStep(4)) return;

    setSubmitLoading(true);

    try {
      // Validate required fields before submission
      const requiredFields = {
        salonId: formData.salonId,
        serviceId: formData.serviceId,
        date: formData.date,
        time: formData.time,
        name: formData.name,
        email: formData.email,
        phone: formData.phone
      };

      const missingFields = Object.entries(requiredFields)
        .filter(([key, value]) => !value)
        .map(([key]) => key);

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      console.log('Form data validation passed. Required fields:', requiredFields);

      // Calculate pricing data
      const pricingData = selectedService ? calculateBookingCost(selectedService, {
        premiumStaff: selectedStaff?.isPremium || false,
        rushBooking: isRushBooking(formData.date)
      }) : null;

      const bookingData = {
        userId: user?.id?.toString() || '38',
        userName: formData.name,
        salon: parseInt(formData.salonId, 10),
        service: parseInt(formData.serviceId, 10),
        staff: formData.staffId ? parseInt(formData.staffId, 10) : null,
        date: formData.date,
        time: formData.time,
        paymentMethod: formData.paymentMethod,
        notes: formData.specialRequests || '',
        status: 'pending'
      };

      console.log('Submitting booking data:', JSON.stringify(bookingData, null, 2));

      // Get auth token from localStorage or cookies
      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/bookings/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
          'X-CSRFToken': getCsrfToken(),
        },
        body: JSON.stringify(bookingData),
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        let errorData;
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          try {
            errorData = await response.json();
            console.error('Booking submission error details:', JSON.stringify(errorData, null, 2));
          } catch (parseError) {
            console.error('Failed to parse error response:', parseError);
            errorData = { message: 'Unknown error occurred' };
          }
        } else {
          // Handle HTML response (server error page)
          const errorText = await response.text();
          console.error('Server returned HTML error page:', errorText.substring(0, 500));
          errorData = { message: `Server error (${response.status}): Check server logs` };
        }
        
        throw new Error(errorData.message || errorData.detail || `HTTP ${response.status}: Failed to create booking`);
      }

      const result = await response.json();
      console.log('Booking created successfully:', result);

      toast.success('Booking created successfully! 🎉');

      // Navigate to confirmation page with pricing data
      navigate('/booking-confirm', {
        state: {
          bookingData: {
            ...result,
            pricingData: pricingData,
            totalAmount: pricingData?.total || selectedService?.price || 1000,
            email: formData.email,
            phone: formData.phone
          },
          bookingId: result.id
        }
      });

    } catch (error) {
      console.error('Booking submission failed:', error);
      toast.error(error.message || 'Failed to create booking');
    } finally {
      setSubmitLoading(false);
    }
  }, [formData, user, navigate, validateStep]);

  const getCsrfToken = () => {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrftoken') {
        return decodeURIComponent(value);
      }
    }
    return '';
  };

  // Initialize form data from URL params
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const salonIdFromUrl = queryParams.get('salonId');

    if (salonIdFromUrl) {
      setFormData(prev => ({ ...prev, salonId: salonIdFromUrl }));
    }
  }, [location.search]);

  // Luxury Gen Z select styles
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderColor: state.isFocused ? 'rgba(255, 215, 0, 0.6)' : 'rgba(255, 255, 255, 0.1)',
      borderWidth: '2px',
      borderRadius: '16px',
      minHeight: '56px',
      padding: '0 0.25rem',
      boxShadow: state.isFocused ? '0 0 0 3px rgba(255, 215, 0, 0.2), 0 4px 12px rgba(255, 215, 0, 0.1)' : 'none',
      backdropFilter: 'blur(10px)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      transform: state.isFocused ? 'translateY(-1px)' : 'none',
      '&:hover': {
        borderColor: 'rgba(255, 215, 0, 0.4)',
        backgroundColor: 'rgba(255, 255, 255, 0.12)'
      }
    }),
    input: (provided) => ({
      ...provided,
      color: 'white',
      fontSize: '1rem',
      fontWeight: 500
    }),
    singleValue: (provided) => ({
      ...provided,
      color: 'white',
      fontSize: '1rem',
      fontWeight: 500
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: 'rgba(20, 20, 30, 0.95)',
      border: '2px solid rgba(255, 215, 0, 0.3)',
      borderRadius: '16px',
      boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4), 0 15px 35px rgba(255, 20, 147, 0.2)',
      backdropFilter: 'blur(20px) saturate(180%)',
      zIndex: 9999
    }),
    option: (provided, state) => ({
      ...provided,
      color: 'white',
      backgroundColor: state.isSelected
        ? 'rgba(255, 215, 0, 0.2)'
        : state.isFocused
        ? 'rgba(255, 255, 255, 0.08)'
        : 'transparent',
      fontSize: '1rem',
      fontWeight: 500,
      padding: '12px 16px',
      transition: 'all 0.2s ease',
      '&:hover': {
        backgroundColor: state.isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.12)'
      }
    }),
    placeholder: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.5)',
      fontSize: '1rem',
      fontWeight: 400
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.6)',
      transition: 'color 0.2s ease',
      '&:hover': {
        color: 'rgba(255, 215, 0, 0.8)'
      }
    }),
    clearIndicator: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.6)',
      transition: 'color 0.2s ease',
      '&:hover': {
        color: 'rgba(255, 20, 147, 0.8)'
      }
    }),
    loadingIndicator: (provided) => ({
      ...provided,
      color: 'rgba(255, 215, 0, 0.8)'
    }),
    noOptionsMessage: (provided) => ({
      ...provided,
      color: 'rgba(255, 255, 255, 0.7)',
      fontSize: '0.9rem',
      fontStyle: 'italic'
    }),
    loadingMessage: (provided) => ({
      ...provided,
      color: 'rgba(255, 215, 0, 0.8)',
      fontSize: '14px'
    })
  };

  const steps = [
    { number: 1, title: 'Personal Info', icon: '1' },
    { number: 2, title: 'Select Service', icon: '2' },
    { number: 3, title: 'Choose Time', icon: '3' },
    { number: 4, title: 'Confirm', icon: '4' }
  ];

  const paymentMethods = [
    { value: 'mpesa', label: 'M-Pesa', icon: '📱' },
    { value: 'paystack-inline', label: 'Paystack (All Methods)', icon: '💳' },
    { value: 'paystack', label: 'Paystack (Card Only)', icon: '💳' },
    { value: 'paypal', label: 'PayPal', icon: '🌐' },
    { value: 'bank', label: 'Bank Transfer', icon: '🏦' },
    { value: 'wise', label: 'Wise', icon: '💱' },
    { value: 'visa', label: 'Visa/Card', icon: '💳' },
  ];

  return (
    <div className="booking-form-profile-container">
      <div className="profile-container">
        {/* Header - Profile Pattern */}
        <div className="profile-header">
          <button
            onClick={() => navigate(-1)}
            className="back-button"
          >
            ← Back to Home
          </button>
          <div className="booking-icon">✨</div>
          <h1 className="profile-title">Book Your Glow Up</h1>
          <p className="profile-subtitle">Premium booking experience</p>
        </div>

        {/* Content Area - Profile Pattern */}
        <div className="profile-content">

          {/* Progress Indicator Section */}
          <div className="profile-section">
            <h3 className="section-title">📋 Booking Progress</h3>
            <div className="progress-container">
              <div className="progress-steps">
                <div className="progress-line">
                  <div
                    className="progress-fill"
                    style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                  ></div>
                </div>
                {steps.map((step) => (
                  <div
                    key={step.number}
                    className={`progress-step ${
                      currentStep === step.number ? 'active' :
                      currentStep > step.number ? 'completed' : ''
                    }`}
                  >
                    {currentStep > step.number ? '✓' : step.number}
                  </div>
                ))}
              </div>
              <div className="step-labels">
                {steps.map((step) => (
                  <div
                    key={step.number}
                    className={`step-label ${
                      currentStep === step.number ? 'active' :
                      currentStep > step.number ? 'completed' : ''
                    }`}
                  >
                    {step.title}
                  </div>
                ))}
              </div>
            </div>
          </div>
          {/* Main Form */}
          <form onSubmit={handleSubmit} noValidate>

            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="profile-section">
                <h3 className="section-title">👤 Personal Information</h3>

                <div className="form-group">
                  <label className="form-label required">Full Name</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`form-input ${errors.name ? 'error' : ''}`}
                      placeholder="Enter your full name"
                      required
                    />
                  {errors.name && <div className="error-message">⚠ {errors.name}</div>}
                </div>

                <div className="form-group">
                  <label className="form-label required">Email Address</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`form-input ${errors.email ? 'error' : ''}`}
                      placeholder="<EMAIL>"
                      required
                    />
                  {errors.email && <div className="error-message">⚠ {errors.email}</div>}
                </div>

                <div className="form-group">
                  <label className="form-label required">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`form-input ${errors.phone ? 'error' : ''}`}
                      placeholder="+254 712 345 678"
                      required
                    />
                  {errors.phone && <div className="error-message">⚠ {errors.phone}</div>}
                </div>
              </div>
            )}

            {/* Step 2: Salon & Service Selection */}
            {currentStep === 2 && (
              <div className="profile-section">
                <h3 className="section-title">🏢 Select Salon & Service</h3>

              {/* Location Selection */}
              <div className="luxury-form-group">
                <label className="luxury-form-label">County</label>
                  <AsyncSelect
                    cacheOptions
                    defaultOptions={true}
                    loadOptions={fetchCounties}
                    value={selectedCounty}
                    onChange={(option) => {
                      setSelectedCounty(option);
                      setFormData(prev => ({ ...prev, countyId: option?.value || '' }));
                    }}
                    placeholder="Start typing county name..."
                    isClearable
                    styles={selectStyles}
                    noOptionsMessage={({ inputValue }) =>
                      inputValue ? `No counties found for "${inputValue}"` : "Type to search counties (e.g., 'Nairobi', 'Mombasa')"
                    }
                    loadingMessage={() => "Searching counties..."}
                    filterOption={null}
                  />
              </div>

              <div className="luxury-form-group">
                <label className="luxury-form-label">Town</label>
                  <AsyncSelect
                    cacheOptions
                    defaultOptions={true}
                    loadOptions={fetchTowns}
                    value={selectedTown}
                    onChange={(option) => {
                      setSelectedTown(option);
                      setFormData(prev => ({ ...prev, townId: option?.value || '' }));
                    }}
                    placeholder={selectedCounty ? "Type town name..." : "Select county first"}
                    isDisabled={!selectedCounty}
                    isClearable
                    styles={selectStyles}
                    noOptionsMessage={({ inputValue }) =>
                      inputValue ? `No towns found for "${inputValue}"` : selectedCounty ? "Type to search towns" : "Select county first"
                    }
                    loadingMessage={() => "Loading towns..."}
                    filterOption={null}
                  />
              </div>

              <div className="luxury-form-group">
                <label className="luxury-form-label required">Salon</label>
                  <AsyncSelect
                    cacheOptions
                    defaultOptions={true}
                    loadOptions={fetchSalons}
                    value={selectedSalon}
                    onChange={(option) => {
                      setSelectedSalon(option);
                      setFormData(prev => ({ ...prev, salonId: option?.value || '' }));
                    }}
                    placeholder="Type 'Glam Studio', 'Beauty Palace'..."
                    isClearable
                    styles={selectStyles}
                    noOptionsMessage={({ inputValue }) =>
                      inputValue ? `No salons found for "${inputValue}"` : "Type to search salons"
                    }
                    loadingMessage={() => "Searching salons..."}
                    filterOption={null}
                  />
                {errors.salonId && <div className="error-message">⚠️ {errors.salonId}</div>}
              </div>

              {/* Service Selection */}
              <div className="luxury-form-group">
                <label className="luxury-form-label required">Service</label>
                  <AsyncSelect
                    cacheOptions
                    defaultOptions={true}
                    loadOptions={fetchServices}
                    value={selectedService}
                    onChange={(option) => {
                      setSelectedService(option);
                      setFormData(prev => ({ ...prev, serviceId: option?.value || '' }));
                    }}
                    placeholder={formData.salonId ? "Type 'Hair Cut', 'Manicure'..." : "Select salon first"}
                    isDisabled={!formData.salonId}
                    isClearable
                    styles={selectStyles}
                    noOptionsMessage={({ inputValue }) =>
                      inputValue ? `No services found for "${inputValue}"` : "Type to search services"
                    }
                    loadingMessage={() => "Loading services..."}
                    filterOption={null}
                  />
                {errors.serviceId && <div className="luxury-error-message">⚠ {errors.serviceId}</div>}
              </div>

              <div className="luxury-form-group">
                <label className="luxury-form-label">Preferred Staff (Optional)</label>
                  <AsyncSelect
                    cacheOptions
                    defaultOptions={true}
                    loadOptions={fetchStaff}
                    value={selectedStaff}
                    onChange={(option) => {
                      setSelectedStaff(option);
                      setFormData(prev => ({ ...prev, staffId: option?.value || '' }));
                    }}
                    placeholder={formData.serviceId ? "Type 'Sarah', 'Grace'..." : "Select service first"}
                    isDisabled={!formData.serviceId}
                    isClearable
                    styles={selectStyles}
                    noOptionsMessage={({ inputValue }) =>
                      inputValue ? `No staff found for "${inputValue}"` : "Type to search staff"
                    }
                    loadingMessage={() => "Loading staff..."}
                    filterOption={null}
                  />
              </div>
            </div>
          )}

          {/* Step 3: Date & Time Selection */}
          {currentStep === 3 && (
            <div className="form-section">
              <div className="luxury-section-header">
                <div className="luxury-section-icon">📅</div>
                <h2 className="luxury-section-title">Choose Date & Time</h2>
              </div>

              <div className="row">
                <div className="col-md-6">
                  <div className="luxury-form-group">
                    <label className="luxury-form-label required">Appointment Date</label>
                      <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        min={new Date().toISOString().split('T')[0]}
                        className={`luxury-form-input ${errors.date ? 'error' : ''}`}
                        required
                      />
                    {errors.date && <div className="luxury-error-message">⚠ {errors.date}</div>}
                  </div>
                </div>

                <div className="col-md-6">
                  <div className="luxury-form-group">
                    <label className="luxury-form-label required">Available Time Slots</label>
                    {loading ? (
                      <div className="loading-state">
                        <div className="luxury-loading-spinner"></div>
                        Loading available slots...
                      </div>
                    ) : (
                        <select
                          name="time"
                          value={formData.time}
                          onChange={handleInputChange}
                          className={`luxury-form-select ${errors.time ? 'error' : ''}`}
                          required
                        >
                          <option value="">Select time slot</option>
                          {availableSlots.map(slot => (
                            <option key={slot.time} value={slot.time}>
                              {slot.time} {slot.available ? '' : '(Unavailable)'}
                            </option>
                          ))}
                        </select>
                    )}
                    {errors.time && <div className="luxury-error-message">⚠ {errors.time}</div>}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Confirmation & Payment */}
          {currentStep === 4 && (
            <div className="form-section">
              <div className="luxury-section-header">
                <div className="luxury-section-icon">✅</div>
                <h2 className="luxury-section-title">Confirm Your Booking</h2>
              </div>

              {/* Booking Summary */}
              <div className="booking-summary mb-4">
                <div className="row">
                  <div className="col-md-6">
                    <div className="summary-item">
                      <span className="summary-label">Salon:</span>
                      <span className="summary-value">{selectedSalon?.name || 'Selected Salon'}</span>
                    </div>
                    <div className="summary-item">
                      <span className="summary-label">Service:</span>
                      <span className="summary-value">{selectedService?.name || 'Selected Service'}</span>
                    </div>
                    <div className="summary-item">
                      <span className="summary-label">Staff:</span>
                      <span className="summary-value">{selectedStaff?.name || 'Any available staff'}</span>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="summary-item">
                      <span className="summary-label">Date:</span>
                      <span className="summary-value">{formData.date}</span>
                    </div>
                    <div className="summary-item">
                      <span className="summary-label">Time:</span>
                      <span className="summary-value">{formData.time}</span>
                    </div>
                    <div className="summary-item">
                      <span className="summary-label">Duration:</span>
                      <span className="summary-value">{selectedService?.duration || 60} minutes</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing Display */}
              {selectedService && (
                <div className="pricing-section mb-4">
                  <h4 className="pricing-title">💰 Cost Breakdown</h4>
                  <PricingDisplay 
                    service={selectedService} 
                    options={{
                      premiumStaff: selectedStaff?.isPremium || false,
                      rushBooking: isRushBooking(formData.date)
                    }}
                    showBreakdown={true}
                  />
                </div>
              )}

              {/* Payment Method Selection */}
              <div className="payment-method-section mb-4">
                <h4 className="payment-title">💳 Choose Payment Method</h4>
                
                {/* Dropdown Payment Method Selection */}
                <div className="luxury-form-group mb-3">
                  <label className="luxury-form-label required">Payment Method (Dropdown)</label>
                      <select
                        name="paymentMethod"
                        value={formData.paymentMethod}
                        onChange={handleInputChange}
                        className="luxury-form-select"
                        required
                      >
                        {paymentMethods.map(method => (
                          <option key={method.value} value={method.value}>
                            {method.icon} {method.label}
                          </option>
                        ))}
                      </select>
                </div>

                {/* Radio Button Payment Method Selection */}
                <div className="payment-methods-grid">
                  {paymentMethods.map(method => (
                    <label key={method.value} className={`payment-method-option ${formData.paymentMethod === method.value ? 'selected' : ''}`}>
                      <input
                        type="radio"
                        name="paymentMethod"
                        value={method.value}
                        checked={formData.paymentMethod === method.value}
                        onChange={handleInputChange}
                        className="payment-method-input"
                      />
                      <div className="payment-method-content">
                        <div className="payment-method-icon">{method.icon}</div>
                        <div className="payment-method-label">{method.label}</div>
                      </div>
                    </label>
                  ))}
                  </div>
                </div>

              {/* Special Requests */}
                  <div className="luxury-form-group">
                    <label className="luxury-form-label">Special Requests (Optional)</label>
                      <textarea
                        name="specialRequests"
                        value={formData.specialRequests}
                        onChange={handleInputChange}
                        className="luxury-form-textarea"
                        rows="3"
                        placeholder="Any special requests or preferences..."
                      />
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="form-section">
            <div className="d-flex justify-content-center align-items-center gap-3">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={handlePrevStep}
                  className="luxury-btn-secondary"
                  disabled={submitLoading}
                >
                  ← Previous
                </button>
              )}
              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="luxury-btn-primary"
                >
                  Next Step →
                </button>
              ) : (
                <button
                  type="submit"
                  className="luxury-btn-primary"
                  disabled={submitLoading}
                >
                  {submitLoading ? (
                    <>
                      <div className="luxury-loading-spinner"></div>
                      Processing...
                    </>
                  ) : (
                    'Complete Booking'
                  )}
                </button>
              )}
            </div>
          </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingForm;
