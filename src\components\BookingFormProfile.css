/* Booking Form - Profile Design Pattern with Dark Theme */

/* Main Container - Profile Pattern with Dark Theme */
.booking-form-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Floating Background Effects */
.booking-form-profile-container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
  animation: backgroundFloat 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes backgroundFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.booking-form-profile-container .profile-container {
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(255, 20, 147, 0.1);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.booking-form-profile-container .profile-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 215, 0, 0.6), 
    rgba(255, 20, 147, 0.6), 
    transparent);
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Header - Profile Pattern with Dark Theme */
.booking-form-profile-container .profile-header {
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.9) 0%,
    rgba(25, 25, 35, 0.8) 50%,
    rgba(30, 30, 40, 0.9) 100%);
  padding: 2rem 1.5rem;
  text-align: center;
  color: white;
  position: relative;
}

.booking-form-profile-container .back-button {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.booking-form-profile-container .back-button:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.booking-form-profile-container .booking-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  filter: drop-shadow(0 2px 8px rgba(255, 215, 0, 0.3));
}

.booking-form-profile-container .profile-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff, #f0f6fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.booking-form-profile-container .profile-subtitle {
  font-size: 1rem;
  opacity: 0.8;
  margin: 0;
  font-weight: 400;
  color: #8b949e;
}

/* Content Area - Profile Pattern */
.booking-form-profile-container .profile-content {
  padding: 2rem 1.5rem;
}

/* Sections - Profile Pattern with Dark Theme */
.booking-form-profile-container .profile-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.booking-form-profile-container .section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Progress Indicator */
.booking-form-profile-container .progress-container {
  margin-top: 1rem;
}

.booking-form-profile-container .progress-steps {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.booking-form-profile-container .progress-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-50%);
  z-index: 1;
}

.booking-form-profile-container .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  border-radius: 1px;
  transition: width 0.3s ease;
}

.booking-form-profile-container .progress-step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #8b949e;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.booking-form-profile-container .progress-step.active {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-color: #FFD700;
  color: #333;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.booking-form-profile-container .progress-step.completed {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.booking-form-profile-container .step-labels {
  display: flex;
  justify-content: space-between;
}

.booking-form-profile-container .step-label {
  font-size: 0.75rem;
  color: #8b949e;
  text-align: center;
  font-weight: 500;
  width: 40px;
}

.booking-form-profile-container .step-label.active {
  color: #FFD700;
  font-weight: 600;
}

.booking-form-profile-container .step-label.completed {
  color: #10b981;
}

/* Form Elements */
.booking-form-profile-container .form-group {
  margin-bottom: 1.5rem;
}

.booking-form-profile-container .form-label {
  display: block;
  font-weight: 500;
  color: #f0f6fc;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.booking-form-profile-container .form-label.required::after {
  content: ' *';
  color: #ff6b6b;
}

.booking-form-profile-container .form-input,
.booking-form-profile-container .form-select,
.booking-form-profile-container .form-textarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.booking-form-profile-container .form-input:focus,
.booking-form-profile-container .form-select:focus,
.booking-form-profile-container .form-textarea:focus {
  outline: none;
  border-color: #FFD700;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.booking-form-profile-container .form-input.error,
.booking-form-profile-container .form-select.error,
.booking-form-profile-container .form-textarea.error {
  border-color: #ff6b6b;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.booking-form-profile-container .form-input::placeholder,
.booking-form-profile-container .form-textarea::placeholder {
  color: #8b949e;
}

.booking-form-profile-container .error-message {
  color: #ff6b6b;
  font-size: 0.75rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Action Buttons */
.booking-form-profile-container .form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.booking-form-profile-container .btn-secondary {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #f0f6fc;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.booking-form-profile-container .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
}

.booking-form-profile-container .btn-primary {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: none;
  color: #333;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.booking-form-profile-container .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
}

.booking-form-profile-container .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .booking-form-profile-container {
    padding: 0.75rem 0.25rem;
  }

  .booking-form-profile-container .profile-container {
    border-radius: 16px;
  }

  .booking-form-profile-container .profile-header {
    padding: 1.5rem 1rem;
  }

  .booking-form-profile-container .profile-title {
    font-size: 1.5rem;
  }

  .booking-form-profile-container .profile-content {
    padding: 1.5rem 1rem;
  }

  .booking-form-profile-container .back-button {
    top: 0.75rem;
    left: 0.75rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .booking-form-profile-container .progress-step {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }

  .booking-form-profile-container .step-label {
    font-size: 0.625rem;
    width: 32px;
  }

  .booking-form-profile-container .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .booking-form-profile-container .btn-secondary,
  .booking-form-profile-container .btn-primary {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .booking-form-profile-container .profile-header {
    padding: 1rem 0.75rem;
  }

  .booking-form-profile-container .profile-title {
    font-size: 1.25rem;
  }

  .booking-form-profile-container .profile-content {
    padding: 1rem 0.75rem;
  }

  .booking-form-profile-container .profile-section {
    padding: 1rem;
  }

  .booking-form-profile-container .booking-icon {
    font-size: 2rem;
  }
}
