/* ===== Booking Success - Profile Design Pattern ===== */

/* Main Container */
.booking-success-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.1) 0%,
    rgba(16, 185, 129, 0.1) 25%,
    rgba(5, 150, 105, 0.1) 50%,
    rgba(4, 120, 87, 0.1) 75%,
    rgba(6, 95, 70, 0.1) 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Profile Container */
.booking-success-profile-container .profile-container {
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

/* Floating Effects */
.booking-success-profile-container .auth-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.booking-success-profile-container .auth-sparkle {
  position: absolute;
  font-size: 1.5rem;
  animation: sparkle 3s ease-in-out infinite;
  opacity: 0.6;
}

.booking-success-profile-container .auth-sparkle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.booking-success-profile-container .auth-sparkle:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.booking-success-profile-container .auth-sparkle:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

/* Header */
.booking-success-profile-container .profile-header {
  text-align: center;
  padding: 3rem 2rem 2rem 2rem;
  position: relative;
  z-index: 2;
}

.booking-success-profile-container .auth-icon-wrapper {
  margin-bottom: 1.5rem;
}

.booking-success-profile-container .auth-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.booking-success-profile-container .profile-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #065f46;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.booking-success-profile-container .profile-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* Content */
.booking-success-profile-container .profile-content {
  padding: 0 2rem 2rem 2rem;
  position: relative;
  z-index: 2;
}

.booking-success-profile-container .profile-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.booking-success-profile-container .section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #065f46;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Booking Summary Grid */
.booking-success-profile-container .booking-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.booking-success-profile-container .summary-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.booking-success-profile-container .summary-icon {
  color: #10b981;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.booking-success-profile-container .summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.booking-success-profile-container .summary-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.booking-success-profile-container .summary-value {
  font-size: 1rem;
  color: #1f2937;
  font-weight: 600;
}

/* Thank You Section */
.booking-success-profile-container .thank-you-message {
  font-size: 1.1rem;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  text-align: center;
}

.booking-success-profile-container .success-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.booking-success-profile-container .feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.booking-success-profile-container .feature-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.booking-success-profile-container .feature-text {
  font-size: 0.95rem;
  color: #374151;
  font-weight: 500;
}

/* Action Buttons */
.booking-success-profile-container .action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.booking-success-profile-container .btn-primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  min-width: 160px;
}

.booking-success-profile-container .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  color: white;
  text-decoration: none;
}

.booking-success-profile-container .btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #10b981;
  border: 2px solid #10b981;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-width: 160px;
}

.booking-success-profile-container .btn-secondary:hover {
  background: #10b981;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
  text-decoration: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .booking-success-profile-container .profile-container {
    margin: 0 0.5rem;
    border-radius: 20px;
  }
  
  .booking-success-profile-container .profile-header {
    padding: 2rem 1.5rem 1.5rem 1.5rem;
  }

  .booking-success-profile-container .profile-title {
    font-size: 2rem;
  }

  .booking-success-profile-container .auth-icon {
    width: 70px;
    height: 70px;
    font-size: 2rem;
  }

  .booking-success-profile-container .booking-summary-grid {
    grid-template-columns: 1fr;
  }

  .booking-success-profile-container .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .booking-success-profile-container .btn-primary,
  .booking-success-profile-container .btn-secondary {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .booking-success-profile-container .profile-content {
    padding: 0 1rem 1.5rem 1rem;
  }

  .booking-success-profile-container .profile-header {
    padding: 1.5rem 1rem 1rem 1rem;
  }

  .booking-success-profile-container .profile-title {
    font-size: 1.75rem;
  }

  .booking-success-profile-container .profile-subtitle {
    font-size: 1rem;
  }

  .booking-success-profile-container .section-title {
    font-size: 1.1rem;
  }

  .booking-success-profile-container .summary-item {
    padding: 0.75rem;
  }

  .booking-success-profile-container .feature-item {
    padding: 0.5rem;
  }
} 