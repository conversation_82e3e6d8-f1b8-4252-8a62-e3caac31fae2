import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { FaCheckCircle, FaCalendarAlt, FaMapMarkerAlt, FaUser, FaClock } from 'react-icons/fa';
import './BookingSuccess.css';

const BookingSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [bookingData, setBookingData] = useState(null);

  useEffect(() => {
    // Get booking data from location state
    const data = location.state?.bookingData;
    if (data) {
      setBookingData(data);
    }
  }, [location.state]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="booking-success-profile-container">
      <div className="profile-container">
        {/* Floating Effects */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">✨</span>
          <span className="auth-sparkle">💫</span>
          <span className="auth-sparkle">⭐</span>
        </div>

        {/* Header Section */}
        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">🎉</div>
          </div>
          <h1 className="profile-title">Booking Confirmed!</h1>
          <p className="profile-subtitle">Your appointment has been successfully booked</p>
        </div>

        {/* Content Area */}
        <div className="profile-content">
          {/* Booking Summary Section */}
          {bookingData && (
            <div className="profile-section">
              <h3 className="section-title">📋 Booking Summary</h3>
              <div className="booking-summary-grid">
                <div className="summary-item">
                  <FaUser className="summary-icon" />
                  <div className="summary-content">
                    <span className="summary-label">Customer</span>
                    <span className="summary-value">{bookingData.userName || 'You'}</span>
                  </div>
                </div>
                
                <div className="summary-item">
                  <FaMapMarkerAlt className="summary-icon" />
                  <div className="summary-content">
                    <span className="summary-label">Salon</span>
                    <span className="summary-value">{bookingData.salon || 'Selected Salon'}</span>
                  </div>
                </div>

                <div className="summary-item">
                  <FaCalendarAlt className="summary-icon" />
                  <div className="summary-content">
                    <span className="summary-label">Date</span>
                    <span className="summary-value">{bookingData.date || 'Selected Date'}</span>
                  </div>
                </div>

                <div className="summary-item">
                  <FaClock className="summary-icon" />
                  <div className="summary-content">
                    <span className="summary-label">Time</span>
                    <span className="summary-value">{bookingData.time || 'Selected Time'}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Thank You Section */}
          <div className="profile-section">
            <h3 className="section-title">💝 Thank You</h3>
            <p className="thank-you-message">
              Thank you for choosing SalonGenz. We're excited to provide you with an amazing salon experience!
            </p>
            <div className="success-features">
              <div className="feature-item">
                <span className="feature-icon">📧</span>
                <span className="feature-text">Confirmation email sent</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📱</span>
                <span className="feature-text">SMS reminder before appointment</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">⭐</span>
                <span className="feature-text">Premium booking experience</span>
              </div>
            </div>
          </div>

          {/* Action Buttons Section */}
          <div className="profile-section">
            <div className="action-buttons">
              <Link to="/my-bookings" className="btn-primary">
                View My Bookings
              </Link>
              <Link to="/" className="btn-secondary">
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingSuccess;
