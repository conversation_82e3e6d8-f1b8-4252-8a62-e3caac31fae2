/* User Bookings - Profile Design Pattern */

/* Main Container - Profile Pattern */
.user-bookings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.user-bookings-container .profile-container {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header - Profile Pattern */
.user-bookings-container .profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem 1rem;
  text-align: center;
  color: white;
}

.user-bookings-container .profile-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.user-bookings-container .profile-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* Content Area - Profile Pattern */
.user-bookings-container .profile-content {
  padding: 1.5rem 1rem;
}

/* Sections - Profile Pattern */
.user-bookings-container .profile-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.user-bookings-container .section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Back Button */
.user-bookings-container .back-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-bookings-container .back-button:hover {
  background: #5a67d8;
}

/* Empty State */
.user-bookings-container .empty-state {
  text-align: center;
  padding: 2rem 1rem;
}

.user-bookings-container .empty-icon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.user-bookings-container .empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.user-bookings-container .empty-subtitle {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

/* Action Buttons */
.user-bookings-container .action-buttons {
  text-align: right;
}

.user-bookings-container .btn-primary {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.user-bookings-container .btn-primary:hover {
  background: #5a67d8;
}

/* Booking Cards */
.user-bookings-container .booking-cards-container {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.user-bookings-container .booking-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-bookings-container .booking-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-bookings-container .booking-card-body {
  padding: 1.5rem;
}

.user-bookings-container .booking-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.user-bookings-container .booking-details {
  margin-bottom: 1rem;
}

.user-bookings-container .booking-field {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.4;
}

.user-bookings-container .booking-field strong {
  color: #1f2937;
  font-weight: 600;
}

/* Status Badges */
.user-bookings-container .status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 0.5rem;
}

.user-bookings-container .status-success {
  background: #d1fae5;
  color: #065f46;
}

.user-bookings-container .status-danger {
  background: #fee2e2;
  color: #991b1b;
}

.user-bookings-container .status-primary {
  background: #dbeafe;
  color: #1e40af;
}

.user-bookings-container .status-warning {
  background: #fef3c7;
  color: #92400e;
}

.user-bookings-container .gift-badge {
  background: #d1fae5;
  color: #065f46;
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Booking Actions */
.user-bookings-container .booking-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.user-bookings-container .btn-cancel {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-bookings-container .btn-cancel:hover {
  background: #dc2626;
}

/* Pagination */
.user-bookings-container .pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.user-bookings-container .pagination-mobile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-bookings-container .pagination-desktop {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-bookings-container .pagination-btn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-bookings-container .pagination-btn:hover:not(.disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.user-bookings-container .pagination-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.user-bookings-container .pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-bookings-container .pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.user-bookings-container .pagination-ellipsis {
  padding: 0.5rem 0.25rem;
  color: #9ca3af;
}

/* Mobile FAB */
.user-bookings-container .mobile-fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.user-bookings-container .mobile-fab {
  background: #667eea;
  color: white;
  border: none;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  font-size: 1.25rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-bookings-container .mobile-fab:hover {
  background: #5a67d8;
  transform: scale(1.1);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .user-bookings-container {
    padding: 0.75rem 0.25rem;
  }

  .user-bookings-container .profile-container {
    border-radius: 12px;
  }

  .user-bookings-container .profile-header {
    padding: 1rem 0.75rem;
  }

  .user-bookings-container .profile-title {
    font-size: 1.25rem;
  }

  .user-bookings-container .profile-content {
    padding: 1rem 0.75rem;
  }

  .user-bookings-container .action-buttons {
    text-align: center;
  }

  .user-bookings-container .pagination-desktop {
    display: none;
  }
}

@media (max-width: 480px) {
  .user-bookings-container .booking-card-body {
    padding: 1rem;
  }

  .user-bookings-container .booking-title {
    font-size: 1rem;
  }

  .user-bookings-container .mobile-fab-container {
    bottom: 1rem;
    right: 1rem;
  }

  .user-bookings-container .mobile-fab {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }
}

/* Hide mobile FAB on desktop */
@media (min-width: 768px) {
  .user-bookings-container .mobile-fab-container {
    display: none;
  }

  .user-bookings-container .pagination-mobile {
    display: none;
  }
}
