import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useNotification } from '../../context/NotificationContext';
import { useAuth } from '../../context/AuthContext';
import { useIsMobile } from '../../context/ResponsiveContext';
import './UserBookings.css';

const UserBookings = () => {
  const { showNotification } = useNotification();
  const { authFetch, user } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [userBookings, setUserBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(isMobile ? 2 : 4);

  // Determine user role and page configuration
  const isVendor = user && user.is_vendor;
  const isAdmin = user && user.is_superuser;
  
  const getPageConfig = () => {
    if (isAdmin) {
      return {
        title: 'All System Bookings',
        subtitle: 'Manage all bookings across all salons',
        emptyMessage: 'No bookings found in the system.',
        canCancel: true
      };
    } if (isVendor) {
      return {
        title: 'Salon Bookings',
        subtitle: 'All bookings for your salon(s)',
        emptyMessage: 'No bookings found for your salon.',
        canCancel: true
      };
    } 
    return {
      title: 'My Bookings',
      subtitle: 'Your personal booking history',
      emptyMessage: 'You haven\'t made any bookings yet.',
      canCancel: true
    };
  };

  const pageConfig = getPageConfig();

  // Fetch user bookings from API
  const fetchUserBookings = async () => {
    setLoading(true);
    setError(null);
    try {
      // If vendor, use vendor bookings endpoint
      const isVendor = user && user.is_vendor;
      let endpoint;
      
      if (isVendor) {
        endpoint = '/api/vendor/bookings/';
      } else {
        // Use the new user-specific bookings endpoint
        endpoint = `/api/user/bookings/?user_id=${user?.userId || user?.id}`;
      }
      
      const res = await authFetch(endpoint);
      if (!res.ok) {
        throw new Error('Failed to fetch bookings');
      }
      const data = await res.json();
      setUserBookings(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserBookings();
  }, []);

  useEffect(() => {
    setPerPage(isMobile ? 2 : 4);
    setCurrentPage(1); // Reset to first page when switching views
  }, [isMobile]);

  // Helper to get CSRF token
  const getCookie = (name) => {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (`${name}=`)) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  };

  const handleCancelBooking = async (bookingId) => {
    try {
      const res = await fetch(`/api/bookings/${bookingId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify({ status: 'Cancelled' }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.detail || 'Failed to cancel booking');
      }

      showNotification('Booking cancelled successfully!', 'success');
      fetchUserBookings(); // Refresh the list

      const data = await res.json();
      // Filter bookings to only those belonging to the current user
      const filtered = data.filter(
        booking => booking.userId === (user?.userId || user?.id || user?.username || 'guest')
      );
      setUserBookings(filtered);
    } catch (err) {
      showNotification(`Cancellation failed: ${err.message}`, 'danger');
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(userBookings.length / perPage);
  const paginatedBookings = userBookings.slice((currentPage - 1) * perPage, currentPage * perPage);

  // Helper for scalable pagination (desktop)
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i++) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  // Render logic
  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">
            Loading
            {pageConfig.title.toLowerCase()}
            ...
          </p>
        </div>
      </div>
    ); 
  }

  if (error) {
    return (
      <div className="container mt-5">
        <div className="alert alert-danger text-center">
          <h4>Error Loading Bookings</h4>
          <p>{error}</p>
          <button onClick={fetchUserBookings} className="btn btn-primary">
            Retry
          </button>
        </div>
      </div>
    ); 
  }

  return (
    <div className="user-bookings-container">
      <div className="profile-container">
        {/* Header */}
        <div className="profile-header">
          <h1 className="profile-title">📅 {pageConfig.title}</h1>
          <p className="profile-subtitle">{pageConfig.subtitle}</p>
        </div>

        <div className="profile-content">
          {/* Navigation */}
          <div className="profile-section">
            <button className="back-button" onClick={() => navigate(-1)}>
              <i className="bi bi-arrow-left me-2" />
              Back
            </button>
          </div>

          {userBookings.length === 0 ? (
            <div className="profile-section">
              <div className="empty-state">
                <div className="empty-icon">
                  <i className="bi bi-calendar-x" />
                </div>
                <h4 className="empty-title">{pageConfig.emptyMessage}</h4>
                <p className="empty-subtitle">{pageConfig.subtitle}</p>
                {!isVendor && !isAdmin && (
                  <a href="/" className="btn-primary">
                    Book Your First Appointment
                  </a>
                )}
              </div>
            </div>
          ) : (
            <>
              {!isVendor && !isAdmin && (
                <div className="profile-section">
                  <div className="action-buttons">
                    <button className="btn-primary" onClick={() => navigate('/book')}>
                      <i className="bi bi-calendar-plus me-2" />
                      Book Your Appointment
                    </button>
                  </div>
                </div>
              )}

              <div className="profile-section">
                <h3 className="section-title">📋 Your Bookings</h3>
                <div className="booking-cards-container">
                  {paginatedBookings.map((booking) => (
                    <div key={booking.id} className="booking-card">
                      <div className="booking-card-body">
                        <h5 className="booking-title">{booking.salon?.name || booking.salon_name || 'Salon Name'}</h5>
                        <div className="booking-details">
                          <div className="booking-info">
                            <p className="booking-field">
                              <strong>Service:</strong>
                              {' '}
                              {booking.service?.name || booking.service_name || 'Service Name'}
                            </p>
                            <p className="booking-field">
                              <strong>Date:</strong>
                              {' '}
                              {new Date(booking.date).toLocaleDateString()}
                            </p>
                            <p className="booking-field">
                              <strong>Time:</strong>
                              {' '}
                              {booking.time}
                            </p>
                            <p className="booking-field">
                              <strong>Staff:</strong>
                              {' '}
                              {booking.staff?.name || booking.staff_name || 'Staff Name'}
                            </p>
                            <p className="booking-field">
                              <strong>Price:</strong>
                              {' '}
                              Ksh
                              {' '}
                              {booking.service?.price || 'N/A'}
                            </p>
                            {booking.notes && (
                              <p className="booking-field">
                                <strong>Notes:</strong>
                                {' '}
                                {booking.notes}
                              </p>
                            )}
                            {booking.is_gift && (
                              <p className="booking-field">
                                <strong>Gift Booking:</strong>
                                {' '}
                                <span className="gift-badge">Yes</span>
                              </p>
                            )}
                            <p className="booking-field">
                              <strong>Status:</strong>
                              <span className={`status-badge ${
                                booking.status === 'Completed' ? 'status-success'
                                  : booking.status === 'Cancelled' ? 'status-danger'
                                    : booking.status === 'Confirmed' ? 'status-primary'
                                      : 'status-warning'
                              }`}
                              >
                                {booking.status}
                              </span>
                            </p>
                          </div>

                          {booking.status !== 'Cancelled' && booking.status !== 'Completed' && pageConfig.canCancel && (
                            <div className="booking-actions">
                              <button
                                onClick={() => handleCancelBooking(booking.id)}
                                className="btn-cancel"
                              >
                                Cancel Booking
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="profile-section">
                  <div className="pagination-container">
                    {isMobile ? (
                      <div className="pagination-mobile">
                        <button className="pagination-btn" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>&laquo;</button>
                        <span className="pagination-info">
                          Page {currentPage} of {totalPages}
                        </span>
                        <button className="pagination-btn" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>&raquo;</button>
                      </div>
                    ) : (
                      <div className="pagination-desktop">
                        <button className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`} onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>&laquo;</button>
                        {getPageNumbers().map((num, idx) => (num === 'ellipsis-left' || num === 'ellipsis-right' ? (
                          <span key={num + idx} className="pagination-ellipsis">&hellip;</span>
                        ) : (
                          <button key={num} className={`pagination-btn ${currentPage === num ? 'active' : ''}`} onClick={() => setCurrentPage(num)}>{num}</button>
                        )))}
                        <button className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`} onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>&raquo;</button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Mobile FAB for Book Appointment - only for regular customers */}
          {!isVendor && !isAdmin && (
            <div className="mobile-fab-container">
              <button
                className="mobile-fab"
                onClick={() => navigate('/book')}
                aria-label="Book Appointment"
              >
                <i className="bi bi-calendar-plus" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserBookings;
