import React, { useState } from 'react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';

const CalendarView = () => {
  const [date, setDate] = useState(new Date());

  const onChange = (newDate) => {
    setDate(newDate);
  };

  return (
    <div className="container mt-5">
      <div className="glam-card p-4">
        <h2 className="card-title text-center mb-4 text-light">Calendar View</h2>
        <div className="d-flex justify-content-center">
          <Calendar
            onChange={onChange}
            value={date}
            className="react-calendar-custom"
          />
        </div>
        <p className="text-center mt-3 text-light">
          Selected Date: 
          {' '}
          {date.toDateString()}
        </p>
        {/* Here you would display available slots for the selected date */}
        <div className="mt-4">
          <h4 className="text-center text-light">
            Available Slots for
            {date.toDateString()}
          </h4>
          <ul className="list-group">
            <li className="list-group-item d-flex justify-content-between align-items-center">
              <span className="text-light">10:00 AM - Haircut with Alice</span>
              <button className="btn btn-sm btn-primary glam-btn">Book</button>
            </li>
            <li className="list-group-item d-flex justify-content-between align-items-center">
              <span className="text-light">11:00 AM - Manicure with Bob</span>
              <button className="btn btn-sm btn-primary glam-btn">Book</button>
            </li>
            <li className="list-group-item d-flex justify-content-between align-items-center">
              <span className="text-light">02:00 PM - Facial with Charlie</span>
              <button className="btn btn-sm btn-primary glam-btn">Book</button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CalendarView;
