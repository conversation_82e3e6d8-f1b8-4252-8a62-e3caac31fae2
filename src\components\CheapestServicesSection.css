/* Cheapest Services Section - Mobile First Design with Wow Animations */

.cheapest-services-section {
  padding: 2rem 1rem;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
  border-radius: 20px;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.cheapest-services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.01)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.cheapest-services-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

/* Section Header */
.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3498db, #2980b9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
  font-size: 1rem;
  color: #bdc3c7;
  margin: 0;
  font-weight: 400;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1rem;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.error-text {
  color: #e74c3c;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

/* Services Grid - Modest & Sleek */
.services-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

/* Service Card */
.service-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.cheapest-card {
  border-color: rgba(52, 152, 219, 0.3);
}

.service-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

/* Staggered animation delays for service cards */
.service-card:nth-child(1) { animation-delay: 0.1s; }
.service-card:nth-child(2) { animation-delay: 0.2s; }
.service-card:nth-child(3) { animation-delay: 0.3s; }
.service-card:nth-child(4) { animation-delay: 0.4s; }

.cheapest-card:hover {
  border-color: rgba(52, 152, 219, 0.5);
  box-shadow: 0 10px 30px rgba(52, 152, 219, 0.2);
}

.card-image-container {
  position: relative;
  height: 100px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.service-card:hover .card-image {
  transform: scale(1.08);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), transparent);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
}

.price-badge {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.price-icon {
  font-size: 0.9rem;
}

.card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: calc(100% - 120px);
  min-height: 180px;
}

.service-name {
  font-size: 1rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.service-salon {
  color: #bdc3c7;
  font-size: 0.8rem;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.service-salon::before {
  content: '🏪';
  font-size: 0.8rem;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: auto;
  margin-top: 0.5rem;
  gap: 0.5rem;
  flex-shrink: 0;
}

.service-rating {
  color: #f39c12;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.service-status {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.cheapest-status {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.view-details-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  width: 100%;
  margin-top: 0.75rem;
  flex-shrink: 0;
}

.cheapest-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.view-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
  color: white;
  text-decoration: none;
}

.cheapest-btn:hover {
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
  color: white;
}

.btn-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.view-details-btn:hover .btn-icon {
  transform: translateX(3px);
}

/* Show More Button */
.show-more-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.show-more-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
  border-radius: 25px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.show-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
}

.show-more-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.show-more-btn:hover .show-more-icon {
  transform: translateY(2px);
}

/* View All Button */
.view-all-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.view-all-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border: none;
  border-radius: 25px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
}

.view-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(155, 89, 182, 0.4);
  color: white;
  text-decoration: none;
}

.view-all-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.view-all-btn:hover .view-all-icon {
  transform: translateX(3px);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
}

.empty-state h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.empty-state p {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .cheapest-services-section {
    padding: 3rem 2rem;
  }

  .section-header {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .cheapest-services-section {
    padding: 4rem 3rem;
  }

  .section-title {
    font-size: 3rem;
  }

  .services-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .card-image-container {
    height: 140px;
  }
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .cheapest-services-container {
    max-width: 1400px;
  }

  .services-grid {
    gap: 2rem;
  }
}

/* Landscape Mode */
@media (max-height: 500px) and (orientation: landscape) {
  .cheapest-services-section {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .card-image-container {
    height: 150px;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .service-card {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Wow Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-8px);
  }
  70% {
    transform: translateY(-4px);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .service-card,
  .view-details-btn,
  .show-more-btn,
  .view-all-btn {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }

  .card-image {
    transition: none;
  }

  .btn-icon {
    transition: none;
  }
}

/* Focus States for Accessibility */
.service-card:focus-within {
  outline: 3px solid rgba(52, 152, 219, 0.5);
  outline-offset: 2px;
}

.view-details-btn:focus,
.show-more-btn:focus,
.view-all-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .service-card:hover {
    transform: none;
  }

  .view-details-btn:hover,
  .show-more-btn:hover,
  .view-all-btn:hover {
    transform: none;
  }
}

/* Mobile Optimization - Extra Small Screens */
@media (max-width: 320px) {
  .cheapest-services-section {
    padding: 1.5rem 0.5rem;
  }

  .services-grid {
    gap: 0.75rem;
    padding: 0 0.25rem;
  }

  .card-image-container {
    height: 80px;
  }

  .service-card {
    border-radius: 12px;
  }

  .card-content {
    padding: 0.75rem;
  }

  .service-name {
    font-size: 0.9rem;
    line-height: 1.2;
  }

  .service-salon {
    font-size: 0.75rem;
  }

  .service-meta {
    gap: 0.5rem;
    margin: 0.5rem 0;
  }

  .service-price,
  .service-duration {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .view-details-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 0 0.25rem;
  }

  .service-card {
    border-radius: 16px;
  }

  .card-image-container {
    height: 100px;
  }

  .card-content {
    padding: 0.75rem;
    min-height: 160px;
  }

  .service-name {
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
  }

  .service-salon {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .service-meta {
    margin-top: 0.4rem;
    gap: 0.4rem;
  }

  .service-rating {
    font-size: 0.7rem;
  }

  .service-status {
    font-size: 0.6rem;
    padding: 0.15rem 0.4rem;
  }

  .view-details-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.75rem;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 0.6rem;
    min-height: 150px;
  }

  .service-name {
    font-size: 0.85rem;
  }

  .service-salon {
    font-size: 0.7rem;
  }

  .view-details-btn {
    padding: 0.5rem 0.7rem;
    font-size: 0.7rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .cheapest-services-section {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.15), rgba(41, 128, 185, 0.15));
  }
} 