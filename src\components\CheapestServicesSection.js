import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './CheapestServicesSection.css';
import UniversalPagination from './UniversalPagination';

const CheapestServicesSection = () => {
  const { authFetch } = useAuth();
  const [cheapestServices, setCheapestServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // Replace the one-liner getPerPage with a multi-line version for max-len compliance
  const getPerPage = () => {
    if (window.innerWidth <= 480) {
      return 2;
    }
    return 4;
  };
  const [perPage, setPerPage] = useState(getPerPage());
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    const handleResize = () => setPerPage(getPerPage());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalPages = Math.ceil(cheapestServices.length / perPage);
  const paginatedServices = cheapestServices.slice(
    (currentPage - 1) * perPage,
    currentPage * perPage
  );
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i += 1) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i += 1) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  useEffect(() => {
    const fetchCheapestServices = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await authFetch('/api/recommendations/?type=cheap');
        if (!response.ok) throw new Error('Failed to fetch cheapest services');
        const data = await response.json();
        // Assuming the data returned is a list of Recommendation objects
        // We need to extract the Service objects from these recommendations
        const serviceIds = data.map(rec => rec.object_id);
        // Fetch service details for these IDs
        const serviceDetailsPromises = serviceIds.map(
          id => authFetch(`/api/services/${id}/`).then(serviceRes => serviceRes.json())
        );
        const serviceDetails = await Promise.all(serviceDetailsPromises);
        setCheapestServices(serviceDetails);
      } catch (err) {
        setError('Could not load cheapest services.');
      }
      setLoading(false);
    };
    fetchCheapestServices();
  }, [authFetch]);

  return (
    <section className="cheapest-services-section">
      <div className="cheapest-services-container">
        {/* Section Header */}
        <div className="section-header">
          <div className="header-content">
            <h2 className="section-title">Best Value Services</h2>
            <p className="section-subtitle">Quality services at unbeatable prices</p>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="loading-state">
            <div className="loading-spinner" />
            <p className="loading-text">Loading best value services...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="error-state">
            <div className="error-icon">⚠️</div>
            <p className="error-text">{error}</p>
          </div>
        )}

        {/* Services Grid */}
        {!loading && !error && paginatedServices.length > 0 && (
          <div className="services-grid">
            {paginatedServices.map(service => (
              <div key={service.id} className="service-card cheapest-card">
                <div className="card-image-container">
                  <img
                    src={service.imageUrl || service.image_url || 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop&auto=format'}
                    className="card-image"
                    alt={`${service.name} - Best Value Service`}
                    loading="lazy"
                    onError={(e) => {
                      e.target.src = 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop&auto=format';
                    }}
                    sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 25vw"
                  />
                  <div className="card-overlay">
                    <div className="price-badge animate-bounce">
                      <span className="price-icon">💰</span>
                      Ksh
                      {' '}
                      {service.price}
                    </div>
                  </div>
                </div>
                <div className="card-content">
                  <h3 className="service-name">{service.name}</h3>
                  <p className="service-salon">{service.salon_name}</p>
                  <div className="service-meta">
                    <span className="service-rating">⭐ 4.6</span>
                    <span className="service-status cheapest-status">Best Value</span>
                  </div>
                  <Link to={`/service/${service.id}`} className="view-details-btn cheapest-btn">
                    View Service
                    <span className="btn-icon">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Universal Pagination */}
        <UniversalPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          variant="default"
          className="cheapest-services-pagination"
        />

        {/* Empty State */}
        {!loading && !error && cheapestServices.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">💸</div>
            <h3>No Best Value Services</h3>
            <p>Check back later for amazing deals on salon services.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default CheapestServicesSection;
