import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaCheckCircle, FaCalendarAlt, FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa';
import PricingDisplay from './PricingDisplay';
import './CheckoutSuccess.css';

const CheckoutSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [bookingData, setBookingData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get booking data from location state or localStorage
    const data = location.state?.bookingData || JSON.parse(localStorage.getItem('lastBooking'));
    if (data) {
      setBookingData(data);
      // Store in localStorage for persistence
      localStorage.setItem('lastBooking', JSON.stringify(data));
    }
    setLoading(false);
  }, [location.state]);

  if (loading) {
    return (
      <div className="checkout-success-container">
        <div className="checkout-success-card">
          <div className="loading-spinner"></div>
          <p>Loading booking details...</p>
        </div>
      </div>
    );
  }

  if (!bookingData) {
    return (
      <div className="checkout-success-container">
        <div className="checkout-success-card">
          <div className="error-message">
            <h3>Booking Not Found</h3>
            <p>We couldn't find your booking details. Please check your email or contact support.</p>
            <button className="btn-primary" onClick={() => navigate('/')}>
              Return to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString) => {
    return timeString;
  };

  return (
    <div className="checkout-success-container">
      <div className="checkout-success-card">
        {/* Success Header */}
        <div className="success-header">
          <div className="success-icon">
            <FaCheckCircle />
          </div>
          <h1 className="success-title">Booking Confirmed! 🎉</h1>
          <p className="success-subtitle">
            Your appointment has been successfully booked. We've sent you a confirmation email.
          </p>
        </div>

        {/* Booking Summary */}
        <div className="booking-summary">
          <h3 className="summary-title">📋 Booking Summary</h3>
          <div className="summary-grid">
            <div className="summary-item">
              <FaCalendarAlt className="summary-icon" />
              <div className="summary-content">
                <span className="summary-label">Date & Time</span>
                <span className="summary-value">
                  {formatDate(bookingData.appointment_date)} at {formatTime(bookingData.appointment_time)}
                </span>
              </div>
            </div>
            
            <div className="summary-item">
              <FaMapMarkerAlt className="summary-icon" />
              <div className="summary-content">
                <span className="summary-label">Salon</span>
                <span className="summary-value">{bookingData.salon_name || 'Selected Salon'}</span>
              </div>
            </div>

            <div className="summary-item">
              <FaCheckCircle className="summary-icon" />
              <div className="summary-content">
                <span className="summary-label">Service</span>
                <span className="summary-value">{bookingData.service_name || 'Selected Service'}</span>
              </div>
            </div>

            <div className="summary-item">
              <FaPhone className="summary-icon" />
              <div className="summary-content">
                <span className="summary-label">Contact</span>
                <span className="summary-value">{bookingData.customer_phone || 'Your Phone'}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Summary */}
        {bookingData.pricingData && (
          <div className="pricing-summary">
            <h3 className="pricing-title">💰 Payment Summary</h3>
            <PricingDisplay 
              service={bookingData.pricingData.breakdown.service}
              options={bookingData.pricingData.options || {}}
              showBreakdown={false}
              compact={true}
            />
          </div>
        )}

        {/* Next Steps */}
        <div className="next-steps">
          <h3 className="steps-title">📅 What's Next?</h3>
          <div className="steps-list">
            <div className="step-item">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Confirmation Email</h4>
                <p>Check your email for booking confirmation and salon details</p>
              </div>
            </div>
            
            <div className="step-item">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Salon Reminder</h4>
                <p>You'll receive a reminder 24 hours before your appointment</p>
              </div>
            </div>
            
            <div className="step-item">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Arrive on Time</h4>
                <p>Please arrive 10 minutes before your scheduled time</p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="contact-info">
          <h3 className="contact-title">📞 Need Help?</h3>
          <div className="contact-grid">
            <div className="contact-item">
              <FaEnvelope className="contact-icon" />
              <div className="contact-content">
                <span className="contact-label">Email Support</span>
                <span className="contact-value"><EMAIL></span>
              </div>
            </div>
            
            <div className="contact-item">
              <FaPhone className="contact-icon" />
              <div className="contact-content">
                <span className="contact-label">Phone Support</span>
                <span className="contact-value">+254 700 000 000</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons">
          <button className="btn-secondary" onClick={() => navigate('/my-bookings')}>
            View My Bookings
          </button>
          <button className="btn-primary" onClick={() => navigate('/')}>
            Book Another Service
          </button>
        </div>

        {/* Booking ID */}
        <div className="booking-id">
          <p>Booking ID: <strong>{bookingData.id || 'N/A'}</strong></p>
          <p className="booking-note">
            Keep this ID handy for any inquiries about your booking
          </p>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSuccess; 