import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const Footer = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isBottomNavVisible, setIsBottomNavVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Scroll detection for hiding/showing bottom nav
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Hide bottom nav when scrolling down, show when scrolling up
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsBottomNavVisible(false);
      } else if (currentScrollY < lastScrollY) {
        setIsBottomNavVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <>
      {/* Scroll-Aware Mobile Bottom Nav */}
      <nav className={`mobile-bottom-nav d-md-none d-lg-none ${isBottomNavVisible ? 'visible' : 'hidden'}`}>
        <button
          className={location.pathname === '/' ? 'active' : ''}
          onClick={() => navigate('/')}
          aria-label="Home"
        >
          <i className="bi bi-house-door-fill" />
          <span>Home</span>
        </button>
        <button
          className={location.pathname.startsWith('/ai-features') ? 'active' : ''}
          onClick={() => navigate('/ai-features')}
          aria-label="AI Vibes"
        >
          <i className="bi bi-stars" />
          <span>AI Vibes</span>
        </button>
        <button
          className={location.pathname.startsWith('/my-bookings') ? 'active' : ''}
          onClick={() => navigate('/my-bookings')}
          aria-label="Bookings"
        >
          <i className="bi bi-calendar-check-fill" />
          <span>Bookings</span>
        </button>
        <button
          className={location.pathname.startsWith('/profile') ? 'active' : ''}
          onClick={() => navigate('/profile')}
          aria-label="Profile"
        >
          <i className="bi bi-person-fill" />
          <span>Portfolio</span>
        </button>
      </nav>
      {/* Gen Z Mobile-First Footer */}
      <footer className="genz-footer-container">
        <div className="footer-container">
          {/* Mobile: Minimal footer with just essentials */}
          <div className="mobile-footer d-md-none">
            <div className="footer-content">
              {/* Developer Credit - Left Side */}
              <div className="footer-section dev-credit">
                <a href="https://codegx.com" target="_blank" rel="noopener noreferrer" className="dev-link">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#00e6d0" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" fill="rgba(0, 230, 208, 0.15)" />
                    <path d="M2 17L12 22L22 17" stroke="#00e6d0" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M2 12L12 17L22 12" stroke="#00e6d0" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <span>CODEGX TECHNOLOGIES</span>
                </a>
              </div>

              {/* Copyright - Center */}
              <div className="footer-section copyright">
                <span>&copy; 2025 SalonGenz</span>
              </div>

              {/* Legal Links - Right Side */}
              <div className="footer-section legal-icons">
                <button
                  onClick={() => navigate('/privacy')}
                  className="legal-icon"
                  title="Privacy & Security"
                  aria-label="Privacy Policy"
                >
                  <i className="bi bi-shield-check"></i>
                </button>
                <button
                  onClick={() => navigate('/terms')}
                  className="legal-icon"
                  title="Terms & Conditions"
                  aria-label="Terms of Service"
                >
                  <i className="bi bi-file-text"></i>
                </button>
                <button
                  onClick={() => navigate('/policy')}
                  className="legal-icon"
                  title="Cookie Settings"
                  aria-label="Cookie Policy"
                >
                  <i className="bi bi-gear"></i>
                </button>
              </div>
            </div>
          </div>

          {/* Desktop: Enhanced Corporate Footer */}
          <div className="desktop-footer d-none d-md-block text-white py-4">
            <div className="footer-desktop-content">
              <div className="footer-section copyright-section">
                <span className="copyright-text">✨ &copy; 2025 SalonGenz. All Rights Reserved. ✨</span>
              </div>
              <div className="footer-section dev-section">
                <a href="https://codegx.com" target="_blank" rel="noopener noreferrer" className="dev-link-desktop">
                  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#00e6d0" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" fill="rgba(0, 230, 208, 0.15)" />
                    <path d="M2 17L12 22L22 17" stroke="#00e6d0" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M2 12L12 17L22 12" stroke="#00e6d0" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <span>Developed by Codegx Technologies</span>
                </a>
              </div>
              <div className="footer-section legal-section">
                <a href="/privacy" className="legal-link">
                  <i className="bi bi-shield-check"></i>Privacy
                </a>
                <span className="separator">•</span>
                <a href="/terms" className="legal-link">
                  <i className="bi bi-file-text"></i>Terms
                </a>
                <span className="separator">•</span>
                <a href="/policy" className="legal-link">
                  <i className="bi bi-gear"></i>Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
