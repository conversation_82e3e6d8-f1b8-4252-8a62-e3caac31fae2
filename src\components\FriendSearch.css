/* Friend Search Modal Styles */

.friend-search-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 20px;
  border-radius: 20px;
}

.friend-search-modal {
  background: linear-gradient(135deg, rgba(248,225,255,0.95) 0%, rgba(255,230,179,0.95) 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: #333;
}

.close-btn {
  background: rgba(255, 255, 255, 0.3);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 1.2rem;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* Search Section */
.search-section {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #ffd700;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

.search-icon {
  position: absolute;
  left: 12px;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.5);
}

.searching-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
  color: #333;
  font-size: 0.9rem;
}

.search-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-top: 2px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Search Results */
.search-results {
  max-height: 300px;
  overflow-y: auto;
  padding: 0 20px;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-results-icon {
  font-size: 2rem;
  display: block;
  margin-bottom: 12px;
}

.no-results p {
  margin: 0 0 8px 0;
  font-weight: 600;
}

.no-results small {
  color: rgba(0, 0, 0, 0.5);
}

.friend-result {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.friend-result:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 12px;
  margin: 0 -12px;
}

.friend-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.friend-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.friend-details {
  flex: 1;
}

.friend-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 700;
  font-size: 0.95rem;
  color: #333;
  margin-bottom: 4px;
}

.verified-badge {
  color: #ffd700;
  font-size: 0.8rem;
}

.friend-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.location,
.mutual-friends {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}

.add-friend-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.add-friend-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
}

.add-friend-btn.sent {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  cursor: default;
}

.add-friend-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Suggestions Section */
.search-suggestions {
  padding: 20px;
}

.search-suggestions h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  font-weight: 700;
  color: #333;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.suggestion-card {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.suggestion-card:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
}

.suggestion-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  margin: 0 auto 8px auto;
  box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
}

.suggestion-name {
  font-weight: 600;
  font-size: 0.8rem;
  color: #333;
  margin-bottom: 4px;
}

.suggestion-mutual {
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 12px;
}

.suggestion-add-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestion-add-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.4);
}

.suggestion-add-btn.sent {
  background: linear-gradient(135deg, #4ade80, #22c55e);
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .friend-search-overlay {
    padding: 12px;
  }
  
  .friend-search-modal {
    max-height: 85vh;
  }
  
  .modal-header,
  .search-section,
  .search-suggestions {
    padding: 16px;
  }
  
  .search-results {
    padding: 0 16px;
  }
  
  .friend-avatar {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }
  
  .suggestions-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
  
  .suggestion-card {
    padding: 12px 8px;
  }
  
  .friend-meta {
    flex-direction: row;
    gap: 8px;
  }
}
