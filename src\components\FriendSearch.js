import React, { useState, useCallback, useMemo } from 'react';
import { toast } from 'react-toastify';
import GenZTooltip from './GenZTooltip';
import './FriendSearch.css';

const FriendSearch = ({ isOpen, onClose, onFriendAdded, currentUserId }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [sentRequests, setSentRequests] = useState({});

  // Mock user database for friend search
  const userDatabase = useMemo(() => [
    { id: 1, name: '<PERSON>', avatar: '💅', verified: true, mutualFriends: 5, location: 'NYC' },
    { id: 2, name: '<PERSON> K.', avatar: '🦋', verified: false, mutualFriends: 2, location: 'LA' },
    { id: 3, name: '<PERSON>', avatar: '✨', verified: true, mutualFriends: 8, location: 'Miami' },
    { id: 4, name: '<PERSON>', avatar: '🌟', verified: false, mutualFriends: 1, location: 'Chicago' },
    { id: 5, name: 'Bella R.', avatar: '🌸', verified: true, mutualFriends: 12, location: 'Seattle' },
    { id: 6, name: 'Jordan T.', avatar: '⚡', verified: false, mutualFriends: 3, location: 'Austin' },
    { id: 7, name: 'Mia L.', avatar: '✨', verified: true, mutualFriends: 6, location: 'Denver' },
    { id: 8, name: 'Tyler K.', avatar: '🌟', verified: false, mutualFriends: 4, location: 'Portland' },
    { id: 9, name: 'Chloe W.', avatar: '🦄', verified: true, mutualFriends: 15, location: 'SF' },
    { id: 10, name: 'Ethan P.', avatar: '🔥', verified: false, mutualFriends: 7, location: 'Boston' },
    { id: 11, name: 'Luna V.', avatar: '🌙', verified: true, mutualFriends: 9, location: 'Vegas' },
    { id: 12, name: 'Zara H.', avatar: '💎', verified: false, mutualFriends: 2, location: 'Atlanta' },
    { id: 13, name: 'River M.', avatar: '🌊', verified: true, mutualFriends: 11, location: 'Phoenix' },
    { id: 14, name: 'Nova S.', avatar: '⭐', verified: false, mutualFriends: 6, location: 'Dallas' },
    { id: 15, name: 'Sky L.', avatar: '☁️', verified: true, mutualFriends: 4, location: 'Nashville' }
  ], []);

  const handleSearch = useCallback(async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Filter users based on search query
    const results = userDatabase.filter(user => 
      user.id !== currentUserId && // Exclude current user
      (user.name.toLowerCase().includes(query.toLowerCase()) ||
       user.location.toLowerCase().includes(query.toLowerCase()))
    ).slice(0, 8); // Limit to 8 results
    
    setSearchResults(results);
    setIsSearching(false);
  }, [userDatabase, currentUserId]);

  const handleInputChange = useCallback((e) => {
    const query = e.target.value;
    setSearchQuery(query);
    handleSearch(query);
  }, [handleSearch]);

  const handleSendRequest = useCallback((userId, userName) => {
    setSentRequests(prev => ({ ...prev, [userId]: true }));
    
    // Simulate friend request
    toast.success(`Friend request sent to ${userName}! 👥`, {
      position: "top-center",
      autoClose: 2000,
    });
    
    // Haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
    
    if (onFriendAdded) {
      onFriendAdded(userId);
    }
  }, [onFriendAdded]);

  if (!isOpen) return null;

  return (
    <div className="friend-search-overlay" onClick={onClose}>
      <div className="friend-search-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Find Friends ✨</h3>
          <GenZTooltip text="Close friend search" position="left">
            <button className="close-btn" onClick={onClose}>×</button>
          </GenZTooltip>
        </div>
        
        <div className="search-section">
          <div className="search-input-container">
            <input
              type="text"
              placeholder="Search by name or location..."
              value={searchQuery}
              onChange={handleInputChange}
              className="search-input"
              autoFocus
            />
            <div className="search-icon">🔍</div>
          </div>
          
          {isSearching && (
            <div className="searching-indicator">
              <div className="search-spinner"></div>
              <span>Finding your vibes...</span>
            </div>
          )}
        </div>

        <div className="search-results">
          {searchQuery && !isSearching && searchResults.length === 0 && (
            <div className="no-results">
              <span className="no-results-icon">😔</span>
              <p>No friends found matching "{searchQuery}"</p>
              <small>Try searching by name or location</small>
            </div>
          )}
          
          {searchResults.map(user => (
            <div key={user.id} className="friend-result">
              <div className="friend-info">
                <div className="friend-avatar">{user.avatar}</div>
                <div className="friend-details">
                  <div className="friend-name">
                    {user.name}
                    {user.verified && <span className="verified-badge">✓</span>}
                  </div>
                  <div className="friend-meta">
                    <span className="location">📍 {user.location}</span>
                    <span className="mutual-friends">
                      👥 {user.mutualFriends} mutual friends
                    </span>
                  </div>
                </div>
              </div>
              
              <GenZTooltip 
                text={sentRequests[user.id] ? "Request sent! ✨" : "Send friend request"} 
                position="left"
              >
                <button
                  className={`add-friend-btn ${sentRequests[user.id] ? 'sent' : ''}`}
                  onClick={() => handleSendRequest(user.id, user.name)}
                  disabled={sentRequests[user.id]}
                >
                  {sentRequests[user.id] ? '✓ Sent' : '+ Add'}
                </button>
              </GenZTooltip>
            </div>
          ))}
        </div>

        {!searchQuery && (
          <div className="search-suggestions">
            <h4>Suggested Friends</h4>
            <div className="suggestions-grid">
              {userDatabase.slice(0, 6).map(user => (
                <div key={user.id} className="suggestion-card">
                  <div className="suggestion-avatar">{user.avatar}</div>
                  <div className="suggestion-name">{user.name}</div>
                  <div className="suggestion-mutual">{user.mutualFriends} mutual</div>
                  <GenZTooltip text="Add as friend" position="top">
                    <button
                      className={`suggestion-add-btn ${sentRequests[user.id] ? 'sent' : ''}`}
                      onClick={() => handleSendRequest(user.id, user.name)}
                      disabled={sentRequests[user.id]}
                    >
                      {sentRequests[user.id] ? '✓' : '+'}
                    </button>
                  </GenZTooltip>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FriendSearch;
