import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import axios from '../api/axiosInstance';
import styles from './FriendSearchModal.module.css';

export default function FriendSearchModal({ show, onClose, onFriendAdded, currentUserId }) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);

  if (!show) return null;

  const search = async () => {
    setSearching(true);
    setLoading(true);
    try {
      const res = await axios.get(`/search-users/?q=${encodeURIComponent(query)}`);
      setResults(res.data);
    } catch {
      toast.error('Search failed');
    }
    setLoading(false);
    setSearching(false);
  };

  const addFriend = async (userId) => {
    try {
      await axios.post('/friendships/', { from_user_id: currentUserId, to_user_id: userId });
      toast.success('Friend added!');
      setResults(results.filter(u => u.id !== userId));
      if (onFriendAdded) onFriendAdded();
    } catch {
      toast.error('Could not add friend');
    }
  };

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <button type="button" className={styles.closeButton} onClick={onClose}>&times;</button>
        <h2 className={styles.title}>Find Friends</h2>
        <div className={styles.searchBar}>
          <input
            value={query}
            onChange={e => setQuery(e.target.value)}
            placeholder="Search by username..."
            className={styles.input}
            onKeyDown={e => { if (e.key === 'Enter') search(); }}
          />
          <button
            type="button"
            onClick={search}
            disabled={loading || !query}
            className={styles.searchButton}
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
        <ul className={styles.resultsList}>
          {results.length === 0 && !searching && (
            <li className={styles.noResults}>
              No users found
            </li>
          )}
          {results.map(u => (
            <li key={u.id} className={styles.resultItem}>
              <span>
                {u.username}
                {' '}
                {u.first_name}
                {' '}
                {u.last_name}
              </span>
              <button type="button" onClick={() => addFriend(u.id)} className={styles.addButton}>Add Friend</button>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

FriendSearchModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onFriendAdded: PropTypes.func,
  currentUserId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number
  ]).isRequired
};

FriendSearchModal.defaultProps = {
  onFriendAdded: () => { },
}; 
