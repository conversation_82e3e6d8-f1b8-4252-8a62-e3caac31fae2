.modalOverlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(60, 0, 120, 0.10);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  pointer-events: auto;
}
.modalContent {
  background: linear-gradient(135deg, #f8a5c2 0%, #a18cd1 100%);
  border-radius: 18px 18px 0 0;
  box-shadow: 0 8px 32px rgba(160,140,209,0.18), 0 2px 8px #f472b633;
  padding: 2rem 2.5rem 2rem 2.5rem;
  min-width: 340px;
  max-width: 400px;
  min-height: 340px;
  max-height: 90vh;
  position: relative;
  margin: 0 2.5rem 2.5rem 0;
  animation: popIn 0.2s cubic-bezier(.4,2,.6,1) 1;
  border: 1.5px solid #fff0fa;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
@keyframes popIn {
  from { transform: translateY(40px) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}
.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  color: #7c3aed;
  cursor: pointer;
}
.title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 1.2rem;
  text-align: center;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 8px #a18cd1cc;
}
.searchBar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.2rem;
}
.input {
  flex: 1;
  padding: 0.6rem 1rem;
  border-radius: 8px;
  border: 1px solid #fff0fa;
  font-size: 1rem;
  background: #fff0fa;
  color: #7c3aed;
}
.searchButton {
  background: linear-gradient(90deg, #7c3aed 60%, #f472b6 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px #f472b633;
}
.searchButton:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
}
.resultsList {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto;
}
.resultItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.7rem 0.2rem;
  border-bottom: 1px solid #fff0fa;
  color: #3d246c;
  font-weight: 500;
}
.addButton {
  background: #7c3aed;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.4rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px #a18cd133;
}
.addButton:hover {
  background: #f472b6;
}
.noResults {
  color: #fff;
  text-align: center;
  padding: 1rem 0;
  font-weight: 400;
  opacity: 0.8;
}
@media (max-width: 600px) {
  .modalOverlay {
    align-items: flex-end;
  }
  .modalContent {
    max-width: 95vw;
    width: 95vw;
    min-width: unset;
    border-radius: 18px 18px 0 0;
    padding: 1.1rem 0.7rem 1.2rem 0.7rem;
    font-size: 0.97rem;
  }
  .searchBar {
    margin-bottom: 1.2rem;
    margin-left: 0.1rem;
    margin-right: 0.1rem;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
  }
  .input {
    font-size: 1rem;
    padding: 0.6rem 0.7rem;
    border-radius: 8px;
    margin-right: 0.2rem;
  }
  .modalHeader {
    font-size: 1.1rem;
    padding-bottom: 0.7rem;
  }
  .userList {
    max-height: 38vh;
  }
  .userCard {
    padding: 0.5rem 0.7rem;
    font-size: 0.97rem;
  }
  .addFriendBtn {
    font-size: 0.97rem;
    padding: 0.3rem 0.7rem;
  }
} 
