/* Gen Z Mobile Features - Mobile-First Design */

.genz-features {
  padding: 20px 16px;
  margin: 20px 0;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.genz-features.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Live Activity Banner */
.live-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(102, 126, 234, 0.1));
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #ff4757;
  border-radius: 50%;
  animation: pulse-live 1.5s ease-in-out infinite;
}

.live-text {
  font-size: 0.75rem;
  font-weight: 700;
  color: #ff4757;
  letter-spacing: 1px;
}

.live-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.live-count {
  font-size: 1.2rem;
  font-weight: 800;
  color: #fff;
  line-height: 1;
}

.live-label {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
}

.unified-bookings-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.13), rgba(102, 126, 234, 0.13));
  border-radius: 16px;
  padding: 18px 16px 14px 16px;
  margin-bottom: 20px;
  border: 1.5px solid rgba(255, 255, 255, 0.22);
  box-shadow: 0 4px 24px rgba(255, 179, 71, 0.07);
  position: relative;
  gap: 10px;
}

.live-bookings-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  margin-bottom: 8px;
}

.live-count {
  font-size: 1.3rem;
  font-weight: 800;
  color: #fff;
  line-height: 1;
  margin-right: 4px;
}

.live-label {
  font-size: 0.9rem;
  color: #ffd700;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.feature-badges-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 6px;
}

.feature-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255,255,255,0.18);
  border-radius: 12px;
  padding: 3px 9px 3px 6px;
  font-size: 0.82rem;
  font-weight: 600;
  color: #333;
  box-shadow: 0 2px 8px rgba(255, 179, 71, 0.07);
  border: 1px solid rgba(255,255,255,0.18);
  position: relative;
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  text-decoration: none;
}

.feature-badge:hover, .feature-badge:focus {
  background: rgba(255,255,255,0.32);
  color: #ff6b9d;
  box-shadow: 0 4px 16px rgba(255, 179, 71, 0.13);
  outline: none;
}

.feature-badge .badge-label {
  background: linear-gradient(90deg, #FFD700 0%, #ffb347 100%);
  color: #fff;
  font-size: 0.7em;
  font-weight: 700;
  border-radius: 8px;
  padding: 2px 7px;
  margin-left: 4px;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 4px rgba(255, 179, 71, 0.13);
}

.feature-badge.smart-style .badge-label {
  background: linear-gradient(90deg, #667eea 0%, #ffb347 100%);
}
.feature-badge.intelligent-booking .badge-label {
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
}
.feature-badge.virtual-tryon .badge-label {
  background: linear-gradient(90deg, #11998e 0%, #ffd700 100%);
}
.feature-badge.trend-prediction .badge-label {
  background: linear-gradient(90deg, #ff6b9d 0%, #667eea 100%);
}

/* Viral Actions */
.viral-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.viral-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
}

.share-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.challenge-btn {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
}

.viral-btn:active {
  transform: scale(0.95);
}

.viral-emoji {
  font-size: 1.2rem;
}

.viral-text {
  font-size: 0.85rem;
}

/* FOMO Ticker */
.fomo-ticker {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 8px 0;
  overflow: hidden;
  position: relative;
}

.ticker-content {
  display: flex;
  animation: scroll-ticker 20s linear infinite;
  white-space: nowrap;
}

.ticker-content span {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 40px;
  font-weight: 500;
}

/* Animations */
@keyframes pulse-live {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes scroll-ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Responsive Design */
@media (max-width: 600px) {
  .unified-bookings-card {
    padding: 12px 8px 10px 8px;
    gap: 7px;
  }
  .feature-badges-row {
    gap: 5px;
  }
  .feature-badge {
    font-size: 0.78rem;
    padding: 3px 7px 3px 5px;
  }
  .live-bookings-row {
    gap: 6px;
  }
  .feature-badges-row-inline {
    gap: 4px;
    margin-left: 6px;
  }
}

@media (min-width: 768px) {
  .genz-features {
    padding: 30px 24px;
  }
}

/* Remove old feature grid styles if not used anymore */
.live-bookings-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
  width: 100%;
}

.live-count {
  font-size: 1.05rem;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  margin-right: 4px;
}

.feature-badges-row-inline {
  display: flex;
  align-items: center;
  gap: 7px;
  margin-left: 12px;
  flex-wrap: wrap;
}

.smart-gift-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  color: #fff !important;
  font-size: 0.92rem;
  font-weight: 800;
  border-radius: 16px;
  padding: 7px 18px 7px 13px;
  box-shadow: 0 4px 18px rgba(255, 107, 157, 0.18), 0 1.5px 6px 0 rgba(255,215,0,0.08);
  border: none;
  text-decoration: none;
  transition: background 0.2s, box-shadow 0.2s, color 0.2s, transform 0.15s;
  letter-spacing: 0.01em;
  outline: none;
  position: relative;
  z-index: 2;
}
.gift-subtext-inline {
  font-size: 0.62em;
  font-weight: 400;
  color: #fff;
  opacity: 0.7;
  margin-top: -2px;
  letter-spacing: 0.01em;
  line-height: 1.1;
  text-align: center;
  display: block;
}
.smart-gift-btn:hover, .smart-gift-btn:focus {
  background: linear-gradient(90deg, #ffd700 0%, #ff6b9d 100%);
  color: #fff;
  transform: scale(1.04);
  box-shadow: 0 8px 28px rgba(255, 107, 157, 0.28);
}
.smart-gift-btn span[role="img"] {
  font-size: 1.25em;
  margin-right: 2px;
}
@media (max-width: 600px) {
  .smart-gift-btn {
    font-size: 0.86rem;
    padding: 6px 12px 6px 10px;
  }
  .gift-subtext-inline {
    font-size: 0.58em;
  }
}
