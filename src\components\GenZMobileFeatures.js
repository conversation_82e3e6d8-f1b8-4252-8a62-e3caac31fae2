import React, { useState, useEffect } from 'react';
import './GenZMobileFeatures.css';

const GenZMobileFeatures = () => {
  const [activeFeature, setActiveFeature] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [liveCount, setLiveCount] = useState(156);

  // Simulate live booking counter for FOMO effect
  useEffect(() => {
    const interval = setInterval(() => {
      setLiveCount(prev => prev + Math.floor(Math.random() * 3));
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  // Intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.querySelector('.genz-features');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  const features = [
    {
      id: 1,
      emoji: '🔥',
      title: 'Trending Now',
      subtitle: 'What\'s hot in beauty',
      color: '#ff6b9d',
      count: '2.3K'
    },
    {
      id: 2,
      emoji: '⚡',
      title: 'Quick Book',
      subtitle: 'Book in 30 seconds',
      color: '#667eea',
      count: 'Fast'
    },
    {
      id: 3,
      emoji: '💎',
      title: 'VIP Access',
      subtitle: 'Exclusive deals',
      color: '#ffd700',
      count: 'Premium'
    },
    {
      id: 4,
      emoji: '🎯',
      title: 'Perfect Match',
      subtitle: 'AI-powered styling',
      color: '#11998e',
      count: '99%'
    }
  ];

  const handleFeatureClick = (index) => {
    setActiveFeature(index);
    // Haptic feedback simulation for mobile
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
  };

  return (
    <div className={`genz-features ${isVisible ? 'visible' : ''}`}>
      {/* Unified Live Bookings Card */}
      <div className="live-banner unified-bookings-card">
        <div className="live-indicator">
          <span className="live-dot"></span>
          <span className="live-text">LIVE</span>
        </div>
        <div className="live-bookings-row">
          <span className="live-count">{liveCount}</span>
          <span className="live-label">Bookings</span>
          <div className="feature-badges-row-inline">
            <a href="/ai-style-advisor" className="feature-badge smart-style" tabIndex={0}>
              <span role="img" aria-label="Smart Style">🧠</span> Smart Style <span className="badge-label">AI</span>
            </a>
            <a href="/ai-smart-scheduling" className="feature-badge intelligent-booking" tabIndex={0}>
              <span role="img" aria-label="Intelligent Booking">📅</span> Intelligent Booking <span className="badge-label">Smart</span>
            </a>
            <a href="/ai-virtual-try-on" className="feature-badge virtual-tryon" tabIndex={0}>
              <span role="img" aria-label="Virtual TryOn">🎭</span> Virtual TryOn <span className="badge-label">AR</span>
            </a>
            <a href="/ai-trend-predictor" className="feature-badge trend-prediction" tabIndex={0}>
              <span role="img" aria-label="Trend Prediction">🔮</span> Trend Prediction <span className="badge-label">Trends</span>
            </a>
            <a href="/gift-book" className="smart-gift-btn" tabIndex={0}>
              <span role="img" aria-label="Gift">🎁</span> Gift
              <span className="gift-subtext-inline">Someone Special</span>
            </a>
          </div>
        </div>
      </div>

      {/* Viral Engagement Buttons */}
      <div className="viral-actions">
        <button className="viral-btn share-btn">
          <span className="viral-emoji">📱</span>
          <span className="viral-text">Share with Squad</span>
        </button>
        <button className="viral-btn challenge-btn">
          <span className="viral-emoji">🏆</span>
          <span className="viral-text">Style Challenge</span>
        </button>
      </div>

      {/* FOMO Ticker */}
      <div className="fomo-ticker">
        <div className="ticker-content">
          <span>🔥 Sarah just booked a makeover</span>
          <span>✨ 5 spots left for today</span>
          <span>💅 Emma got 50% off</span>
          <span>🎉 New trend alert!</span>
        </div>
      </div>
    </div>
  );
};

export default GenZMobileFeatures;
