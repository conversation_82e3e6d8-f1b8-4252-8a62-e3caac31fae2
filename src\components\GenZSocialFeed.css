/* Gen Z Social Feed - Mobile First with Home Page Theme Integration */

.genz-social-feed {
  padding: 24px 16px;
  margin: 32px 0;
  background: linear-gradient(135deg, rgba(248,225,255,0.9) 0%, rgba(255,230,179,0.9) 100%);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  scroll-margin-top: 100px; /* Account for fixed header */
}

.genz-social-feed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="bg" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,182,193,0.1)"/><stop offset="100%" stop-color="rgba(255,215,0,0.05)"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23bg)"/><circle cx="200" cy="200" r="3" fill="rgba(255,215,0,0.2)"/><circle cx="800" cy="300" r="2" fill="rgba(255,182,193,0.3)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

/* Header */
.feed-header {
  position: relative;
  z-index: 1;
  margin-bottom: 24px;
}

.feed-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.8rem;
  font-weight: 800;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
  justify-content: center;
}

.title-icon {
  font-size: 2rem;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(5deg); }
}

/* Filters */
.feed-filters {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: #333;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.filter-btn.active {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
  border-color: #ffd700;
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.4);
}

.filter-icon {
  font-size: 1rem;
}

.filter-label {
  font-size: 0.8rem;
}

/* Create Post Section */
.create-post-section {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.create-post-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(255, 107, 157, 0.3);
}

.create-post-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(255, 107, 157, 0.4);
}

.create-icon {
  font-size: 1.1rem;
}

.find-friends-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.find-friends-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4);
}

.find-icon {
  font-size: 1.1rem;
}

/* Posts Grid */
.posts-grid {
  position: relative;
  z-index: 1;
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
}

/* Centered layout for few posts */
.posts-grid.centered {
  justify-items: center;
}

.posts-grid.centered .post-card {
  max-width: 400px;
  width: 100%;
}

/* Mobile: 2 posts, Desktop: 3 posts */
@media (min-width: 768px) {
  .posts-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .posts-grid.centered {
    grid-template-columns: repeat(auto-fit, minmax(300px, 400px));
    justify-content: center;
  }
}

@media (max-width: 767px) {
  .posts-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .posts-grid.centered {
    justify-items: center;
  }
}

@media (max-width: 480px) {
  .posts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Post Card */
.post-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.post-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(255, 215, 0, 0.2);
}

/* Post Header */
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 700;
  font-size: 0.9rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 4px;
}

.verified {
  color: #ffd700;
  font-size: 0.8rem;
}

.post-time {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}

.salon-badge {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 600;
}

.follow-btn {
  background: rgba(255, 215, 0, 0.2);
  color: #333;
  border: 1px solid rgba(255, 215, 0, 0.5);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.follow-btn:hover {
  background: rgba(255, 215, 0, 0.3);
  transform: scale(1.05);
}

.follow-btn.following {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
  border-color: #ffd700;
}

/* Post Content */
.post-content {
  margin-bottom: 12px;
}

.post-text {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #333;
  margin: 0 0 12px 0;
}

.post-image {
  border-radius: 12px;
  overflow: hidden;
  aspect-ratio: 4/3;
}

.post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.post-card:hover .post-image img {
  transform: scale(1.05);
}

/* Tags */
.post-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.tag {
  background: rgba(255, 215, 0, 0.2);
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

/* Post Actions */
.post-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.7);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 8px;
}

.action-btn:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: scale(1.05);
}

.like-btn.liked {
  color: #ff6b9d;
}

.action-icon {
  font-size: 1rem;
}

.action-count, .action-label {
  font-weight: 600;
  font-size: 0.75rem;
}

/* Comments Section */
.comments-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.comments-list {
  margin-bottom: 12px;
}

.comment {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comment:last-child {
  border-bottom: none;
}

.comment-user {
  font-weight: 700;
  font-size: 0.8rem;
  color: #333;
}

.comment-text {
  font-size: 0.85rem;
  color: #555;
  line-height: 1.3;
}

.comment-time {
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.5);
}

.comment-input-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.comment-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  outline: none;
  transition: all 0.3s ease;
}

.comment-input:focus {
  border-color: #ffd700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.comment-submit {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.comment-submit:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #333;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 215, 0, 0.3);
  border-top: 3px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.feed-footer {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-top: 24px;
}

.view-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
  border: none;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
}

.view-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(255, 215, 0, 0.4);
}

.btn-icon {
  font-size: 1rem;
}

.view-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

.end-message {
  text-align: center;
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.9rem;
  font-style: italic;
}

/* Gen Z Pagination Styles */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  min-width: 100px;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.pagination-btn .btn-icon {
  font-size: 1rem;
}

.page-info {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.page-counter {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  text-align: center;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  font-size: 0.85rem;
}

/* Mobile pagination */
@media (max-width: 768px) {
  .pagination-controls {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-btn {
    min-width: 120px;
    padding: 12px 24px;
  }

  .page-info {
    order: -1;
    margin-bottom: 8px;
  }
}

/* Create Post Modal - Local positioning within social feed */
.modal-overlay-local {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 20px;
  border-radius: 20px;
}

.create-post-modal {
  background: linear-gradient(135deg, rgba(248,225,255,0.95) 0%, rgba(255,230,179,0.95) 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-content {
  padding: 20px;
}

.post-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-family: inherit;
  resize: vertical;
  outline: none;
  transition: all 0.3s ease;
}

.post-textarea:focus {
  border-color: #ffd700;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
}

.image-upload {
  margin: 16px 0;
}

.file-input {
  display: none;
}

.upload-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.upload-label:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: #ffd700;
}

.image-preview {
  position: relative;
  margin-top: 12px;
}

.image-preview img {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  border-radius: 12px;
}

.remove-image {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}

.post-submit-btn {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
  border: none;
  padding: 12px 32px;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
}

.post-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(255, 215, 0, 0.4);
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .genz-social-feed {
    padding: 20px 12px;
    margin: 24px 0;
  }

  .feed-title {
    font-size: 1.5rem;
  }

  .filter-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .post-card {
    padding: 14px;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .modal-overlay {
    padding: 12px;
  }

  .create-post-modal {
    max-height: 90vh;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 16px;
  }
}
