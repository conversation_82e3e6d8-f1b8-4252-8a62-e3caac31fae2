import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-toastify';
import GenZTooltip from './GenZTooltip';
import FriendSearch from './FriendSearch';
import './GenZSocialFeed.css';

const GenZSocialFeed = () => {
  const { user } = useAuth();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeFilter, setActiveFilter] = useState('trending');
  const [showComments, setShowComments] = useState({});
  const [commentTexts, setCommentTexts] = useState({});
  const [showCreatePost, setShowCreatePost] = useState(false);
  const [newPost, setNewPost] = useState({ content: '', image: null });
  const [followStatus, setFollowStatus] = useState({});
  const [likedPosts, setLikedPosts] = useState({});
  const [sharedPosts, setSharedPosts] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPosts, setTotalPosts] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [showFriendSearch, setShowFriendSearch] = useState(false);
  const [postsPerPage] = useState(() => window.innerWidth <= 768 ? 2 : 3);

  // Comprehensive database simulation with 20+ posts
  const [allPosts, setAllPosts] = useState([
    {
      id: 1,
      user: { id: 1, name: 'Sarah M.', avatar: '💅', verified: true },
      content: 'Just got the most INSANE balayage at Glam Studio! The vibes were immaculate ✨',
      image: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop',
      salon: 'Glam Studio',
      service: 'Balayage',
      likes: 247,
      shares: 18,
      comments: [
        { id: 1, user: 'Maya K.', text: 'Obsessed! 😍', timeAgo: '1h' },
        { id: 2, user: 'Zoe L.', text: 'Need this energy in my life!', timeAgo: '45m' }
      ],
      timeAgo: '2h',
      timestamp: Date.now() - 2 * 60 * 60 * 1000,
      trending: true,
      tags: ['#balayage', '#glowup', '#vibes']
    },
    {
      id: 2,
      user: { id: 2, name: 'Maya K.', avatar: '🦋', verified: false },
      content: 'Butterfly locs hitting different! This salon understood the assignment 💯',
      image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop',
      salon: 'Urban Roots',
      service: 'Butterfly Locs',
      likes: 189,
      shares: 12,
      comments: [
        { id: 3, user: 'Alex R.', text: 'Absolutely stunning! 🔥', timeAgo: '2h' }
      ],
      timeAgo: '4h',
      timestamp: Date.now() - 4 * 60 * 60 * 1000,
      trending: true,
      tags: ['#locs', '#naturalhair', '#slay']
    },
    {
      id: 3,
      user: { id: 3, name: 'Zoe L.', avatar: '✨', verified: true },
      content: 'Nails said main character energy! The nail tech was so talented 🔥',
      image: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop',
      salon: 'Nail Artistry',
      service: 'Nail Art',
      likes: 156,
      comments: [],
      timeAgo: '6h',
      trending: false,
      tags: ['#nails', '#art', '#maincharacter']
    },
    {
      id: 4,
      user: { id: 4, name: 'Alex R.', avatar: '🌟', verified: false },
      content: 'Fresh cut got me feeling like a whole new person! Barber skills unmatched',
      image: 'https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=400&h=300&fit=crop',
      salon: 'Sharp Cuts',
      service: 'Haircut & Style',
      likes: 203,
      shares: 8,
      comments: [
        { id: 4, user: 'Sarah M.', text: 'Looking fresh! 💯', timeAgo: '3h' }
      ],
      timeAgo: '8h',
      timestamp: Date.now() - 8 * 60 * 60 * 1000,
      trending: false,
      tags: ['#freshcut', '#barber', '#newme']
    },
    // Additional comprehensive database entries
    {
      id: 5,
      user: { id: 5, name: 'Bella R.', avatar: '🌸', verified: true },
      content: 'Pink hair era activated! This color transformation is everything 💕',
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=300&fit=crop',
      salon: 'Color Pop Studio',
      service: 'Hair Color',
      likes: 312,
      shares: 25,
      comments: [
        { id: 5, user: 'Luna P.', text: 'Pink princess vibes! 👑', timeAgo: '30m' },
        { id: 6, user: 'Kai M.', text: 'This is ART! 🎨', timeAgo: '15m' }
      ],
      timeAgo: '1h',
      timestamp: Date.now() - 1 * 60 * 60 * 1000,
      trending: true,
      tags: ['#pinkhair', '#transformation', '#colorpop']
    },
    {
      id: 6,
      user: { id: 6, name: 'Jordan T.', avatar: '⚡', verified: false },
      content: 'Fade game strong! Barber really understood the assignment 🔥',
      image: 'https://images.unsplash.com/photo-1621605815971-fbc98d665033?w=400&h=300&fit=crop',
      salon: 'Elite Cuts',
      service: 'Fade Cut',
      likes: 156,
      shares: 7,
      comments: [],
      timeAgo: '3h',
      timestamp: Date.now() - 3 * 60 * 60 * 1000,
      trending: false,
      tags: ['#fade', '#barber', '#fresh']
    },
    {
      id: 7,
      user: { id: 7, name: 'Mia L.', avatar: '✨', verified: true },
      content: 'Gel nails hitting different! The nail art is pure perfection 💎',
      image: 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400&h=300&fit=crop',
      salon: 'Nail Haven',
      service: 'Gel Nails',
      likes: 89,
      shares: 4,
      comments: [
        { id: 7, user: 'Aria K.', text: 'Need this energy! 💅', timeAgo: '2h' }
      ],
      timeAgo: '5h',
      timestamp: Date.now() - 5 * 60 * 60 * 1000,
      trending: false,
      tags: ['#gelnails', '#nailart', '#perfection']
    },
    {
      id: 8,
      user: { id: 8, name: 'Tyler K.', avatar: '🌟', verified: false },
      content: 'Beard trim got me looking like a whole CEO! Precision on point ✂️',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
      salon: 'Gentleman\'s Club',
      service: 'Beard Trim',
      likes: 134,
      shares: 6,
      comments: [
        { id: 8, user: 'Marcus J.', text: 'CEO vibes indeed! 💼', timeAgo: '1h' }
      ],
      timeAgo: '6h',
      timestamp: Date.now() - 6 * 60 * 60 * 1000,
      trending: false,
      tags: ['#beard', '#ceo', '#precision']
    },
    {
      id: 9,
      user: { id: 9, name: 'Chloe W.', avatar: '🦄', verified: true },
      content: 'Unicorn hair achieved! This rainbow blend is pure magic 🌈',
      image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop',
      salon: 'Fantasy Hair',
      service: 'Rainbow Color',
      likes: 445,
      shares: 32,
      comments: [
        { id: 9, user: 'Nova S.', text: 'Living for this rainbow! 🌈', timeAgo: '4h' },
        { id: 10, user: 'River M.', text: 'Unicorn vibes activated! 🦄', timeAgo: '3h' },
        { id: 11, user: 'Sky L.', text: 'This is EVERYTHING! ✨', timeAgo: '2h' }
      ],
      timeAgo: '12h',
      timestamp: Date.now() - 12 * 60 * 60 * 1000,
      trending: true,
      tags: ['#unicornhair', '#rainbow', '#magic']
    },
    {
      id: 10,
      user: { id: 10, name: 'Ethan P.', avatar: '🔥', verified: false },
      content: 'Skin fade perfection! This barber is an artist fr 🎨',
      image: 'https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=400&h=300&fit=crop',
      salon: 'Artisan Cuts',
      service: 'Skin Fade',
      likes: 78,
      shares: 3,
      comments: [],
      timeAgo: '7h',
      timestamp: Date.now() - 7 * 60 * 60 * 1000,
      trending: false,
      tags: ['#skinfade', '#artist', '#perfection']
    },
    {
      id: 11,
      user: { id: 11, name: 'Luna V.', avatar: '🌙', verified: true },
      content: 'French tips but make it holographic! The future is now ✨',
      image: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop',
      salon: 'Future Nails',
      service: 'Holographic Nails',
      likes: 267,
      shares: 19,
      comments: [
        { id: 12, user: 'Sage R.', text: 'Holographic queen! 👑', timeAgo: '5h' },
        { id: 13, user: 'Phoenix K.', text: 'The future looks bright! ✨', timeAgo: '4h' }
      ],
      timeAgo: '9h',
      timestamp: Date.now() - 9 * 60 * 60 * 1000,
      trending: true,
      tags: ['#holographic', '#future', '#nails']
    },
    {
      id: 12,
      user: { id: 12, name: 'Zara H.', avatar: '💎', verified: false },
      content: 'Box braids with gold accessories! Feeling like royalty 👑',
      image: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=300&fit=crop',
      salon: 'Crown Braids',
      service: 'Box Braids',
      likes: 198,
      shares: 11,
      comments: [
        { id: 14, user: 'Amara T.', text: 'Absolute royalty! 👑', timeAgo: '6h' }
      ],
      timeAgo: '10h',
      timestamp: Date.now() - 10 * 60 * 60 * 1000,
      trending: false,
      tags: ['#boxbraids', '#royalty', '#gold']
    }
  ]);

  // Responsive post limits based on screen size
  const getPostLimit = useCallback(() => {
    if (window.innerWidth <= 768) return 2; // Mobile: 2 posts
    return 3; // Desktop: 3 posts
  }, []);

  const [postLimit, setPostLimit] = useState(getPostLimit());

  // Handle window resize for responsive scaling
  useEffect(() => {
    const handleResize = () => {
      setPostLimit(getPostLimit());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [getPostLimit]);

  // Real-world data fetching simulation
  const fetchPosts = useCallback(async (page = 1, filter = 'trending') => {
    setIsLoadingMore(true);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    let filtered = [...allPosts];

    // Apply filters with real-world logic
    if (filter === 'trending') {
      filtered = filtered.filter(post => {
        const engagementRate = (post.likes + post.comments.length * 2 + post.shares * 3) / 100;
        const recencyBonus = (Date.now() - post.timestamp) < 24 * 60 * 60 * 1000 ? 1.5 : 1;
        return engagementRate * recencyBonus > 2; // Trending threshold
      });
    } else if (filter === 'recent') {
      filtered = filtered.sort((a, b) => b.timestamp - a.timestamp);
    } else if (filter === 'friends') {
      filtered = filtered.filter(post => followStatus[post.user.id]);
    }

    // Smart content prioritization algorithm
    filtered = filtered.sort((a, b) => {
      const scoreA = getPostPriority(a);
      const scoreB = getPostPriority(b);
      return scoreB - scoreA;
    });

    setTotalPosts(filtered.length);
    setIsLoadingMore(false);

    return filtered;
  }, [allPosts, followStatus]);

  // Advanced priority scoring algorithm
  const getPostPriority = useCallback((post) => {
    const likes = post.likes || 0;
    const comments = post.comments?.length || 0;
    const shares = post.shares || 0;

    // Engagement score (weighted)
    const engagementScore = (likes * 1) + (comments * 2.5) + (shares * 4);

    // Recency score (newer = higher)
    const hoursOld = (Date.now() - post.timestamp) / (1000 * 60 * 60);
    const recencyScore = Math.max(0, 100 - hoursOld * 2);

    // Trending bonus
    const trendingBonus = post.trending ? 150 : 0;

    // Verified user bonus
    const verifiedBonus = post.user.verified ? 50 : 0;

    return engagementScore + recencyScore + trendingBonus + verifiedBonus;
  }, []);

  // Smart pagination with Gen Z style
  const { paginatedPosts, totalPages, hasNextPage, hasPrevPage } = useMemo(() => {
    let filtered = [...allPosts];

    // Apply filters
    if (activeFilter === 'trending') {
      filtered = filtered.filter(post => {
        const engagementRate = (post.likes + post.comments.length * 2 + post.shares * 3) / 100;
        const recencyBonus = (Date.now() - post.timestamp) < 24 * 60 * 60 * 1000 ? 1.5 : 1;
        return engagementRate * recencyBonus > 2;
      });
    } else if (activeFilter === 'recent') {
      filtered = filtered.sort((a, b) => b.timestamp - a.timestamp);
    } else if (activeFilter === 'friends') {
      filtered = filtered.filter(post => followStatus[post.user.id]);
    }

    // Sort by priority
    filtered = filtered.sort((a, b) => getPostPriority(b) - getPostPriority(a));

    const totalPages = Math.ceil(filtered.length / postsPerPage);
    const startIndex = (currentPage - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
    const paginatedPosts = filtered.slice(startIndex, endIndex);

    return {
      paginatedPosts,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1
    };
  }, [allPosts, activeFilter, followStatus, getPostPriority, currentPage, postsPerPage]);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // Interactive functions
  const handleLike = useCallback((postId) => {
    setAllPosts(prev => prev.map(post =>
      post.id === postId
        ? { ...post, likes: post.likes + (likedPosts[postId] ? -1 : 1) }
        : post
    ));

    setLikedPosts(prev => ({
      ...prev,
      [postId]: !prev[postId]
    }));

    // Haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }

    toast.success(likedPosts[postId] ? 'Unliked! 💔' : 'Liked! 💖', {
      position: "top-center",
      autoClose: 1000,
    });
  }, [likedPosts]);

  const handleShare = useCallback((postId) => {
    setAllPosts(prev => prev.map(post =>
      post.id === postId
        ? { ...post, shares: (post.shares || 0) + 1 }
        : post
    ));

    setSharedPosts(prev => ({
      ...prev,
      [postId]: true
    }));

    // Simulate sharing
    if (navigator.share) {
      navigator.share({
        title: 'Check out this salon vibe!',
        text: 'Amazing salon experience shared on SalonG',
        url: window.location.href
      });
    } else {
      // Fallback - copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied! Share those vibes! 📤', {
        position: "top-center",
        autoClose: 2000,
      });
    }

    // Haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate(100);
    }
  }, []);

  const handleComment = useCallback((postId) => {
    setShowComments(prev => ({
      ...prev,
      [postId]: !prev[postId]
    }));
  }, []);

  const handleSubmitComment = useCallback((postId) => {
    const commentText = commentTexts[postId];
    if (!commentText?.trim()) return;

    const newComment = {
      id: Date.now(),
      user: user?.name || 'You',
      text: commentText,
      timeAgo: 'now'
    };

    setAllPosts(prev => prev.map(post =>
      post.id === postId
        ? { ...post, comments: [...(post.comments || []), newComment] }
        : post
    ));

    setCommentTexts(prev => ({
      ...prev,
      [postId]: ''
    }));

    toast.success('Comment added! 💬');
  }, [commentTexts, user]);

  const handleFollow = useCallback((userId) => {
    setFollowStatus(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));

    toast.success(followStatus[userId] ? 'Unfollowed! 👋' : 'Following! 👥');
  }, [followStatus]);

  const handleCreatePost = useCallback(() => {
    if (!newPost.content.trim()) {
      toast.error('Please add some content! ✍️');
      return;
    }

    const post = {
      id: Date.now(),
      user: {
        id: user?.id || 999,
        name: user?.name || 'You',
        avatar: '🌟',
        verified: false
      },
      content: newPost.content,
      image: newPost.image,
      salon: 'Your Salon',
      service: 'New Style',
      likes: 0,
      shares: 0,
      comments: [],
      timeAgo: 'now',
      timestamp: Date.now(),
      trending: true, // New posts start as trending
      tags: ['#newpost', '#vibes']
    };

    setAllPosts(prev => [post, ...prev]);
    setNewPost({ content: '', image: null });
    setShowCreatePost(false);

    toast.success('Post shared! ✨ Your vibes are live!');
  }, [newPost, user]);

  // Gen Z style pagination functions
  const handleNextPage = useCallback(async () => {
    if (!hasNextPage) return;

    setIsLoadingMore(true);

    // Simulate loading delay for smooth UX
    await new Promise(resolve => setTimeout(resolve, 800));

    setCurrentPage(prev => prev + 1);
    setIsLoadingMore(false);

    toast.success('Fresh vibes loaded! ✨', {
      position: "top-center",
      autoClose: 1500,
    });
  }, [hasNextPage]);

  const handlePrevPage = useCallback(async () => {
    if (!hasPrevPage) return;

    setIsLoadingMore(true);

    // Simulate loading delay
    await new Promise(resolve => setTimeout(resolve, 800));

    setCurrentPage(prev => prev - 1);
    setIsLoadingMore(false);

    toast.success('Previous vibes loaded! ✨', {
      position: "top-center",
      autoClose: 1500,
    });
  }, [hasPrevPage]);

  const filters = [
    { id: 'trending', label: 'Trending', icon: '🔥' },
    { id: 'recent', label: 'Recent', icon: '⏰' },
    { id: 'friends', label: 'Friends', icon: '👥' }
  ];

  if (loading) {
    return (
      <div className="genz-social-feed">
        <div className="feed-header">
          <h2 className="feed-title">
            <span className="title-icon">✨</span>
            Social Vibes
          </h2>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading the vibes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="genz-social-feed">
      {/* Header with filters */}
      <div className="feed-header">
        <h2 className="feed-title">
          <span className="title-icon">✨</span>
          Social Vibes
        </h2>
        <div className="feed-filters">
          {filters.map(filter => (
            <GenZTooltip
              key={filter.id}
              text={
                filter.id === 'trending' ? 'Hot posts with high engagement 🔥' :
                filter.id === 'recent' ? 'Latest posts from everyone ⏰' :
                'Posts from people you follow 👥'
              }
              position="bottom"
            >
              <button
                className={`filter-btn ${activeFilter === filter.id ? 'active' : ''}`}
                onClick={() => setActiveFilter(filter.id)}
              >
                <span className="filter-icon">{filter.icon}</span>
                <span className="filter-label">{filter.label}</span>
              </button>
            </GenZTooltip>
          ))}
        </div>
      </div>

      {/* Create Post Button */}
      <div className="create-post-section">
        <GenZTooltip text="Create a new post with photos and vibes ✨" position="bottom">
          <button
            className="create-post-btn"
            onClick={() => setShowCreatePost(true)}
          >
            <span className="create-icon">✨</span>
            <span>Share Your Vibes</span>
          </button>
        </GenZTooltip>

        <GenZTooltip text="Find and add new friends 👥" position="bottom">
          <button
            className="find-friends-btn"
            onClick={() => setShowFriendSearch(true)}
          >
            <span className="find-icon">👥</span>
            <span>Find Friends</span>
          </button>
        </GenZTooltip>
      </div>

      {/* Posts Grid - Centered when few posts */}
      <div className={`posts-grid ${paginatedPosts.length <= 2 ? 'centered' : ''}`}>
        {paginatedPosts.map(post => (
          <div key={post.id} className="post-card">
            <div className="post-header">
              <div className="user-info">
                <div className="user-avatar">{post.user.avatar}</div>
                <div className="user-details">
                  <span className="user-name">
                    {post.user.name}
                    {post.user.verified && <span className="verified">✓</span>}
                  </span>
                  <span className="post-time">{post.timeAgo} ago</span>
                </div>
              </div>
              <div className="salon-badge">{post.salon}</div>
              {post.user.id !== (user?.id || 999) && (
                <GenZTooltip
                  text={followStatus[post.user.id] ? 'Unfollow this user' : 'Follow to become friends 👥'}
                  position="left"
                >
                  <button
                    className={`follow-btn ${followStatus[post.user.id] ? 'following' : ''}`}
                    onClick={() => handleFollow(post.user.id)}
                  >
                    {followStatus[post.user.id] ? 'Following' : 'Follow'}
                  </button>
                </GenZTooltip>
              )}
            </div>

            <div className="post-content">
              <p className="post-text">{post.content}</p>
              {post.image && (
                <div className="post-image">
                  <img src={post.image} alt="Style post" loading="lazy" />
                </div>
              )}
            </div>

            <div className="post-tags">
              {post.tags.map(tag => (
                <span key={tag} className="tag">{tag}</span>
              ))}
            </div>

            <div className="post-actions">
              <GenZTooltip
                text={likedPosts[post.id] ? 'Unlike this post 💔' : 'Like this post 💖'}
                position="top"
              >
                <button
                  className={`action-btn like-btn ${likedPosts[post.id] ? 'liked' : ''}`}
                  onClick={() => handleLike(post.id)}
                >
                  <span className="action-icon">💖</span>
                  <span className="action-count">{post.likes}</span>
                </button>
              </GenZTooltip>

              <GenZTooltip
                text={showComments[post.id] ? 'Hide comments' : 'View and add comments 💬'}
                position="top"
              >
                <button
                  className="action-btn comment-btn"
                  onClick={() => handleComment(post.id)}
                >
                  <span className="action-icon">💬</span>
                  <span className="action-count">{post.comments.length}</span>
                </button>
              </GenZTooltip>

              <GenZTooltip text="Share this post with friends 📤" position="top">
                <button
                  className="action-btn share-btn"
                  onClick={() => handleShare(post.id)}
                >
                  <span className="action-icon">📤</span>
                  <span className="action-count">{post.shares || 0}</span>
                </button>
              </GenZTooltip>
            </div>

            {/* Comments Section */}
            {showComments[post.id] && (
              <div className="comments-section">
                <div className="comments-list">
                  {post.comments.map(comment => (
                    <div key={comment.id} className="comment">
                      <span className="comment-user">{comment.user}</span>
                      <span className="comment-text">{comment.text}</span>
                      <span className="comment-time">{comment.timeAgo}</span>
                    </div>
                  ))}
                </div>
                <div className="comment-input-section">
                  <input
                    type="text"
                    placeholder="Add a comment..."
                    value={commentTexts[post.id] || ''}
                    onChange={(e) => setCommentTexts(prev => ({
                      ...prev,
                      [post.id]: e.target.value
                    }))}
                    onKeyPress={(e) => e.key === 'Enter' && handleSubmitComment(post.id)}
                    className="comment-input"
                  />
                  <button
                    onClick={() => handleSubmitComment(post.id)}
                    className="comment-submit"
                  >
                    Send
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Gen Z Pagination */}
      <div className="feed-footer">
        <div className="pagination-controls">
          <GenZTooltip text="Previous vibes ⬅️" position="top">
            <button
              className="pagination-btn prev-btn"
              onClick={handlePrevPage}
              disabled={!hasPrevPage || isLoadingMore}
            >
              <span className="btn-icon">⬅️</span>
              <span>Previous</span>
            </button>
          </GenZTooltip>

          <div className="page-info">
            {isLoadingMore ? (
              <div className="loading-indicator">
                <div className="loading-spinner-small"></div>
                <span>Loading vibes...</span>
              </div>
            ) : (
              <span className="page-counter">
                Page {currentPage} of {totalPages} ✨
              </span>
            )}
          </div>

          <GenZTooltip text="Next vibes ➡️" position="top">
            <button
              className="pagination-btn next-btn"
              onClick={handleNextPage}
              disabled={!hasNextPage || isLoadingMore}
            >
              <span>Next</span>
              <span className="btn-icon">➡️</span>
            </button>
          </GenZTooltip>
        </div>

        {!hasNextPage && !hasPrevPage && totalPages <= 1 && (
          <p className="end-message">All vibes in one place! ✨</p>
        )}
      </div>

      {/* Create Post Modal - Positioned within social feed */}
      {showCreatePost && (
        <div className="modal-overlay-local" onClick={() => setShowCreatePost(false)}>
          <div className="create-post-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Share Your Vibes ✨</h3>
              <button
                className="close-btn"
                onClick={() => setShowCreatePost(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-content">
              <textarea
                placeholder="What's your vibe today? Share your salon experience..."
                value={newPost.content}
                onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                className="post-textarea"
                rows="4"
              />
              <div className="image-upload">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      const reader = new FileReader();
                      reader.onload = (e) => setNewPost(prev => ({ ...prev, image: e.target.result }));
                      reader.readAsDataURL(file);
                    }
                  }}
                  className="file-input"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="upload-label">
                  📷 Add Photo
                </label>
              </div>
              {newPost.image && (
                <div className="image-preview">
                  <img src={newPost.image} alt="Preview" />
                  <button
                    onClick={() => setNewPost(prev => ({ ...prev, image: null }))}
                    className="remove-image"
                  >
                    ×
                  </button>
                </div>
              )}
            </div>
            <div className="modal-footer">
              <button
                onClick={handleCreatePost}
                className="post-submit-btn"
              >
                Share Vibes ✨
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Friend Search Modal */}
      <FriendSearch
        isOpen={showFriendSearch}
        onClose={() => setShowFriendSearch(false)}
        onFriendAdded={(userId) => {
          setFollowStatus(prev => ({ ...prev, [userId]: true }));
          setShowFriendSearch(false);
        }}
        currentUserId={user?.id || 999}
      />
    </div>
  );
};

export default GenZSocialFeed;
