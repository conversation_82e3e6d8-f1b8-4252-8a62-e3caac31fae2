/* Gen Z Tooltip Styles */

.genz-tooltip-container {
  position: relative;
  display: inline-block;
}

.genz-tooltip {
  position: absolute;
  z-index: 9999;
  opacity: 0;
  animation: tooltipFadeIn 0.3s ease-out forwards;
  pointer-events: none;
  white-space: nowrap;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(5px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tooltip-content {
  background: linear-gradient(135deg, #333 0%, #555 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  max-width: 250px;
  text-align: center;
  word-wrap: break-word;
  hyphens: auto;
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

/* Top tooltip */
.genz-tooltip-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
}

/* Responsive positioning to prevent hiding */
@media (max-width: 768px) {
  .genz-tooltip-top {
    left: 0;
    right: 0;
    transform: none;
    margin: 0 10px 8px 10px;
  }

  .genz-tooltip-bottom {
    left: 0;
    right: 0;
    transform: none;
    margin: 8px 10px 0 10px;
  }

  .genz-tooltip-left,
  .genz-tooltip-right {
    /* Convert to top tooltip on mobile */
    bottom: 100%;
    top: auto;
    left: 0;
    right: 0;
    transform: none;
    margin: 0 10px 8px 10px;
  }

  .genz-tooltip-left .tooltip-arrow,
  .genz-tooltip-right .tooltip-arrow {
    /* Use top arrow style on mobile */
    top: 100%;
    bottom: auto;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #333;
    border-bottom: none;
  }
}

.genz-tooltip-top .tooltip-arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #333;
}

/* Bottom tooltip */
.genz-tooltip-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
}

.genz-tooltip-bottom .tooltip-arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #333;
}

/* Left tooltip */
.genz-tooltip-left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-right: 8px;
}

.genz-tooltip-left .tooltip-arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid #333;
}

/* Right tooltip */
.genz-tooltip-right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
}

.genz-tooltip-right .tooltip-arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid #333;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .tooltip-content {
    font-size: 0.7rem;
    padding: 6px 10px;
    max-width: 150px;
  }
  
  .tooltip-arrow {
    border-width: 5px;
  }
  
  .genz-tooltip-top .tooltip-arrow {
    border-top-width: 5px;
  }
  
  .genz-tooltip-bottom .tooltip-arrow {
    border-bottom-width: 5px;
  }
  
  .genz-tooltip-left .tooltip-arrow {
    border-left-width: 5px;
  }
  
  .genz-tooltip-right .tooltip-arrow {
    border-right-width: 5px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .tooltip-content {
    background: #000;
    border: 2px solid #fff;
    color: #fff;
  }
  
  .tooltip-arrow {
    border-color: #000 transparent transparent transparent;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .genz-tooltip {
    animation: none;
    opacity: 1;
  }
  
  @keyframes tooltipFadeIn {
    from, to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}
