/* ===== AI Gift Messages - Enterprise Gen Z Design ===== */

/* CSS Reset for Gift Booking Form - Override Global Styles */
.gift-booking-page * {
  box-sizing: border-box;
}

.gift-booking-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  color: #1a1a1a;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
  /* Performance optimizations */
  will-change: transform;
  contain: layout style paint;
}

.gift-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gift-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: giftFloat 8s ease-in-out infinite;
}

.gift-orb-1 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  top: 15%;
  left: -8%;
  animation-delay: 0s;
}

.gift-orb-2 {
  width: 180px;
  height: 180px;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  top: 70%;
  right: -5%;
  animation-delay: 3s;
}

.gift-orb-3 {
  width: 220px;
  height: 220px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  bottom: 10%;
  left: 60%;
  animation-delay: 6s;
}

@keyframes giftFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  33% { transform: translateY(-15px) rotate(120deg) scale(1.05); }
  66% { transform: translateY(10px) rotate(240deg) scale(0.95); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  border-color: rgba(255, 107, 157, 0.3);
  color: #f0f6fc;
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Modern Header */
.gift-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.gift-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.gift-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 107, 157, 0.1);
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #ff6b9d;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.gift-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #ff6b9d, #ffeaa7, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: giftGradientShift 4s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: giftSparkle 2s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}

@keyframes giftGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes giftSparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.3) rotate(180deg); opacity: 0.7; }
}

.gift-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* ===== Enhanced Gift Booking Form Styles - Mobile First ===== */

/* Modern Container */
.gift-booking-container-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  position: relative;
  z-index: 1;
}

/* AI Toggle */
.ai-features-toggle-modern {
  text-align: center;
  margin-bottom: 2rem;
}

.ai-toggle-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 107, 157, 0.1);
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 12px;
  color: #ff6b9d;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-toggle-button:hover {
  background: rgba(255, 107, 157, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.2);
}

.ai-toggle-button .toggle-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.3), transparent);
  transition: left 0.5s ease;
}

.ai-toggle-button:hover .toggle-glow {
  left: 100%;
}

/* Modern Layout - Mobile First */
.gift-booking-layout-modern {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: calc(100vh - 200px);
  align-items: stretch;
  width: 100%;
  max-width: 100%;
}

.main-form-column {
  width: 100%;
  min-width: 0;
}

.ai-features-column {
  width: 100%;
  min-width: 0;
}

/* Desktop Layout */
@media (min-width: 1200px) {
  .gift-booking-layout-modern {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    min-height: calc(100vh - 300px);
    align-items: start;
  }
}

/* Form Card - Mobile First */
.gift-form-card {
  background: #ffffff;
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 16px;
  padding: 1rem;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
  color: #1a1a1a;
  height: fit-content;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Desktop Form Card */
@media (min-width: 768px) {
  .gift-form-card {
    padding: 2rem;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  }
}

/* AI Features Column - Mobile First */
.ai-features-column {
  background: #ffffff;
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 16px;
  padding: 1rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
  color: #1a1a1a;
  height: fit-content;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Desktop AI Features Column */
@media (min-width: 1200px) {
  .ai-features-column {
    padding: 2rem;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
    position: sticky;
    top: 2rem;
  }
}

.ai-features-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.ai-features-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.ai-icon {
  font-size: 1.8rem;
}

.ai-features-subtitle {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 1.5rem 0;
}

/* AI Toggle Button */
.ai-toggle-button {
  background: linear-gradient(135deg, #667eea, #4facfe);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ai-toggle-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.ai-toggle-button.active {
  background: linear-gradient(135deg, #4facfe, #667eea);
}

.toggle-icon {
  font-size: 1.1rem;
}

.toggle-text {
  font-weight: 600;
}

.ai-magic-grid {
  display: grid;
  gap: 1.5rem;
}

.ai-magic-item h6 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.9rem;
}

.ai-magic-item span {
  font-size: 1.1rem;
}

.ai-magic-buttons {
  display: flex;
  gap: 0.75rem;
  width: 100%;
  max-width: 100%;
}

.ai-magic-buttons .btn {
  flex: 1;
  padding: 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ai-magic-buttons .btn-primary {
  background: linear-gradient(135deg, #667eea, #4facfe);
  border: none;
  color: white;
}

.ai-magic-buttons .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.ai-magic-buttons .btn-outline-primary {
  border: 2px solid #667eea;
  color: #667eea;
  background: transparent;
}

.ai-magic-buttons .btn-outline-primary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Message Options Overlay */
.message-options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: white;
}

.message-options-horizontal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  width: 100%;
  max-width: 1200px;
}

/* AI Magic Inactive State */
.ai-magic-inactive {
  position: relative;
  height: 400px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.3);
  border: 2px dashed rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.ai-magic-inactive:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(255, 255, 255, 0.4);
}

.inactive-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.inactive-content {
  text-align: center;
  color: #666;
}

.inactive-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.inactive-text {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #667eea;
}

.inactive-subtitle {
  font-size: 0.9rem;
  color: #999;
  margin: 0;
}

/* AI Tooltip */
.ai-tooltip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #667eea;
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: tooltipBounce 0.3s ease;
}

.tooltip-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.tooltip-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

@keyframes tooltipBounce {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* AI Message Options Section - Inside AI Column */
.ai-message-options-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.message-options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  justify-content: center;
  align-items: stretch;
}

/* Single message layout */
.message-options-grid.single-message .message-option-card-inline {
  width: 100%;
  max-width: 100%;
}

/* Two messages layout */
.message-options-grid.two-messages .message-option-card-inline {
  width: calc(50% - 0.5rem);
  min-width: 200px;
}

/* Three or more messages layout */
.message-options-grid.multiple-messages .message-option-card-inline:nth-child(1),
.message-options-grid.multiple-messages .message-option-card-inline:nth-child(2) {
  width: calc(50% - 0.5rem);
  min-width: 200px;
}

.message-options-grid.multiple-messages .message-option-card-inline:nth-child(n+3) {
  width: 100%;
}

/* Mobile responsive - stack all messages on small screens */
@media (max-width: 768px) {
  .message-options-grid.two-messages .message-option-card-inline,
  .message-options-grid.multiple-messages .message-option-card-inline {
    width: 100% !important;
    min-width: unset;
  }
}

.message-option-card-inline {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.message-option-card-inline:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.gift-form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(255, 234, 167, 0.05));
  opacity: 0.5;
}

/* Form Sections */
.form-section-modern {
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.section-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.form-group-modern {
  position: relative;
}

.form-label-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1rem;
}

.form-control-modern {
  width: 100%;
  padding: 0.875rem 1rem;
  background: #ffffff;
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 12px;
  color: #1a1a1a;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-control-modern:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.6);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: #ffffff;
}

.form-control-modern.error {
  border-color: rgba(255, 99, 99, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 99, 99, 0.1);
}

.error-message {
  color: #ff6b6b;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: '⚠️';
  font-size: 0.7rem;
}

/* Base Mobile Styles (320px+) - Mobile First */
.gift-booking-container {
  width: 100%;
  padding: 0 8px;
  max-width: 100vw;
  box-sizing: border-box;
}

.gift-booking-layout {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: stretch;
  width: 100%;
  max-width: 100%;
}

.main-form-column {
  width: 100%;
  order: 1;
  min-width: 0;
}

.ai-features-column {
  width: 100%;
  order: 2;
  margin-top: 12px;
  min-width: 0;
}

/* Tablet Styles (768px+) */
@media (min-width: 768px) {
  .gift-booking-container {
    padding: 0 20px;
  }
  
  .gift-booking-layout {
    gap: 20px;
  }
  
  .ai-features-column {
    margin-top: 20px;
  }
}

/* Desktop Styles (1200px+) */
@media (min-width: 1200px) {
  .gift-booking-layout {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .main-form-column {
    flex: 1;
    min-width: 0;
    order: 1;
  }
  
  .ai-features-column {
    width: 400px;
    flex-shrink: 0;
    order: 2;
    margin-top: 0;
  }
}

/* Gift Header Styling - Mobile First */
.gift-header {
  position: relative;
  padding: 15px 0;
  text-align: center;
}

.gift-icon-large {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 10px;
  animation: bounce 2s ease-in-out infinite;
}

/* Tablet and Desktop Header */
@media (min-width: 768px) {
  .gift-header {
    padding: 20px 0;
  }
  
  .gift-icon-large {
    font-size: 3rem;
    margin-bottom: 15px;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* AI Features Toggle - Mobile First */
.ai-features-toggle {
  text-align: center;
  margin-bottom: 20px;
}

.ai-features-toggle .btn {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
  min-height: 44px; /* Touch-friendly */
  width: 100%;
  max-width: 280px;
}

/* Tablet and Desktop Toggle */
@media (min-width: 768px) {
  .ai-features-toggle .btn {
    padding: 10px 20px;
    font-size: 16px;
    width: auto;
    max-width: none;
  }
}

.ai-features-toggle .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
  background: linear-gradient(135deg, #ff8e53, #ff6b9d);
}

/* AI Features Column */
.ai-features-column {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* AI Features Section - Gen Z Horizontal Layout */
.ai-features-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}

/* AI Features Horizontal Container */
.ai-features-horizontal-container {
  width: 100%;
  padding: 20px;
  background: linear-gradient(135deg, #8A2BE2 0%, #6C2EB5 100%);
  border-radius: 15px;
  margin-bottom: 20px;
}

.ai-features-horizontal-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  margin-bottom: 20px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ai-feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;
  height: 100%;
}

.ai-feature-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  border-color: rgba(255, 107, 157, 0.4);
}

.ai-card-header h5 {
  color: #FFD700;
  font-size: 0.9rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-align: center;
}

.ai-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ai-quick-tips {
  flex: 1;
  margin-bottom: 10px;
}

.ai-quick-tips .tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  color: white;
  margin-bottom: 4px;
}

.ai-action-buttons {
  text-align: center;
}

.ai-action-buttons .btn {
  font-size: 0.7rem;
  padding: 4px 8px;
  border-radius: 6px;
}

/* AI Magic Grid within Message Generator */
.ai-magic-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* Responsive AI Magic Grid */
@media (max-width: 1400px) {
  .ai-magic-grid {
    grid-template-columns: 1fr !important;
    gap: 8px;
  }
}

.ai-magic-item {
  background: #f8f9ff;
  border-radius: 12px;
  padding: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  overflow: hidden;
}

.ai-magic-item:hover {
  background: #ffffff;
  transform: translateY(-2px);
  border-color: rgba(102, 126, 234, 0.4);
}

.ai-magic-item h6 {
  color: #FFD700;
  font-size: 0.85rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-magic-item .form-select,
.ai-magic-item .btn {
  font-size: 0.8rem;
  padding: 6px 10px;
  border-radius: 8px;
}

.ai-features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.ai-features-section h5 {
  color: #FFD700;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Form Labels with Icons */
.form-label {
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-label i {
  color: #FFD700;
  font-size: 1.1rem;
}

/* Enhanced Form Controls - Mobile First - Override Global Styles */
.gift-booking-page .form-control,
.gift-booking-page .form-select,
.gift-form-card .form-control,
.gift-form-card .form-select,
.gift-booking-container .form-control,
.gift-booking-container .form-select {
  background: #ffffff !important;
  background-color: #ffffff !important;
  border: 2px solid #cbd5e0 !important;
  border-radius: 8px !important;
  color: #2d3748 !important;
  font-size: 16px !important; /* Prevent zoom on iOS */
  padding: 12px 16px !important;
  transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
  min-height: 44px !important; /* Touch-friendly */
  width: 100%;
  box-sizing: border-box;
  will-change: border-color, box-shadow;
  cursor: pointer !important;
  backdrop-filter: none !important;
}

/* Tablet and Desktop Form Controls */
@media (min-width: 768px) {
  .form-control, .form-select {
    font-size: 1rem;
    padding: 12px 16px;
    min-height: 44px;
  }
}

.gift-booking-page .form-control:focus,
.gift-booking-page .form-select:focus,
.gift-form-card .form-control:focus,
.gift-form-card .form-select:focus,
.gift-booking-container .form-control:focus,
.gift-booking-container .form-select:focus {
  background: #ffffff !important;
  background-color: #ffffff !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
  transform: translateY(-2px);
  color: #2d3748 !important;
  outline: none !important;
}

.form-control::placeholder {
  color: #718096 !important;
  font-style: italic;
}

/* Ensure form controls have proper visibility */
.form-control, .form-select {
  color: #2d3748 !important;
}

.form-control:focus, .form-select:focus {
  color: #2d3748 !important;
}

.form-select option {
  background: #2c2c2c !important;
  color: white !important;
  padding: 10px;
}

/* Emoji Picker - Mobile First */
.emoji-picker {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 10px;
  width: 100%;
}

.emoji-picker button {
  cursor: pointer;
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-width: 32px;
  min-height: 32px;
}

.emoji-picker button:hover {
  background-color: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}

.emoji-categories {
  width: 100%;
}

.emoji-category {
  margin-bottom: 8px;
  width: 100%;
}

.emoji-category small {
  display: inline-block;
  margin-bottom: 4px;
  margin-right: 8px;
}

.emoji-row {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
  margin-bottom: 8px;
}

.emoji-btn, .emoji-btn-small {
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.emoji-btn:hover, .emoji-btn-small:hover {
  background-color: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
  border-color: #667eea !important;
}

.emoji-btn:active, .emoji-btn-small:active {
  transform: scale(0.95);
}

.emoji-more-btn:hover {
  background-color: rgba(113, 128, 150, 0.1) !important;
  border-color: #4a5568 !important;
}

.back-btn:hover {
  background-color: rgba(113, 128, 150, 0.1) !important;
  border-color: #4a5568 !important;
}

/* Force emoji buttons to be clickable */
.gift-booking-page .emoji-btn,
.gift-booking-page .emoji-btn-small,
.gift-booking-page .emoji-more-btn {
  pointer-events: auto !important;
  cursor: pointer !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  background-color: #ffffff !important;
  border: 2px solid #667eea !important;
  color: #667eea !important;
  z-index: 10 !important;
  position: relative !important;
}

/* Force textarea to be editable */
.gift-booking-page textarea[name="gift_message"],
.gift-booking-page #gift_message {
  background-color: #ffffff !important;
  color: #2d3748 !important;
  border: 2px solid #cbd5e0 !important;
  pointer-events: auto !important;
  cursor: text !important;
}

/* Nuclear option - Force all form elements to be responsive */
.gift-booking-page input,
.gift-booking-page select,
.gift-booking-page textarea,
.gift-booking-page button {
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
}

.gift-booking-page input:not([type="submit"]):not([type="button"]),
.gift-booking-page select,
.gift-booking-page textarea {
  background-color: #ffffff !important;
  color: #2d3748 !important;
  border: 2px solid #cbd5e0 !important;
  cursor: text !important;
}

.gift-booking-page select {
  cursor: pointer !important;
}

.gift-booking-page button {
  cursor: pointer !important;
}

/* Force gift message textarea to be fully editable */
.gift-booking-page textarea#gift_message,
.gift-booking-page textarea[name="gift_message"] {
  background-color: #ffffff !important;
  color: #2d3748 !important;
  border: 2px solid #cbd5e0 !important;
  cursor: text !important;
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  resize: vertical !important;
  outline: none !important;
}

.gift-booking-page textarea#gift_message:focus,
.gift-booking-page textarea[name="gift_message"]:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
}

.emoji-picker small {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  font-size: 0.85rem;
}

.emoji-picker .btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  transition: all 0.2s ease;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

/* Tablet and Desktop Emoji Picker */
@media (min-width: 768px) {
  .emoji-picker {
    gap: 5px;
    margin-top: 8px;
  }
  
  .emoji-picker small {
    font-size: 0.9rem;
  }
  
  .emoji-picker .btn {
    min-width: 35px;
    height: 35px;
    font-size: 1.2rem;
  }
}

.emoji-picker .btn:hover {
  background: #FFD700;
  border-color: #FFD700;
  color: #333;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* Enhanced Submit Button - Mobile First */
.glam-btn {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: none;
  color: #333;
  font-weight: 700;
  font-size: 16px;
  padding: 16px 24px;
  border-radius: 30px;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  min-height: 48px; /* Touch-friendly */
  width: 100%;
}

/* Tablet and Desktop Submit Button */
@media (min-width: 768px) {
  .glam-btn {
    font-size: 1.1rem;
    padding: 15px 30px;
    min-height: 44px;
    width: auto;
  }
}

.glam-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.glam-btn:hover::before {
  left: 100%;
}

.glam-btn:hover {
  background: linear-gradient(135deg, #FFA500, #FFD700);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
}

.glam-btn:active {
  transform: translateY(-1px) scale(1.01);
}

/* Loading Animation */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

/* Invalid Feedback Styling */
.invalid-feedback {
  color: #ff6b6b;
  font-size: 0.875rem;
  margin-top: 5px;
  font-weight: 500;
}

/* Alert Styling */
.alert {
  border-radius: 15px;
  border: none;
  padding: 15px 20px;
  font-weight: 500;
}

.alert-danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.alert-success {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: white;
}

.alert-warning {
  background: linear-gradient(135deg, #ffd43b, #fcc419);
  color: #333;
}

/* Responsive Design */
/* AI Magic Buttons - Responsive */
@media (max-width: 768px) {
  .ai-magic-buttons .btn {
    font-size: 0.75rem;
    padding: 8px 12px;
    border-radius: 10px;
  }
}

/* Message Options Horizontal Layout */
.ai-message-options-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  margin-top: 15px;
}

.message-options-horizontal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.message-option-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.message-option-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 107, 157, 0.3);
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .ai-features-horizontal-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    max-width: 500px;
  }

  .ai-feature-card {
    padding: 12px;
  }

  .message-options-horizontal {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .gift-icon-large {
    font-size: 2.5rem;
  }

  .ai-features-horizontal-container {
    padding: 15px;
  }

  .ai-features-horizontal-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    max-width: 100%;
  }

  .ai-feature-card {
    padding: 12px;
  }

  .ai-card-header h5 {
    font-size: 0.85rem;
  }

  .ai-quick-tips .tip-item {
    font-size: 0.7rem;
  }

  .ai-magic-grid {
    grid-template-columns: 1fr !important;
    gap: 8px;
  }

  .ai-magic-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .message-options-horizontal {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .emoji-picker {
    justify-content: center;
  }
  
  .glam-btn {
    font-size: 1rem;
    padding: 12px 25px;
  }
  
  /* Stack columns on mobile */
  .row .col-md-6 {
    margin-bottom: 1rem;
  }
}

/* Enhanced 2-column layout */
.form-row {
  margin-bottom: 1.5rem;
}

.form-row .col-md-6 {
  padding: 0 10px;
}

.form-row .col-md-6:first-child {
  padding-left: 0;
}

.form-row .col-md-6:last-child {
  padding-right: 0;
}

/* Better spacing for form sections */
.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section h6 {
  color: #FFD700;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

@media (max-width: 480px) {
  .gift-icon-large {
    font-size: 2rem;
  }
  
  .ai-features-section {
    padding: 15px;
  }
  
  .form-control, .form-select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .emoji-picker .btn {
    min-width: 30px;
    height: 30px;
    font-size: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .ai-features-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .gift-icon-large {
    animation: none;
  }
  
  .ai-features-section::before {
    animation: none;
  }
  
  .glam-btn::before {
    display: none;
  }
  
  .glam-btn:hover {
    transform: none;
  }
  
  .emoji-picker .btn:hover {
    transform: none;
  }
}

/* Focus States for Accessibility */
.glam-btn:focus {
  outline: 3px solid rgba(255, 215, 0, 0.5);
  outline-offset: 2px;
}

.form-control:focus,
.form-select:focus {
  outline: none;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .glam-btn:hover {
    transform: none;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
  }
  
  .glam-btn:active {
    transform: scale(0.98);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
  }
  
  .emoji-picker .btn:hover {
    transform: none;
  }
  
  .emoji-picker .btn:active {
    transform: scale(0.95);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .glam-btn {
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

/* Animation for form elements on load */
.glam-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects for form sections */
.form-group {
  transition: all 0.3s ease;
}

.form-group:hover {
  transform: translateY(-2px);
}

/* Special styling for the gift message textarea */
textarea.form-control {
  resize: vertical;
  min-height: 100px;
  text-align: left !important;
}

/* Ensure all AI-generated content is left-aligned */
.ai-generated-content,
.message-text,
textarea[name="gift_message"],
#gift_message {
  text-align: left !important;
}

textarea.form-control:focus {
  min-height: 120px;
}

/* Enhanced select dropdowns */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23FFD700' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px 12px;
  padding-right: 40px;
}

/* Pulse animation for AI features button */
.ai-features-toggle .btn {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
  }
  50% {
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.6), 0 0 0 5px rgba(255, 107, 157, 0.1);
  }
}

/* Success state styling */
.form-control.is-valid,
.form-select.is-valid {
  border-color: #51cf66 !important;
  box-shadow: 0 0 0 3px rgba(81, 207, 102, 0.2) !important;
}

/* Error state styling */
.form-control.is-invalid,
.form-select.is-invalid {
  border-color: #ff6b6b !important;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2) !important;
}

/* Message Options Styling - Mobile First */
.message-options-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.message-options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 15px;
}

.message-option-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 15px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  min-height: 80px;
}

/* Tablet and Desktop Message Options */
@media (min-width: 768px) {
  .message-options-section {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .message-options-list {
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .message-option-card {
    padding: 20px;
    min-height: 90px;
  }
}

.message-option-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #FFD700;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
}

.message-content {
  margin-bottom: 15px;
}

.message-text {
  color: white;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
  word-wrap: break-word;
}

.message-option-card .btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 0.85rem;
  padding: 10px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  min-height: 44px;
  width: 100%;
}

/* Tablet and Desktop Message Text and Buttons */
@media (min-width: 768px) {
  .message-text {
    font-size: 1rem;
  }
  
  .message-option-card .btn {
    font-size: 0.9rem;
    padding: 8px 16px;
    min-height: 40px;
    width: auto;
  }
}

.message-option-card .btn:hover {
  background: #FFD700;
  border-color: #FFD700;
  color: #333;
  transform: scale(1.05);
}

/* AI Tips Section - Mobile First */
.ai-tips-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 20px;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tip-icon {
  font-size: 1.1rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.tip-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Tablet and Desktop AI Tips */
@media (min-width: 768px) {
  .ai-tips-section {
    padding: 20px;
    margin-top: 25px;
  }
  
  .tips-list {
    gap: 12px;
  }
  
  .tip-item {
    gap: 10px;
    padding: 10px;
  }
  
  .tip-icon {
    font-size: 1.2rem;
  }
  
  .tip-text {
    font-size: 0.9rem;
  }
}

/* Button group styling */
.btn-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-group .btn {
  flex: 1;
  min-width: 150px;
}

/* Language Toggle Styling */
.btn-group-sm .btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  min-width: auto;
  flex: none;
}

.btn-group-sm .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.5);
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.btn-group-sm .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.8);
  color: white;
}

.btn-check:checked + .btn-outline-light {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: white;
  color: white;
  font-weight: 600;
}

/* Mobile-First Responsive Design */

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .gift-booking-container {
    padding: 0 8px;
  }
  
  .gift-booking-layout {
    gap: 12px;
  }
  
  .ai-features-column {
    margin-top: 12px;
  }
  
  .form-section {
    padding: 15px;
    margin-bottom: 1.5rem;
  }
  
  .form-section h6 {
    font-size: 1rem;
  }
  
  .glam-card {
    padding: 15px !important;
  }
  
  .gift-icon-large {
    font-size: 2rem;
  }
}

/* Medium Mobile (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
  .gift-booking-container {
    padding: 0 12px;
  }
  
  .gift-booking-layout {
    gap: 15px;
  }
  
  .ai-features-column {
    margin-top: 15px;
  }
}

/* Tablet (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .gift-booking-container {
    padding: 0 20px;
  }
  
  .gift-booking-layout {
    gap: 20px;
  }
  
  .ai-features-column {
    margin-top: 20px;
  }
}

/* Desktop (1200px+) */
@media (min-width: 1200px) {
  .gift-booking-layout {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .main-form-column {
    flex: 1;
    min-width: 0;
    order: 1;
  }
  
  .ai-features-column {
    width: 400px;
    flex-shrink: 0;
    order: 2;
    margin-top: 0;
  }
}

/* Button group responsive */
@media (max-width: 767px) {
  .btn-group {
    flex-direction: column;
    gap: 10px;
  }
  
  .btn-group .btn {
    width: 100%;
    margin-bottom: 0;
  }
  
  .d-grid.gap-2 {
    gap: 10px !important;
  }
}

/* Modern Mobile Responsiveness */
@media (max-width: 768px) {
  .gift-orb-1, .gift-orb-2, .gift-orb-3 {
    width: 120px;
    height: 120px;
    filter: blur(40px);
  }

  .container {
    padding: 16px;
  }

  .gift-title-modern {
    font-size: 2.5rem;
  }

  .gift-subtitle-modern {
    font-size: 1rem;
  }

  .gift-form-card {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .section-header {
    gap: 0.5rem;
  }

  .section-icon {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .gift-title-modern {
    font-size: 1.4rem !important;
  }

  .gift-header-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }

  .gift-form-card {
    padding: 1.25rem;
  }

  .form-control-modern {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .ai-toggle-button {
    padding: 0.6rem 1.25rem;
    font-size: 0.9rem;
  }

  /* AI Magic Grid Mobile Optimization */
  .ai-magic-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .ai-magic-item {
    padding: 10px;
  }

  .ai-magic-item h6 {
    font-size: 0.8rem;
    margin-bottom: 6px;
  }

  .ai-magic-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .ai-magic-buttons .btn {
    font-size: 0.7rem;
    padding: 6px 10px;
  }

  .message-option-card {
    padding: 8px;
  }

  .message-option-card .message-text {
    font-size: 0.8rem !important;
  }

  .ai-features-section {
    padding: 12px;
    margin-bottom: 10px;
  }
}