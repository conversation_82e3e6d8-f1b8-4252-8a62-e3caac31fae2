import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../context/NotificationContext';
import { useAuth } from '../context/AuthContext';
import aiService from '../services/aiService';
import './GiftBookingForm.css';

const GiftBookingForm = () => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const { user, authFetch } = useAuth();

  const [salons, setSalons] = useState([]);
  const [services, setServices] = useState([]);
  const [staff, setStaff] = useState([]);
  const [recipients, setRecipients] = useState([]);
  const [isGeneratingMessage, setIsGeneratingMessage] = useState(false);
  const [showAIFeatures, setShowAIFeatures] = useState(false);
  const [messageOptions, setMessageOptions] = useState([]);
  const [showMessageOptions, setShowMessageOptions] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [showMoreEmojis, setShowMoreEmojis] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);

  const [formData, setFormData] = useState({
    recipient_id: '',
    recipient_name: '',
    recipient_email: '',
    recipient_contact: '',
    salonId: '',
    serviceId: '',
    staffId: '',
    date: '',
    time: '',
    gift_message: '',
    paymentMethod: 'mpesa',
    relationship: '',
    occasion: '',
    tone: 'friendly',
    aiLanguage: 'english'
  });

  const [errors, setErrors] = useState({});
  const [csrftoken, setCsrftoken] = useState('');

  const relationshipOptions = [
    { value: 'bestie', label: '👯‍♀️ Bestie', emoji: '👯‍♀️' },
    { value: 'sister', label: '👭 Sister', emoji: '👭' },
    { value: 'mom', label: '👩‍👧 Mom', emoji: '👩‍👧' },
    { value: 'girlfriend', label: '💕 Girlfriend', emoji: '💕' },
    { value: 'friend', label: '👯‍♀️ Friend', emoji: '👯‍♀️' },
    { value: 'colleague', label: '💼 Colleague', emoji: '💼' },
    { value: 'other', label: '💫 Other', emoji: '💫' }
  ];

  const occasionOptions = [
    { value: 'birthday', label: '🎂 Birthday', emoji: '🎂' },
    { value: 'anniversary', label: '💕 Anniversary', emoji: '💕' },
    { value: 'friendship', label: '👯‍♀️ Friendship Day', emoji: '👯‍♀️' },
    { value: 'thank_you', label: '🙏 Thank You', emoji: '🙏' },
    { value: 'just_because', label: '✨ Just Because', emoji: '✨' },
    { value: 'congratulations', label: '🎉 Congratulations', emoji: '🎉' },
    { value: 'get_well', label: '💝 Get Well Soon', emoji: '💝' }
  ];

  const toneOptions = [
    { value: 'friendly', label: '😊 Friendly & Warm', emoji: '😊' },
    { value: 'playful', label: '😄 Playful & Fun', emoji: '😄' },
    { value: 'romantic', label: '💕 Romantic & Sweet', emoji: '💕' },
    { value: 'sassy', label: '💁‍♀️ Sassy & Confident', emoji: '💁‍♀️' },
    { value: 'caring', label: '🤗 Caring & Supportive', emoji: '🤗' }
  ];

  useEffect(() => {
    // Get CSRF token
    const getCookie = (name) => {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.substring(0, name.length + 1) === (`${name}=`)) {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    };
    setCsrftoken(getCookie('csrftoken'));

    // Fetch salons
    authFetch('/api/salons/')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setSalons(data);
        } else if (data && Array.isArray(data.results)) {
          setSalons(data.results);
        } else {
          setSalons([]);
        }
      })
      .catch(err => showNotification('Failed to load salons.', 'danger'));

    // Mock recipients (in real app, this would be friends list)
    setRecipients([
      { id: 'friend1', name: 'Sarah Johnson', email: '<EMAIL>' },
      { id: 'friend2', name: 'Maria Garcia', email: '<EMAIL>' },
      { id: 'friend3', name: 'Lisa Chen', email: '<EMAIL>' },
    ]);
  }, [authFetch, showNotification]);

  useEffect(() => {
    if (formData.salonId) {
      // Fetch services for selected salon
      authFetch(`/api/salons/${formData.salonId}/services/`)
        .then(res => res.json())
        .then(data => setServices(data))
        .catch(err => showNotification('Failed to load services.', 'danger'));

      // Fetch staff for selected salon
      authFetch(`/api/salons/${formData.salonId}/staff/`)
        .then(res => res.json())
        .then(data => setStaff(data))
        .catch(err => showNotification('Failed to load staff.', 'danger'));
    }
  }, [formData.salonId, authFetch, showNotification]);

  // Force clear any cached styles and ensure form responsiveness
  useEffect(() => {
    const timer = setTimeout(() => {
      const formElements = document.querySelectorAll('.gift-booking-page .form-control, .gift-booking-page .form-select, .gift-booking-page textarea');
      formElements.forEach(element => {
        element.style.backgroundColor = '#ffffff';
        element.style.color = '#2d3748';
        element.style.border = '2px solid #cbd5e0';
        element.style.pointerEvents = 'auto';
        element.style.cursor = element.tagName === 'TEXTAREA' ? 'text' : 'pointer';
      });

      // Force emoji buttons to be clickable
      const emojiButtons = document.querySelectorAll('.emoji-btn, .emoji-btn-small, .emoji-more-btn');
      emojiButtons.forEach(button => {
        button.style.pointerEvents = 'auto';
        button.style.cursor = 'pointer';
        button.style.backgroundColor = '#ffffff';
        button.style.border = '2px solid #667eea';
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [forceUpdate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // AI Message Generation
  const generateAIMessage = async () => {
    if (!formData.relationship || !formData.occasion) {
      showNotification('Please select relationship and occasion first!', 'warning');
      return;
    }

    setIsGeneratingMessage(true);
    
    try {
      // Get salon and service details for context
      const selectedSalon = salons.find(s => s.id === parseInt(formData.salonId));
      const selectedService = services.find(s => s.id === parseInt(formData.serviceId));

      // Extract first name only
      const getFirstName = (fullName) => {
        if (!fullName) return 'Your friend';
        return fullName.split(' ')[0];
      };

      const userProfile = {
        language: formData.aiLanguage,
        senderName: getFirstName(user?.name || user?.username),
        salonName: selectedSalon?.name || 'the salon',
        salonLocation: selectedSalon?.town || 'town',
        serviceName: selectedService?.name || 'hair styling',
        servicePrice: selectedService?.price || ''
      };

      const message = await aiService.generateGiftMessage(
        formData.relationship,
        formData.occasion,
        formData.tone,
        formData.recipient_name,
        userProfile
      );
      
      setFormData(prev => ({
        ...prev,
        gift_message: message
      }));
      
      // Analyze the generated message
      const analysis = aiService.analyzeMessage(message);
      if (analysis.suggestions.length > 0) {
        showNotification(`✨ AI generated a message! 💡 Tip: ${analysis.suggestions[0]}`, 'success');
      } else {
        showNotification('✨ Perfect AI-generated message!', 'success');
      }
    } catch (error) {
      showNotification('Failed to generate AI message. Try again!', 'danger');
    } finally {
      setIsGeneratingMessage(false);
    }
  };

  // Generate multiple message options
  const generateMessageOptions = async () => {
    if (!formData.relationship || !formData.occasion) {
      showNotification('Please select relationship and occasion first!', 'warning');
      return;
    }

    setIsGeneratingMessage(true);
    
    try {
      // Get salon and service details for context
      const selectedSalon = salons.find(s => s.id === parseInt(formData.salonId));
      const selectedService = services.find(s => s.id === parseInt(formData.serviceId));

      // Extract first name only
      const getFirstName = (fullName) => {
        if (!fullName) return 'Your friend';
        return fullName.split(' ')[0];
      };

      const userProfile = {
        language: formData.aiLanguage,
        senderName: getFirstName(user?.name || user?.username),
        salonName: selectedSalon?.name || 'the salon',
        salonLocation: selectedSalon?.town || 'town',
        serviceName: selectedService?.name || 'hair styling',
        servicePrice: selectedService?.price || ''
      };

      const options = await aiService.generateMessageOptions(
        formData.relationship,
        formData.occasion,
        formData.tone,
        formData.recipient_name,
        userProfile
      );
      
      setMessageOptions(options);
      setShowMessageOptions(true);
      showNotification(`✨ Generated ${options.length} message options!`, 'success');
    } catch (error) {
      showNotification('Failed to generate message options. Try again!', 'danger');
    } finally {
      setIsGeneratingMessage(false);
    }
  };

  // Select a message option
  const selectMessageOption = (message) => {
    setFormData(prev => ({
      ...prev,
      gift_message: message
    }));
    setShowMessageOptions(false);
    showNotification('✨ Message selected!', 'success');
  };

  const validate = () => {
    const newErrors = {};
    if (!formData.recipient_name || !formData.recipient_name.trim()) newErrors.recipient_name = 'Please enter recipient name';
    if (!formData.recipient_contact || !formData.recipient_contact.trim()) newErrors.recipient_contact = 'Please enter recipient phone or email';
    if (!formData.salonId) newErrors.salonId = 'Please select a salon';
    if (!formData.serviceId) newErrors.serviceId = 'Please select a service';
    if (!formData.staffId) newErrors.staffId = 'Please select a staff member';
    if (!formData.date) newErrors.date = 'Please select a date';
    if (!formData.time) newErrors.time = 'Please select a time';
    if (!formData.gift_message.trim()) newErrors.gift_message = 'Please add a gift message';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Send gift notification email
  const sendGiftNotificationEmail = async (bookingResult, bookingData) => {
    try {
      // Get salon and service details for context
      const selectedSalon = salons.find(s => s.id === parseInt(formData.salonId));
      const selectedService = services.find(s => s.id === parseInt(formData.serviceId));

      // Extract first name only
      const getFirstName = (fullName) => {
        if (!fullName) return 'Your friend';
        return fullName.split(' ')[0];
      };

      const notificationData = {
        type: 'gift',
        recipientEmail: formData.recipient_contact,
        recipientName: formData.recipient_name,
        giftMessage: formData.gift_message,
        senderName: getFirstName(user?.name || user?.username),
        salonDetails: {
          name: selectedSalon?.name || 'Salon',
          location: selectedSalon?.town || 'Location'
        },
        bookingDetails: {
          id: bookingResult.id || 'N/A',
          service: selectedService?.name || 'Beauty Service',
          date: formData.date,
          time: formData.time
        }
      };

      const response = await fetch('http://127.0.0.1:8000/api/notifications/send-confirmation/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrftoken,
        },
        body: JSON.stringify(notificationData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send gift notification');
      }

      const result = await response.json();
      console.log('✅ Gift notification sent:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to send gift notification:', error);
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validate()) {
      try {
        const bookingData = {
          purchaser_id: user.userId,
          recipient_name: formData.recipient_name,
          recipient_contact: formData.recipient_contact,
          gift_message: formData.gift_message,
          booking: {
            userName: formData.recipient_name,
            salon: formData.salonId,
            service: formData.serviceId,
            staff: formData.staffId,
            date: formData.date,
            time: formData.time,
            status: 'Confirmed',
            notes: `Gift booking from ${user.name || user.username}`,
          }
        };

        const res = await authFetch('/api/bookings/gift/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrftoken,
          },
          body: JSON.stringify(bookingData),
        });

        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.detail || 'Failed to create gift booking');
        }

        const bookingResult = await res.json();

        // Send gift notification email
        try {
          await sendGiftNotificationEmail(bookingResult, bookingData);
        } catch (emailError) {
          console.warn('Failed to send gift notification email:', emailError);
          // Don't fail the entire booking for email issues
        }

        showNotification('🎉 Gift booking created successfully! The recipient will be notified.', 'success');
        navigate('/booking-success');
      } catch (err) {
        setErrors({ ...errors, payment: err.message });
        showNotification(`Gift booking failed: ${err.message}`, 'danger');
      }
    }
  };

  const paymentMethods = [
    { value: 'mpesa', label: 'M-Pesa' },
    { value: 'paypal', label: 'PayPal' },
    { value: 'banktransfer', label: 'Bank Transfer' },
    { value: 'wise', label: 'Wise' },
    { value: 'visa', label: 'Visa' },
  ];

  const availableServices = formData.salonId
    ? services.filter(service => service.salon === parseInt(formData.salonId))
    : [];

  const availableStaff = formData.serviceId
    ? staff.filter(member => {
      const parsedServiceId = parseInt(formData.serviceId);
      const isStaffForSalon = member.salon === parseInt(formData.salonId);
      const canPerformService = !Number.isNaN(parsedServiceId) && member.services.includes(parsedServiceId);
      return isStaffForSalon && canPerformService;
    })
    : [];

  return (
    <div className={`gift-booking-page cache-bust-${forceUpdate}`}>
      <div className="gift-background-effects">
        <div className="gift-gradient-orb gift-orb-1"></div>
        <div className="gift-gradient-orb gift-orb-2"></div>
        <div className="gift-gradient-orb gift-orb-3"></div>
      </div>
      <div className="container">
        {/* Back Button */}
        <div className="back-button-container">
          <button
            onClick={() => navigate(-1)}
            className="back-button-modern"
          >
            <span className="back-icon">←</span>
            <span className="back-text">Back</span>
            <div className="button-glow"></div>
          </button>
        </div>
        <div className="gift-booking-container-modern">
          <div className="gift-header-modern">
            <div className="gift-header-content">
              <div className="gift-header-badge">
                <span className="badge-icon">🎁</span>
                <span className="badge-text">AI GIFT GENERATOR</span>
              </div>
              <h1 className="gift-title-modern">
                <span className="title-gradient">Gift the Glow</span>
                <span className="title-accent">✨</span>
              </h1>
              <p className="gift-subtitle-modern">
                AI-powered gift messages that hit different
              </p>
            </div>
          </div>

          <div className="gift-booking-layout-modern">
            {/* Main Form Column - 50% */}
            <div className="main-form-column">
              <div className="gift-form-card">
              <form onSubmit={handleSubmit}>
                {/* Recipient Information Section */}
                <div className="form-section-modern">
                  <div className="section-header">
                    <span className="section-icon">💝</span>
                    <h3 className="section-title">Who's Getting Spoiled?</h3>
                  </div>
                  <div className="form-grid">
                    <div className="form-group-modern">
                      <label htmlFor="recipient_name" className="form-label-modern">
                        <span className="label-icon">👤</span>
                        <span className="label-text">Choose Your Person</span>
                      </label>
                      <input
                        type="text"
                        id="recipient_name"
                        name="recipient_name"
                        value={formData.recipient_name}
                        onChange={handleChange}
                        placeholder="Enter recipient's name"
                        className={`form-control ${errors.recipient_name ? 'is-invalid' : ''}`}
                        style={{
                          color: '#2d3748',
                          backgroundColor: '#ffffff',
                          border: '2px solid #cbd5e0',
                          borderRadius: '8px',
                          fontSize: '16px',
                          padding: '12px 16px',
                          cursor: 'text',
                          pointerEvents: 'auto'
                        }}
                        required
                      />
                      {errors.recipient_name && <div className="invalid-feedback">{errors.recipient_name}</div>}
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label htmlFor="recipient_contact" className="form-label" style={{ color: '#2d3748' }}>
                          <i className="bi bi-telephone me-2" />
                          Recipient Contact
                        </label>
                        <input
                          type="text"
                          id="recipient_contact"
                          name="recipient_contact"
                          value={formData.recipient_contact}
                          onChange={handleChange}
                          placeholder="Phone or email address"
                          className={`form-control ${errors.recipient_contact ? 'is-invalid' : ''}`}
                          style={{
                            color: '#2d3748',
                            backgroundColor: '#ffffff',
                            border: '2px solid #cbd5e0',
                            borderRadius: '8px',
                            fontSize: '16px',
                            padding: '12px 16px'
                          }}
                          required
                        />
                        {errors.recipient_contact && <div className="invalid-feedback">{errors.recipient_contact}</div>}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Gift Message */}
                <div className="mb-3">
                  <label htmlFor="gift_message" className="form-label" style={{ color: '#2d3748' }}>
                    <i className="bi bi-chat-heart me-2" />
                    Gift Message
                  </label>
                  <textarea
                    id="gift_message"
                    name="gift_message"
                    value={formData.gift_message}
                    onChange={handleChange}
                    className={`form-control ${errors.gift_message ? 'is-invalid' : ''}`}
                    style={{
                      color: '#2d3748',
                      backgroundColor: '#ffffff',
                      border: '2px solid #cbd5e0',
                      borderRadius: '8px',
                      fontSize: '16px',
                      lineHeight: '1.5',
                      padding: '12px 16px',
                      cursor: 'text',
                      pointerEvents: 'auto'
                    }}
                    rows="4"
                    placeholder="Type your message here or unlock AI magic below ✨"
                    required
                  />
                  {errors.gift_message && <div className="invalid-feedback">{errors.gift_message}</div>}
            
                  {/* Enhanced Emoji Picker */}
                  <div className="emoji-picker mt-2">
                    <small style={{ color: '#2d3748' }}>Quick add: </small>
                    {['💝', '✨', '💕', '🎉', '💅', '💁‍♀️', '🔥', '💯', '👑', '💎'].map(emoji => (
                      <button
                        key={emoji}
                        type="button"
                        className="emoji-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          setFormData(prev => ({
                            ...prev,
                            gift_message: prev.gift_message + emoji
                          }));
                        }}
                        style={{
                          fontSize: '1.2rem',
                          padding: '8px 12px',
                          border: '2px solid #667eea',
                          borderRadius: '8px',
                          color: '#667eea',
                          backgroundColor: '#ffffff',
                          cursor: 'pointer',
                          minWidth: '40px',
                          minHeight: '40px',
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        {emoji}
                      </button>
                    ))}
                    <button
                      type="button"
                      className="emoji-more-btn"
                      onClick={(e) => {
                        e.preventDefault();
                        setShowMoreEmojis(!showMoreEmojis);
                      }}
                      style={{
                        fontSize: '0.9rem',
                        padding: '8px 16px',
                        border: '2px solid #718096',
                        borderRadius: '8px',
                        color: '#718096',
                        backgroundColor: '#ffffff',
                        cursor: 'pointer',
                        minHeight: '40px',
                        fontWeight: '600'
                      }}
                    >
                      {showMoreEmojis ? '▲ Less' : '▼ More...'}
                    </button>

                    {showMoreEmojis && (
                      <div className="mt-2">
                        <div className="emoji-categories">
                          <div className="emoji-category">
                            <small style={{ color: '#2d3748', fontWeight: 'bold' }}>Beauty & Style: </small>
                            <div className="emoji-row">
                              {['💄', '💋', '🌸', '🌺', '🌹', '🦋', '✨', '💫', '⭐', '🌟'].map(emoji => (
                                <button
                                  key={emoji}
                                  type="button"
                                  className="emoji-btn-small"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setFormData(prev => ({
                                      ...prev,
                                      gift_message: prev.gift_message + emoji
                                    }));
                                  }}
                                  style={{
                                    fontSize: '1.1rem',
                                    padding: '6px 8px',
                                    border: '1px solid #667eea',
                                    borderRadius: '6px',
                                    color: '#667eea',
                                    backgroundColor: '#ffffff',
                                    cursor: 'pointer',
                                    minWidth: '32px',
                                    minHeight: '32px',
                                    margin: '2px'
                                  }}
                                >
                                  {emoji}
                                </button>
                              ))}
                            </div>
                          </div>
                          <div className="emoji-category mt-1">
                            <small style={{ color: '#2d3748', fontWeight: 'bold' }}>Celebration: </small>
                            <div className="emoji-row">
                              {['🎊', '🎈', '🥳', '🍾', '🥂', '🎂', '🎁', '🎀', '🌈', '💐'].map(emoji => (
                                <button
                                  key={emoji}
                                  type="button"
                                  className="emoji-btn-small"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setFormData(prev => ({
                                      ...prev,
                                      gift_message: prev.gift_message + emoji
                                    }));
                                  }}
                                  style={{
                                    fontSize: '1.1rem',
                                    padding: '6px 8px',
                                    border: '1px solid #667eea',
                                    borderRadius: '6px',
                                    color: '#667eea',
                                    backgroundColor: '#ffffff',
                                    cursor: 'pointer',
                                    minWidth: '32px',
                                    minHeight: '32px',
                                    margin: '2px'
                                  }}
                                >
                                  {emoji}
                                </button>
                              ))}
                            </div>
                          </div>
                          <div className="emoji-category mt-1">
                            <small style={{ color: '#2d3748', fontWeight: 'bold' }}>Love & Friendship: </small>
                            <div className="emoji-row">
                              {['💖', '💗', '💓', '💞', '💘', '🤗', '😘', '😍', '🥰', '😊'].map(emoji => (
                                <button
                                  key={emoji}
                                  type="button"
                                  className="emoji-btn-small"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setFormData(prev => ({
                                      ...prev,
                                      gift_message: prev.gift_message + emoji
                                    }));
                                  }}
                                  style={{
                                    fontSize: '1.1rem',
                                    padding: '6px 8px',
                                    border: '1px solid #667eea',
                                    borderRadius: '6px',
                                    color: '#667eea',
                                    backgroundColor: '#ffffff',
                                    cursor: 'pointer',
                                    minWidth: '32px',
                                    minHeight: '32px',
                                    margin: '2px'
                                  }}
                                >
                                  {emoji}
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Salon & Service Section */}
                <div className="form-section">
                  <h6 style={{ color: '#2d3748' }}>
                    <i className="bi bi-shop me-2" />
                    Salon & Service Details
                  </h6>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label htmlFor="salonId" className="form-label" style={{ color: '#2d3748' }}>
                          <i className="bi bi-shop me-2" />
                          Salon
                        </label>
                        <select
                          id="salonId"
                          name="salonId"
                          value={formData.salonId}
                          onChange={handleChange}
                          className={`form-select ${errors.salonId ? 'is-invalid' : ''}`}
                          style={{
                            color: '#2d3748',
                            backgroundColor: '#ffffff',
                            border: '2px solid #cbd5e0',
                            borderRadius: '8px',
                            fontSize: '16px',
                            padding: '12px 16px',
                            cursor: 'pointer'
                          }}
                          required
                        >
                          <option value="">Select salon...</option>
                          {salons.map(salon => (
                            <option key={salon.id} value={salon.id} className="bg-dark" style={{ color: '#f7fafc' }}>
                              {salon.name}
                              {' '}
                              -
                              {salon.town}
                            </option>
                          ))}
                        </select>
                        {errors.salonId && <div className="invalid-feedback">{errors.salonId}</div>}
                      </div>
                    </div>

                    <div className="col-md-6">
                      <div className="mb-3">
                        <label htmlFor="serviceId" className="form-label" style={{ color: '#2d3748' }}>
                          <i className="bi bi-scissors me-2" />
                          Service
                        </label>
                        <select
                          id="serviceId"
                          name="serviceId"
                          value={formData.serviceId}
                          onChange={handleChange}
                          className={`form-select ${errors.serviceId ? 'is-invalid' : ''}`}
                          style={{
                            color: '#2d3748',
                            backgroundColor: '#ffffff',
                            border: '2px solid #cbd5e0',
                            borderRadius: '8px',
                            fontSize: '16px',
                            padding: '12px 16px',
                            cursor: 'pointer'
                          }}
                          required
                          disabled={!formData.salonId}
                        >
                          <option value="">Select service...</option>
                          {availableServices.map(service => (
                            <option key={service.id} value={service.id} className="bg-dark" style={{ color: '#f7fafc' }}>
                              {service.name}
                              {' '}
                              - Ksh
                              {service.price}
                            </option>
                          ))}
                        </select>
                        {errors.serviceId && <div className="invalid-feedback">{errors.serviceId}</div>}
                      </div>
                    </div>
                  </div>

                  {/* Staff Selection - Full Width */}
                  <div className="mb-3">
                    <label htmlFor="staffId" className="form-label" style={{ color: '#2d3748' }}>
                      <i className="bi bi-person-workspace me-2" />
                      Staff Member
                    </label>
                    <select
                      id="staffId"
                      name="staffId"
                      value={formData.staffId}
                      onChange={handleChange}
                      className={`form-select ${errors.staffId ? 'is-invalid' : ''}`}
                      style={{
                        color: '#2d3748',
                        backgroundColor: '#ffffff',
                        border: '2px solid #cbd5e0',
                        borderRadius: '8px',
                        fontSize: '16px',
                        padding: '12px 16px',
                        cursor: 'pointer'
                      }}
                      required
                      disabled={!formData.serviceId}
                    >
                      <option value="">Select staff member...</option>
                      {availableStaff.map(member => (
                        <option key={member.id} value={member.id} className="bg-dark" style={{ color: '#f7fafc' }}>
                          {member.name}
                          {' '}
                          (
                          {member.role}
                          )
                        </option>
                      ))}
                    </select>
                    {errors.staffId && <div className="invalid-feedback">{errors.staffId}</div>}
                  </div>
                </div>

                {/* Schedule & Payment Section */}
                <div className="form-section">
                  <h6 style={{ color: '#2d3748' }}>
                    <i className="bi bi-calendar-heart me-2" />
                    Schedule & Payment
                  </h6>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label htmlFor="date" className="form-label" style={{ color: '#2d3748' }}>
                          <i className="bi bi-calendar-heart me-2" />
                          Date
                        </label>
                        <input
                          type="date"
                          id="date"
                          name="date"
                          value={formData.date}
                          onChange={handleChange}
                          className={`form-control ${errors.date ? 'is-invalid' : ''}`}
                          style={{
                            color: '#2d3748',
                            backgroundColor: '#ffffff',
                            border: '2px solid #cbd5e0',
                            borderRadius: '8px',
                            fontSize: '16px',
                            padding: '12px 16px',
                            cursor: 'pointer'
                          }}
                          required
                        />
                        {errors.date && <div className="invalid-feedback">{errors.date}</div>}
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label htmlFor="time" className="form-label" style={{ color: '#2d3748' }}>
                          <i className="bi bi-clock me-2" />
                          Time
                        </label>
                        <input
                          type="time"
                          id="time"
                          name="time"
                          value={formData.time}
                          onChange={handleChange}
                          className={`form-control ${errors.time ? 'is-invalid' : ''}`}
                          style={{
                            color: '#2d3748',
                            backgroundColor: '#ffffff',
                            border: '2px solid #cbd5e0',
                            borderRadius: '8px',
                            fontSize: '16px',
                            padding: '12px 16px',
                            cursor: 'pointer'
                          }}
                          required
                        />
                        {errors.time && <div className="invalid-feedback">{errors.time}</div>}
                      </div>
                    </div>
                  </div>

                  {/* Payment Method - Full Width */}
                  <div className="mb-3">
                    <label htmlFor="paymentMethod" className="form-label" style={{ color: '#2d3748' }}>
                      <i className="bi bi-credit-card me-2" />
                      Payment Method
                    </label>
                    <select
                      id="paymentMethod"
                      name="paymentMethod"
                      value={formData.paymentMethod}
                      onChange={handleChange}
                      className="form-select"
                      style={{
                        color: '#2d3748',
                        backgroundColor: '#ffffff',
                        border: '2px solid #cbd5e0',
                        borderRadius: '8px',
                        fontSize: '16px',
                        padding: '12px 16px',
                        cursor: 'pointer'
                      }}
                      required
                    >
                      {paymentMethods.map((method) => (
                        <option key={method.value} value={method.value} className="bg-dark" style={{ color: '#f7fafc' }}>
                          {method.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {errors.payment && <div className="alert alert-danger mt-3">{errors.payment}</div>}
          
                <div className="d-flex gap-3 mt-4">
                  <button
                    type="button"
                    className="btn btn-outline-secondary flex-fill back-btn"
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(-1);
                    }}
                    style={{
                      fontSize: '1rem',
                      padding: '12px 20px',
                      borderRadius: '12px',
                      border: '2px solid #718096',
                      backgroundColor: '#ffffff',
                      color: '#718096',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <i className="bi bi-arrow-left me-2" />
                    Back
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary flex-fill glam-btn"
                    style={{
                      fontSize: '1rem',
                      padding: '12px 20px',
                      borderRadius: '12px',
                      background: 'linear-gradient(135deg, #667eea, #764ba2)',
                      border: 'none',
                      fontWeight: '600'
                    }}
                  >
                    <i className="bi bi-gift me-2" />
                    🎁 Send Surprise Gift
                  </button>
                </div>
              </form>
              </div>
            </div>

            {/* AI Features Column - 50% */}
            <div className="ai-features-column">
              <div className="ai-features-header">
                <h3 className="ai-features-title">
                  <span className="ai-icon">🤖</span>
                  AI Magic Unlocked ✨
                </h3>
                <p className="ai-features-subtitle">
                  Generate perfect gift messages with AI
                </p>

                {/* AI Magic Toggle Button */}
                <button
                  type="button"
                  className={`ai-toggle-button ${showAIFeatures ? 'active' : ''}`}
                  onClick={() => setShowAIFeatures(!showAIFeatures)}
                >
                  <span className="toggle-icon">🤖</span>
                  <span className="toggle-text">
                    {showAIFeatures ? 'Hide AI Magic' : 'Unlock AI Magic'}
                  </span>
                </button>
              </div>

              {/* AI Magic Grid - Conditional */}
              {showAIFeatures ? (
                <div className="ai-magic-grid">
                <div className="ai-magic-item">
                  <h6>
                    <span>💕</span>
                    Relationship
                  </h6>
                  <select
                    name="relationship"
                    value={formData.relationship}
                    onChange={handleChange}
                    className="form-select form-control-modern"
                  >
                    <option value="">Choose...</option>
                    <option value="bestie">Bestie</option>
                    <option value="partner">Partner</option>
                    <option value="family">Family</option>
                    <option value="colleague">Colleague</option>
                    <option value="friend">Friend</option>
                  </select>
                </div>

                <div className="ai-magic-item">
                  <h6>
                    <span>🎉</span>
                    Occasion
                  </h6>
                  <select
                    name="occasion"
                    value={formData.occasion}
                    onChange={handleChange}
                    className="form-select form-control-modern"
                  >
                    <option value="">Choose...</option>
                    <option value="birthday">Birthday</option>
                    <option value="anniversary">Anniversary</option>
                    <option value="graduation">Graduation</option>
                    <option value="promotion">Promotion</option>
                    <option value="justbecause">Just Because</option>
                  </select>
                </div>

                <div className="ai-magic-item">
                  <h6>
                    <span>😊</span>
                    Tone
                  </h6>
                  <select
                    name="tone"
                    value={formData.tone}
                    onChange={handleChange}
                    className="form-select form-control-modern"
                  >
                    <option value="friendly">Friendly</option>
                    <option value="playful">Playful</option>
                    <option value="romantic">Romantic</option>
                    <option value="sassy">Sassy</option>
                    <option value="caring">Caring</option>
                  </select>
                </div>

                <div className="ai-magic-item">
                  <h6>
                    <span>🎯</span>
                    AI Magic
                  </h6>
                  <div className="ai-magic-buttons">
                    <button
                      type="button"
                      className="btn btn-primary"
                      onClick={generateAIMessage}
                      disabled={isGeneratingMessage || !formData.relationship || !formData.occasion}
                    >
                      {isGeneratingMessage ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-1" />
                          Gen...
                        </>
                      ) : (
                        <>
                          <i className="bi bi-magic me-1" />
                          Generate
                        </>
                      )}
                    </button>

                    <button
                      type="button"
                      className="btn btn-outline-primary"
                      onClick={generateMessageOptions}
                      disabled={isGeneratingMessage || !formData.relationship || !formData.occasion}
                    >
                      <i className="bi bi-list-ul me-1" />
                      Options
                    </button>
                  </div>
                </div>

                {/* Message Options Display - Inside AI Column */}
                {showMessageOptions && messageOptions.length > 0 && (
                  <div className="ai-message-options-section">
                    <h5 className="text-center mb-3" style={{ color: '#667eea' }}>
                      <i className="bi bi-list-ul me-2" />
                      Pick Your Vibe ✨
                    </h5>

                    <div
                      className={`message-options-grid ${
                        messageOptions.length === 1 ? 'single-message' :
                        messageOptions.length === 2 ? 'two-messages' :
                        'multiple-messages'
                      }`}
                    >
                      {messageOptions.map((message, index) => (
                        <div key={index} className="message-option-card-inline">
                          <div className="message-content mb-2">
                            <p className="message-text mb-2" style={{ fontSize: '0.85rem', lineHeight: '1.4', color: '#1a1a1a' }}>
                              {message}
                            </p>
                          </div>
                          <div className="d-flex justify-content-between align-items-center">
                            <span className="badge bg-primary" style={{ fontSize: '0.7rem' }}>
                              Option {index + 1}
                            </span>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-primary"
                              onClick={() => selectMessageOption(message)}
                              style={{ fontSize: '0.75rem', padding: '6px 8px' }}
                              title="Select this message"
                              aria-label="Select this message"
                            >
                              <i className="bi bi-check-circle" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="text-center mt-3">
                      <button
                        type="button"
                        className="btn btn-sm btn-outline-secondary"
                        onClick={() => setShowMessageOptions(false)}
                        style={{ fontSize: '0.8rem' }}
                      >
                        <i className="bi bi-x-circle me-1" />
                        Close Options
                      </button>
                    </div>
                  </div>
                )}
              </div>
              ) : (
                <div className="ai-magic-inactive" onClick={() => setShowTooltip(true)}>
                  <div className="inactive-overlay">
                    <div className="inactive-content">
                      <span className="inactive-icon">🔒</span>
                      <p className="inactive-text">AI Magic Features</p>
                      <p className="inactive-subtitle">Click "Unlock AI Magic" to access</p>
                    </div>
                  </div>

                  {/* Tooltip */}
                  {showTooltip && (
                    <div className="ai-tooltip">
                      <span className="tooltip-text">Unlock AI Magic first! 🤖✨</span>
                      <button
                        className="tooltip-close"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowTooltip(false);
                        }}
                      >
                        ×
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiftBookingForm;
