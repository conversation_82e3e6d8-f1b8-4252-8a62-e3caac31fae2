/* Dark Charcoal Glassmorphism Navbar */
:root {
  --navbar-bg: rgba(26, 26, 26, 0.85);
  --navbar-glass: rgba(40, 40, 40, 0.9);
  --navbar-text: #ffffff;
  --navbar-text-muted: rgba(255, 255, 255, 0.7);
  --navbar-accent: #ffb347;
  --navbar-secondary: #ff6f61;
  --navbar-border: rgba(255, 255, 255, 0.1);
  --navbar-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --navbar-glow: 0 0 20px rgba(255, 179, 71, 0.2);
  --navbar-radius: 16px;
  --navbar-transition: all 0.2s ease;
}

/* Main Navbar - Dark Glassmorphism */
.modern-navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--navbar-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--navbar-border);
  box-shadow: var(--navbar-shadow), var(--navbar-glow);
  transition: var(--navbar-transition);
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-left: auto;
  margin-right: 2rem;
}

.desktop-nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--navbar-text);
  text-decoration: none;
  border-radius: 8px;
  transition: var(--navbar-transition);
  position: relative;
  font-weight: 500;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--navbar-border);
}

.desktop-nav-link:hover {
  background: rgba(255, 179, 71, 0.1);
  color: var(--navbar-accent);
  transform: translateY(-1px);
}

.desktop-nav-link.active {
  background: rgba(255, 179, 71, 0.15);
  color: var(--navbar-accent);
  font-weight: 600;
}

.desktop-dropdown {
  position: relative;
}

.desktop-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid var(--navbar-border);
  border-radius: 12px;
  box-shadow: var(--navbar-shadow);
  padding: 0.5rem;
  margin-top: 0.5rem;
  z-index: 1004;
  animation: dropdownSlideIn 0.2s ease forwards;
}

.desktop-dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  color: var(--navbar-text);
  text-decoration: none;
  border-radius: 8px;
  transition: var(--navbar-transition);
  position: relative;
  font-size: 0.9rem;
}

.desktop-dropdown-item:hover {
  background: rgba(255, 179, 71, 0.1);
  color: var(--navbar-accent);
}

.desktop-dropdown-item.active {
  background: rgba(255, 179, 71, 0.15);
  color: var(--navbar-accent);
  font-weight: 600;
}

/* Desktop Auth Section */
.desktop-auth {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.desktop-user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.desktop-user-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--navbar-accent), var(--navbar-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--navbar-border);
  transition: var(--navbar-transition);
}

.desktop-user-avatar:hover {
  border-color: var(--navbar-accent);
  transform: scale(1.05);
}

.desktop-avatar-initials {
  font-size: 0.9rem;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.desktop-user-details {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.desktop-user-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--navbar-text);
}

.desktop-user-role {
  font-size: 0.75rem;
  color: var(--navbar-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.desktop-notification-bell {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  border: 1px solid var(--navbar-border);
  cursor: pointer;
  transition: var(--navbar-transition);
  backdrop-filter: blur(10px);
}

.desktop-notification-bell:hover {
  background: rgba(255, 179, 71, 0.1);
  border-color: var(--navbar-accent);
  box-shadow: 0 0 15px rgba(255, 179, 71, 0.2);
}

.desktop-bell-icon {
  font-size: 0.9rem;
  color: var(--navbar-text);
  transition: var(--navbar-transition);
}

.desktop-notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: var(--navbar-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  color: #fff;
  border: 2px solid var(--navbar-glass);
  animation: genZPulse 2s infinite;
}

.desktop-logout-button {
  background: rgba(255, 111, 97, 0.1);
  border: 1px solid var(--navbar-secondary);
  color: var(--navbar-secondary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--navbar-transition);
  backdrop-filter: blur(10px);
}

.desktop-logout-button:hover {
  background: rgba(255, 111, 97, 0.2);
  border-color: var(--navbar-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 111, 97, 0.3);
}

.desktop-auth-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.desktop-auth-link {
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--navbar-transition);
  backdrop-filter: blur(10px);
  border: 1px solid var(--navbar-border);
}

.desktop-auth-link.login {
  background: rgba(255, 255, 255, 0.05);
  color: var(--navbar-text);
}

.desktop-auth-link.login:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--navbar-text);
  transform: translateY(-1px);
}

.desktop-auth-link.signup {
  background: linear-gradient(135deg, var(--navbar-accent), var(--navbar-secondary));
  color: #fff;
  border-color: var(--navbar-accent);
}

.desktop-auth-link.signup:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 179, 71, 0.3);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Brand Section */
.brand-section {
  flex-shrink: 0;
}

.brand-link {
  text-decoration: none;
  color: inherit;
  transition: var(--navbar-transition);
}

.brand-link:hover {
  transform: translateY(-1px);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--navbar-text);
  letter-spacing: -0.5px;
  line-height: 1;
  background: linear-gradient(135deg, var(--navbar-accent), var(--navbar-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 10px rgba(255, 179, 71, 0.3);
}

.logo-tagline {
  font-size: 0.7rem;
  font-weight: 500;
  color: var(--navbar-text-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: -2px;
  opacity: 0.9;
}

/* GenZ Mobile Toggle Button - Unique Design */
.mobile-toggle {
  display: none;
  background: linear-gradient(135deg, rgba(255, 179, 71, 0.1), rgba(255, 111, 97, 0.1));
  border: 2px solid transparent;
  background-clip: padding-box;
  padding: 0.75rem;
  cursor: pointer;
  border-radius: 16px;
  transition: var(--navbar-transition);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.mobile-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--navbar-accent), var(--navbar-secondary));
  border-radius: 16px;
  padding: 2px;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.mobile-toggle:hover {
  background: linear-gradient(135deg, rgba(255, 179, 71, 0.2), rgba(255, 111, 97, 0.2));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 179, 71, 0.3), 0 0 20px rgba(255, 179, 71, 0.2);
}

.mobile-toggle:active {
  transform: translateY(0) scale(0.95);
}

/* Unique Gen Z Hamburger Design */
.hamburger {
  display: flex;
  flex-direction: column;
  width: 28px;
  height: 22px;
  position: relative;
  justify-content: space-between;
  align-items: center;
}

.hamburger span {
  display: block;
  height: 3px;
  width: 100%;
  background: linear-gradient(90deg, var(--navbar-accent), var(--navbar-secondary));
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
  box-shadow: 0 0 10px rgba(255, 179, 71, 0.4);
  position: relative;
}

.hamburger span::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.mobile-toggle:hover .hamburger span::before {
  opacity: 1;
}

/* Unique GenZ Hamburger Animation - Different widths for each line */
.hamburger span:nth-child(1) {
  width: 100%;
  transform-origin: left center;
}

.hamburger span:nth-child(2) {
  width: 75%;
  align-self: flex-end;
  transform-origin: right center;
}

.hamburger span:nth-child(3) {
  width: 50%;
  align-self: flex-start;
  transform-origin: left center;
}

/* Active state - Unique transformation */
.mobile-toggle.active .hamburger span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
  width: 100%;
  box-shadow: 0 0 15px rgba(255, 179, 71, 0.6);
}

.mobile-toggle.active .hamburger span:nth-child(2) {
  opacity: 0;
  transform: scale(0) rotate(90deg);
  width: 0%;
}

.mobile-toggle.active .hamburger span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
  width: 100%;
  box-shadow: 0 0 15px rgba(255, 179, 71, 0.6);
}

/* Pulsing effect when active */
.mobile-toggle.active {
  animation: genZPulse 2s infinite;
}

@keyframes genZPulse {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(255, 179, 71, 0.3), 0 0 20px rgba(255, 179, 71, 0.2);
  }
  50% {
    box-shadow: 0 8px 35px rgba(255, 179, 71, 0.5), 0 0 30px rgba(255, 179, 71, 0.4);
  }
}

/* Modern Navigation - Mobile First */
@media (max-width: 1023px) {
  .modern-nav {
    display: none;
  }

  .modern-nav.nav-open {
    display: block;
  }
}

.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 999;
  opacity: 0;
  animation: fadeIn 0.2s ease forwards;
}

.nav-content {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 320px;
  max-width: 85vw;
  background: var(--navbar-glass);
  backdrop-filter: blur(20px);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.4);
  border-left: 1px solid var(--navbar-border);
  z-index: 1002;
  transform: translateX(100%);
  animation: slideInRight 0.2s ease forwards;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--navbar-border);
}

.nav-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--navbar-text);
}

.nav-close {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--navbar-border);
  font-size: 1.5rem;
  color: var(--navbar-text);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: var(--navbar-transition);
  backdrop-filter: blur(10px);
}

.nav-close:hover {
  background: rgba(255, 179, 71, 0.1);
  border-color: var(--navbar-accent);
  box-shadow: 0 0 15px rgba(255, 179, 71, 0.2);
}

.nav-links {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
  pointer-events: auto;
}

.nav-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  color: var(--navbar-text);
  text-decoration: none;
  transition: var(--navbar-transition);
  position: relative;
  border-left: 3px solid transparent;
  backdrop-filter: blur(10px);
  cursor: pointer;
  pointer-events: auto;
  z-index: 1003;
  min-height: 48px;
  touch-action: manipulation;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  font-size: inherit;
  font-family: inherit;
}

.nav-link:hover {
  background: rgba(255, 179, 71, 0.1);
  color: var(--navbar-accent);
  border-left-color: var(--navbar-accent);
  box-shadow: inset 0 0 20px rgba(255, 179, 71, 0.1);
}

.nav-link.active {
  background: rgba(255, 179, 71, 0.15);
  color: var(--navbar-accent);
  border-left-color: var(--navbar-accent);
  font-weight: 600;
  box-shadow: inset 0 0 20px rgba(255, 179, 71, 0.2);
}

.nav-link-text {
  font-size: 0.95rem;
}

/* Vendor Signup link - smaller font to fit better */
.nav-link[href="/register-vendor"] .nav-link-text,
.desktop-nav-link[href="/register-vendor"] {
  font-size: 0.85rem;
}

@media (max-width: 768px) {
  .nav-link[href="/register-vendor"] .nav-link-text {
    font-size: 0.9rem;
  }
}

.nav-indicator {
  width: 6px;
  height: 6px;
  background: var(--navbar-accent);
  border-radius: 50%;
}

/* Dropdown Styles */
.nav-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dropdown-arrow {
  font-size: 0.8rem;
  color: var(--navbar-text-muted);
  transition: var(--navbar-transition);
  margin-left: 0.5rem;
}

.dropdown-toggle.active .dropdown-arrow {
  transform: rotate(180deg);
  color: var(--navbar-accent);
}

.dropdown-menu {
  background: rgba(40, 40, 40, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid var(--navbar-border);
  border-radius: 8px;
  margin: 0.5rem 1rem;
  overflow: hidden;
  animation: dropdownSlideIn 0.2s ease forwards;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  color: var(--navbar-text);
  text-decoration: none;
  transition: var(--navbar-transition);
  position: relative;
  border-left: 3px solid transparent;
  cursor: pointer;
  pointer-events: auto;
  z-index: 1003;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  font-size: 0.9rem;
  font-family: inherit;
}

.dropdown-item:hover {
  background: rgba(255, 179, 71, 0.1);
  color: var(--navbar-accent);
  border-left-color: var(--navbar-accent);
}

.dropdown-item.active {
  background: rgba(255, 179, 71, 0.15);
  color: var(--navbar-accent);
  border-left-color: var(--navbar-accent);
  font-weight: 600;
}

.dropdown-item-text {
  font-size: 0.9rem;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Auth Section */
.nav-auth {
  padding: 1.5rem;
  border-top: 1px solid var(--navbar-border);
  margin-top: auto;
  flex-shrink: 0;
}

.user-profile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  text-align: center;
}

/* Notification Bell */
.notification-bell {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  border: 1px solid var(--navbar-border);
  cursor: pointer;
  transition: var(--navbar-transition);
  backdrop-filter: blur(10px);
}

.notification-bell:hover {
  background: rgba(255, 179, 71, 0.1);
  border-color: var(--navbar-accent);
  box-shadow: 0 0 15px rgba(255, 179, 71, 0.2);
}

.bell-icon {
  font-size: 1rem;
  color: var(--navbar-text);
  transition: var(--navbar-transition);
}

.notification-badge {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 700;
  border: 2px solid var(--navbar-glass);
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.4);
}

.user-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--navbar-accent), var(--navbar-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 16px rgba(255, 179, 71, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.avatar-initials {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1a1a1a;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.user-greeting-nav {
  font-size: 0.9rem;
  color: var(--navbar-text-muted);
  font-weight: 500;
}

.user-details {
  margin-bottom: 0.5rem;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--navbar-text);
  margin-bottom: 0.2rem;
}

.user-role {
  font-size: 0.8rem;
  color: var(--navbar-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.logout-button {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--navbar-transition);
  width: 100%;
}

.logout-button:hover {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.auth-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.auth-link {
  display: block;
  padding: 0.75rem 1rem;
  text-align: center;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: var(--navbar-transition);
}

.auth-link.login {
  background: rgba(255, 179, 71, 0.1);
  color: var(--navbar-accent);
  border: 1px solid rgba(255, 179, 71, 0.3);
  backdrop-filter: blur(10px);
}

.auth-link.login:hover {
  background: var(--navbar-accent);
  color: #1a1a1a;
  border-color: var(--navbar-accent);
  box-shadow: 0 0 20px rgba(255, 179, 71, 0.4);
}

.auth-link.signup {
  background: linear-gradient(135deg, var(--navbar-accent), var(--navbar-secondary));
  color: #1a1a1a;
  border: 1px solid var(--navbar-accent);
  font-weight: 600;
}

.auth-link.signup:hover {
  background: linear-gradient(135deg, var(--navbar-secondary), var(--navbar-accent));
  border-color: var(--navbar-secondary);
  transform: translateY(-1px);
  box-shadow: 0 0 25px rgba(255, 179, 71, 0.5);
}

/* Animations - Optimized */
@keyframes fadeIn {
  to { opacity: 1; }
}

@keyframes slideInRight {
  to { transform: translateX(0); }
}

/* Desktop Navigation Styles */
@media (min-width: 769px) {
  .mobile-nav-toggle,
  .mobile-nav-close {
    display: none;
  }

  .header-nav {
    display: block;
    position: static;
    background: transparent;
    backdrop-filter: none;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    opacity: 1;
    visibility: visible;
    transform: none;
  }

  .header-nav .list-inline-item {
    margin-right: 16px;
  }
}

/* Desktop Navigation */
@media (min-width: 1024px) {
  .desktop-nav {
    display: flex;
  }
  
  .mobile-toggle {
    display: none;
  }

  .modern-nav {
    display: none;
  }

  .nav-overlay {
    display: none;
  }

  .nav-content {
    position: static;
    height: auto;
    width: auto;
    max-width: none;
    background: none;
    box-shadow: none;
    transform: none;
    animation: none;
    flex-direction: row;
    align-items: center;
    gap: 2rem;
  }

  .nav-header {
    display: none;
  }

  .nav-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0;
    overflow: visible;
    flex: none;
  }

  .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border-left: none;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--navbar-border);
    backdrop-filter: blur(10px);
  }

  .nav-link:hover {
    background: rgba(255, 179, 71, 0.1);
    border-color: var(--navbar-accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 179, 71, 0.2);
  }

  .nav-link.active {
    background: rgba(255, 179, 71, 0.15);
    border-color: var(--navbar-accent);
    box-shadow: 0 0 15px rgba(255, 179, 71, 0.3);
  }

  .nav-auth {
    padding: 0;
    border: none;
    margin: 0;
  }

  .user-profile {
    flex-direction: row;
    gap: 1rem;
    text-align: left;
    align-items: center;
  }

  .notification-bell {
    width: 32px;
    height: 32px;
  }

  .bell-icon {
    font-size: 0.9rem;
  }

  .notification-badge {
    width: 16px;
    height: 16px;
    font-size: 0.6rem;
    top: -3px;
    right: -3px;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    margin-bottom: 0;
  }

  .avatar-initials {
    font-size: 0.9rem;
  }

  .user-details {
    margin-bottom: 0;
  }

  .user-name {
    font-size: 0.9rem;
    margin-bottom: 0.1rem;
  }

  .user-role {
    font-size: 0.7rem;
  }

  .logout-button {
    width: auto;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .auth-actions {
    flex-direction: row;
    gap: 0.5rem;
  }

  .auth-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Mobile Responsive */
@media (max-width: 1023px) {
  .desktop-nav {
    display: none;
  }
  
  .mobile-toggle {
    display: block;
  }

  .navbar-container {
    padding: 0.5rem 1rem;
  }

  .logo-text {
    font-size: 1.3rem;
  }

  .logo-tagline {
    font-size: 0.65rem;
  }

  /* Ensure mobile navigation links are clickable */
  .nav-link {
    padding: 1.25rem 1.5rem;
    min-height: 56px;
    font-size: 1rem;
    border-left: 4px solid transparent;
  }

  .nav-link:active {
    background: rgba(255, 179, 71, 0.2);
    transform: scale(0.98);
  }

  .nav-links {
    padding: 0.5rem 0;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0.5rem 0.75rem;
  }

  .logo-text {
    font-size: 1.2rem;
  }

  .logo-tagline {
    font-size: 0.6rem;
  }

  .nav-content {
    width: 280px;
  }

  .nav-auth {
    padding: 1rem;
  }

  .auth-actions {
    gap: 0.5rem;
  }

  .auth-link {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
  }
}

/* Accessibility & Performance - Optimized */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.mobile-toggle:focus,
.nav-close:focus,
.nav-link:focus,
.auth-link:focus,
.logout-button:focus {
  outline: 2px solid var(--navbar-accent);
  outline-offset: 2px;
}

.active-link .nav-link-active {
  position: relative;
  color: var(--accent-color, #ffb347) !important;
  font-weight: 600;
}

.nav-link-anim {
  transition: color 0.2s, background 0.2s, box-shadow 0.2s, transform 0.2s;
}

.nav-link-anim:hover, .nav-link-active {
  color: var(--accent-color, #ffb347) !important;
  background: rgba(255,255,255,0.08);
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.nav-underline {
    display: block;
    height: 3px;
    width: 100%;
    background: linear-gradient(90deg, var(--accent-color, #ffb347), #ff6f61);
    border-radius: 2px;
    margin-top: 2px;
    animation: navUnderlineIn 0.3s cubic-bezier(0.4,0,0.2,1);
}

@keyframes navUnderlineIn {
    from { width: 0; opacity: 0; }
    to { width: 100%; opacity: 1; }
}

.sticky-nav {
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    background: rgba(52, 58, 64, 0.97) !important;
    transition: box-shadow 0.3s;
}

/* Enhanced Navigation Layout */
.nav-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.main-nav-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-section {
  border-left: 1px solid rgba(255,255,255,0.2);
  padding-left: 1.5rem;
  margin-left: 1rem;
}

/* User Avatar */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-color, #ffb347), #ff6f61);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: transform 0.2s, box-shadow 0.2s;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* User Info */
.user-info {
  text-align: left;
}

.user-role {
  font-size: 12px;
  color: rgba(255,255,255,0.7);
  text-transform: capitalize;
}

/* Logo and Tagline */
.logo-link {
  font-weight: 800;
  font-size: 1.8rem;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.logo-link:hover {
  color: var(--accent-color, #ffb347) !important;
  text-shadow: 0 4px 8px rgba(0,0,0,0.4);
  transform: translateY(-1px);
}

.tagline {
  font-style: italic;
  color: rgba(255,255,255,0.9);
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 1.2px;
  margin: 0;
  line-height: 1.3;
  text-transform: uppercase;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.brand-section:hover .tagline {
  opacity: 1;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-btn {
  transition: all 0.2s;
}

.logout-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  color: #dc3545;
}

/* Enhanced Mobile Styles */
@media (max-width: 991px) {
  .nav-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .auth-section {
    border-left: none;
    border-top: 1px solid rgba(255,255,255,0.2);
    padding-left: 0;
    padding-top: 1rem;
    margin-left: 0;
  }
  
  .user-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .auth-buttons {
    justify-content: center;
  }
}

/* Brand Section */
.brand-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.brand-section:hover::before {
  opacity: 1;
}

.brand-section h1 {
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.logo-link {
  font-weight: 900;
  font-size: 2rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 3px 6px rgba(0,0,0,0.4);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.logo-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color, #ffb347), #ff6f61);
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 1px;
}

.logo-link:hover {
  background: linear-gradient(135deg, var(--accent-color, #ffb347) 0%, #ff6f61 50%, #ff8a65 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 6px 12px rgba(255, 179, 71, 0.3);
  transform: translateY(-2px) scale(1.02);
}

.logo-link:hover::after {
  width: 100%;
}

.tagline {
  font-style: italic;
  color: rgba(255,255,255,0.95);
  font-size: 11px;
  font-weight: 500;
  letter-spacing: 2px;
  margin: 0;
  line-height: 1.4;
  text-transform: uppercase;
  opacity: 0.8;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.tagline::before {
  content: '✦';
  margin-right: 8px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.tagline::after {
  content: '✦';
  margin-left: 8px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.brand-section:hover .tagline {
  opacity: 1;
  color: rgba(255,255,255,1);
  transform: translateY(-1px);
  letter-spacing: 2.5px;
}

.brand-section:hover .tagline::before,
.brand-section:hover .tagline::after {
  opacity: 1;
  transform: rotate(180deg);
}

.user-greeting-nav {
  font-size: 0.85rem;
  color: var(--navbar-text-muted);
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  font-weight: 500;
  letter-spacing: 0.01em;
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
}

@media (max-width: 1023px) {
  .user-greeting-nav {
    font-size: 0.92rem;
    margin-left: 0.4rem;
    margin-right: 0.4rem;
  }
}
