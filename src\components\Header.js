import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FaBell, FaSearch, FaTimes } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import './HeaderModern.css';

const Header = () => {
  const { user, logout } = useAuth();
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [notificationCount, setNotificationCount] = useState(0);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const navigate = useNavigate();

  // Debug logging
  console.log('Header - Current user data:', user);
  console.log('Header - is_superuser:', user?.is_superuser);
  console.log('Header - is_staff:', user?.is_staff);
  console.log('Header - is_vendor:', user?.is_vendor);

  const navLinks = [
    { to: '/', label: '🏠 Home', show: true },

    { to: '/ai-features', label: '✨ AI Magic', show: true },
    { 
      to: '/my-bookings', 
      label: user?.is_vendor ? '📅 Salon Bookings' : user?.is_superuser ? '📊 All Bookings' : '📅 My Bookings', 
      show: !!user 
    },
    { to: '/vendor/profile', label: '🏪 Vendor Profile', show: user && user.is_vendor },
    { to: '/profile', label: '👤 Profile', show: !!user && !user.is_vendor && !user.is_superuser },

    { to: '/register-vendor', label: '🏪 Vendor Signup', show: !user || (user && !user.is_vendor) },
    { to: '/admin', label: '🏠 Home', show: user && user.is_superuser },
  ];

  const getUserInitials = () => {
    if (!user) return '';
    const name = user.name || user.username || '';
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
      .slice(0, 2);
  };

  const getFormattedUsername = () => {
    if (!user) return '';
    const name = user.name || user.username || '';
    // Capitalize first letter of each word
    return name.split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const getShortName = () => {
    if (!user) return '';
    const name = user.name || user.username || '';
    const firstName = name.split(' ')[0];
    return firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
  };

  const getUserRole = () => {
    if (!user) return '';
    if (user.is_superuser) return 'Admin';
    if (user.is_vendor) return 'Vendor';
    return 'Customer';
  };

  const getGreeting = () => {
    const now = new Date();
    const hour = now.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };

  // Simulate notification count (in real app, this would come from API)
  useEffect(() => {
    if (user) {
      // Simulate getting notifications
      const mockNotificationCount = Math.floor(Math.random() * 3); // 0, 1, or 2
      setNotificationCount(mockNotificationCount);
    } else {
      setNotificationCount(0);
    }
  }, [user]);

  const toggleMobileNav = () => {
    setIsMobileNavOpen(!isMobileNavOpen);
  };

  const closeMobileNav = () => {
    setIsMobileNavOpen(false);
  };

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
    if (!isSearchOpen) {
      setSearchQuery('');
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle navigation with proper link handling
  const handleNavLinkClick = (to, e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Mobile nav link clicked:', to);
    console.log('Current location:', location.pathname);
    console.log('Target location:', to);
    
    closeMobileNav();
    
    // Use navigate for proper routing with error handling
    try {
      console.log('Attempting navigation to:', to);
      navigate(to);
      console.log('Navigation successful');
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to window.location if navigate fails
      console.log('Using fallback navigation');
      window.location.href = to;
    }
  };

  // Close mobile nav when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobileNavOpen && !event.target.closest('.modern-nav') && !event.target.closest('.mobile-toggle')) {
        closeMobileNav();
      }

    };

    // Close mobile nav on escape key
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isMobileNavOpen) {
        closeMobileNav();
      }
    };

    // Allow body scroll even when mobile nav is open
    document.body.style.overflow = 'unset';

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isMobileNavOpen]);

  return (
    <header className="modern-header">
      <div className="nav-container">
        <Link to="/" className="nav-brand" onClick={closeMobileNav}>
          <div className="brand-content">
            <div className="brand-main">
              <span className="brand-icon">💄</span>
              <span className="brand-text">SalonGenz</span>
            </div>
            <span className="brand-tagline">SMART SALON FINDER</span>
          </div>
        </Link>

        {/* Desktop Navigation */}
        <ul className="nav-menu">
          {navLinks.filter(l => l.show).map((link, index) => (
            <li key={`nav-${index}`} className="nav-item">
              <Link
                to={link.to}
                className={`nav-link ${location.pathname === link.to ? 'active' : ''}`}
                onClick={closeMobileNav}
              >
                {link.label}
              </Link>
            </li>
          ))}
        </ul>

        {/* Auth Section */}
        <div className="nav-actions">
          {/* Search Icon */}
          <div className="search-icon" onClick={toggleSearch}>
            <FaSearch className="search-icon-svg" />
          </div>

          {user ? (
            <>
              {/* Message Bell Icon */}
              <div className="message-bell" onClick={() => navigate('/messages')}>
                <FaBell className="bell-icon" />
                {notificationCount > 0 && (
                  <span className="notification-badge">{notificationCount}</span>
                )}
              </div>

              <div className="user-profile">
                <div className="user-avatar">
                  {getUserInitials()}
                </div>
                <div className="user-welcome">
                  <span className="welcome-text">{getTimeBasedGreeting()}, {getShortName()}</span>
                  <span className="user-role-badge">{getUserRole()}</span>
                </div>
              </div>
              <button
                aria-label="Logout"
                onClick={(e) => {
                  e.preventDefault();
                  try {
                    logout();
                    navigate('/');
                  } catch (error) {
                    console.error('Logout error:', error);
                    window.location.href = '/';
                  }
                }}
                className="logout-button"
                title="Logout"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                  <polyline points="16,17 21,12 16,7" />
                  <line x1="21" y1="12" x2="9" y2="12" />
                </svg>
              </button>
            </>
          ) : (
            <>
              <Link to="/login" className="auth-button">
                Login
              </Link>
              <Link to="/signup" className="auth-button primary">
                Sign Up
              </Link>
            </>
          )}

          {/* Mobile Toggle */}
          <button
            className="mobile-toggle"
            onClick={toggleMobileNav}
            aria-label="Toggle mobile menu"
          >
            {isMobileNavOpen ? '✕' : '☰'}
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`mobile-menu ${isMobileNavOpen ? 'open' : ''}`}>
          {/* Search Option in Mobile Menu */}
          <div
            className="mobile-nav-link mobile-search-link"
            onClick={() => {
              toggleSearch();
              setIsMobileNavOpen(false);
            }}
          >
            <FaSearch className="mobile-search-icon" />
            Search
          </div>

          {/* Messages Option in Mobile Menu */}
          {user && (
            <Link
              to="/messages"
              className="mobile-nav-link"
              onClick={closeMobileNav}
            >
              <FaBell className="mobile-search-icon" />
              Messages
              {notificationCount > 0 && (
                <span className="mobile-notification-badge">{notificationCount}</span>
              )}
            </Link>
          )}

          {navLinks.filter(l => l.show).map((link, index) => (
            <Link
              key={`mobile-${index}`}
              to={link.to}
              className={`mobile-nav-link ${location.pathname === link.to ? 'active' : ''}`}
              onClick={(e) => handleNavLinkClick(link.to, e)}
            >
              {link.label}
            </Link>
          ))}

          <div className="mobile-auth-buttons">
            {user ? (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  try {
                    logout();
                    navigate('/');
                  } catch (error) {
                    console.error('Logout error:', error);
                    window.location.href = '/';
                  }
                }}
                className="mobile-auth-button"
              >
                Logout
              </button>
            ) : (
              <>
                <Link to="/login" className="mobile-auth-button" onClick={closeMobileNav}>
                  Login
                </Link>
                <Link to="/signup" className="mobile-auth-button primary" onClick={closeMobileNav}>
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </div>

        {/* Search Overlay */}
        {isSearchOpen && (
          <div className="search-overlay">
            <div className="search-container">
              <form onSubmit={handleSearch} className="search-form">
                <div className="search-input-wrapper">
                  <FaSearch className="search-input-icon" />
                  <input
                    type="text"
                    placeholder="Search salons, services, locations..."
                    value={searchQuery}
                    onChange={handleSearchInputChange}
                    className="search-input"

                  />
                  <button
                    type="button"
                    onClick={toggleSearch}
                    className="search-close"
                  >
                    <FaTimes />
                  </button>
                </div>
              </form>
              <div className="search-suggestions">
                <div className="search-suggestion-category">
                  <h4>Popular Searches</h4>
                  <div className="search-suggestion-items">
                    <span onClick={() => setSearchQuery('hair salon')}>Hair Salon</span>
                    <span onClick={() => setSearchQuery('nail art')}>Nail Art</span>
                    <span onClick={() => setSearchQuery('massage')}>Massage</span>
                    <span onClick={() => setSearchQuery('facial')}>Facial</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>

  );
};

export default Header;
