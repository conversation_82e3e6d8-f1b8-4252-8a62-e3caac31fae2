/* Mobile-First Enterprise Navigation */
.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  gap: 0.5rem;
}

.nav-brand {
  text-decoration: none;
  color: white;
  margin-right: 2rem;
  flex-shrink: 0;
}

.nav-brand:hover {
  color: white;
  text-decoration: none;
}

.brand-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.brand-main {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: -0.025em;
}

.brand-icon {
  font-size: 1.5rem;
}

.brand-text {
  font-weight: 700;
}

.brand-tagline {
  font-size: 0.6rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: -2px;
  text-align: center;
  width: 100%;
}

/* Mobile adjustments */
@media (max-width: 480px) {
  .brand-tagline {
    font-size: 0.55rem;
    letter-spacing: 0.3px;
  }

  .nav-container {
    padding: 0 0.75rem;
    gap: 0.25rem;
  }

  .user-profile {
    gap: 0.25rem;
  }

  .welcome-text {
    font-size: 0.7rem;
    max-width: 100px;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
  }

  .logout-button {
    width: 32px;
    height: 32px;
  }

  .logout-button svg {
    width: 16px;
    height: 16px;
  }

  /* Hide search icon on mobile - show only in menu */
  .search-icon {
    display: none;
  }

  /* Ensure mobile toggle is always visible and properly positioned */
  .mobile-toggle {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
    margin-left: 0.25rem;
  }

  /* Improve nav-actions layout on mobile */
  .nav-actions {
    gap: 0.25rem;
    flex-shrink: 0;
  }

  /* Hide message bell on very small screens - show in menu instead */
  .message-bell {
    display: none;
  }
}

.nav-menu {
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
  flex: 1;
  justify-content: flex-start;
  margin-left: 1rem;
}

.nav-item {
  position: relative;
}

.nav-item.dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
  white-space: nowrap;
  background: none;
  border: none;
  cursor: pointer;
}

.dropdown-toggle:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.dropdown-toggle.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  min-width: 180px;
  z-index: 1000;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  color: #374151;
  text-decoration: none;
  font-size: 0.85rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: #111827;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  text-decoration: none;
  transform: translateY(-1px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
  min-width: 0;
}

/* Message Bell Icon */
.message-bell {
  position: relative;
  padding: 0.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-bell:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.bell-icon {
  color: white;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.message-bell:hover .bell-icon {
  animation: bellRing 0.5s ease-in-out;
}

@keyframes bellRing {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(15deg); }
  75% { transform: rotate(-15deg); }
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Search Icon */
.search-icon {
  position: relative;
  padding: 0.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.search-icon-svg {
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

/* Search Overlay */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.search-container {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-form {
  padding: 1.5rem;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input-icon {
  color: #6c757d;
  margin-right: 0.75rem;
  font-size: 1rem;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: #333;
  outline: none;
}

.search-input::placeholder {
  color: #6c757d;
}

.search-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  margin-left: 0.5rem;
}

.search-close:hover {
  background: #e9ecef;
  color: #333;
}

.search-suggestions {
  border-top: 1px solid #e9ecef;
  padding: 1.5rem;
}

.search-suggestion-category h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6c757d;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-suggestion-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.search-suggestion-items span {
  background: #f8f9fa;
  color: #495057;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.search-suggestion-items span:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-welcome {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  flex: 1;
}

.welcome-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.logout-button svg {
  flex-shrink: 0;
}

.user-role-badge {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.auth-button {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  background: transparent;
  cursor: pointer;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  text-decoration: none;
  transform: translateY(-1px);
}

.auth-button.primary {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.auth-button.primary:hover {
  background: rgba(255, 255, 255, 0.25);
}

.mobile-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.3rem;
  flex-shrink: 0;
  margin-left: 0.5rem;
}

.mobile-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 0.75rem;
  display: none;
  flex-direction: column;
  gap: 0.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  max-height: 80vh;
  overflow-y: auto;
}

.mobile-menu.open {
  display: flex;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-height: 48px;
  margin: 0.125rem 0;
}

.mobile-nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.mobile-search-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 0.5rem;
  padding-bottom: 0.75rem;
}

.mobile-search-icon {
  font-size: 1rem;
  color: #FFD700;
}

.mobile-notification-badge {
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.mobile-nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-auth-button {
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  background: transparent;
  cursor: pointer;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-auth-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
}

.mobile-auth-button.primary {
  background: rgba(255, 255, 255, 0.15);
}

.mobile-dropdown {
  margin-bottom: 0.5rem;
}

.mobile-dropdown-menu {
  padding-left: 1rem;
  margin-top: 0.5rem;
}

.mobile-dropdown-item {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.85rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 0.25rem;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.mobile-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Tablet and Desktop */
@media (min-width: 768px) {
  .nav-container {
    padding: 0 1.5rem;
    min-height: 70px;
  }
  
  .nav-brand {
    font-size: 1.25rem;
  }
  
  .nav-menu {
    display: flex;
    gap: 0.75rem;
  }

  .nav-link,
  .dropdown-toggle {
    font-size: 0.85rem;
    padding: 0.6rem 0.9rem;
  }
  
  .mobile-toggle {
    display: none;
  }
  
  .mobile-menu {
    display: none !important;
  }
  
  .auth-button {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
  }

  .user-profile {
    gap: 0.75rem;
  }

  .welcome-text {
    max-width: 160px;
    font-size: 0.8rem;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    font-size: 0.8rem;
  }
}

@media (min-width: 1024px) {
  .nav-container {
    padding: 0 2rem;
  }
  
  .nav-menu {
    gap: 0.5rem;
  }
  
  .nav-link {
    font-size: 0.9rem;
    padding: 0.7rem 1rem;
  }
  
  .nav-actions {
    gap: 0.75rem;
  }

  .message-bell {
    padding: 0.4rem;
  }

  .bell-icon {
    font-size: 1rem;
  }

  .notification-badge {
    width: 16px;
    height: 16px;
    font-size: 0.65rem;
  }

  .search-icon {
    padding: 0.4rem;
  }

  .search-icon-svg {
    font-size: 0.9rem;
  }

  .search-overlay {
    padding-top: 5vh;
  }

  .search-container {
    width: 95%;
  }

  .search-form {
    padding: 1rem;
  }

  .search-input-wrapper {
    padding: 0.6rem 0.8rem;
  }

  .search-input {
    font-size: 0.9rem;
  }

  .search-suggestions {
    padding: 1rem;
  }

  .search-suggestion-items span {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .mobile-menu {
    padding: 0.75rem 0.5rem;
    gap: 0.125rem;
  }

  .mobile-nav-link {
    padding: 0.6rem 0.75rem;
    font-size: 0.95rem;
    min-height: 44px;
  }

  .mobile-auth-buttons {
    padding: 0.5rem 0;
    gap: 0.5rem;
  }

  .mobile-auth-button {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}

@media (min-width: 1200px) {
  .nav-link,
  .dropdown-toggle {
    font-size: 0.95rem;
    padding: 0.75rem 1.25rem;
  }

  .nav-menu {
    gap: 0.75rem;
  }

  .user-profile {
    gap: 1rem;
  }

  .welcome-text {
    max-width: 200px;
    font-size: 0.85rem;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 0.85rem;
  }
}
