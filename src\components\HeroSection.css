/* ===== Gen-Z Hero Section - Mobile First Design ===== */

/* Gen-Z Hero Base Styles - Mobile First */
.genz-hero {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 50%,
    #4facfe 100%);
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 1rem 0.75rem;
  box-sizing: border-box;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Floating Elements */
.hero-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-emoji {
  position: absolute;
  font-size: 1.5rem;
  animation: float 4s ease-in-out infinite;
  opacity: 0.6;
  will-change: transform;
}

.floating-emoji:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floating-emoji:nth-child(2) {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.floating-emoji:nth-child(3) {
  bottom: 30%;
  left: 5%;
  animation-delay: 2s;
}

.floating-emoji:nth-child(4) {
  bottom: 15%;
  right: 10%;
  animation-delay: 3s;
}

.floating-emoji:nth-child(5) {
  top: 50%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-20px) rotate(5deg); }
  50% { transform: translateY(-10px) rotate(-5deg); }
  75% { transform: translateY(-15px) rotate(3deg); }
}

/* Main Content Container */
.hero-main-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  align-items: center;
  z-index: 2;
  position: relative;
}

/* Hero Text Section */
.hero-text-section {
  text-align: center;
  color: white;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

.badge-emoji {
  font-size: 1.2rem;
}

.hero-main-title {
  font-size: 2.2rem;
  font-weight: 900;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.gradient-text {
  background: linear-gradient(45deg, #FFD700, #FF6B9D, #8A2BE2);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientText 3s ease infinite;
}

@keyframes gradientText {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.hero-description {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  opacity: 0.95;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Feature Pills */
.hero-features-pills {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.feature-pill {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  transition: all 0.2s ease;
}

.feature-pill:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.pill-icon {
  font-size: 1.1rem;
}

/* CTA Section */
.hero-cta-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 1rem;
}

.genz-button {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  font-size: 1rem;
  font-weight: 700;
  border-radius: 25px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  min-width: 180px;
  justify-content: center;
}

.hero-cta-primary {
  background: linear-gradient(45deg, #FFD700, #FF6B9D);
  color: #1a1a1a;
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
}

.hero-cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 107, 157, 0.6);
}

.button-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.genz-button-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.genz-button-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.premium-tag {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #FF6B9D;
  color: white;
  font-size: 0.6rem;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-weight: 600;
  line-height: 1;
  z-index: 1;
}



/* Visual Section */
.hero-visual-section {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* Hero section specific trending card - Higher specificity */
.genz-hero .trending-salon-card {
  position: relative;
  width: 100%;
  max-width: min(320px, 90vw) !important;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.salon-showcase {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  min-height: 320px;
}

.salon-showcase.active {
  opacity: 1;
  transform: translateY(0);
}

.salon-showcase.hidden {
  opacity: 0;
  transform: translateY(20px);
}

.trending-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(45deg, #FF6B9D, #8A2BE2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 3;
  animation: bounce 2s infinite;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Hero section specific styling - Higher specificity */
.genz-hero .salon-image-container {
  position: relative;
  width: 100%;
  height: min(320px, 40vw) !important;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.salon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: transform 0.3s ease;
}

.salon-showcase:hover .salon-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
}

/* Hero section salon info - Higher specificity for visibility */
.genz-hero .salon-info {
  color: #1a1a1a !important;
  z-index: 2;
  position: relative;
}

/* Hero section salon name - Higher specificity for visibility */
.genz-hero .salon-name {
  font-size: 1.4rem;
  font-weight: 800;
  margin-bottom: 0.75rem;
  color: #1a1a1a !important;
  line-height: 1.2;
}

/* Hero section salon location - Higher specificity for visibility */
.genz-hero .salon-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: #555 !important;
  margin-bottom: 1rem;
  font-weight: 500;
}

.location-icon {
  font-size: 1.1rem;
}

.salon-rating {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
}

.rating-stars {
  font-size: 1rem;
}

.rating-text {
  font-weight: 700;
  color: #1a1a1a;
  font-size: 1rem;
}

/* Distance indicator in hero salon showcase */
.distance-indicator {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-left: 0.5rem;
  padding: 0.2rem 0.5rem;
  background: rgba(255, 140, 66, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 140, 66, 0.2);
}

.distance-indicator .distance-icon {
  font-size: 0.8rem;
  opacity: 0.8;
}

.distance-indicator .distance-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: #ff8c42;
}

.salon-perks {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.perk-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(102, 126, 234, 0.15);
  color: #667eea;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 700;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.perk-icon {
  font-size: 1rem;
}

.salon-cta-button {
  display: block;
  width: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  text-align: center;
  padding: 1rem;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.salon-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(102, 126, 234, 0.5);
  text-decoration: none;
  color: white;
}

.showcase-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.showcase-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.showcase-dot.active {
  background: #FFD700;
  transform: scale(1.2);
}

.visual-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle,
.decoration-triangle,
.decoration-square {
  position: absolute;
  opacity: 0.1;
}

.decoration-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #FFD700;
  top: 10%;
  right: 10%;
  animation: float 8s ease-in-out infinite;
}

.decoration-triangle {
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 50px solid #FF6B9D;
  bottom: 20%;
  left: 15%;
  animation: float 6s ease-in-out infinite reverse;
}

.decoration-square {
  width: 60px;
  height: 60px;
  background: #8A2BE2;
  transform: rotate(45deg);
  top: 60%;
  right: 20%;
  animation: float 10s ease-in-out infinite;
}

/* ===== Responsive Design - Mobile First ===== */

/* Mobile (480px and below) - Already optimized in base styles */
@media (max-width: 480px) {
  .genz-hero {
    min-height: 100vh;
    padding: 0.75rem 0.5rem;
  }

  .floating-emoji {
    font-size: 1.2rem;
    opacity: 0.5;
  }

  .hero-main-title {
    font-size: 2rem;
    margin-bottom: 0.75rem;
  }

  .hero-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    max-width: 300px;
  }

  .hero-features-pills {
    gap: 0.4rem;
    margin-bottom: 1rem;
  }

  .feature-pill {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }

  .hero-cta-section {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .genz-button {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    min-width: 160px;
  }

  .genz-hero .trending-salon-card {
    max-width: 300px !important;
  }

  .salon-showcase {
    padding: 0.875rem;
    min-height: 280px;
  }

  .genz-hero .salon-image-container {
    height: 300px !important;
  }

  .genz-hero .salon-name {
    font-size: 1.1rem;
    color: #1a1a1a !important;
  }

  .genz-hero .salon-location {
    font-size: 0.85rem;
    color: #555 !important;
  }

  /* Mobile distance indicator adjustments */
  .distance-indicator {
    margin-left: 0.3rem;
    padding: 0.15rem 0.4rem;
  }

  .distance-indicator .distance-icon {
    font-size: 0.7rem;
  }

  .distance-indicator .distance-text {
    font-size: 0.7rem;
  }
}

/* Tablet (768px and up) */
@media (min-width: 768px) {
  .genz-hero {
    padding: 3rem 2rem;
  }

  .hero-main-content {
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
  }

  .hero-text-section {
    text-align: left;
  }

  .hero-main-title {
    font-size: 3.5rem;
  }

  .hero-description {
    font-size: 1.3rem;
    margin-left: 0;
    margin-right: 0;
  }

  .hero-features-pills {
    justify-content: flex-start;
  }

  .hero-cta-section {
    flex-direction: row;
    gap: 1.5rem;
    align-items: center;
    justify-content: flex-start;
  }

  .genz-hero .trending-salon-card {
    max-width: 450px !important;
  }

  .salon-showcase {
    padding: 2rem;
    min-height: 450px;
  }

  .genz-hero .salon-image-container {
    height: 350px !important;
  }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
  .genz-hero {
    padding: 4rem 3rem;
  }

  .hero-main-title {
    font-size: 4rem;
  }

  .hero-description {
    font-size: 1.4rem;
  }

  .floating-emoji {
    font-size: 2.5rem;
  }

  .genz-hero .trending-salon-card {
    max-width: 500px !important;
  }

  .genz-hero .salon-image-container {
    height: 380px !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .genz-hero,
  .floating-emoji,
  .gradient-text,
  .button-shine,
  .trending-badge {
    animation: none;
  }

  .salon-showcase,
  .genz-button,
  .feature-pill {
    transition: none;
  }
}

/* Remove horizontal padding/margin from parent container if present */
.container > .hero-section {
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  padding-left: 0;
  padding-right: 0;
}

@media (max-width: 600px) {
  .hero-section {
    padding-top: 56px;
  }
  .hero-section {
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    padding-left: 0;
    padding-right: 0;
  }
}

/* Gen Z Floating Elements - Optimized */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  transition: all 0.2s ease;
}

.float-icon {
  position: absolute;
  font-size: 18px;
  opacity: 0.6;
  animation: float 8s ease-in-out infinite;
  filter: drop-shadow(0 1px 3px rgba(0,0,0,0.1));
  transition: all 0.2s ease;
  cursor: pointer;
  pointer-events: auto;
}

.float-icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 6px rgba(0,0,0,0.15));
  opacity: 0.8;
}

.float-icon-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 10s;
}

.float-icon-2 {
  top: 25%;
  right: 15%;
  animation-delay: 2s;
  animation-duration: 9s;
}

.float-icon-3 {
  top: 60%;
  left: 8%;
  animation-delay: 4s;
  animation-duration: 11s;
}

.float-icon-4 {
  top: 70%;
  right: 12%;
  animation-delay: 6s;
  animation-duration: 8s;
}

.float-icon-5 {
  top: 40%;
  left: 20%;
  animation-delay: 8s;
  animation-duration: 10.5s;
}

.float-icon-6 {
  top: 50%;
  right: 25%;
  animation-delay: 10s;
  animation-duration: 9.5s;
}

.float-icon-7 {
  top: 80%;
  left: 30%;
  animation-delay: 12s;
  animation-duration: 11.5s;
}

.float-icon-8 {
  top: 20%;
  left: 50%;
  animation-delay: 14s;
  animation-duration: 10s;
}

.hero-glass {
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  backdrop-filter: blur(12px);
  padding: 24px 18px;
  text-align: center;
  max-width: 98vw;
  width: 100%;
  position: relative;
  border: 1px solid rgba(255,255,255,0.3);
  z-index: 2;
  transition: all 0.2s ease;
}

.hero-glass::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, rgba(255,215,0,0.3), rgba(255,182,193,0.3), rgba(255,215,0,0.3));
  border-radius: 18px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.hero-glass:hover::before {
  opacity: 1;
}

/* External JavaScript enhanced styles - Optimized */
.premium-slider-outer {
  position: relative;
  overflow: hidden;
}

.premium-slider-outer:hover .premium-slider {
  animation-play-state: paused;
}

.premium-slider {
  transition: transform 0.4s ease;
  will-change: transform;
}

.premium-slide {
  transition: all 0.2s ease;
}

.premium-slide:hover {
  transform: scale(1.01);
}

.premium-slide:focus {
  outline: 2px solid rgba(255, 215, 0, 0.5);
  outline-offset: 4px;
}

.slider-dot {
  transition: all 0.2s ease;
  cursor: pointer;
}

.slider-dot:hover {
  transform: scale(1.1);
  background: linear-gradient(90deg, #ffd700 0%, #ffb347 100%);
  box-shadow: 0 2px 8px rgba(255, 179, 71, 0.2);
}

.slider-dot:focus {
  outline: 2px solid rgba(255, 215, 0, 0.5);
  outline-offset: 2px;
}

/* Touch/swipe indicators - Simplified */
.premium-slider-outer::before,
.premium-slider-outer::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #666;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.premium-slider-outer::before {
  left: 8px;
  content: '←';
}

.premium-slider-outer::after {
  right: 8px;
  content: '→';
}

.premium-slider-outer:hover::before,
.premium-slider-outer:hover::after {
  opacity: 1;
}

/* Animation enhancements for external JS - Optimized */
.animate-in {
  animation: slideInUp 0.4s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Performance optimizations - Reduced */
.hero-section {
  will-change: transform;
}

.premium-slider {
  will-change: transform;
}

.floating-elements {
  will-change: transform, opacity;
}

/* Reduced motion support for external JS */
@media (prefers-reduced-motion: reduce) {
  .premium-slider {
    transition: none;
  }
  
  .float-icon {
    animation: none;
  }
  
  .premium-slide:hover {
    transform: none;
  }
  
  .slider-dot:hover {
    transform: none;
  }
}

/* High contrast mode for external JS */
@media (prefers-contrast: high) {
  .premium-slide:focus {
    outline: 3px solid #000;
  }
  
  .slider-dot:focus {
    outline: 3px solid #000;
  }
}

/* Touch device optimizations for external JS */
@media (hover: none) and (pointer: coarse) {
  .premium-slider-outer::before,
  .premium-slider-outer::after {
    display: none;
  }
  
  .premium-slide:hover {
    transform: none;
  }
  
  .slider-dot:hover {
    transform: none;
  }
}

/* Optimized floating animation */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-8px) rotate(1deg) scale(1.02); 
  }
  50% { 
    transform: translateY(-4px) rotate(-0.5deg) scale(0.98); 
  }
  75% { 
    transform: translateY(-6px) rotate(0.5deg) scale(1.01); 
  }
}

/* Desktop ad banner size increase */
@media (min-width: 768px) {
  .premium-vendor-img-wrapper {
    width: 280px !important;
    height: 280px !important;
  }
  
  .premium-vendor-img {
    width: 280px !important;
    height: 280px !important;
  }
  
  .premium-badge {
    font-size: 1.3rem !important;
    padding: 10px 20px !important;
    border-radius: 20px !important;
    bottom: -15px !important;
  }
  
  .premium-vendor-name {
    font-size: 1.4rem !important;
  }
  
  .premium-vendor-meta {
    font-size: 1.1rem !important;
  }
  
  .premium-vendor-tagline {
    font-size: 1rem !important;
  }
}

@media (min-width: 1024px) {
  .premium-vendor-img-wrapper {
    width: 320px !important;
    height: 320px !important;
  }
  
  .premium-vendor-img {
    width: 320px !important;
    height: 320px !important;
  }
  
  .premium-badge {
    font-size: 1.5rem !important;
    padding: 12px 24px !important;
    border-radius: 24px !important;
    bottom: -18px !important;
  }
  
  .premium-vendor-name {
    font-size: 1.6rem !important;
  }
  
  .premium-vendor-meta {
    font-size: 1.2rem !important;
  }
  
  .premium-vendor-tagline {
    font-size: 1.1rem !important;
  }
}

/* Gen Z Engagement Badges */
.trending-badge, .social-count, .new-badge, .fire-badge {
  font-size: 0.55rem;
  font-weight: 800;
  padding: 2px 6px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  animation: pulse-glow 2s ease-in-out infinite;
  position: absolute;
  top: -2px;
  right: -2px;
  z-index: 15;
  display: inline-block;
  min-width: fit-content;
  white-space: nowrap;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transform: scale(1);
  transition: transform 0.2s ease;
}

.trending-badge {
  background: linear-gradient(45deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.visible-badge {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

.social-count {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.new-badge {
  background: linear-gradient(45deg, #ffd700, #ffb347);
  color: #333;
  font-weight: 900;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.fire-badge {
  background: linear-gradient(45deg, #ff4757, #ff6b9d);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Badge hover effects */
.feature-item:hover .trending-badge,
.feature-item:hover .social-count,
.feature-item:hover .new-badge,
.feature-item:hover .fire-badge {
  transform: scale(1.1);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(255, 255, 255, 0.6);
}

/* Ensure badges are always visible */
.trending-badge, .social-count, .new-badge, .fire-badge {
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: none; /* Don't interfere with feature item clicks */
}

/* Social Proof Bar */
.social-proof-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 20px;
  padding: 12px;
  background: rgba(255,255,255,0.1);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
}

.proof-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #333;
}

.proof-emoji {
  font-size: 1rem;
  animation: bounce-subtle 2s ease-in-out infinite;
}

.proof-text {
  white-space: nowrap;
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes bounce-subtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-2px);
  }
  60% {
    transform: translateY(-1px);
  }
}

.animate-social-proof {
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease-out 1.4s forwards;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-cta {
  background: linear-gradient(90deg, #FFD700 0%, #ffb347 100%);
  color: #2F4F4F;
  font-weight: 700;
  font-size: 1rem;
  border: none;
  border-radius: 25px;
  padding: 14px 24px;
  box-shadow: 0 4px 16px rgba(255,215,0,0.18);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 44px; /* Touch target size */
  min-width: 120px;
}

.hero-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.hero-cta:hover::before {
  left: 100%;
}

.hero-cta:hover, .hero-cta:focus {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(255,215,0,0.28);
}

/* Advanced Features Grid */
.advanced-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 16px 0;
  padding: 0 4px;
}

.advanced-feature {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.advanced-feature:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.clickable-advanced {
  cursor: pointer;
  user-select: none;
}

.clickable-advanced:hover {
  background: rgba(255, 255, 255, 0.35) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.clickable-advanced:active {
  transform: translateY(0);
}

.advanced-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.advanced-text {
  font-size: 11px;
  font-weight: 600;
  color: #2d3748;
}

/* Hero CTA Button Group */
.hero-cta-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
  align-items: center;
}

.hero-cta.primary {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
}

.hero-cta.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
  border: 2px solid rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: visible !important;
}

.cta-icon {
  font-size: 16px;
}

/* Premium Badge - Cache Bust v2.0 - 2025-07-28 */
.premium-badge {
  position: absolute !important;
  bottom: -8px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: linear-gradient(45deg, #dc2626, #ef4444) !important;
  color: white !important;
  font-size: 0.5rem !important;
  font-weight: 800 !important;
  padding: 2px 6px !important;
  border-radius: 6px !important;
  letter-spacing: 0.5px !important;
  text-transform: uppercase !important;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.4) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  z-index: 10 !important;
  white-space: nowrap !important;
  opacity: 1 !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
  display: block !important;
  visibility: visible !important;
}

.hero-cta.secondary:hover .premium-badge {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 3px 12px rgba(220, 38, 38, 0.8), 0 0 0 2px rgba(255, 255, 255, 0.6);
}

.gift-subtext {
  font-size: 10px;
  opacity: 0.8;
  display: block;
  margin-top: 2px;
}

/* Entrance Animations */
.animate-hero {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  animation: heroEntrance 0.8s ease-out 0.2s forwards;
}

.animate-greeting {
  opacity: 0;
  transform: translateY(-20px);
  animation: slideInDown 0.6s ease-out 0.4s forwards;
}

.animate-title {
  opacity: 0;
  transform: translateY(-20px);
  animation: slideInDown 0.6s ease-out 0.6s forwards;
}

.animate-brand {
  opacity: 0;
  transform: scale(0.8);
  animation: brandPop 0.5s ease-out 0.8s forwards;
}

.animate-desc {
  opacity: 0;
  transform: translateY(-20px);
  animation: slideInDown 0.6s ease-out 0.8s forwards;
}

.animate-features {
  opacity: 0;
  transform: translateY(20px);
  animation: featuresEntrance 0.6s ease-out 1s forwards;
}

.animate-cta {
  opacity: 0;
  transform: translateY(20px) scale(0.9);
  animation: ctaEntrance 0.6s ease-out 1.2s forwards;
}

/* Animation Keyframes */
@keyframes heroEntrance {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes brandPop {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes featuresEntrance {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ctaEntrance {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-8px) rotate(1deg) scale(1.02); 
  }
  50% { 
    transform: translateY(-4px) rotate(-0.5deg) scale(0.98); 
  }
  75% { 
    transform: translateY(-6px) rotate(0.5deg) scale(1.01); 
  }
}

/* Extra Small Mobile (up to 320px) */
@media (max-width: 320px) {
  .hero-section {
    padding: 0 4px;
  }

  .hero-glass {
    padding: 16px 12px;
    border-radius: 12px;
    margin: 4px;
    max-width: 99vw;
  }

  .hero-greeting {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 8px;
  }

  .hero-title {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .hero-desc {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .hero-features {
    gap: 6px;
    padding: 8px;
  }

  .feature-item {
    padding: 6px 8px;
  }

  .feature-icon {
    font-size: 14px;
  }

  .feature-text {
    font-size: 0.7rem;
  }

  .advanced-features {
    gap: 4px;
    margin: 10px 0;
  }

  .advanced-feature {
    padding: 4px 6px;
  }

  .advanced-text {
    font-size: 9px;
  }

  .hero-cta {
    font-size: 0.85rem;
    padding: 10px 16px;
    min-width: 140px;
  }

  .hero-cta-group {
    gap: 6px;
  }

  /* Extra small mobile badges */
  .trending-badge, .social-count, .new-badge, .fire-badge {
    font-size: 0.45rem !important;
    padding: 1px 3px !important;
    top: -1px !important;
    right: -1px !important;
    border-radius: 3px !important;
    font-weight: 900 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.6) !important;
    letter-spacing: 0.2px !important;
  }
}

/* Small Mobile (320px - 480px) */
@media (min-width: 320px) and (max-width: 480px) {
  .hero-section {
    padding: 0 6px;
  }

  .hero-glass {
    padding: 20px 16px;
    border-radius: 16px;
    max-width: 98vw;
  }
  
  .hero-greeting {
    font-size: 1.1rem;
    line-height: 1.4;
  }
  
  .hero-title {
    font-size: 1rem;
  }
  
  .hero-desc {
    font-size: 0.9rem;
  }
  
  .hero-features {
    padding: 10px;
    gap: 6px;
  }
  
  .feature-item {
    padding: 8px 10px;
  }
  
  .feature-icon {
    font-size: 14px;
  }
  
  .feature-text {
    font-size: 0.75rem;
  }

  .hero-cta {
    font-size: 0.95rem;
    padding: 12px 20px;
  }

  .hero-cta-group {
    gap: 8px;
  }

  /* Gen Z mobile badges */
  .trending-badge, .social-count, .new-badge, .fire-badge {
    font-size: 0.5rem;
    padding: 1px 4px;
    min-width: fit-content;
    white-space: nowrap;
    opacity: 1 !important;
    visibility: visible !important;
    top: -1px;
    right: -1px;
    border-radius: 4px;
    font-weight: 900;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
  }

  .social-proof-bar {
    flex-direction: column;
    gap: 8px;
    padding: 10px;
  }

  .proof-item {
    font-size: 0.7rem;
  }

  .advanced-features {
    gap: 6px;
    margin: 12px 0;
  }

  .advanced-feature {
    padding: 5px 8px;
  }

  .advanced-text {
    font-size: 10px;
  }

  .float-icon {
    font-size: 18px;
  }
}

/* Medium Mobile (481px - 600px) */
@media (min-width: 481px) and (max-width: 600px) {
  .hero-section {
    padding: 0 8px;
  }

  .hero-glass {
    padding: 24px 20px;
    border-radius: 18px;
    max-width: 95vw;
  }
  
  .hero-greeting {
    font-size: 1.4rem;
  }
  
  .hero-title {
    font-size: 1.05rem;
  }
  
  .hero-desc {
    font-size: 0.95rem;
  }
  
  .hero-features {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding: 12px;
  }
  
  .feature-item {
    padding: 8px 10px;
  }
  
  .feature-icon {
    font-size: 16px;
  }
  
  .feature-text {
    font-size: 0.8rem;
  }
  
  .hero-cta {
    font-size: 1rem;
    padding: 14px 24px;
  }
  
  .float-icon {
    font-size: 20px;
  }
}

/* Large Mobile (601px - 767px) */
@media (min-width: 601px) and (max-width: 767px) {
  .hero-section {
    padding: 0 12px;
  }

  .hero-glass {
    padding: 28px 24px;
    border-radius: 20px;
    max-width: 92vw;
  }
  
  .hero-greeting {
    font-size: 1.6rem;
  }
  
  .hero-title {
    font-size: 1.1rem;
  }
  
  .hero-desc {
    font-size: 1rem;
  }
  
  .hero-features {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    padding: 14px;
  }
  
  .feature-item {
    padding: 10px 12px;
  }
  
  .feature-icon {
    font-size: 18px;
  }
  
  .feature-text {
    font-size: 0.85rem;
  }
  
  .hero-cta {
    font-size: 1.05rem;
    padding: 16px 28px;
  }
  
  .float-icon {
    font-size: 22px;
  }
}

/* Tablet (768px - 1023px) */
@media (min-width: 768px) {
  .hero-section {
    min-height: 80vh;
    padding: 0 24px;
  }

  .hero-glass {
    max-width: 580px;
    padding: 40px 36px;
    border-radius: 28px;
  }
  
  .hero-greeting {
    font-size: 2.6rem;
  }
  
  .hero-title {
    font-size: 1.5rem;
  }
  
  .hero-desc {
    font-size: 1.1rem;
  }
  
  .hero-features {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 20px;
  }
  
  .feature-item {
    padding: 12px 16px;
  }
  
  .feature-icon {
    font-size: 20px;
  }
  
  .feature-text {
    font-size: 0.9rem;
  }
  
  .hero-cta {
    font-size: 1.2rem;
    padding: 16px 40px;
  }

  .hero-cta-group {
    flex-direction: row;
    gap: 16px;
  }

  .advanced-features {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin: 24px 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .advanced-feature {
    padding: 10px 14px;
  }

  .advanced-icon {
    font-size: 18px;
  }

  .advanced-text {
    font-size: 13px;
  }

  .float-icon {
    font-size: 28px;
  }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
  .hero-section {
    min-height: 70vh;
  }

  .hero-glass {
    max-width: 650px;
    padding: 48px 44px;
  }
  
  .hero-greeting {
    font-size: 2.8rem;
  }
  
  .hero-title {
    font-size: 1.6rem;
  }
  
  .hero-features {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 24px;
  }
  
  .feature-item {
    padding: 14px 18px;
  }
  
  .feature-icon {
    font-size: 22px;
  }
  
  .feature-text {
    font-size: 1rem;
  }
  
  .float-icon {
    font-size: 32px;
  }
}

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .hero-glass {
    max-width: 720px;
    padding: 56px 52px;
  }
  
  .hero-greeting {
    font-size: 3rem;
  }
  
  .hero-title {
    font-size: 1.8rem;
  }
  
  .hero-desc {
    font-size: 1.2rem;
  }
  
  .hero-cta {
    font-size: 1.3rem;
    padding: 18px 48px;
  }
  
  .float-icon {
    font-size: 36px;
  }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .hero-section {
    min-height: 80vh;
    padding: 0 16px;
  }

  .hero-glass {
    padding: 20px 20px;
    max-width: 95vw;
  }
  
  .hero-greeting {
    font-size: 1.2rem;
    margin-bottom: 8px;
  }
  
  .hero-title {
    font-size: 0.95rem;
    margin-bottom: 12px;
  }
  
  .hero-desc {
    font-size: 0.85rem;
    margin-bottom: 16px;
  }
  
  .hero-features {
    margin-bottom: 16px;
    padding: 10px;
    gap: 6px;
  }
  
  .feature-item {
    padding: 6px 8px;
    min-height: 36px;
  }
  
  .feature-icon {
    font-size: 14px;
  }
  
  .feature-text {
    font-size: 0.7rem;
  }
  
  .hero-cta {
    font-size: 0.9rem;
    padding: 10px 20px;
    min-height: 36px;
  }
  
  .float-icon {
    font-size: 16px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-hero,
  .animate-greeting,
  .animate-title,
  .animate-brand,
  .animate-desc,
  .animate-features,
  .animate-cta {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .hero-section::before {
    animation: none;
  }
  
  .hero-cta::before {
    display: none;
  }
  
  .float-icon {
    animation: none;
  }
  
  .feature-item:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .hero-glass {
    background: rgba(255,255,255,0.95);
    border: 2px solid #000;
  }
  
  .hero-greeting {
    color: #000;
  }
  
  .hero-title {
    color: #000;
  }
  
  .hero-desc {
    color: #333;
  }
  
  .feature-item {
    background: rgba(255,255,255,0.9);
    border: 1px solid #000;
  }
  
  .feature-text {
    color: #000;
  }
  
  .hero-cta {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
}

/* Gift Booking Section */
.gift-booking-section {
  margin-top: 2rem;
  text-align: center;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.gift-booking-btn {
  position: relative;
  background: linear-gradient(135deg, #ff6b9d, #c44569, #ff8e53);
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4);
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  min-height: 60px;
  min-width: 280px;
  justify-content: center;
}

.gift-booking-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.gift-booking-btn:hover::before {
  left: 100%;
}

.gift-booking-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 40px rgba(255, 107, 157, 0.6);
}

.gift-booking-btn:active {
  transform: translateY(-1px) scale(1.02);
}

/* Pulsating Animation */
.pulsate-btn {
  animation: pulsate 2s ease-in-out infinite;
}

@keyframes pulsate {
  0% {
    box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 107, 157, 0.8), 0 0 0 10px rgba(255, 107, 157, 0.1);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4);
    transform: scale(1);
  }
}

.gift-icon {
  font-size: 1.5rem;
  animation: bounce 2s ease-in-out infinite;
}

.gift-text {
  font-weight: 700;
  letter-spacing: 0.5px;
}

.gift-sparkle {
  font-size: 1.2rem;
  animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2) rotate(180deg);
  }
}

/* Mobile Responsive for Gift Button */
@media (max-width: 768px) {
  .gift-booking-btn {
    min-width: 260px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    min-height: 56px;
  }
  
  .gift-icon {
    font-size: 1.3rem;
  }
  
  .gift-sparkle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .gift-booking-btn {
    min-width: 240px;
    padding: 0.75rem 1.25rem;
    font-size: 0.95rem;
    min-height: 52px;
    gap: 0.5rem;
  }
  
  .gift-booking-section {
    margin-top: 1.5rem;
  }
}

/* Landscape Mode */
@media (max-height: 500px) and (orientation: landscape) {
  .gift-booking-section {
    margin-top: 1rem;
  }
  
  .gift-booking-btn {
    min-height: 48px;
    padding: 0.625rem 1.25rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .gift-booking-btn {
    box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .pulsate-btn {
    animation: none;
  }
  
  .gift-icon {
    animation: none;
  }
  
  .gift-sparkle {
    animation: none;
  }
  
  .gift-booking-btn:hover {
    transform: none;
  }
}

/* Focus States for Accessibility */
.gift-booking-btn:focus {
  outline: 3px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.gift-booking-btn:focus:not(:focus-visible) {
  outline: none;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .gift-booking-btn:hover {
    transform: none;
    box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4);
  }
  
  .gift-booking-btn:active {
    transform: scale(0.98);
    box-shadow: 0 4px 16px rgba(255, 107, 157, 0.6);
  }
}

/* Tooltip Container */
.tooltip-container {
  position: relative;
  display: inline-block;
}

/* Luxury Gift Modal - Unique & Classy Design */
.gift-tooltip {
  position: absolute;
  bottom: calc(100% + 15px);
  left: 50%;
  transform: translateX(-50%) translateY(12px);
  background: linear-gradient(145deg,
    rgba(20, 20, 30, 0.98) 0%,
    rgba(30, 25, 40, 0.98) 30%,
    rgba(40, 20, 50, 0.98) 70%,
    rgba(25, 15, 35, 0.98) 100%);
  border-radius: 24px;
  padding: 0;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 15px 35px rgba(255, 20, 147, 0.2),
    0 5px 15px rgba(138, 43, 226, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  width: 240px;
  max-width: 88vw;
  backdrop-filter: blur(20px) saturate(180%);
  border: 2px solid;
  border-image: linear-gradient(145deg,
    rgba(255, 215, 0, 0.6) 0%,
    rgba(255, 20, 147, 0.4) 50%,
    rgba(138, 43, 226, 0.6) 100%) 1;
  position: relative;
  overflow: hidden;
}

/* Luxury Glow Effect */
.tooltip-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(145deg,
    rgba(255, 215, 0, 0.3) 0%,
    rgba(255, 20, 147, 0.2) 50%,
    rgba(138, 43, 226, 0.3) 100%);
  border-radius: 26px;
  filter: blur(8px);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.gift-tooltip.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.gift-tooltip.show .tooltip-glow {
  opacity: 1;
}

/* Floating Sparkles Animation */
.tooltip-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: 24px;
}

.sparkle {
  position: absolute;
  font-size: 0.8rem;
  opacity: 0.7;
  animation: sparkleFloat 3s ease-in-out infinite;
}

.sparkle-1 {
  top: 15%;
  left: 85%;
  animation-delay: 0s;
}

.sparkle-2 {
  top: 70%;
  left: 10%;
  animation-delay: 1s;
}

.sparkle-3 {
  top: 40%;
  left: 90%;
  animation-delay: 2s;
}

@keyframes sparkleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-8px) rotate(180deg);
    opacity: 1;
  }
}

.tooltip-content {
  padding: 1.5rem 2rem;
  color: white;
  position: relative;
  z-index: 2;
}

/* Centered Header Design */
.tooltip-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
  position: relative;
}

.tooltip-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.8) 50%,
    transparent 100%);
}

.tooltip-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(145deg,
    rgba(255, 215, 0, 0.2) 0%,
    rgba(255, 20, 147, 0.15) 100%);
  border-radius: 50%;
  border: 2px solid rgba(255, 215, 0, 0.3);
  box-shadow:
    0 4px 12px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tooltip-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.tooltip-content-section {
  text-align: center;
  width: 100%;
  padding: 0 1rem;
}

.tooltip-title {
  font-weight: 900;
  font-size: 1.1rem;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
  margin: 0 0 0.25rem 0;
  background: linear-gradient(135deg,
    #ffffff 0%,
    rgba(255, 215, 0, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tooltip-subtitle {
  font-size: 0.7rem;
  color: rgba(255, 215, 0, 0.8);
  font-weight: 600;
  margin: 0;
  text-transform: none;
  letter-spacing: 0.3px;
  line-height: 1.4;
  text-align: center;
  padding: 0 1.5rem;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Luxury Description */
.tooltip-description {
  margin: 0 0 1.25rem 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  font-style: italic;
  font-weight: 400;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  position: relative;
  padding: 0 0.5rem;
}

.tooltip-description::before {
  content: '"';
  position: absolute;
  left: 0;
  top: -0.2rem;
  font-size: 1.5rem;
  color: rgba(255, 215, 0, 0.6);
  font-family: serif;
}

.tooltip-description::after {
  content: '"';
  position: absolute;
  right: 0;
  bottom: -0.2rem;
  font-size: 1.5rem;
  color: rgba(255, 215, 0, 0.6);
  font-family: serif;
}

/* Luxury Feature Pills */
.tooltip-features {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.feature-pill {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 60px;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  border: 1px solid rgba(255, 215, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.feature-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.1) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.feature-pill:hover::before {
  left: 100%;
}

.feature-pill:hover {
  transform: translateY(-3px);
  background: linear-gradient(145deg,
    rgba(255, 215, 0, 0.15) 0%,
    rgba(255, 20, 147, 0.1) 100%);
  border-color: rgba(255, 215, 0, 0.4);
  box-shadow:
    0 8px 20px rgba(255, 215, 0, 0.2),
    0 4px 12px rgba(255, 20, 147, 0.1);
}

.feature-icon {
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: iconFloat 2s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

.feature-text {
  font-size: 0.75rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  text-align: center;
}

/* Tooltip Arrow */
.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #34495e;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Mobile Luxury Design */
@media (max-width: 768px) {
  .gift-tooltip {
    width: 220px;
    max-width: 85vw;
    bottom: calc(100% + 12px);
  }

  .tooltip-content {
    padding: 1.25rem 1.5rem;
  }

  .tooltip-header {
    margin-bottom: 0.875rem;
    padding-bottom: 0.625rem;
  }

  .tooltip-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .tooltip-icon {
    font-size: 1.3rem;
  }

  .tooltip-title {
    font-size: 1rem;
  }

  .tooltip-subtitle {
    font-size: 0.65rem;
    line-height: 1.4;
    padding: 0 1.25rem;
  }
}

@media (max-width: 480px) {
  .gift-tooltip {
    width: 200px;
    max-width: 82vw;
    bottom: calc(100% + 10px);
  }

  .tooltip-content {
    padding: 1.25rem 1.5rem;
  }

  .tooltip-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }

  .tooltip-icon-wrapper {
    width: 36px;
    height: 36px;
  }

  .tooltip-icon {
    font-size: 1.2rem;
  }

  .tooltip-title {
    font-size: 0.95rem;
  }

  .tooltip-subtitle {
    font-size: 0.6rem;
    line-height: 1.4;
    padding: 0 1rem;
  }
}

/* Extra Small Mobile Luxury */
@media (max-width: 360px) {
  .gift-tooltip {
    width: 180px;
    max-width: 80vw;
  }

  .tooltip-content {
    padding: 1rem 1.25rem;
  }

  .tooltip-header {
    margin-bottom: 0.625rem;
    padding-bottom: 0.375rem;
  }

  .tooltip-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .tooltip-icon {
    font-size: 1.1rem;
  }

  .tooltip-title {
    font-size: 0.85rem;
  }

  .tooltip-subtitle {
    font-size: 0.55rem;
    line-height: 1.4;
    padding: 0 0.75rem;
  }
}

/* Landscape Mode Tooltip */
@media (max-height: 500px) and (orientation: landscape) {
  .gift-tooltip {
    bottom: calc(100% + 8px);
  }
  
  .tooltip-content {
    padding: 0.75rem;
  }
  
  .tooltip-features {
    grid-template-columns: 1fr 1fr;
    gap: 0.25rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .gift-tooltip {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .gift-tooltip {
    transition: opacity 0.2s ease;
  }
}

/* Focus States for Accessibility */
.tooltip-container:focus-within .gift-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .gift-tooltip {
    display: none;
  }
  
  .tooltip-container:hover .gift-tooltip {
    display: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .gift-tooltip {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .tooltip-arrow {
    border-top-color: #2d2d2d;
  }
}

/* Mobile Info Icon */
.mobile-info-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
  transition: all 0.3s ease;
  z-index: 10;
  animation: pulse-attention 2s ease-in-out infinite;
}

.mobile-info-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.6);
}

.mobile-info-icon:active {
  transform: scale(0.95);
}

.info-icon {
  font-size: 14px;
  color: white;
  font-weight: bold;
}

@keyframes pulse-attention {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 107, 157, 0.6), 0 0 0 4px rgba(255, 107, 157, 0.1);
  }
}

/* Mobile Info Popup */
.mobile-info-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 1rem;
}

.mobile-info-popup.show {
  opacity: 1;
  visibility: visible;
}

.mobile-info-content {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  border-radius: 16px;
  padding: 1.5rem;
  max-width: 320px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.mobile-info-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.mobile-info-icon {
  font-size: 1.5rem;
}

.mobile-info-title {
  font-weight: 700;
  font-size: 1.2rem;
  color: #ecf0f1;
  flex: 1;
}

.mobile-info-close {
  background: none;
  border: none;
  color: #95a5a6;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.mobile-info-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ecf0f1;
}

.mobile-info-description {
  margin: 0 0 1.25rem 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #bdc3c7;
}

.mobile-info-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.mobile-info-feature {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.9rem;
  color: #95a5a6;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.mobile-info-feature:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ecf0f1;
}

.mobile-info-cta {
  width: 100%;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  border: none;
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.mobile-info-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.mobile-info-cta:active {
  transform: translateY(0);
}

/* Desktop: Hide mobile info icon */
@media (min-width: 769px) {
  .mobile-info-icon {
    display: none;
  }
  
  .mobile-info-popup {
    display: none;
  }
}

/* Mobile Responsive Adjustments */
@media (max-width: 480px) {
  .mobile-info-content {
    padding: 1.25rem;
    max-width: 300px;
  }
  
  .mobile-info-features {
    grid-template-columns: 1fr;
    gap: 0.375rem;
  }
  
  .mobile-info-feature {
    font-size: 0.85rem;
    padding: 0.375rem 0.5rem;
  }
  
  .mobile-info-cta {
    padding: 0.875rem;
    font-size: 0.95rem;
  }
}

/* Landscape Mode */
@media (max-height: 500px) and (orientation: landscape) {
  .mobile-info-content {
    padding: 1rem;
    max-width: 280px;
  }
  
  .mobile-info-features {
    grid-template-columns: 1fr 1fr;
    gap: 0.25rem;
  }
  
  .mobile-info-feature {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-info-icon {
    border: 2px solid rgba(255, 255, 255, 0.9);
  }
  
  .mobile-info-content {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .mobile-info-icon {
    animation: none;
  }
  
  .mobile-info-popup {
    transition: opacity 0.2s ease;
  }
  
  .mobile-info-content {
    animation: none;
  }
}

/* Focus States for Accessibility */
.mobile-info-icon:focus {
  outline: 3px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.mobile-info-close:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 1px;
}

.mobile-info-cta:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .mobile-info-content {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
} 

.premium-vendors-carousel {
  display: flex;
  flex-direction: row;
  gap: 24px;
  overflow-x: auto;
  padding: 20px 0 30px 0;
  margin: 0 0 20px 0;
  scrollbar-width: thin;
  scrollbar-color: #ffd700 #f8e1ff;
  max-width: 100%;
  position: relative;
  z-index: 2;
}
.premium-vendor-card {
  flex: 0 0 auto;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.premium-slider-outer {
  width: 100%;
  max-width: 340px;
  margin: 0 auto 24px auto;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}
.premium-slider {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.4,0,0.2,1);
  will-change: transform;
  width: 100%;
}
.premium-slide {
  min-width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  opacity: 1;
  transition: opacity 0.3s;
}
.premium-vendor-img-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.13);
  border: 4px solid #ffd700;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}
.premium-vendor-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}
.premium-badge {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, #ffd700 0%, #ffb347 100%);
  color: #fff;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 16px;
  padding: 7px 16px;
  box-shadow: 0 4px 16px rgba(255, 179, 71, 0.13);
  letter-spacing: 0.5px;
  z-index: 2;
  white-space: nowrap;
  border: 2px solid #fff;
}
.premium-vendor-info {
  width: 100%;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 0;
}
.premium-vendor-name {
  font-size: 1.15rem;
  font-weight: 800;
  color: #222;
  letter-spacing: 0.01em;
  margin-bottom: 2px;
  text-shadow: 0 1px 4px rgba(255,215,0,0.08);
}
.premium-vendor-meta {
  display: flex;
  justify-content: center;
  gap: 10px;
  font-size: 0.92rem;
  color: #ffb347;
  font-weight: 600;
  margin-bottom: 2px;
}
.premium-vendor-rating {
  color: #ffb347;
  font-weight: 700;
}
.premium-vendor-location {
  color: #667eea;
  font-weight: 700;
}
.premium-vendor-tagline {
  font-size: 0.85rem;
  color: #ff6b9d;
  font-weight: 700;
  margin-top: 2px;
  letter-spacing: 0.01em;
  text-shadow: 0 1px 4px rgba(255,107,157,0.08);
}
.premium-slider-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 12px;
}
.slider-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ffd700;
  opacity: 0.3;
  border: none;
  transition: opacity 0.2s, background 0.2s;
  cursor: pointer;
  outline: none;
}
.slider-dot.active {
  opacity: 1;
  background: linear-gradient(90deg, #ffd700 0%, #ffb347 100%);
  box-shadow: 0 2px 8px rgba(255, 179, 71, 0.13);
}
@media (max-width: 600px) {
  .premium-slider-outer {
    max-width: 98vw;
  }
  .premium-vendor-img-wrapper {
    width: 120px;
    height: 120px;
    border-width: 2.5px;
  }
  .premium-badge {
    font-size: 0.8rem;
    padding: 4px 10px;
    border-radius: 10px;
    bottom: -7px;
  }
  .premium-vendor-name {
    font-size: 1rem;
  }
  .premium-vendor-meta {
    font-size: 0.8rem;
  }
  .premium-vendor-tagline {
    font-size: 0.7rem;
  }
} 

.hero-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  background: linear-gradient(120deg, rgba(255,215,0,0.08) 0%, rgba(255,107,157,0.08) 100%);
  animation: heroBgPulse 6s ease-in-out infinite alternate;
  border-radius: inherit;
}
@keyframes heroBgPulse {
  0% { opacity: 0.7; filter: blur(0px); }
  100% { opacity: 1; filter: blur(2.5px); }
}
.hero-headline {
  position: relative;
  z-index: 2;
  font-size: 2.1rem;
  font-weight: 900;
  color: #222;
  letter-spacing: 0.01em;
  text-align: center;
  margin-bottom: 18px;
  margin-top: 0;
  text-shadow: 0 2px 12px rgba(255,215,0,0.09);
  background: linear-gradient(90deg, #ffd700 0%, #ffb347 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
@media (max-width: 600px) {
  .hero-headline {
    font-size: 1.2rem;
    margin-bottom: 10px;
  }
} 

/* Enterprise Hero Layout: 50/50 Split */
.enterprise-hero {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: stretch;
  justify-content: center;
  gap: 0;
  min-height: 70vh;
  background: linear-gradient(135deg, #f8e1ff 0%, #ffe6b3 100%);
  position: relative;
  padding: 0;
  box-sizing: border-box;
}

.enterprise-hero-content {
  position: relative;
  z-index: 2;
  background: rgba(255,255,255,0.82);
  border-radius: 0 20px 20px 0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.10);
  padding: 48px 40px 48px 48px;
  max-width: 100%;
  min-width: 0;
  text-align: center;
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.hero-ad-card {
  background: rgba(255,255,255,0.93);
  border-radius: 20px 0 0 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.13);
  padding: 48px 40px 48px 40px;
  min-width: 0;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  margin: 0;
  animation: fadeIn 0.7s;
  height: 100%;
}

/* Responsive: stack on mobile */
@media (max-width: 900px) {
  .enterprise-hero {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    min-height: 0;
  }
  .enterprise-hero-content, .hero-ad-card {
    border-radius: 20px;
    padding: 28px 12px 20px 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    height: auto;
  }
} 

/* High-contrast text for hero section */
.enterprise-hero-content, .hero-ad-card {
  color: #1a202c;
}

.hero-brand {
  color: #181818 !important;
  text-shadow: 0 2px 8px #fff, 0 1px 0 #fff;
}

.hero-greeting {
  color: #333 !important;
  text-shadow: 0 1px 4px #fff;
}

.enterprise-hero-title {
  color: #181818 !important;
  text-shadow: 0 2px 8px #fff, 0 1px 0 #fff;
}

.enterprise-hero-desc {
  color: #222 !important;
  text-shadow: 0 1px 4px #fff;
}

.hero-cta, .hero-cta.primary, .hero-cta.secondary {
  color: #181818 !important;
  text-shadow: 0 1px 4px #fff;
  background: linear-gradient(90deg, #ffd700 0%, #ffb347 100%) !important;
}

.trust-badge {
  color: #181818 !important;
  background: #fff !important;
  text-shadow: 0 1px 4px #fff;
  border: 1px solid #ffd700;
}

.ad-name {
  color: #181818 !important;
  text-shadow: 0 1px 4px #fff;
}

.ad-meta {
  color: #333 !important;
  text-shadow: 0 1px 4px #fff;
}

.ad-tagline {
  color: #222 !important;
  text-shadow: 0 1px 4px #fff;
}

.ad-cta {
  color: #181818 !important;
  background: linear-gradient(90deg, #ffd700 0%, #ffb347 100%) !important;
  text-shadow: 0 1px 4px #fff;
}

.ad-sponsored-badge {
  color: #181818 !important;
  background: #fff8dc !important;
  text-shadow: 0 1px 4px #fff;
  border: 1px solid #ffd700;
}

.hero-headline-pro {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 8px #fff, 0 1px 0 #fff;
}

.hero-subheadline-pro {
  font-size: 1.15rem;
  color: #181818;
  font-weight: 600;
  margin-bottom: 1.1rem;
  text-shadow: 0 1px 4px #fff;
}

.hero-micro-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.micro-highlight {
  background: #fff8dc;
  color: #181818;
  font-weight: 700;
  font-size: 0.98rem;
  border-radius: 10px;
  padding: 5px 14px;
  box-shadow: 0 1px 4px #fff;
  display: flex;
  align-items: center;
  gap: 0.4em;
  cursor: default;
  transition: background 0.2s, color 0.2s;
}

.micro-icon {
  font-size: 1.1em;
  margin-right: 0.2em;
}

.trending-badge-pro {
  background: linear-gradient(90deg, #ffb347 0%, #ffd700 100%);
  color: #d72660;
  font-weight: 900;
  animation: pulse-glow 1.5s infinite alternate;
  border: 1px solid #ffd700;
}

.b2b-highlight {
  background: transparent;
  color: #667eea;
  font-weight: 800;
  border-bottom: 2px dashed #667eea;
  cursor: pointer;
  transition: color 0.2s, border-bottom 0.2s;
  padding: 0 2px;
}
.b2b-highlight:hover, .b2b-highlight:focus {
  color: #ff6b9d;
  border-bottom: 2px solid #ff6b9d;
  text-decoration: underline;
}
.b2b-link {
  text-decoration: underline;
  font-weight: 900;
}

.ad-spotlight-badge {
  position: absolute;
  top: 18px;
  left: 18px;
  background: linear-gradient(90deg, #ffb347 0%, #ffd700 100%);
  color: #2d3748;
  font-weight: 900;
  font-size: 1rem;
  border-radius: 10px;
  padding: 6px 18px;
  box-shadow: 0 2px 8px rgba(255,215,0,0.13);
  letter-spacing: 0.5px;
  z-index: 3;
  text-shadow: 0 1px 4px #fff;
}

@media (max-width: 900px) {
  .hero-headline-pro {
    font-size: 1.5rem;
  }
  .hero-subheadline-pro {
    font-size: 1rem;
  }
  .hero-micro-highlights {
    gap: 0.4rem 0.7rem;
    margin-bottom: 1rem;
  }
}

.hero-feature-pills-row {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem 0.7rem;
  margin-bottom: 1.5rem;
  align-items: center;
  justify-content: flex-start;
}

.feature-pill-pro {
  display: flex;
  align-items: center;
  gap: 0.4em;
  border: none;
  border-radius: 16px;
  font-size: 1.05rem;
  font-weight: 800;
  padding: 8px 18px;
  background: var(--color-pink, #ff6b9d);
  color: #181818;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.13);
  cursor: pointer;
  transition: transform 0.18s, box-shadow 0.18s, background 0.18s, color 0.18s;
  outline: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.feature-pill-pro:focus, .feature-pill-pro:hover {
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 4px 16px rgba(255, 107, 157, 0.18), 0 2px 8px rgba(255, 215, 0, 0.13);
  background: linear-gradient(90deg, #ffd700 0%, #ff6b9d 100%);
  color: #181818;
}

.feature-pill-icon {
  font-size: 1.4em;
  margin-right: 0.3em;
  line-height: 1;
}

.feature-pill-label {
  font-size: 0.98em;
  font-weight: 900;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  line-height: 1;
}

.feature-pill-pro.pulse {
  animation: pillPulse 1.2s infinite alternate;
}

@keyframes pillPulse {
  0% { box-shadow: 0 2px 8px #ffd700, 0 0 0 0 #ff6b9d; }
  100% { box-shadow: 0 4px 16px #ff6b9d, 0 0 0 8px #ffd70033; }
}

.ad-spotlight-badge {
  position: absolute;
  top: 18px;
  left: 18px;
  background: linear-gradient(90deg, #ffb347 0%, #ffd700 100%);
  color: #2d3748;
  font-weight: 900;
  font-size: 1rem;
  border-radius: 10px;
  padding: 6px 18px;
  box-shadow: 0 2px 8px rgba(255,215,0,0.13);
  letter-spacing: 0.5px;
  z-index: 3;
  text-shadow: 0 1px 4px #fff;
}

.ad-trending-badge {
  margin-left: 0.7em;
  background: #ff6b9d;
  color: #fff;
  font-weight: 900;
  font-size: 0.95em;
  border-radius: 8px;
  padding: 3px 12px;
  animation: pillPulse 1.2s infinite alternate;
  box-shadow: 0 2px 8px #ff6b9d44;
  text-shadow: 0 1px 4px #fff;
}

@media (max-width: 900px) {
  .hero-feature-pills-row {
    gap: 0.4rem 0.5rem;
    margin-bottom: 1rem;
  }
  .feature-pill-pro {
    font-size: 0.98rem;
    padding: 7px 12px;
  }
}

.hero-gradient-blob {
  position: absolute;
  top: -40px;
  left: -60px;
  width: 340px;
  height: 220px;
  background: linear-gradient(120deg, #ff6b9d 0%, #ffd700 60%, #a259ff 100%);
  filter: blur(48px) saturate(1.2);
  opacity: 0.45;
  z-index: 0;
  border-radius: 50% 40% 60% 50% / 60% 50% 40% 50%;
  pointer-events: none;
}

.gradient-text {
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 40%, #a259ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-size: 2.7rem;
  font-weight: 900;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
  z-index: 1;
  position: relative;
}

.hero-feature-pills-masonry {
  display: flex;
  flex-wrap: wrap;
  gap: 0.7rem 1.1rem;
  margin-bottom: 1.7rem;
  align-items: flex-start;
  justify-content: flex-start;
  z-index: 1;
  position: relative;
}

.feature-pill-pro.squircle {
  border-radius: 22px 18px 28px 16px / 18px 28px 16px 22px;
  font-size: 1.08rem;
  font-weight: 900;
  padding: 12px 22px;
  min-width: 120px;
  min-height: 44px;
  box-shadow: 0 4px 18px rgba(255, 107, 157, 0.13), 0 2px 8px rgba(255, 215, 0, 0.13);
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  color: #181818;
  margin-bottom: 0.5rem;
  margin-right: 0.2rem;
  transition: transform 0.18s, box-shadow 0.18s, background 0.18s, color 0.18s;
  outline: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
  border: none;
  cursor: pointer;
  overflow: hidden;
}

.feature-pill-pro.squircle:focus, .feature-pill-pro.squircle:hover {
  transform: translateY(-3px) scale(1.06) rotate(-2deg);
  box-shadow: 0 8px 32px #ff6b9d55, 0 2px 8px #ffd70055;
  background: linear-gradient(90deg, #ffd700 0%, #ff6b9d 100%);
  color: #181818;
  filter: brightness(1.08) saturate(1.1);
}

.feature-pill-icon {
  font-size: 1.5em;
  margin-right: 0.3em;
  line-height: 1;
}

.feature-pill-label {
  font-size: 1em;
  font-weight: 900;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  line-height: 1;
}

.feature-pill-pro.pulse {
  animation: pillPulse 1.2s infinite alternate;
}

@keyframes pillPulse {
  0% { box-shadow: 0 2px 8px #ffd700, 0 0 0 0 #ff6b9d; }
  100% { box-shadow: 0 8px 32px #ff6b9d, 0 0 0 12px #ffd70033; }
}

.ad-spotlight-badge {
  position: absolute;
  top: 18px;
  left: 18px;
  background: linear-gradient(90deg, #ffb347 0%, #ffd700 100%);
  color: #2d3748;
  font-weight: 900;
  font-size: 1rem;
  border-radius: 10px;
  padding: 6px 18px;
  box-shadow: 0 2px 8px rgba(255,215,0,0.13);
  letter-spacing: 0.5px;
  z-index: 3;
  text-shadow: 0 1px 4px #fff;
}

.ad-trending-badge {
  margin-left: 0.7em;
  background: #ff6b9d;
  color: #fff;
  font-weight: 900;
  font-size: 0.95em;
  border-radius: 8px;
  padding: 3px 12px;
  animation: pillPulse 1.2s infinite alternate;
  box-shadow: 0 2px 8px #ff6b9d44;
  text-shadow: 0 1px 4px #fff;
}

.ad-trending-border {
  box-shadow: 0 0 0 6px #ff6b9d, 0 8px 32px #ff6b9d55, 0 2px 8px #ffd70055 !important;
  border-radius: 24px !important;
}

@media (max-width: 900px) {
  .hero-gradient-blob {
    width: 220px;
    height: 120px;
    top: -20px;
    left: -20px;
    filter: blur(32px) saturate(1.1);
  }
  .gradient-text {
    font-size: 1.3rem;
  }
  .hero-feature-pills-masonry {
    gap: 0.4rem 0.5rem;
    margin-bottom: 1rem;
  }
  .feature-pill-pro.squircle {
    font-size: 0.98rem;
    padding: 8px 12px;
    min-width: 90px;
    min-height: 36px;
  }
} 

.hero-feature-collage {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem 1rem;
  margin-bottom: 1.2rem;
  align-items: flex-end;
  justify-content: center;
  z-index: 1;
  position: relative;
}

.collage-pill, .collage-chip, .feature-mini-card-pro {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1.01rem;
  font-weight: 900;
  padding: 5px 16px;
  border-radius: 16px;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  box-shadow: 0 1px 4px #ffd70033;
  margin-bottom: 2px;
  background: #fff8dc;
  color: #232323;
  border: 2px solid #ffd700;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  cursor: pointer;
  min-width: 80px;
  min-height: 32px;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
}

.collage-pill.smart-engine,
.collage-chip.smart-engine,
.feature-mini-card-pro.smart-engine {
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  color: #181818;
  border: 2px solid #ff6b9d;
}
.collage-pill.latest-trends,
.collage-chip.latest-trends,
.feature-mini-card-pro.latest-trends {
  background: linear-gradient(90deg, #667eea 0%, #ffb347 100%);
  color: #fff;
  border: 2px solid #667eea;
}
.collage-pill.social-vibes,
.collage-chip.social-vibes,
.feature-mini-card-pro.social-vibes {
  background: linear-gradient(90deg, #a259ff 0%, #ffd700 100%);
  color: #fff;
  border: 2px solid #a259ff;
}

@media (max-width: 900px) {
  .hero-feature-collage {
    gap: 0.4rem 0.5rem;
    margin-bottom: 0.7rem;
    justify-content: center;
  }
  .collage-pill, .collage-chip, .feature-mini-card-pro {
    font-size: 0.82rem;
    padding: 4px 10px;
    border-radius: 10px;
    min-width: 54px;
    min-height: 24px;
    margin-bottom: 1px;
  }
}

.hero-gradient-blob.subtle {
  opacity: 0.28;
  filter: blur(60px) saturate(1.1);
  top: -30px;
  left: -40px;
  width: 260px;
  height: 120px;
  background: linear-gradient(120deg, #ff6b9d 0%, #ffd700 60%, #a259ff 100%);
  z-index: 0;
}

.hero-headline-pro.gradient-text.with-underline {
  font-size: 2.1rem;
  font-weight: 900;
  letter-spacing: 0.2px;
  margin-bottom: 0.3rem;
  z-index: 1;
  position: relative;
  background: linear-gradient(90deg, #c44569 0%, #ffd700 40%, #6c3483 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 2px 8px #fff, 0 1px 0 #fff, 0 2px 8px #23232399, 0 1px 0 #23232399;
}

.hero-headline-pro.gradient-text.with-underline::after {
  content: '';
  display: block;
  width: 80px;
  height: 6px;
  margin-top: 0.18em;
  border-radius: 4px;
  background: linear-gradient(90deg, #ffd700 0%, #ff6b9d 100%);
  opacity: 0.85;
  animation: underlineShimmer 2.2s infinite linear;
}

@keyframes underlineShimmer {
  0% { filter: brightness(1.1) saturate(1.1); }
  50% { filter: brightness(1.3) saturate(1.3); }
  100% { filter: brightness(1.1) saturate(1.1); }
}

.refined-subheadline {
  font-size: 1.01rem;
  color: #232323;
  font-weight: 600;
  margin-bottom: 0.7rem;
  letter-spacing: 0.04em;
  text-shadow: 0 1px 4px #fff;
  z-index: 1;
  position: relative;
}

@media (max-width: 900px) {
  .hero-headline-pro.gradient-text.with-underline {
    font-size: 1.1rem;
  }
  .refined-subheadline {
    font-size: 0.92rem;
    margin-bottom: 0.4rem;
  }
  .hero-gradient-blob.subtle {
    width: 120px;
    height: 60px;
    top: -10px;
    left: -10px;
    filter: blur(32px) saturate(1.05);
  }
}

.ad-glass-bg {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 0;
  background: rgba(255,255,255,0.55);
  border-radius: 20px 0 0 20px;
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.10), 0 2px 8px #ffd70022;
  backdrop-filter: blur(18px) saturate(1.2);
  pointer-events: none;
  opacity: 0.92;
  transition: opacity 0.3s;
}

.enhanced-badge {
  position: absolute;
  top: 18px;
  left: 18px;
  display: flex;
  align-items: center;
  gap: 0.5em;
  z-index: 2;
  font-size: 1.05rem;
  font-weight: 900;
  background: linear-gradient(90deg, #ffb347 0%, #ffd700 100%);
  color: #2d3748;
  border-radius: 12px;
  padding: 7px 20px;
  box-shadow: 0 2px 8px #ffd70033;
  text-shadow: 0 1px 4px #fff;
  animation: badgeFloat 2.2s infinite alternate;
}

@keyframes badgeFloat {
  0% { transform: translateY(0); }
  50% { transform: translateY(-3px) scale(1.04); }
  100% { transform: translateY(0); }
}

.ad-badge-icon {
  font-size: 1.2em;
  margin-right: 0.3em;
  animation: badgePulse 1.2s infinite alternate;
}

@keyframes badgePulse {
  0% { filter: brightness(1.1); }
  100% { filter: brightness(1.3); }
}

.ad-trending-badge.pulse {
  background: #ff6b9d;
  color: #fff;
  font-weight: 900;
  font-size: 0.98em;
  border-radius: 8px;
  padding: 3px 14px;
  margin-left: 0.7em;
  box-shadow: 0 2px 8px #ff6b9d44;
  text-shadow: 0 1px 4px #fff;
  filter: drop-shadow(0 0 8px #ff6b9d);
  animation: pillPulse 1.2s infinite alternate;
  display: inline-flex;
  align-items: center;
  gap: 0.3em;
}

.ad-confetti {
  font-size: 1.1em;
  margin-left: 0.3em;
  animation: confettiSparkle 1.5s infinite alternate;
}

@keyframes confettiSparkle {
  0% { opacity: 0.7; transform: scale(1) rotate(0deg); }
  50% { opacity: 1; transform: scale(1.2) rotate(20deg); }
  100% { opacity: 0.7; transform: scale(1) rotate(0deg); }
}

.enhanced-img-wrapper {
  position: relative;
  z-index: 1;
  margin-bottom: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.enhanced-ad-img {
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  position: relative;
  z-index: 2;
}

.ad-img-gradient-border {
  position: absolute;
  top: -8px; left: -8px; right: -8px; bottom: -8px;
  border-radius: 24px;
  z-index: 1;
  pointer-events: none;
  background: conic-gradient(from 90deg, #ff6b9d, #ffd700, #a259ff, #ffb347, #ff6b9d);
  opacity: 0.18;
  filter: blur(4px) saturate(1.2);
  animation: borderSpin 6s linear infinite;
}

@keyframes borderSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.enhanced-ad-info {
  text-align: center;
  z-index: 2;
  position: relative;
  margin-top: 0.2rem;
}

.enhanced-ad-name {
  font-size: 1.12rem;
  font-weight: 900;
  color: #1a202c;
  margin-bottom: 0.1rem;
  text-shadow: 0 1px 4px #fff;
}

.enhanced-ad-meta {
  font-size: 0.98rem;
  color: #4a5568;
  font-weight: 700;
  margin-bottom: 0.1rem;
  text-shadow: 0 1px 4px #fff;
}

.enhanced-ad-tagline {
  font-size: 1.01rem;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.6rem;
  text-shadow: 0 1px 4px #fff;
}

.enhanced-ad-cta {
  display: inline-block;
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  color: #181818;
  font-weight: 900;
  font-size: 1.01rem;
  border-radius: 16px;
  padding: 10px 28px;
  margin-top: 0.2rem;
  text-decoration: none;
  box-shadow: 0 2px 8px #ff6b9d33;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  text-shadow: 0 1px 4px #fff;
  border: none;
  outline: none;
}

.enhanced-ad-cta:hover, .enhanced-ad-cta:focus {
  background: linear-gradient(90deg, #ffd700 0%, #ff6b9d 100%);
  color: #111;
  box-shadow: 0 4px 16px #ffd70055;
}

@media (max-width: 900px) {
  .ad-glass-bg {
    border-radius: 20px;
  }
  .enhanced-badge {
    font-size: 0.92rem;
    padding: 5px 12px;
    top: 10px;
    left: 10px;
  }
  .enhanced-img-wrapper {
    margin-bottom: 0.7rem;
  }
  .enhanced-ad-name {
    font-size: 1rem;
  }
  .enhanced-ad-meta {
    font-size: 0.9rem;
  }
  .enhanced-ad-tagline {
    font-size: 0.92rem;
  }
  .enhanced-ad-cta {
    font-size: 0.95rem;
    padding: 8px 18px;
  }
}

.ad-spotlight-stack {
  position: relative;
  height: 100%;
  min-height: 420px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  z-index: 1;
}

.enhanced-spotlight-card {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  margin: auto;
  width: 98%;
  height: 98%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transition: opacity 0.5s cubic-bezier(0.4,0,0.2,1), transform 0.5s cubic-bezier(0.4,0,0.2,1);
  background: none;
  pointer-events: auto;
}

.enhanced-spotlight-card.fade-in {
  opacity: 1;
  transform: translateX(0) scale(1);
  z-index: 2;
}

.enhanced-spotlight-card.fade-out {
  opacity: 0;
  transform: translateX(-40px) scale(0.98);
  z-index: 1;
  pointer-events: none;
}

.enhanced-spotlight-card.next-ad {
  opacity: 0;
  transform: translateX(40px) scale(0.98);
  z-index: 1;
  pointer-events: none;
}

.ad-perks-row {
  display: flex;
  gap: 0.4em;
  margin: 0.3em 0 0.5em 0;
  flex-wrap: wrap;
  justify-content: center;
}

.ad-perk-chip {
  display: flex;
  align-items: center;
  gap: 0.2em;
  background: rgba(255,255,255,0.7);
  color: #232323;
  font-size: 0.88em;
  font-weight: 700;
  border-radius: 10px;
  padding: 3px 10px;
  box-shadow: 0 1px 4px #ffd70033;
  backdrop-filter: blur(6px) saturate(1.1);
  border: 1px solid #ffd70022;
  margin-bottom: 2px;
}

.ad-perk-icon {
  font-size: 1.1em;
  margin-right: 0.2em;
}

.ad-distance {
  color: #ff6b9d;
  font-weight: 900;
  font-size: 0.93em;
  margin-left: 0.7em;
  letter-spacing: 0.02em;
  display: inline-flex;
  align-items: center;
}

.ad-spotlight-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 18px;
  z-index: 10;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 12px;
  width: 100%;
  pointer-events: auto;
}

.ad-spotlight-dot {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #ffd700;
  opacity: 0.35;
  border: none;
  transition: opacity 0.2s, background 0.2s, box-shadow 0.2s;
  cursor: pointer;
  outline: none;
  box-shadow: 0 1px 4px #ffd70033;
}

.ad-spotlight-dot.active {
  opacity: 1;
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  box-shadow: 0 2px 8px #ff6b9d44, 0 0 0 2px #ffd70055;
}

.ad-spotlight-dot:hover, .ad-spotlight-dot:focus {
  opacity: 0.8;
  background: #ffb347;
}

@media (max-width: 900px) {
  .ad-spotlight-stack {
    min-height: 260px;
  }
  .enhanced-spotlight-card {
    width: 100%;
    height: 100%;
    min-height: 220px;
    padding: 0;
  }
  .ad-perk-chip {
    font-size: 0.82em;
    padding: 2px 7px;
  }
  .ad-spotlight-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 900px) {
  .enterprise-hero {
    display: block;
    min-height: 0;
    padding: 0 0 16px 0;
    width: 100vw;
    max-width: 100vw;
    box-sizing: border-box;
  }
  .ad-spotlight-stack {
    min-height: 160px;
    width: 100vw;
    max-width: 100vw;
    padding: 0;
    align-items: flex-start;
    display: block;
    justify-content: unset;
    box-sizing: border-box;
  }
  .enhanced-spotlight-card {
    position: static;
    width: 100vw;
    max-width: 100vw;
    height: auto;
    min-height: 120px;
    padding: 0;
    margin: 0;
    border-radius: 20px;
    box-shadow: 0 4px 16px #ff6b9d22;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
  }
}

@media (max-width: 600px) {
  .hero-content.enterprise-hero-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 1.2rem 0 0 0;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
}

@media (min-width: 900px) {
  .hero-content.enterprise-hero-content {
    align-items: flex-start;
    padding: 2.2rem 0 0 0;
  }
  .hero-headline-pro.gradient-text.with-underline.hero-title-dark {
    text-align: left;
    margin-left: 0;
    margin-right: 0;
  }
  .hero-steps-row {
    justify-content: flex-start;
  }
  .hero-cta-group-small {
    justify-content: flex-start;
    align-items: flex-start;
  }
}

.hero-headline-pro.gradient-text.with-underline.hero-title-dark {
  text-align: center;
  margin: 0 auto 0.7em auto;
  font-size: 2.08rem;
  font-weight: 900;
  letter-spacing: 0.01em;
  line-height: 1.13;
  max-width: 95vw;
}

.hero-steps-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  gap: 1.2rem;
  text-align: center;
  box-sizing: border-box;
}

.hero-cta-group,
.hero-cta-group-small {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  gap: 1rem;
  text-align: center;
  box-sizing: border-box;
  padding-left: 0;
  padding-right: 0;
}

@media (max-width: 600px) {
  .hero-steps-row,
  .hero-cta-group,
  .hero-cta-group-small {
    gap: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

.ad-spotlight-stack {
  min-height: 100px;
  padding: 0 1vw;
}

.enhanced-spotlight-card {
  border-radius: 14px;
  min-height: 80px;
}

.enhanced-img-wrapper {
  margin-bottom: 0.3rem;
}



.ad-img-gradient-border {
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 10px;
  position: absolute; /* Assuming needed for positioning */
}

.ad-perk-chip {
  font-size: 0.68em;
  padding: 1px 3px;
  border-radius: 5px;
}

.enhanced-ad-name {
  font-size: 0.82rem;
}

.enhanced-ad-meta {
  font-size: 0.7rem;
}

.enhanced-ad-tagline {
  font-size: 0.75rem;
  margin-bottom: 0.15rem;
}

.enhanced-ad-cta {
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 7px;
}

.ad-spotlight-dot {
  width: 7px;
  height: 7px;
  border-radius: 50%;
}


@media (max-width: 900px) {
  /* ... existing mobile styles ... */
  .reposition-badge-mobile {
    position: static !important;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 0.3rem;
    margin-left: 0.1rem;
    z-index: 3;
    font-size: 0.82rem;
    padding: 4px 8px;
    border-radius: 8px;
    background: linear-gradient(90deg, #ffb347 0%, #ffd700 100%);
    box-shadow: 0 1px 4px #ffd70033;
    width: max-content;
    max-width: 90vw;
  }
  .ad-spotlight-badge.enhanced-badge {
    position: static !important;
    margin-bottom: 0.3rem;
    margin-left: 0.1rem;
    font-size: 0.82rem;
    padding: 4px 8px;
    border-radius: 8px;
    width: max-content;
    max-width: 90vw;
  }
}

@media (max-width: 600px) {
  .reposition-badge-mobile {
    font-size: 0.7rem;
    padding: 3px 6px;
    border-radius: 6px;
    margin-bottom: 0.18rem;
    margin-left: 0.05rem;
  }
  .ad-spotlight-badge.enhanced-badge {
    font-size: 0.7rem;
    padding: 3px 6px;
    border-radius: 6px;
    margin-bottom: 0.18rem;
    margin-left: 0.05rem;
  }
}

@media (max-width: 900px) {
  /* ... existing mobile styles ... */
  .ad-spotlight-dots {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 6px;
    margin-top: 10px;
    margin-bottom: 0;
    position: static;
    left: 0;
    right: 0;
  }
}

@media (max-width: 900px) {
  /* ... existing mobile styles ... */
  .feature-pill-label,
  .feature-chip-label,
  .feature-mini-card-label {
    font-size: 0.68em !important;
    font-weight: 800;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    line-height: 1;
  }
  .hero-headline-pro.gradient-text.with-underline {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    display: block;
  }
  .hero-headline-pro.gradient-text.with-underline::after {
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0.18em;
    width: 60px;
    height: 5px;
  }
  .hero-cta-group {
    display: flex;
    flex-direction: row;
    gap: 0.7rem;
    justify-content: center;
    align-items: center;
    margin: 0.7rem 0 0.5rem 0;
  }
  .hero-cta.primary {
    background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%) !important;
    color: #181818 !important;
    font-weight: 900;
    border-radius: 14px;
    font-size: 0.98rem;
    padding: 8px 18px;
    box-shadow: 0 2px 8px #ff6b9d33;
    border: none;
    outline: none;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .hero-cta.secondary {
    background: rgba(255,255,255,0.7) !important;
    color: #667eea !important;
    font-weight: 900;
    border-radius: 14px;
    font-size: 0.98rem;
    padding: 8px 18px;
    box-shadow: 0 2px 8px #ffd70022;
    border: 2px solid #667eea;
    outline: none;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    backdrop-filter: blur(6px) saturate(1.1);
  }
  .hero-cta.primary:active, .hero-cta.primary:focus {
    background: linear-gradient(90deg, #ffd700 0%, #ff6b9d 100%) !important;
    color: #181818 !important;
    box-shadow: 0 4px 16px #ffd70055;
  }
  .hero-cta.secondary:active, .hero-cta.secondary:focus {
    background: #fff !important;
    color: #ff6b9d !important;
    border-color: #ff6b9d;
    box-shadow: 0 2px 8px #ff6b9d22;
  }
} 

/* ... existing code ... */
.hero-steps-row {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  margin: 1.2em 0 1.6em 0;
  z-index: 2;
  position: relative;
  justify-content: flex-start;
}
.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 90px;
}
.step-icon {
  font-size: 1.7rem;
  margin-bottom: 0.18em;
  color: #ffd700;
  background: linear-gradient(90deg, #fffbe6 0%, #ffd700 100%);
  border-radius: 50%;
  box-shadow: 0 1.5px 8px #ffd70033;
  padding: 7px 0;
  display: inline-block;
}
.step-label {
  font-size: 1.01rem;
  font-weight: 700;
  color: #232e4d;
  letter-spacing: 0.03em;
  margin-top: 0.08em;
  text-align: center;
  white-space: nowrap;
}
.step-separator {
  width: 32px;
  height: 3px;
  border-radius: 2px;
  background: linear-gradient(90deg, #ffd700 0%, #667eea 100%);
  margin: 0 0.2em;
  opacity: 0.6;
}
@media (max-width: 900px) {
  .hero-steps-row {
    gap: 0.55rem;
    margin: 0.55em 0 1.1em 0;
    justify-content: center;
  }
  .step-item {
    min-width: 70px;
  }
  .step-icon {
    font-size: 1.15rem;
    padding: 4px 0;
  }
  .step-label {
    font-size: 0.85rem;
  }
  .step-separator {
    width: 18px;
    height: 2px;
  }
}

.hero-cta-group-small {
  gap: 0.7rem;
  margin: 1.1rem 0 0.6rem 0;
}
.hero-cta-group-small .hero-cta.primary,
.hero-cta-group-small .hero-cta.secondary {
  font-size: 0.93rem;
  padding: 8px 22px;
  border-radius: 11px;
}
@media (max-width: 900px) {
  .hero-cta-group-small {
    flex-direction: column;
    gap: 0.5rem;
    margin: 0.7rem 0 0.3rem 0;
    align-items: center;
    justify-content: center;
  }
  .hero-cta-group-small .hero-cta.primary,
  .hero-cta-group-small .hero-cta.secondary {
    font-size: 0.89rem;
    padding: 7px 14px;
    border-radius: 8px;
  }

  /* Mobile premium badge adjustments */
  .premium-badge {
    font-size: 0.45rem !important;
    padding: 1px 4px !important;
    bottom: -6px !important;
    transform: translateX(-50%) !important;
    border-radius: 4px !important;
  }

  .hero-cta.secondary:hover .premium-badge {
    transform: translateX(-50%) scale(1.05) !important;
  }
}

/* ...old badge row styles below (can be deleted if not reused)... */
.hero-distinct-subheadline-row {
  display: flex;
  gap: 1.1em;
  margin: 1.2em 0 1.6em 0;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  z-index: 2;
  position: relative;
}
.distinct-badge {
  display: inline-block;
  font-size: 1.08rem;
  font-weight: 900;
  padding: 8px 22px;
  border-radius: 18px;
  letter-spacing: 0.07em;
  text-transform: uppercase;
  box-shadow: 0 3px 14px #ffd70033, 0 1.5px 4px #667eea22;
  margin-bottom: 2px;
  background: #fffbe6;
  color: #181818;
  border: 2.5px solid #ffd700;
  transition: box-shadow 0.2s, transform 0.2s;
}
.distinct-badge:hover, .distinct-badge:focus {
  box-shadow: 0 6px 24px #ffd70055, 0 3px 10px #667eea33;
  transform: translateY(-2px) scale(1.04);
  cursor: pointer;
}
.smart-engine {
  background: linear-gradient(90deg, #fffbe6 0%, #ffd700 100%);
  color: #181818;
  border: 2.5px solid #ffd700;
}
.latest-trends {
  background: linear-gradient(90deg, #eaf0fb 0%, #667eea 100%);
  color: #232e4d;
  border: 2.5px solid #667eea;
}
.social-vibes {
  background: linear-gradient(90deg, #f7eaff 0%, #a259ff 100%);
  color: #232323;
  border: 2.5px solid #a259ff;
}

/* CTA Group Premium Styling */
.hero-cta-group {
  display: flex;
  flex-direction: row;
  gap: 1.3rem;
  margin: 2.1rem 0 1.2rem 0;
  align-items: center;
  justify-content: flex-start;
}
.hero-cta.primary {
  background: linear-gradient(90deg, #ffd700 0%, #667eea 100%);
  color: #181818;
  font-weight: 900;
  border-radius: 16px;
  font-size: 1.08rem;
  padding: 14px 40px;
  box-shadow: 0 4px 18px #ffd70044, 0 2px 8px #667eea22;
  border: none;
  outline: none;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
  position: relative;
  overflow: hidden;
}
.hero-cta.primary:hover, .hero-cta.primary:focus {
  background: linear-gradient(90deg, #ffd700 0%, #181818 100%);
  color: #fffbe6;
  box-shadow: 0 8px 32px #ffd70066, 0 3px 12px #18181833;
  transform: translateY(-2px) scale(1.04);
}
.hero-cta.secondary {
  background: linear-gradient(90deg, #fffbe6 0%, #667eea 100%);
  color: #232e4d;
  font-weight: 900;
  border-radius: 16px;
  font-size: 1.08rem;
  padding: 14px 40px;
  box-shadow: 0 4px 18px #667eea33, 0 2px 8px #ffd70022;
  border: 2px solid #667eea;
  outline: none;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
  position: relative;
  overflow: visible !important;
}
.hero-cta.secondary:hover, .hero-cta.secondary:focus {
  background: linear-gradient(90deg, #667eea 0%, #ffd700 100%);
  color: #fffbe6;
  box-shadow: 0 8px 32px #667eea55, 0 3px 12px #ffd70033;
  border-color: #ffd700;
  transform: translateY(-2px) scale(1.04);
}
@media (max-width: 900px) {
  .hero-distinct-subheadline-row {
    gap: 0.55em;
    margin: 0.55em 0 1.1em 0;
    gap: 0.4em;
    margin: 0.25em 0 0.5em 0;
    justify-content: center;
  }
  .distinct-badge {
    font-size: 0.82rem;
    padding: 4px 10px;
    border-radius: 10px;
    margin-bottom: 1px;
  }
  .hero-mobile-section-separator {
    display: block;
    height: 18px;
    width: 100%;
    margin-bottom: 0.5rem;
  }
} 

.hero-pipe-subheadline {
  text-align: center;
  font-size: 0.98rem;
  font-weight: 600;
  color: #232323;
  margin: 0.18em auto 0.7em auto;
  letter-spacing: 0.01em;
  line-height: 1.2;
  max-width: 90vw;
  display: block;
  text-transform: none;
  background: none;
}
@media (max-width: 900px) {
  .hero-pipe-subheadline {
    font-size: 0.82rem;
    margin: 0.12em auto 0.5em auto;
    max-width: 98vw;
  }
} 

.enhanced-ad-img {
  width: 80vw;
  max-width: 338px;
  height: auto;
  aspect-ratio: 1/1;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  position: relative;
  z-index: 2;
  display: block;
}

@media (min-width: 600px) {
  .enhanced-ad-img {
    width: 270px;
    max-width: 338px;
  }
}

@media (min-width: 900px) {
  .enhanced-ad-img {
    width: 338px;
    max-width: 450px;
  }
}

.ad-spotlight-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 18px;
  z-index: 10;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 12px;
  width: 100%;
  pointer-events: auto;
}

.ad-spotlight-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #ffd700;
  opacity: 0.35;
  border: none;
  transition: opacity 0.2s, background 0.2s, box-shadow 0.2s;
  cursor: pointer;
  outline: none;
  box-shadow: 0 1px 4px #ffd70033;
}

.ad-spotlight-dot.active {
  opacity: 1;
  background: linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%);
  box-shadow: 0 2px 8px #ff6b9d44, 0 0 0 2px #ffd70055;
}

.ad-spotlight-dot:hover, .ad-spotlight-dot:focus {
  opacity: 0.8;
  background: #ffb347;
}

@media (max-width: 600px) {
  .ad-spotlight-dot {
    width: 11px;
    height: 11px;
  }
}



@media (max-width: 900px) {
  .ad-spotlight-dots {
    position: static !important;
    left: unset !important;
    right: unset !important;
    bottom: unset !important;
    margin-top: 2.5rem !important;
    z-index: 10;
  }
}

@media (max-width: 600px) {
  .ad-spotlight-dots {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 8px;
    width: 100%;
    z-index: 20;
    pointer-events: auto;
    background: none;
  }
}

.hero-steps-row,
.hero-cta-group,
.hero-cta-group-small {
  margin-left: auto !important;
  margin-right: auto !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  width: auto !important;
  max-width: 100% !important;
}