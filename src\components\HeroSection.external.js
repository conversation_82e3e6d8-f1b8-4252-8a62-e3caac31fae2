// External JavaScript for HeroSection functionality - Optimized
(function() {
  'use strict';

  // Premium vendors data
  const premiumVendors = [
    {
      id: 1,
      name: 'GlowUp Studio',
      img: 'https://randomuser.me/api/portraits/women/21.jpg',
      rating: 4.9,
      location: 'Nairobi',
      tagline: 'Next-level glam',
    },
    {
      id: 2,
      name: 'Urban Shears',
      img: 'https://randomuser.me/api/portraits/women/22.jpg',
      rating: 4.8,
      location: 'Westlands',
      tagline: 'Chic & sleek',
    },
    {
      id: 3,
      name: 'Elite Touch',
      img: 'https://randomuser.me/api/portraits/women/23.jpg',
      rating: 5.0,
      location: '<PERSON><PERSON><PERSON>',
      tagline: 'Luxury redefined',
    },
    {
      id: 4,
      name: 'Vogue Vault',
      img: 'https://randomuser.me/api/portraits/women/24.jpg',
      rating: 4.7,
      location: 'Lavington',
      tagline: 'Trendy always',
    },
    {
      id: 5,
      name: 'Bliss Lounge',
      img: 'https://randomuser.me/api/portraits/women/25.jpg',
      rating: 4.9,
      location: 'CBD',
      tagline: 'Glow on the go',
    },
    {
      id: 6,
      name: 'Aura Beauty',
      img: 'https://randomuser.me/api/portraits/women/26.jpg',
      rating: 4.8,
      location: 'Parklands',
      tagline: 'Radiate confidence',
      latitude: -1.2630,
      longitude: 36.8510,
    },
  ];

  // Constants
  const SLIDE_INTERVAL = 3500;
  const STICKY_THRESHOLD = 100;

  // Utility functions - Optimized
  function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Stickiness detection - Optimized
  function initStickinessDetection() {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;

    const handleScroll = debounce(() => {
      const rect = heroSection.getBoundingClientRect();
      const isInView = rect.top <= STICKY_THRESHOLD && rect.bottom >= 0;
      
      if (isInView) {
        heroSection.classList.add('sticky-active');
      } else {
        heroSection.classList.remove('sticky-active');
      }
    }, 16); // ~60fps

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Initial check
    handleScroll();
  }

  // Auto-slide functionality - Optimized
  function initAutoSlide() {
    const slider = document.querySelector('.premium-slider');
    const dots = document.querySelectorAll('.slider-dot');
    if (!slider || dots.length === 0) return;

    let currentSlide = 0;
    const totalSlides = dots.length;
    let slideInterval;

    function goToSlide(index) {
      currentSlide = index;
      slider.style.transform = `translateX(-${currentSlide * 100}%)`;
      
      // Update dots
      dots.forEach((dot, idx) => {
        dot.classList.toggle('active', idx === currentSlide);
      });
    }

    function nextSlide() {
      currentSlide = (currentSlide + 1) % totalSlides;
      goToSlide(currentSlide);
    }

    function startAutoSlide() {
      slideInterval = setInterval(nextSlide, SLIDE_INTERVAL);
    }

    function stopAutoSlide() {
      if (slideInterval) {
        clearInterval(slideInterval);
        slideInterval = null;
      }
    }

    // Start auto-slide
    startAutoSlide();

    // Dot click handlers
    dots.forEach((dot, idx) => {
      dot.addEventListener('click', () => {
        goToSlide(idx);
        // Reset interval
        stopAutoSlide();
        setTimeout(startAutoSlide, 1000);
      });
    });

    // Pause on hover - Optimized
    const sliderContainer = document.querySelector('.premium-slider-outer');
    if (sliderContainer) {
      sliderContainer.addEventListener('mouseenter', stopAutoSlide);
      sliderContainer.addEventListener('mouseleave', startAutoSlide);
    }
  }

  // Touch/swipe support for mobile - Optimized
  function initTouchSupport() {
    const slider = document.querySelector('.premium-slider');
    if (!slider) return;

    let startX = 0;
    let currentX = 0;
    let isDragging = false;
    let currentSlide = 0;
    const totalSlides = document.querySelectorAll('.slider-dot').length;

    function goToSlide(index) {
      currentSlide = index;
      slider.style.transform = `translateX(-${currentSlide * 100}%)`;
      
      // Update dots
      const dots = document.querySelectorAll('.slider-dot');
      dots.forEach((dot, idx) => {
        dot.classList.toggle('active', idx === currentSlide);
      });
    }

    function handleTouchStart(e) {
      startX = e.touches[0].clientX;
      isDragging = true;
      slider.style.transition = 'none';
    }

    function handleTouchMove(e) {
      if (!isDragging) return;
      e.preventDefault();
      currentX = e.touches[0].clientX;
      const diff = currentX - startX;
      const translateX = -currentSlide * 100 + (diff / slider.offsetWidth) * 100;
      slider.style.transform = `translateX(${translateX}%)`;
    }

    function handleTouchEnd() {
      if (!isDragging) return;
      isDragging = false;
      slider.style.transition = 'transform 0.4s ease';
      
      const diff = currentX - startX;
      const threshold = slider.offsetWidth * 0.3;
      
      if (Math.abs(diff) > threshold) {
        if (diff > 0 && currentSlide > 0) {
          goToSlide(currentSlide - 1);
        } else if (diff < 0 && currentSlide < totalSlides - 1) {
          goToSlide(currentSlide + 1);
        } else {
          goToSlide(currentSlide);
        }
      } else {
        goToSlide(currentSlide);
      }
    }

    slider.addEventListener('touchstart', handleTouchStart, { passive: false });
    slider.addEventListener('touchmove', handleTouchMove, { passive: false });
    slider.addEventListener('touchend', handleTouchEnd);
  }

  // Floating elements animation - Optimized
  function initFloatingElements() {
    const floatingElements = document.querySelectorAll('.float-icon');
    
    floatingElements.forEach((element, index) => {
      // Add random animation delays
      element.style.animationDelay = `${index * 0.5}s`;
      
      // Add hover effects - Optimized
      element.addEventListener('mouseenter', () => {
        element.style.transform = 'scale(1.1)';
        element.style.filter = 'drop-shadow(0 2px 6px rgba(0,0,0,0.15))';
      });
      
      element.addEventListener('mouseleave', () => {
        element.style.transform = '';
        element.style.filter = '';
      });
    });
  }

  // Accessibility improvements - Optimized
  function initAccessibility() {
    // Keyboard navigation for slider
    const sliderContainer = document.querySelector('.premium-slider-outer');
    if (sliderContainer) {
      sliderContainer.addEventListener('keydown', (e) => {
        const dots = document.querySelectorAll('.slider-dot');
        const currentSlide = Array.from(dots).findIndex(dot => dot.classList.contains('active'));
        
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            if (currentSlide > 0) {
              dots[currentSlide - 1].click();
            }
            break;
          case 'ArrowRight':
            e.preventDefault();
            if (currentSlide < dots.length - 1) {
              dots[currentSlide + 1].click();
            }
            break;
        }
      });
    }

    // Focus management - Optimized
    const vendorLinks = document.querySelectorAll('.premium-slide');
    vendorLinks.forEach(link => {
      link.addEventListener('focus', () => {
        link.setAttribute('aria-hidden', 'false');
      });
      
      link.addEventListener('blur', () => {
        if (!link.matches(':focus-within')) {
          link.setAttribute('aria-hidden', 'true');
        }
      });
    });
  }

  // Performance optimizations - Enhanced
  function initPerformanceOptimizations() {
    // Use Intersection Observer for animations - Optimized
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '50px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.animate-hero, .animate-advanced, .animate-cta');
    animatedElements.forEach(el => observer.observe(el));

    // Optimize scroll performance
    let ticking = false;
    function updateScroll() {
      ticking = false;
      // Scroll-based animations can go here
    }

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateScroll);
        ticking = true;
      }
    }

    window.addEventListener('scroll', requestTick, { passive: true });
  }

  // Initialize everything when DOM is ready - Optimized
  function init() {
    // Wait for React to render
    setTimeout(() => {
      initStickinessDetection();
      initAutoSlide();
      initTouchSupport();
      initFloatingElements();
      initAccessibility();
      initPerformanceOptimizations();
    }, 100);
  }

  // Export functions for React component
  window.HeroSectionUtils = {
    shuffleArray,
    init,
    premiumVendors,
    SLIDE_INTERVAL
  };

  // Auto-initialize if DOM is already ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})(); 