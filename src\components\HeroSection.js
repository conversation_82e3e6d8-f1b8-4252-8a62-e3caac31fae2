import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import { Alien, Lightning, Rainbow } from '@phosphor-icons/react';
import { getLocationWithGPSPriority } from '../services/geolocationService';
import { calculateDistance } from '../services/distanceUtils';
import './HeroSection.css';
import './HeroSection.external';

const premiumVendors = [
  {
    id: 1,
    name: 'GlowUp Studio',
    img: 'https://randomuser.me/api/portraits/women/21.jpg',
    rating: 4.9,
    location: 'Nairobi',
    tagline: 'Next-level glam',
    latitude: -1.2921,
    longitude: 36.8219,
  },
  {
    id: 2,
    name: 'Urban Shears',
    img: 'https://randomuser.me/api/portraits/women/22.jpg',
    rating: 4.8,
    location: 'Westlands',
    tagline: 'Chic & sleek',
    latitude: -1.2634,
    longitude: 36.8078,
  },
  {
    id: 3,
    name: 'Elite Touch',
    img: 'https://randomuser.me/api/portraits/women/23.jpg',
    rating: 5.0,
    location: '<PERSON><PERSON><PERSON>',
    tagline: 'Luxury redefined',
    latitude: -1.2966,
    longitude: 36.7828,
  },
  {
    id: 4,
    name: 'Vogue Vault',
    img: 'https://randomuser.me/api/portraits/women/24.jpg',
    rating: 4.7,
    location: 'Lavington',
    tagline: 'Trendy always',
    latitude: -1.2833,
    longitude: 36.7667,
  },
  {
    id: 5,
    name: 'Bliss Lounge',
    img: 'https://randomuser.me/api/portraits/women/25.jpg',
    rating: 4.9,
    location: 'CBD',
    tagline: 'Glow on the go',
    latitude: -1.2864,
    longitude: 36.8172,
  },
  {
    id: 6,
    name: 'Aura Beauty',
    img: 'https://randomuser.me/api/portraits/women/26.jpg',
    rating: 4.8,
    location: 'Parklands',
    tagline: 'Radiate confidence',
    latitude: -1.2630,
    longitude: 36.8510,
  },
];

const SLIDE_INTERVAL = 5000; // 5 seconds for auto-fade

// Theme color palette (borrowed from Trending Now, Our Salons, etc.)
const themeGradients = [
  'linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%)', // pink-gold
  'linear-gradient(90deg, #667eea 0%, #ffb347 100%)', // blue-gold
  'linear-gradient(90deg, #a259ff 0%, #ffd700 100%)', // purple-gold
  'linear-gradient(90deg, #ffb347 0%, #ff6b9d 100%)', // gold-pink
  'linear-gradient(90deg, #ff6b9d 0%, #a259ff 100%)', // pink-purple
  'linear-gradient(90deg, #ffd700 0%, #667eea 100%)', // gold-blue
  'linear-gradient(90deg, #a259ff 0%, #ffb347 100%)', // purple-gold
];

// Modern, expressive feature highlights: mix pills, chips, mini-cards
const featureHighlights = [
  {
    type: 'pill',
    icon: <span role="img" aria-label="AI Stylist">💇‍♀️</span>,
    label: 'AI Stylist',
    link: '/ai-style-advisor',
    gradient: 'linear-gradient(90deg, #ff6b9d 0%, #ffd700 100%)',
  },
  {
    type: 'chip',
    icon: <span role="img" aria-label="Smart Match">💅</span>,
    label: 'Smart Match',
    link: '/ai-salon-matcher',
    glass: true,
  },
  {
    type: 'mini-card',
    icon: <span role="img" aria-label="Voice Booking">🎤</span>,
    label: 'Voice Booking',
    link: '/ai-voice-integration',
    badge: 'New',
    gradient: 'linear-gradient(90deg, #667eea 0%, #ffb347 100%)',
  },
  {
    type: 'pill',
    icon: <span role="img" aria-label="Trend Radar">🔮</span>,
    label: 'Trend Radar',
    link: '/ai-trend-predictor',
    gradient: 'linear-gradient(90deg, #a259ff 0%, #ffd700 100%)',
  },
  {
    type: 'chip',
    icon: <span role="img" aria-label="Top Rated">👑</span>,
    label: 'Top Rated',
    link: '/top-salons',
    glass: true,
    badge: '4.9+',
  },
  {
    type: 'mini-card',
    icon: <span role="img" aria-label="Gen Z Looks">🦄</span>,
    label: 'Gen Z Looks',
    link: '/trending-looks',
    gradient: 'linear-gradient(90deg, #ff6b9d 0%, #a259ff 100%)',
  },
  {
    type: 'pill',
    icon: <span role="img" aria-label="Trending Now">🔥</span>,
    label: 'Trending Now',
    link: '/trending',
    gradient: 'linear-gradient(90deg, #ffb347 0%, #ff6b9d 100%)',
    pulse: true,
    badge: 'Live',
  },
];

// Example vendor perks (could be dynamic)
const vendorPerks = [];

// Mock function to get distance (replace with real geolocation logic)
function getDistanceFromUser(vendor) {
  // For demo, return a random distance between 0.5 and 5.0 km
  return `${(Math.random() * 4.5 + 0.5).toFixed(1)} km away`;
}

const AD_FADE_INTERVAL = 6000;

const HeroSection = ({ user, onBookClick, onLoginClick }) => {
  const navigate = useNavigate();
  const [randomizedVendors, setRandomizedVendors] = useState([]);
  const [adLoaded, setAdLoaded] = useState(false);
  const [adIndex, setAdIndex] = useState(0);
  const [adFade, setAdFade] = useState(false);
  const [userLocation, setUserLocation] = useState(null);
  const [vendorDistances, setVendorDistances] = useState({});

  // Initialize vendors and get user location
  useEffect(() => {
    const shuffled = [...premiumVendors].sort(() => Math.random() - 0.5);
    setRandomizedVendors(shuffled);
    setAdLoaded(shuffled.length > 0);

    // Get user location for distance calculations
    const getUserLocationForDistances = async () => {
      try {
        console.log('🎯 Getting user location for hero section distances...');
        const location = await getLocationWithGPSPriority();

        if (location && location.latitude && location.longitude) {
          setUserLocation(location);
          console.log('✅ User location obtained for hero distances:', location);

          // Calculate distances to all vendors
          const distances = {};
          shuffled.forEach(vendor => {
            if (vendor.latitude && vendor.longitude) {
              const distance = calculateDistance(
                location.latitude,
                location.longitude,
                vendor.latitude,
                vendor.longitude
              );
              distances[vendor.id] = Math.round(distance * 10) / 10; // Round to 1 decimal
              console.log(`Distance to ${vendor.name}: ${distances[vendor.id]}km`);
            } else {
              console.warn(`Missing coordinates for vendor: ${vendor.name}`);
            }
          });

          setVendorDistances(distances);
          console.log('✅ Vendor distances calculated from user location:', location);
          console.log('✅ Calculated distances:', distances);
        }
      } catch (error) {
        console.log('ℹ️ Could not get user location for hero distances:', error.message);
        // Don't show distances if location unavailable
      }
    };

    getUserLocationForDistances();
  }, []);

  // Auto-fade to next ad
  useEffect(() => {
    if (!adLoaded || randomizedVendors.length < 2) return;
    setAdFade(false);
    const fadeTimeout = setTimeout(() => setAdFade(true), AD_FADE_INTERVAL - 400);
    const interval = setInterval(() => {
      setAdFade(false);
      setAdIndex((prev) => (prev + 1) % randomizedVendors.length);
    }, AD_FADE_INTERVAL);
    return () => {
      clearInterval(interval);
      clearTimeout(fadeTimeout);
    };
  }, [adLoaded, randomizedVendors.length, adIndex]);

  // Manual navigation
  const handleAdDotClick = (idx) => {
    setAdFade(false);
    setAdIndex(idx);
  };

  // Only render the current ad for performance and clarity
  const getVisibleAds = () => {
    if (randomizedVendors.length === 0) return [];
    return [adIndex];
  };

  return (
    <section className="hero-section genz-hero">
      {/* Floating Elements */}
      <div className="hero-floating-elements">
        <div className="floating-emoji">✨</div>
        <div className="floating-emoji">💅</div>
        <div className="floating-emoji">💄</div>
        <div className="floating-emoji">🔥</div>
        <div className="floating-emoji">💎</div>
      </div>

      {/* Main Hero Content */}
      <div className="hero-main-content">
        {/* Hero Text */}
        <div className="hero-text-section">
          <div className="hero-badge">
            <span className="badge-emoji">🌟</span>
            Kenya's #1 Beauty Platform
          </div>

          <h1 className="hero-main-title">
            Your <span className="gradient-text">Glow Up</span>
            <br />Starts Here
          </h1>

          <p className="hero-description">
            Book Salon Services instantly. Smart Hair Style Matching and exclusive Gen-Z experiences.
          </p>



          {/* CTA Buttons */}
          <div className="hero-cta-section">
            <button
              className="hero-cta-primary genz-button"
              onClick={user ? onBookClick : onLoginClick}
              type="button"
            >
              <span className="cta-icon">✨</span>
              {user ? 'Book Your Glow Up' : 'Start Your Journey'}
              <div className="button-shine"></div>
            </button>

            <button
              className="hero-cta-secondary genz-button-outline"
              onClick={() => navigate('/register-vendor')}
              type="button"
            >
              <span className="cta-icon">🏪</span>
              List Your Salon
              <span className="premium-tag">PREMIUM</span>
            </button>
          </div>

        </div>

        {/* Visual Section */}
        <div className="hero-visual-section">
          {/* Trending Salon Showcase */}
          {adLoaded && randomizedVendors.length > 0 && (
            <div className="trending-salon-card">
              {randomizedVendors.length > 0 && (
                <div className="salon-showcase active">
                  {(() => {
                    const vendor = randomizedVendors[adIndex];
                    const isTrending = vendor.rating >= 4.9;
                    return (
                      <>
                    {/* Trending Badge */}
                    <div className="trending-badge">
                      {isTrending ? (
                        <>
                          <span className="badge-fire">🔥</span>
                          <span className="badge-text">Trending Now</span>
                        </>
                      ) : (
                        <>
                          <span className="badge-star">⭐</span>
                          <span className="badge-text">Featured</span>
                        </>
                      )}
                    </div>

                    {/* Salon Image */}
                    <div className="salon-image-container">
                      <img
                        src={vendor.img}
                        alt={vendor.name}
                        className="salon-image"
                        loading="lazy"
                      />
                      <div className="image-overlay"></div>
                    </div>

                    {/* Salon Info */}
                    <div className="salon-info">
                      <h3 className="salon-name">{vendor.name}</h3>
                      <div className="salon-location">
                        <span className="location-icon">📍</span>
                        {vendor.location}
                      </div>
                      <div className="salon-rating">
                        <span className="rating-stars">⭐⭐⭐⭐⭐</span>
                        <span className="rating-text">{vendor.rating || '4.9'}</span>
                        {vendorDistances[vendor.id] && (
                          <span className="distance-indicator">
                            <span className="distance-icon">📍</span>
                            <span className="distance-text">{vendorDistances[vendor.id]}km</span>
                          </span>
                        )}
                      </div>

                      {/* Salon Perks */}
                      <div className="salon-perks">
                        {vendorPerks.slice(0, 2).map((perk) => (
                          <span key={perk.label} className="perk-tag">
                            <span className="perk-icon">{perk.icon}</span>
                            {perk.label}
                          </span>
                        ))}
                      </div>

                      {/* CTA Button */}
                      <a
                        href={`/vendor/${vendor.id}`}
                        className="salon-cta-button"
                        aria-label={`View ${vendor.name} details`}
                      >
                        Book Now ✨
                      </a>
                    </div>
                  </>
                    );
                  })()}
                </div>
              )}

              {/* Navigation Dots */}
              <div className="showcase-dots">
                {randomizedVendors.map((_, idx) => (
                  <button
                    key={idx}
                    className={`showcase-dot ${adIndex === idx ? 'active' : ''}`}
                    onClick={() => handleAdDotClick(idx)}
                    aria-label={`Go to salon ${idx + 1}`}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Decorative Elements */}
          <div className="visual-decorations">
            <div className="decoration-circle"></div>
            <div className="decoration-triangle"></div>
            <div className="decoration-square"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

HeroSection.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    username: PropTypes.string
  }),
  onBookClick: PropTypes.func.isRequired,
  onLoginClick: PropTypes.func.isRequired
};

HeroSection.defaultProps = {
  user: {},
};

export default HeroSection; 
