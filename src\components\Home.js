// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Slider from 'react-slick';
import { useAuth } from '../context/AuthContext';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import NearbySalonsSection from './NearbySalonsSection';
import './PopularSalonsSection.css';
import GenZSocialFeed from './GenZSocialFeed';
import ModernSalonFinder from './ModernSalonFinder';
import HeroSection from './HeroSection';
import GenZMobileFeatures from './GenZMobileFeatures';
import MobileCTA from './MobileCTA';
import PremiumTicker from './PremiumTicker';
import TrendingNowSection from './TrendingNowSection';
import CheapestServicesSection from './CheapestServicesSection';
import UniversalPagination from './UniversalPagination';
import { getLocationWithGPSPriority } from '../services/geolocationService';
import { calculateDistance } from '../services/distanceUtils';

const Home = () => {
  const { user, authFetch } = useAuth();
  const [greeting, setGreeting] = useState('');
  const [salons, setSalons] = useState([]);
  const [featuredSalons, setFeaturedSalons] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [featuredLoading, setFeaturedLoading] = useState(false);
  const [error, setError] = useState(null);
  const [county, setCounty] = useState('Nairobi County');
  const [town, setTown] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [featuredCurrentPage, setFeaturedCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(4);
  const [userLocation, setUserLocation] = useState(null);
  const [featuredDistances, setFeaturedDistances] = useState({});

  const fetchSalons = React.useCallback(async (countyParam, townParam, term) => {
    setLoading(true);
    setError(null);
    // PRODUCTION OPTIMIZATION: Add limit parameter for better performance
    let url = `${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/salons/search/?limit=20`;
    if (countyParam) url += `&county=${encodeURIComponent(countyParam)}`;
    if (townParam) url += `&town=${encodeURIComponent(townParam)}`;
    if (term) url += `&q=${encodeURIComponent(term)}`;
    try {
      console.log('🔍 Fetching salons from:', url);
      const res = await authFetch(url);
      if (!res.ok) {
        // Use mock data if API fails
        console.warn(`API returned ${res.status}, using fallback data`);
        setSalons([
          {
            id: 1,
            name: 'Glamour Salon',
            imageUrl:
              'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop',
            location: 'Nairobi CBD, Nairobi County',
            town: 'Nairobi CBD',
            county: 'Nairobi County',
            latitude: -1.2864,
            longitude: 36.8172,
          },
          {
            id: 2,
            name: 'Style Studio',
            imageUrl:
              'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=300&fit=crop',
            location: 'Westlands, Nairobi County',
            town: 'Westlands',
            county: 'Nairobi County',
            latitude: -1.2634,
            longitude: 36.8078,
          },
          {
            id: 3,
            name: 'Beauty Haven',
            imageUrl:
              'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400&h=300&fit=crop',
            location: 'Kilimani, Nairobi County',
            town: 'Kilimani',
            county: 'Nairobi County',
            latitude: -1.2966,
            longitude: 36.7828,
          },
        ]);
        return;
      }
      const data = await res.json();
      console.log(`✅ Fetched ${data.length} salons successfully`);
      setSalons(data || []);
    } catch (err) {
      console.error('❌ Error fetching salons:', err);
      setError('Failed to load salons. Showing sample data.');
      setSalons([
        {
          id: 1,
          name: 'Glamour Salon',
          imageUrl:
            'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop',
          town: 'Nairobi CBD',
          county: 'Nairobi County',
        },
        {
          id: 2,
          name: 'Style Studio',
          imageUrl:
            'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=300&fit=crop',
          town: 'Westlands',
          county: 'Nairobi County',
        },
        {
          id: 3,
          name: 'Beauty Haven',
          imageUrl:
            'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400&h=300&fit=crop',
          town: 'Kilimani',
          county: 'Nairobi County',
        },
      ]);
    } finally {
      setLoading(false);
    }
  }, [authFetch]);

  // Fetch featured/premium salons
  const fetchFeaturedSalons = React.useCallback(async () => {
    setFeaturedLoading(true);
    try {
      console.log('🌟 Fetching featured salons...');
      const res = await authFetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/salons/search/?limit=12`);

      if (!res.ok) {
        console.warn(`Featured salons API returned ${res.status}, using fallback data`);
        // Use premium mock data for featured salons
        setFeaturedSalons([
          {
            id: 101,
            name: 'Elite Beauty Lounge',
            imageUrl: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop',
            town: 'Westlands',
            county: 'Nairobi County',
            address: 'Westlands Square',
            latitude: -1.2634,
            longitude: 36.8078
          },
          {
            id: 102,
            name: 'Glamour Studio Premium',
            imageUrl: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop',
            town: 'Karen',
            county: 'Nairobi County',
            address: 'Karen Shopping Centre',
            latitude: -1.3197,
            longitude: 36.6859
          },
          {
            id: 103,
            name: 'Luxury Hair Spa',
            imageUrl: 'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=300&fit=crop',
            town: 'Kilimani',
            county: 'Nairobi County',
            address: 'Yaya Centre',
            latitude: -1.2966,
            longitude: 36.7828
          },
          {
            id: 104,
            name: 'Royal Beauty Palace',
            imageUrl: 'https://images.unsplash.com/photo-1559599101-f09722fb4948?w=400&h=300&fit=crop',
            town: 'Kiserian',
            county: 'Kajiado County',
            address: 'Kiserian Town',
            latitude: -1.3833,
            longitude: 36.6500
          }
        ]);
        return;
      }

      const data = await res.json();
      // Filter for premium/featured salons (top-rated or premium tier)
      const featured = data.filter((salon, index) => index < 8); // Take first 8 as featured
      console.log(`✅ Fetched ${featured.length} featured salons`);
      setFeaturedSalons(featured);

      // Note: Distance calculation will happen in separate useEffect when userLocation is available
    } catch (err) {
      console.error('❌ Error fetching featured salons:', err);
      setFeaturedSalons([]);
    } finally {
      setFeaturedLoading(false);
    }
  }, [authFetch]);

  useEffect(() => {
    // Set dynamic greeting
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good Morning');
    } else if (hour < 18) {
      setGreeting('Good Afternoon');
    } else {
      setGreeting('Good Evening');
    }
    fetchSalons('Nairobi County', '', ''); // Default to Nairobi County on mount
    fetchFeaturedSalons(); // Fetch featured salons on mount

    // Get user location for distance calculations
    const getUserLocationForDistances = async () => {
      try {
        console.log('🎯 Getting user location for featured salon distances...');
        const location = await getLocationWithGPSPriority();

        if (location && location.latitude && location.longitude) {
          setUserLocation(location);
          console.log('✅ User location obtained for featured distances:', location);
          console.log(`📍 Location source: ${location.source || 'unknown'}`);
          console.log(`📍 Coordinates: (${location.latitude}, ${location.longitude})`);
        } else {
          console.warn('❌ Invalid location data received:', location);
        }
      } catch (error) {
        console.log('ℹ️ Could not get user location for featured distances:', error.message);
      }
    };

    getUserLocationForDistances();
  }, [fetchSalons, fetchFeaturedSalons]);

  // Calculate distances when both userLocation and featuredSalons are available
  useEffect(() => {
    if (userLocation && featuredSalons.length > 0) {
      console.log('🎯 Calculating distances for featured salons...');
      console.log('User location:', userLocation);
      console.log('Featured salons count:', featuredSalons.length);

      const distances = {};
      featuredSalons.forEach(salon => {
        if (salon.latitude && salon.longitude) {
          const distance = calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            salon.latitude,
            salon.longitude
          );
          distances[salon.id] = Math.round(distance * 10) / 10; // Round to 1 decimal
          console.log(`Distance to ${salon.name}: ${distances[salon.id]}km`);
        } else {
          console.warn(`Missing coordinates for salon: ${salon.name}`);
        }
      });

      setFeaturedDistances(distances);
      console.log('✅ Featured salon distances calculated:', distances);
    } else {
      console.log('⏳ Waiting for user location or featured salons...');
      console.log('User location available:', !!userLocation);
      console.log('Featured salons available:', featuredSalons.length);
    }
  }, [userLocation, featuredSalons]);

  // Fetch all unique counties and towns from backend on mount
  const [allCounties, setAllCounties] = useState(['Nairobi County']);
  const [allTowns, setAllTowns] = useState([]);

  useEffect(() => {
    // Fetch all counties
    fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/salons/search/`)
      .then(res => {
        if (!res.ok) {
          console.log('API failed, using default counties');
          setAllCounties(['Nairobi County', 'Mombasa County', 'Kisumu County']);
          return [];
        }
        return res.json();
      })
      .then(data => {
        if (data && data.length > 0) {
          const uniqueCounties = Array.from(new Set(data.map(s => s.county).filter(Boolean)));
          setAllCounties(uniqueCounties.length ? uniqueCounties : ['Nairobi County']);
        } else {
          setAllCounties(['Nairobi County', 'Mombasa County', 'Kisumu County']);
        }
      })
      .catch(err => {
        console.log('Error fetching counties, using defaults:', err.message);
        setAllCounties(['Nairobi County', 'Mombasa County', 'Kisumu County']);
      });
  }, []);

  useEffect(() => {
    // Fetch towns for selected county
    if (county) {
      fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/salons/search/?county=${encodeURIComponent(county)}`)
        .then(res => {
          if (!res.ok) {
            console.log('API failed, using default towns');
            setAllTowns(['Nairobi CBD', 'Westlands', 'Kilimani']);
            return [];
          }
          return res.json();
        })
        .then(data => {
          if (data && data.length > 0) {
            const uniqueTowns = Array.from(new Set(data.map(s => s.town).filter(Boolean)));
            setAllTowns(uniqueTowns);
          } else {
            setAllTowns(['Nairobi CBD', 'Westlands', 'Kilimani']);
          }
        })
        .catch(err => {
          console.log('Error fetching towns, using defaults:', err.message);
          setAllTowns(['Nairobi CBD', 'Westlands', 'Kilimani']);
        });
    } else {
      setAllTowns([]);
    }
  }, [county]);

  const counties = allCounties;
  const towns = allTowns;

  const handleCountyChange = (e) => {
    const newCounty = e.target.value;
    setCounty(newCounty);
    setTown('');
    fetchSalons(newCounty, '', searchTerm);
  };

  const handleTownChange = (e) => {
    const newTown = e.target.value;
    setTown(newTown);
    fetchSalons(county, newTown, searchTerm);
  };

  // Mobile detection and pagination logic
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      setPerPage(mobile ? 2 : 4);
      setCurrentPage(1);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Pagination logic for search results
  const totalPages = Math.ceil(salons.length / perPage);
  const startIndex = (currentPage - 1) * perPage;
  const paginatedSalons = salons.slice(startIndex, startIndex + perPage);

  // Pagination logic for featured salons
  const featuredTotalPages = Math.ceil(featuredSalons.length / perPage);
  const featuredStartIndex = (featuredCurrentPage - 1) * perPage;
  const paginatedFeaturedSalons = featuredSalons.slice(featuredStartIndex, featuredStartIndex + perPage);

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) pages.push(i);
        pages.push('ellipsis-right');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('ellipsis-left');
        for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push('ellipsis-left');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
        pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  // Featured salons pagination helper
  const getFeaturedPageNumbers = () => {
    const pages = [];
    const maxVisible = 5;

    if (featuredTotalPages <= maxVisible) {
      for (let i = 1; i <= featuredTotalPages; i++) {
        pages.push(i);
      }
    } else {
      if (featuredCurrentPage <= 3) {
        for (let i = 1; i <= 4; i++) pages.push(i);
        pages.push('ellipsis-right');
        pages.push(featuredTotalPages);
      } else if (featuredCurrentPage >= featuredTotalPages - 2) {
        pages.push(1);
        pages.push('ellipsis-left');
        for (let i = featuredTotalPages - 3; i <= featuredTotalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push('ellipsis-left');
        for (let i = featuredCurrentPage - 1; i <= featuredCurrentPage + 1; i++) pages.push(i);
        pages.push('ellipsis-right');
        pages.push(featuredTotalPages);
      }
    }
    return pages;
  };

  return (
    <div>
      {/* Premium Ticker - Only on Home Page */}
      <PremiumTicker />

      <div className="container mt-5 mb-5">
        {/* Modern Mobile-First Hero Section */}
        <HeroSection
          greeting={greeting}
          user={user}
          onBookClick={() => window.location.assign('/book')}
          onLoginClick={() => window.location.assign('/login')}
        />

        {/* Gen Z Mobile Engagement Features */}
        <GenZMobileFeatures />



        {/* Location-Based Recommendations */}
        <NearbySalonsSection />

        {/* Gen Z Social Feed with Scaling Management */}
        <GenZSocialFeed />

        {/* Trending Now Section - Popular Salons & Services */}
        <TrendingNowSection />

        {/* Best Services Section - Value Services */}
        <CheapestServicesSection />

        {/* Modern Salon Finder */}
        <ModernSalonFinder
          counties={counties}
          towns={towns}
          salons={salons}
          loading={loading}
          onSearch={(county, town, searchTerm) => {
            setCounty(county);
            setTown(town);
            setSearchTerm(searchTerm);
            fetchSalons(county, town, searchTerm);
          }}
          onCountyChange={(county) => {
            setCounty(county);
            setTown('');
            fetchSalons(county, '', searchTerm);
          }}
          onTownChange={(town) => {
            setTown(town);
            fetchSalons(county, town, searchTerm);
          }}
        />





        {/* Featured Salons Section - Premium/Featured Salons Only */}
        <section className="popular-salons-section">
          <div className="popular-salons-container">
            <div className="section-header">
              <h2 className="section-title">
                {county ? `Featured Salons in ${county}` : 'Featured Salons'}
              </h2>
              <p className="section-subtitle">Premium salons handpicked for excellence</p>
            </div>

            {/* Loading State */}
            {featuredLoading && (
              <div className="loading-state">
                <div className="loading-spinner" />
                <p className="loading-text">Loading featured salons...</p>
              </div>
            )}

            {/* Featured Salons Grid */}
            {!featuredLoading && paginatedFeaturedSalons.length > 0 && (
              <div className="salons-grid">
                {paginatedFeaturedSalons.map(salon => (
                  <div key={salon.id} className="salon-card popular-card">
                    <div className="card-image-container">
                      <img
                        src={salon.imageUrl || 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format'}
                        className="card-image"
                        alt={`${salon.name} - Salon`}
                        loading="lazy"
                        onError={(e) => {
                          e.target.src = 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format';
                        }}
                        sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 25vw"
                      />
                      <div className="card-overlay">
                        <div className="popular-badge animate-pulse">
                          <span className="popular-icon">⭐</span>
                          Premium
                        </div>
                      </div>
                    </div>
                    <div className="card-content">
                      <h3 className="salon-name">{salon.name}</h3>
                      <p className="salon-location">
                        {salon.town}
                        {salon.address && `, ${salon.address}`}
                      </p>
                      <div className="salon-meta">
                        <span className="salon-rating">⭐ 4.8</span>
                        {featuredDistances[salon.id] && (
                          <span className="salon-distance">
                            <span className="distance-icon">📍</span>
                            {featuredDistances[salon.id]}km
                          </span>
                        )}
                        <span className="salon-status popular-status">Open</span>
                      </div>
                      <Link to={`/salon/${salon.id}`} className="view-details-btn popular-btn">
                        Details
                        <span className="btn-icon">→</span>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Featured Salons Pagination */}
            <UniversalPagination
              currentPage={featuredCurrentPage}
              totalPages={featuredTotalPages}
              onPageChange={setFeaturedCurrentPage}
              variant="default"
              className="featured-salons-pagination"
            />

            {/* Empty State */}
            {!featuredLoading && featuredSalons.length === 0 && (
              <div className="empty-state">
                <div className="empty-icon">⭐</div>
                <h3>No Featured Salons</h3>
                <p>Featured salons will appear here soon.</p>
              </div>
            )}
          </div>
        </section>
      </div>


    </div>
  );
};

export default Home;
