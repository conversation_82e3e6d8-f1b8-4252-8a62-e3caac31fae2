import React, { memo } from 'react';

// Memoized floating elements component for performance
const LazyFloatingElements = memo(() => {
  const floatingIcons = [
    { id: 1, emoji: '💅', delay: 0, duration: 8 },
    { id: 2, emoji: '✨', delay: 1, duration: 7 },
    { id: 3, emoji: '💄', delay: 2, duration: 9 },
    { id: 4, emoji: '🌟', delay: 3, duration: 6 },
    { id: 5, emoji: '💋', delay: 4, duration: 8.5 },
    { id: 6, emoji: '🎀', delay: 5, duration: 7.5 },
    { id: 7, emoji: '💎', delay: 6, duration: 9.5 },
    { id: 8, emoji: '🔥', delay: 7, duration: 8 },
    // Additional Gen Z icons for more engagement
    { id: 9, emoji: '🦄', delay: 8, duration: 7.2 },
    { id: 10, emoji: '🌈', delay: 9, duration: 8.8 },
    { id: 11, emoji: '💖', delay: 10, duration: 6.5 },
    { id: 12, emoji: '🎉', delay: 11, duration: 9.2 }
  ];

  return (
    <div className="floating-elements" role="presentation" aria-hidden="true">
      {floatingIcons.map((icon) => (
        <div
          key={icon.id}
          className={`float-icon float-icon-${icon.id}`}
          style={{
            animationDelay: `${icon.delay}s`,
            animationDuration: `${icon.duration}s`
          }}
        >
          {icon.emoji}
        </div>
      ))}
    </div>
  );
});

LazyFloatingElements.displayName = 'LazyFloatingElements';

export default LazyFloatingElements;
