import React from 'react';
import './LegalPages.css';

const CookiePolicy = () => {
  return (
    <div className="legal-page">
      <div className="container">
        <div className="legal-content">
          <h1 className="legal-title">
            <i className="bi bi-cookie me-2"></i>
            Cookie Policy
          </h1>
          
          <div className="legal-section">
            <h2>What Are Cookies</h2>
            <p>Cookies are small text files that are placed on your device when you visit our website. They help us provide you with a better experience.</p>
          </div>

          <div className="legal-section">
            <h2>How We Use Cookies</h2>
            <p>We use cookies to:</p>
            <ul>
              <li>Remember your preferences and settings</li>
              <li>Keep you logged in</li>
              <li>Analyze how you use our website</li>
              <li>Improve our services</li>
              <li>Provide personalized content</li>
            </ul>
          </div>

          <div className="legal-section">
            <h2>Types of Cookies We Use</h2>
            
            <h3>Essential Cookies</h3>
            <p>These cookies are necessary for the website to function properly. They enable core functionality such as security and accessibility.</p>
            
            <h3>Performance Cookies</h3>
            <p>These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.</p>
            
            <h3>Functional Cookies</h3>
            <p>These cookies enable the website to provide enhanced functionality and personalization based on your interactions.</p>
            
            <h3>Targeting Cookies</h3>
            <p>These cookies may be set through our site by our advertising partners to build a profile of your interests.</p>
          </div>

          <div className="legal-section">
            <h2>Managing Cookies</h2>
            <p>You can control and manage cookies in various ways:</p>
            <ul>
              <li>Browser settings: Most browsers allow you to refuse cookies</li>
              <li>Third-party tools: Use privacy tools to manage tracking</li>
              <li>Opt-out links: Use provided opt-out mechanisms</li>
            </ul>
          </div>

          <div className="legal-section">
            <h2>Third-Party Cookies</h2>
            <p>We may use third-party services that place cookies on your device. These include analytics providers and payment processors.</p>
          </div>

          <div className="legal-section">
            <h2>Updates to This Policy</h2>
            <p>We may update this Cookie Policy from time to time. We will notify you of any changes by posting the new policy on this page.</p>
          </div>

          <div className="legal-section">
            <h2>Contact Us</h2>
            <p>If you have questions about our use of cookies, please contact <NAME_EMAIL></p>
          </div>

          <div className="legal-footer">
            <p>Last updated: January 2025</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookiePolicy;
