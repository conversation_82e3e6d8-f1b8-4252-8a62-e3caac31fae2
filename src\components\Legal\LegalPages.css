/* Legal Pages - Mobile-First Design */

.legal-page {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(248,225,255,0.9) 0%, rgba(255,230,179,0.9) 100%);
  padding: 20px 0;
}

.legal-content {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.legal-title {
  font-size: 2rem;
  font-weight: 800;
  color: #333;
  margin-bottom: 32px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.legal-title i {
  color: #ffd700;
  font-size: 2.2rem;
}

.legal-section {
  margin-bottom: 32px;
}

.legal-section h2 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

.legal-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #444;
  margin: 20px 0 12px 0;
}

.legal-section p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 16px;
}

.legal-section ul {
  margin: 16px 0;
  padding-left: 24px;
}

.legal-section li {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #555;
  margin-bottom: 8px;
}

.legal-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}

.legal-footer p {
  font-size: 0.9rem;
  color: rgba(0, 0, 0, 0.6);
  font-style: italic;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .legal-page {
    padding: 16px 0 100px 0; /* Extra bottom padding for mobile nav */
  }
  
  .legal-content {
    margin: 0 16px;
    padding: 20px;
    border-radius: 16px;
  }
  
  .legal-title {
    font-size: 1.6rem;
    flex-direction: column;
    gap: 8px;
  }
  
  .legal-title i {
    font-size: 1.8rem;
  }
  
  .legal-section {
    margin-bottom: 24px;
  }
  
  .legal-section h2 {
    font-size: 1.2rem;
  }
  
  .legal-section h3 {
    font-size: 1rem;
  }
  
  .legal-section p {
    font-size: 0.9rem;
  }
  
  .legal-section li {
    font-size: 0.85rem;
  }
  
  .legal-section ul {
    padding-left: 20px;
  }
}

@media (max-width: 480px) {
  .legal-content {
    margin: 0 12px;
    padding: 16px;
  }
  
  .legal-title {
    font-size: 1.4rem;
  }
  
  .legal-section h2 {
    font-size: 1.1rem;
  }
  
  .legal-section p {
    font-size: 0.85rem;
  }
  
  .legal-section li {
    font-size: 0.8rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .legal-content {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .legal-title {
    color: #fff;
  }
  
  .legal-section h2 {
    color: #fff;
  }
  
  .legal-section h3 {
    color: #e0e0e0;
  }
  
  .legal-section p,
  .legal-section li {
    color: #ccc;
  }
  
  .legal-footer p {
    color: rgba(255, 255, 255, 0.6);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .legal-content {
    background: #fff;
    border: 2px solid #000;
  }
  
  .legal-title,
  .legal-section h2,
  .legal-section h3,
  .legal-section p,
  .legal-section li {
    color: #000;
  }
  
  .legal-section h2 {
    border-bottom-color: #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .legal-content {
    transition: none;
  }
}
