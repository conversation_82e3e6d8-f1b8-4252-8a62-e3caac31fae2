import React from 'react';
import './LegalPages.css';

const TermsOfService = () => {
  return (
    <div className="legal-page">
      <div className="container">
        <div className="legal-content">
          <h1 className="legal-title">
            <i className="bi bi-file-earmark-text me-2"></i>
            Terms of Service
          </h1>
          
          <div className="legal-section">
            <h2>Acceptance of Terms</h2>
            <p>By accessing and using SalonGenz, you accept and agree to be bound by the terms and provision of this agreement.</p>
          </div>

          <div className="legal-section">
            <h2>Use License</h2>
            <p>Permission is granted to temporarily use SalonGenz for personal, non-commercial transitory viewing only.</p>
            
            <h3>This license shall not allow you to:</h3>
            <ul>
              <li>Modify or copy the materials</li>
              <li>Use the materials for commercial purposes</li>
              <li>Attempt to reverse engineer any software</li>
              <li>Remove any copyright or proprietary notations</li>
            </ul>
          </div>

          <div className="legal-section">
            <h2>Booking Terms</h2>
            <p>When you book an appointment through our platform:</p>
            <ul>
              <li>You agree to arrive on time for your appointment</li>
              <li>Cancellations must be made at least 24 hours in advance</li>
              <li>No-shows may result in charges</li>
              <li>Salon policies apply to all services</li>
            </ul>
          </div>

          <div className="legal-section">
            <h2>Payment Terms</h2>
            <p>Payment is processed securely through our platform. Refunds are subject to individual salon policies.</p>
          </div>

          <div className="legal-section">
            <h2>User Conduct</h2>
            <p>You agree not to use the service to:</p>
            <ul>
              <li>Violate any laws or regulations</li>
              <li>Harass or abuse other users</li>
              <li>Post inappropriate content</li>
              <li>Interfere with the platform's operation</li>
            </ul>
          </div>

          <div className="legal-section">
            <h2>Limitation of Liability</h2>
            <p>SalonGenz shall not be liable for any damages arising from the use of this service.</p>
          </div>

          <div className="legal-section">
            <h2>Contact Information</h2>
            <p>For questions about these Terms, contact <NAME_EMAIL></p>
          </div>

          <div className="legal-footer">
            <p>Last updated: January 2025</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
