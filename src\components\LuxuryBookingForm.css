/* Luxury Gen Z Booking Form - Mobile First */

/* Container & Background */
.luxury-booking-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%);
  position: relative;
  overflow-x: hidden;
  padding: 1rem;
}

/* Floating Background Effects */
.luxury-booking-container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
  animation: backgroundFloat 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes backgroundFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

/* Main Wrapper */
.luxury-booking-wrapper {
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Back Button */
.luxury-back-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.luxury-back-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.3);
  color: rgba(255, 215, 0, 0.9);
  transform: translateX(-2px);
}

/* Header Section */
.luxury-booking-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.luxury-booking-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(145deg, 
    rgba(255, 215, 0, 0.2) 0%,
    rgba(255, 20, 147, 0.15) 100%);
  border-radius: 50%;
  border: 2px solid rgba(255, 215, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
  box-shadow: 
    0 8px 20px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.luxury-booking-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffd700, #ff1493);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.luxury-booking-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.4;
  max-width: 600px;
  margin: 0 auto;
}

/* Divider */
.luxury-divider {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 215, 0, 0.5) 25%, 
    rgba(255, 20, 147, 0.5) 75%, 
    transparent 100%);
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}

.luxury-divider::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.8), 
    transparent);
  animation: dividerShine 3s ease-in-out infinite;
}

@keyframes dividerShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Progress Bar */
.luxury-progress-container {
  margin-bottom: 2rem;
}

.luxury-progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 1rem;
}

.luxury-progress-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-50%);
  z-index: 1;
}

.luxury-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ff1493);
  transition: width 0.5s ease;
  z-index: 2;
}

.luxury-step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  position: relative;
  z-index: 3;
  transition: all 0.3s ease;
}

.luxury-step.active {
  background: linear-gradient(135deg, #ffd700, #ff1493);
  border-color: rgba(255, 215, 0, 0.5);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
  transform: scale(1.1);
}

.luxury-step.completed {
  background: linear-gradient(135deg, #00ff88, #00cc6a);
  border-color: rgba(0, 255, 136, 0.5);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
}

.luxury-step-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.luxury-step-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.luxury-step-label.active {
  color: #ffd700;
  font-weight: 600;
}

.luxury-step-label.completed {
  color: #00ff88;
  font-weight: 600;
}

/* Form Card */
.luxury-form-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(255, 20, 147, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.luxury-form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 215, 0, 0.6), 
    rgba(255, 20, 147, 0.6), 
    transparent);
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Section Headers */
.luxury-section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.luxury-section-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, #ffd700, #ff1493);
  border-radius: 1px;
}

.luxury-section-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.2) 0%, 
    rgba(255, 20, 147, 0.15) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.luxury-section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Form Groups */
.luxury-form-group {
  margin-bottom: 1.5rem;
}

.luxury-form-label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.luxury-form-label.required::after {
  content: ' *';
  color: #ff1493;
  font-weight: 700;
}

/* Form Inputs */
.luxury-form-input {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.luxury-form-input:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 
    0 0 0 4px rgba(255, 215, 0, 0.1),
    0 4px 12px rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.luxury-form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

.luxury-form-input.error {
  border-color: #ff1493;
  box-shadow: 0 0 0 4px rgba(255, 20, 147, 0.1);
}

.luxury-form-select {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.luxury-form-select:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1);
}

.luxury-form-select option {
  background: rgba(20, 20, 30, 0.95);
  color: white;
  padding: 0.5rem;
}

.luxury-form-textarea {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.luxury-form-textarea:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 
    0 0 0 4px rgba(255, 215, 0, 0.1),
    0 4px 12px rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.luxury-form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

/* Error Messages */
.luxury-error-message {
  color: #ff1493;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 20, 147, 0.1);
  border: 1px solid rgba(255, 20, 147, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Button Container */
.luxury-btn-container {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

/* Primary Button */
.luxury-btn-primary {
  background: linear-gradient(135deg, #ffd700, #ff1493);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.luxury-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.5s ease;
}

.luxury-btn-primary:hover::before {
  left: 100%;
}

.luxury-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
}

.luxury-btn-primary:active {
  transform: translateY(0);
}

.luxury-btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Secondary Button */
.luxury-btn-secondary {
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.luxury-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.4);
  color: rgba(255, 215, 0, 0.9);
  transform: translateY(-1px);
}

/* Loading Spinner */
.luxury-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: luxurySpinnerRotate 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

@keyframes luxurySpinnerRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success Message */
.luxury-success-message {
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
  padding: 1rem;
  color: #00ff88;
  font-weight: 600;
  text-align: center;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(0, 255, 136, 0); }
}

/* Booking Summary Styles */
.booking-summary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
}

.summary-value {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: right;
}

/* Pricing Section Styles */
.pricing-section {
  margin-bottom: 2rem;
}

.pricing-title {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

/* Payment Method Selection Styles */
.payment-method-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.payment-title {
  color: rgba(255, 215, 0, 0.9);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.payment-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.payment-method-option {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  backdrop-filter: blur(5px);
}

.payment-method-option:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.15);
}

.payment-method-option.selected {
  background: rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 
    0 8px 20px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.payment-method-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.payment-method-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.payment-method-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.payment-method-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.95rem;
  flex-grow: 1;
}

.payment-method-option.selected .payment-method-label {
  color: rgba(255, 215, 0, 0.9);
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .luxury-booking-container {
    padding: 0.5rem;
  }

  .luxury-booking-wrapper {
    max-width: 100%;
  }

  .luxury-booking-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .luxury-booking-title {
    font-size: 2rem;
  }

  .luxury-booking-subtitle {
    font-size: 1rem;
  }

  .luxury-form-card {
    padding: 1.5rem;
  }

  .luxury-step {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .luxury-step-label {
    font-size: 0.7rem;
  }

  .luxury-section-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .luxury-section-title {
    font-size: 1.3rem;
  }

  .luxury-form-input,
  .luxury-form-select,
  .luxury-form-textarea {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }

  .luxury-btn-container {
    flex-direction: column;
    gap: 0.75rem;
  }

  .luxury-btn-primary {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  .luxury-btn-secondary {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  /* Booking Summary Mobile */
  .booking-summary {
    padding: 1rem;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .summary-value {
    text-align: left;
  }

  /* Mobile Responsive for Payment Methods */
  .payment-methods-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .payment-method-option {
    padding: 0.75rem;
  }
  
  .payment-method-icon {
    width: 35px;
    height: 35px;
    font-size: 1.25rem;
  }
  
  .payment-method-label {
    font-size: 0.9rem;
  }
  
  .payment-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .luxury-booking-container {
    padding: 0.25rem;
  }

  .luxury-form-card {
    padding: 1rem;
    border-radius: 16px;
  }

  .luxury-booking-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .luxury-booking-title {
    font-size: 1.8rem;
  }

  .luxury-booking-subtitle {
    font-size: 0.9rem;
  }

  .luxury-step {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .luxury-step-label {
    font-size: 0.65rem;
  }

  .luxury-form-group {
    margin-bottom: 1rem;
  }

  .luxury-form-input,
  .luxury-form-select,
  .luxury-form-textarea {
    padding: 0.75rem 0.875rem;
    font-size: 0.9rem;
  }

  .luxury-btn-primary {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
  }

  /* Booking Summary Small Mobile */
  .booking-summary {
    padding: 0.75rem;
  }

  .summary-item {
    padding: 0.5rem 0;
  }

  .summary-label,
  .summary-value {
    font-size: 0.85rem;
  }

  .payment-method-section {
    padding: 1rem;
  }
  
  .payment-method-option {
    padding: 0.5rem;
  }
  
  .payment-method-content {
    gap: 0.5rem;
  }
  
  .payment-method-icon {
    width: 30px;
    height: 30px;
    font-size: 1rem;
  }
  
  .payment-method-label {
    font-size: 0.85rem;
  }
}

@media (max-width: 360px) {
  .luxury-form-card {
    padding: 0.75rem;
  }

  .luxury-booking-title {
    font-size: 1.6rem;
  }

  .luxury-form-input,
  .luxury-form-select,
  .luxury-form-textarea {
    padding: 0.625rem 0.75rem;
    font-size: 0.85rem;
  }

  .luxury-btn-primary {
    padding: 0.625rem 1rem;
    font-size: 0.85rem;
  }
}

@media (min-width: 768px) {
  .luxury-booking-wrapper {
    max-width: 800px;
  }

  .luxury-form-card {
    padding: 2.5rem;
  }

  .luxury-btn-container {
    justify-content: space-between;
  }

  .luxury-btn-primary {
    padding: 1.25rem 2.5rem;
  }
}

@media (min-width: 1024px) {
  .luxury-booking-wrapper {
    max-width: 900px;
  }

  .luxury-form-card {
    padding: 3rem;
  }
}
