/* Messages - Kenya-First Mobile Design */
.messages-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
  padding: 0.75rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.messages-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #667eea;
}

.messages-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.unread-badge {
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.messages-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.messages-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding: 0 0.5rem;
}

.tab {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.tab:hover {
  border-color: #667eea;
  color: #667eea;
}

.tab.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  gap: 1rem;
  border-left: 4px solid transparent;
}

.message-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.message-card.unread {
  border-left-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02), rgba(255, 255, 255, 1));
}

.message-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.message-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
}

.message-time {
  color: #64748b;
  font-size: 0.8rem;
  font-weight: 500;
  flex-shrink: 0;
}

.message-text {
  color: #475569;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-type {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-type.system {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.message-type.admin {
  background: rgba(139, 69, 19, 0.1);
  color: #8b4513;
}

.message-type.booking {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.message-type.social {
  background: rgba(236, 72, 153, 0.1);
  color: #ec4899;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.no-messages {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.no-messages-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-messages h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.no-messages p {
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* Tablet */
@media (min-width: 768px) {
  .messages-container {
    padding: 2rem 1rem;
  }
  
  .messages-header h1 {
    font-size: 2rem;
  }
  
  .messages-tabs {
    padding: 0;
  }
  
  .tab {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
  }
  
  .message-card {
    padding: 2rem;
  }
  
  .message-icon {
    font-size: 1.8rem;
    width: 50px;
    height: 50px;
  }
  
  .message-title {
    font-size: 1.1rem;
  }
  
  .message-text {
    font-size: 1rem;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .messages-container {
    padding: 3rem 2rem;
  }
  
  .messages-tabs {
    justify-content: flex-start;
  }
  
  .message-header {
    align-items: center;
  }
}
