import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Messages.css';

const Messages = () => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const { user, authFetch } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    loadMessages();
  }, [user, navigate]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      // For now, we'll use mock data since the messages API isn't implemented yet
      const mockMessages = [
        {
          id: 1,
          type: 'system',
          title: 'Welcome to SalonGenz!',
          message: 'Thank you for joining our platform. Explore amazing salons and book your next appointment.',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          read: false
        },
        {
          id: 2,
          type: 'admin',
          title: 'Platform Update',
          message: 'We have added new features to enhance your booking experience. Check out the AI-powered salon recommendations!',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          read: false
        },
        {
          id: 3,
          type: 'booking',
          title: 'Booking Confirmation',
          message: 'Your appointment at Glamour Salon has been confirmed for tomorrow at 2:00 PM.',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          read: true
        }
      ];
      
      setMessages(mockMessages);
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = (messageId) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId ? { ...msg, read: true } : msg
      )
    );
  };

  const getFilteredMessages = () => {
    if (activeTab === 'all') return messages;
    return messages.filter(msg => msg.type === activeTab);
  };

  const getMessageIcon = (type) => {
    switch (type) {
      case 'system': return '🔔';
      case 'admin': return '👨‍💼';
      case 'booking': return '📅';
      case 'social': return '💬';
      default: return '📧';
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (loading) {
    return (
      <div className="messages-container">
        <div className="messages-loading">
          <div className="loading-spinner"></div>
          <p>Loading messages...</p>
        </div>
      </div>
    );
  }

  const filteredMessages = getFilteredMessages();
  const unreadCount = messages.filter(msg => !msg.read).length;

  return (
    <div className="messages-container">
      <div className="messages-header">
        <h1>Messages</h1>
        {unreadCount > 0 && (
          <span className="unread-badge">{unreadCount} unread</span>
        )}
      </div>

      <div className="messages-tabs">
        <button 
          className={`tab ${activeTab === 'all' ? 'active' : ''}`}
          onClick={() => setActiveTab('all')}
        >
          All ({messages.length})
        </button>
        <button 
          className={`tab ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => setActiveTab('system')}
        >
          System
        </button>
        <button 
          className={`tab ${activeTab === 'admin' ? 'active' : ''}`}
          onClick={() => setActiveTab('admin')}
        >
          Admin
        </button>
        <button 
          className={`tab ${activeTab === 'booking' ? 'active' : ''}`}
          onClick={() => setActiveTab('booking')}
        >
          Bookings
        </button>
      </div>

      <div className="messages-list">
        {filteredMessages.length === 0 ? (
          <div className="no-messages">
            <div className="no-messages-icon">📭</div>
            <h3>No messages</h3>
            <p>You're all caught up! No messages in this category.</p>
          </div>
        ) : (
          filteredMessages.map((message) => (
            <div 
              key={message.id} 
              className={`message-card ${!message.read ? 'unread' : ''}`}
              onClick={() => markAsRead(message.id)}
            >
              <div className="message-icon">
                {getMessageIcon(message.type)}
              </div>
              <div className="message-content">
                <div className="message-header">
                  <h3 className="message-title">{message.title}</h3>
                  <span className="message-time">{formatTimestamp(message.timestamp)}</span>
                </div>
                <p className="message-text">{message.message}</p>
                <div className="message-meta">
                  <span className={`message-type ${message.type}`}>
                    {message.type}
                  </span>
                  {!message.read && <span className="unread-dot"></span>}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Messages;
