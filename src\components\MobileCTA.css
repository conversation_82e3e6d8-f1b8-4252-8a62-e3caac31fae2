/* Mobile CTA - Gen Z Optimized Call-to-Action */

.mobile-cta {
  padding: 20px 16px;
  margin: 24px 0;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.mobile-cta.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Urgency Banner */
.urgency-banner {
  background: linear-gradient(135deg, #ff4757, #ff6b9d);
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 16px;
  animation: pulse-urgency 2s ease-in-out infinite;
  box-shadow: 0 4px 16px rgba(255, 71, 87, 0.3);
}

.urgency-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: 600;
}

.urgency-icon {
  font-size: 1.2rem;
  margin-right: 8px;
}

.urgency-text {
  flex: 1;
  font-size: 0.9rem;
}

.countdown {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 0.75rem;
}

.countdown-time {
  font-size: 1rem;
  font-weight: 800;
  color: #ffd700;
}

/* Primary CTA Button */
.cta-primary {
  width: 100%;
  position: relative;
  background: linear-gradient(135deg, #ffd700, #ffb347, #ff8e53);
  border: none;
  border-radius: 16px;
  padding: 18px 24px;
  margin-bottom: 16px;
  cursor: pointer;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.4);
  transition: all 0.3s ease;
  min-height: 60px;
}

.cta-primary:active {
  transform: scale(0.98);
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.6);
}

.cta-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
  z-index: 2;
}

.cta-text {
  font-size: 1.1rem;
  font-weight: 800;
  color: #333;
  letter-spacing: 0.5px;
}

.cta-icon {
  font-size: 1.3rem;
  animation: bounce-icon 2s ease-in-out infinite;
}

.cta-shimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

/* Social Proof Section */
.social-proof {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.proof-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 1.1rem;
  font-weight: 800;
  color: #ffd700;
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 2px;
}

.recent-activity {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
}

.activity-avatar {
  font-size: 1rem;
}

.activity-text {
  flex: 1;
}

.activity-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.7rem;
}

/* Secondary CTA */
.cta-secondary {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 14px 20px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
}

.cta-secondary:active {
  transform: scale(0.98);
}

.cta-secondary-text {
  font-size: 0.95rem;
}

.cta-secondary-icon {
  font-size: 1.1rem;
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  justify-content: space-around;
  gap: 8px;
}

.trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.trust-icon {
  font-size: 1.2rem;
  margin-bottom: 4px;
}

.trust-text {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

/* Animations */
@keyframes pulse-urgency {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 24px rgba(255, 71, 87, 0.5);
  }
}

@keyframes bounce-icon {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .mobile-cta {
    padding: 16px 12px;
  }
  
  .cta-primary {
    padding: 16px 20px;
    min-height: 56px;
  }
  
  .cta-text {
    font-size: 1rem;
  }
  
  .urgency-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .proof-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
  }
}

@media (min-width: 768px) {
  .mobile-cta {
    max-width: 500px;
    margin: 32px auto;
    padding: 24px;
  }
  
  .cta-primary {
    padding: 20px 32px;
    min-height: 64px;
  }
  
  .cta-text {
    font-size: 1.2rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .urgency-banner,
  .cta-icon,
  .cta-shimmer {
    animation: none;
  }
  
  .mobile-cta {
    transition: opacity 0.3s ease;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .cta-primary {
    background: #ffd700;
    border: 2px solid #000;
  }
  
  .cta-secondary {
    background: #000;
    border: 2px solid #fff;
  }
  
  .social-proof {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #fff;
  }
}
