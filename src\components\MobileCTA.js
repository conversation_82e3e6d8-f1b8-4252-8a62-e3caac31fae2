import React, { useState, useEffect } from 'react';
import './MobileCTA.css';

const MobileCTA = ({ 
  primaryText = "Book Your Glow Up", 
  secondaryText = "Join 10K+ happy clients",
  urgencyText = "5 spots left today",
  onPrimaryClick,
  onSecondaryClick,
  showUrgency = true,
  showSocialProof = true,
  variant = "primary" // primary, secondary, gradient
}) => {
  const [timeLeft, setTimeLeft] = useState(3600); // 1 hour in seconds
  const [isVisible, setIsVisible] = useState(false);
  const [clickCount, setClickCount] = useState(2847);

  // Countdown timer for urgency
  useEffect(() => {
    if (!showUrgency) return;
    
    const timer = setInterval(() => {
      setTimeLeft(prev => prev > 0 ? prev - 1 : 3600);
    }, 1000);
    
    return () => clearInterval(timer);
  }, [showUrgency]);

  // Simulate live click counter
  useEffect(() => {
    const interval = setInterval(() => {
      setClickCount(prev => prev + Math.floor(Math.random() * 2));
    }, 8000);
    
    return () => clearInterval(interval);
  }, []);

  // Intersection observer for entrance animation
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.querySelector('.mobile-cta');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePrimaryClick = () => {
    // Haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate([100, 50, 100]);
    }
    
    onPrimaryClick?.();
  };

  const handleSecondaryClick = () => {
    // Light haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
    
    onSecondaryClick?.();
  };

  return (
    <div className={`mobile-cta ${variant} ${isVisible ? 'visible' : ''}`}>
      {/* Urgency Banner */}
      {showUrgency && (
        <div className="urgency-banner">
          <div className="urgency-content">
            <span className="urgency-icon">⏰</span>
            <span className="urgency-text">{urgencyText}</span>
            <div className="countdown">
              <span className="countdown-label">Offer ends in:</span>
              <span className="countdown-time">{formatTime(timeLeft)}</span>
            </div>
          </div>
        </div>
      )}

      {/* Main CTA Button */}
      <button 
        className="cta-primary"
        onClick={handlePrimaryClick}
      >
        <div className="cta-content">
          <span className="cta-text">{primaryText}</span>
          <span className="cta-icon">✨</span>
        </div>
        <div className="cta-shimmer"></div>
      </button>

      {/* Social Proof */}
      {showSocialProof && (
        <div className="social-proof">
          <div className="proof-stats">
            <div className="stat-item">
              <span className="stat-number">{clickCount.toLocaleString()}</span>
              <span className="stat-label">bookings made</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">4.9</span>
              <span className="stat-label">★★★★★</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">156</span>
              <span className="stat-label">booked today</span>
            </div>
          </div>
          
          {/* Recent Activity */}
          <div className="recent-activity">
            <div className="activity-item">
              <span className="activity-avatar">👩</span>
              <span className="activity-text">Sarah just booked a makeover</span>
              <span className="activity-time">2m ago</span>
            </div>
          </div>
        </div>
      )}

      {/* Secondary CTA */}
      <button 
        className="cta-secondary"
        onClick={handleSecondaryClick}
      >
        <span className="cta-secondary-text">{secondaryText}</span>
        <span className="cta-secondary-icon">👥</span>
      </button>


    </div>
  );
};

export default MobileCTA;
