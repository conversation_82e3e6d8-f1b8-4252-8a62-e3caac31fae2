
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
  }
  
  .modal-content {
    background-color: rgba(47, 79, 79, 0.9); /* Dark Slate Gray with transparency */
    backdrop-filter: blur(10px); /* Frosted glass effect */
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    animation: slide-down 0.3s ease-out;
    color: var(--text-light); /* Ensure text is light */
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2); /* Lighter border */
    color: var(--accent-color); /* Accent color for title */
  }
  
  .modal-title {
    margin-bottom: 0;
    font-size: 1.25rem;
    color: var(--accent-color); /* Accent color for title */
  }
  
  .modal-body {
    padding: 1rem;
  }

  .modal-body .form-label {
    color: var(--text-light); /* Ensure form labels are light */
  }

  .modal-body .form-control {
    background-color: rgba(255, 255, 255, 0.1); /* Frosted input fields */
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
  }

  .modal-body .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 215, 0, 0.25);
  }

  .modal-body .btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--text-dark);
  }

  .modal-body .btn-primary:hover {
    background-color: #E6B800;
    border-color: #E6B800;
  }

  .modal-body .btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
  }

  .modal-body .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .modal-body .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
  }

  .modal-body .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
  }

  .modal-body p {
    color: var(--text-light); /* Ensure paragraph text is light */
  }

  .btn-close {
    filter: invert(1); /* Invert color for visibility on dark background */
  }
  
  @keyframes slide-down {
    from {
      transform: translateY(-50px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Location Modal Styles */
  .location-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
  }

  .location-modal-content {
    background: linear-gradient(135deg, #ff8c42, #ff6b35);
    border-radius: 12px;
    box-shadow: 0 15px 30px rgba(255, 140, 66, 0.3);
    width: 36%;
    max-width: 180px;
    animation: slide-down 0.3s ease-out;
    overflow: hidden;
  }

  .location-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .location-modal-header h3 {
    margin: 0;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
  }

  .close-location-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .close-location-modal:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }

  .location-modal-body {
    padding: 0.8rem;
  }

  .location-modal-body p {
    color: white;
    margin-bottom: 0.6rem;
    font-size: 0.7rem;
    line-height: 1.3;
    text-align: center;
  }

  .location-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
  }

  .location-input {
    width: 100%;
    padding: 0.4rem 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    font-size: 0.7rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
  }

  .location-input::placeholder {
    color: #666;
    font-style: italic;
  }

  .location-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.8);
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  }

  .location-submit-btn {
    width: 100%;
    padding: 0.4rem 0.6rem;
    background: white;
    color: #ff6b35;
    border: none;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  .location-submit-btn:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
  }

  .location-submit-btn:active {
    transform: translateY(0);
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .location-modal {
      padding: 1rem;
    }
    
    .location-modal-content {
      width: 85%;
      max-width: 280px;
      margin: 0 auto;
    }

    .location-modal-header {
      padding: 0.8rem;
    }

    .location-modal-header h3 {
      font-size: 1rem;
    }

    .location-modal-body {
      padding: 1rem;
    }

    .location-modal-body p {
      font-size: 0.8rem;
      margin-bottom: 0.8rem;
    }

    .location-input {
      padding: 0.6rem 0.8rem;
      font-size: 0.8rem;
    }

    .location-submit-btn {
      padding: 0.6rem 1rem;
      font-size: 0.8rem;
    }
  }

  /* Small Mobile Responsive */
  @media (max-width: 480px) {
    .location-modal-content {
      width: 90%;
      max-width: 250px;
    }

    .location-modal-header h3 {
      font-size: 0.9rem;
    }

    .location-modal-body p {
      font-size: 0.75rem;
    }

    .location-input {
      padding: 0.5rem 0.7rem;
      font-size: 0.75rem;
    }

    .location-submit-btn {
      padding: 0.5rem 0.8rem;
      font-size: 0.75rem;
    }
  }
  