// ESLint globally disabled during development
import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import styles from './ModernPagination.module.css';

const ModernPagination = ({
  currentPage,
  totalPages,
  onPageChange,
  showPageNumbers = true,
  showFirstLast = true,
  maxVisiblePages = 5,
  className = ''
}) => {
  if (totalPages <= 1) return null;

  const getVisiblePages = () => {
    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('ellipsis-start');
      }
    }
    
    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('ellipsis-end');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className={`${styles.pagination} ${className}`}>
      {/* Mobile-first: Simple prev/next with page info */}
      <div className={styles.mobileView}>
        <button
          className={`${styles.navButton} ${currentPage === 1 ? styles.disabled : ''}`}
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          aria-label="Previous page"
        >
          <FaChevronLeft />
        </button>
        
        <div className={styles.pageInfo}>
          <span className={styles.currentPage}>{currentPage}</span>
          <span className={styles.separator}>of</span>
          <span className={styles.totalPages}>{totalPages}</span>
        </div>
        
        <button
          className={`${styles.navButton} ${currentPage === totalPages ? styles.disabled : ''}`}
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          aria-label="Next page"
        >
          <FaChevronRight />
        </button>
      </div>

      {/* Desktop: Full pagination with page numbers */}
      {showPageNumbers && (
        <div className={styles.desktopView}>
          {/* First page button */}
          {showFirstLast && currentPage > 2 && (
            <button
              className={styles.pageButton}
              onClick={() => onPageChange(1)}
              aria-label="Go to first page"
            >
              First
            </button>
          )}
          
          {/* Previous button */}
          <button
            className={`${styles.navButton} ${currentPage === 1 ? styles.disabled : ''}`}
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            <FaChevronLeft />
          </button>
          
          {/* Page numbers */}
          <div className={styles.pageNumbers}>
            {visiblePages.map((page, index) => {
              if (typeof page === 'string') {
                return (
                  <span key={`ellipsis-${index}`} className={styles.ellipsis}>
                    ...
                  </span>
                );
              }
              
              return (
                <button
                  key={page}
                  className={`${styles.pageButton} ${page === currentPage ? styles.active : ''}`}
                  onClick={() => onPageChange(page)}
                  aria-label={`Go to page ${page}`}
                  aria-current={page === currentPage ? 'page' : undefined}
                >
                  {page}
                </button>
              );
            })}
          </div>
          
          {/* Next button */}
          <button
            className={`${styles.navButton} ${currentPage === totalPages ? styles.disabled : ''}`}
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            aria-label="Next page"
          >
            <FaChevronRight />
          </button>
          
          {/* Last page button */}
          {showFirstLast && currentPage < totalPages - 1 && (
            <button
              className={styles.pageButton}
              onClick={() => onPageChange(totalPages)}
              aria-label="Go to last page"
            >
              Last
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ModernPagination;
