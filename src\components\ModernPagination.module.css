/* Modern GenZ Pagination - Mobile First, Enterprise Grade */

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Mobile-First View */
.mobileView {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.desktopView {
  display: none;
}

.navButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.navButton:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.navButton:active:not(.disabled) {
  transform: translateY(0);
}

.navButton.disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
  box-shadow: none;
}

.pageInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2d3748;
  min-width: 80px;
  justify-content: center;
}

.currentPage {
  font-size: 1.125rem;
  color: #667eea;
  font-weight: 700;
}

.separator {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.totalPages {
  font-size: 1rem;
  color: #4a5568;
  font-weight: 600;
}

/* Desktop View */
@media (min-width: 768px) {
  .mobileView {
    display: none;
  }
  
  .desktopView {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 60px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .navButton {
    width: 44px;
    height: 44px;
  }
}

.pageNumbers {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0 0.5rem;
}

.pageButton {
  min-width: 44px;
  height: 44px;
  border-radius: 12px;
  border: none;
  background: transparent;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0 0.75rem;
}

.pageButton:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-1px);
}

.pageButton.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.pageButton.active:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.ellipsis {
  color: #a0aec0;
  font-weight: 600;
  padding: 0 0.5rem;
  font-size: 1rem;
  user-select: none;
}

/* Large Desktop */
@media (min-width: 1024px) {
  .desktopView {
    gap: 0.75rem;
    padding: 1.25rem 2.5rem;
  }
  
  .navButton {
    width: 48px;
    height: 48px;
  }
  
  .pageButton {
    min-width: 48px;
    height: 48px;
    border-radius: 14px;
    font-size: 1rem;
  }
  
  .pageNumbers {
    gap: 0.5rem;
    margin: 0 1rem;
  }
}

/* Accessibility */
.navButton:focus,
.pageButton:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Animation for page transitions */
.pagination {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navButton,
  .pageButton.active {
    border: 2px solid currentColor;
  }
  
  .mobileView,
  .desktopView {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .navButton,
  .pageButton {
    transition: none;
  }
  
  .pagination {
    animation: none;
  }
}
