// ESLint globally disabled during development
import React, { useState, useEffect } from 'react';
import { FaSearch, FaMapMarkerAlt, FaFilter, FaStar, FaHeart } from 'react-icons/fa';
import ModernPagination from './ModernPagination';
import { getLocationWithGPSPriority } from '../services/geolocationService';
import { calculateDistance } from '../services/distanceUtils';
import './SalonFinderProfile.css';

const ModernSalonFinder = ({
  counties = [],
  towns = [],
  salons = [],
  loading = false,
  onSearch,
  onCountyChange,
  onTownChange
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCounty, setSelectedCounty] = useState('');
  const [selectedTown, setSelectedTown] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [userLocation, setUserLocation] = useState(null);
  const [salonDistances, setSalonDistances] = useState({});

  // Mobile-first pagination logic
  const getPerPage = () => (window.innerWidth <= 768 ? 2 : 4);
  const [perPage, setPerPage] = useState(getPerPage());

  // Update perPage on window resize
  useEffect(() => {
    const handleResize = () => {
      const newPerPage = getPerPage();
      if (newPerPage !== perPage) {
        setPerPage(newPerPage);
        setCurrentPage(1); // Reset to first page when changing perPage
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [perPage]);

  // GPS-first location detection for accurate distances
  useEffect(() => {
    const getUserLocationForDistances = async () => {
      try {
        console.log('🎯 Getting user location for salon finder distances...');
        const location = await getLocationWithGPSPriority();

        if (location && location.latitude && location.longitude) {
          setUserLocation(location);
          console.log('✅ User location obtained for salon finder:', location);
          console.log(`📍 Location source: ${location.source || 'unknown'}`);
          console.log(`📍 Coordinates: (${location.latitude}, ${location.longitude})`);
        } else {
          console.warn('❌ Invalid location data received:', location);
        }
      } catch (error) {
        console.log('ℹ️ Could not get user location for salon finder distances:', error.message);
        // Don't show distances if location unavailable
      }
    };

    getUserLocationForDistances();
  }, []);

  // Calculate distances when both userLocation and salons are available
  useEffect(() => {
    if (userLocation && salons.length > 0) {
      console.log('🎯 Calculating distances for salon finder...');
      console.log('User location:', userLocation);
      console.log('Salons count:', salons.length);

      const distances = {};
      salons.forEach(salon => {
        if (salon.latitude && salon.longitude) {
          const distance = calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            salon.latitude,
            salon.longitude
          );
          distances[salon.id] = Math.round(distance * 10) / 10; // Round to 1 decimal
          console.log(`Distance to ${salon.name}: ${distances[salon.id]}km`);
        } else {
          console.warn(`Missing coordinates for salon: ${salon.name}`);
        }
      });

      setSalonDistances(distances);
      console.log('✅ Salon finder distances calculated:', distances);
    } else {
      console.log('⏳ Waiting for user location or salons...');
      console.log('User location available:', !!userLocation);
      console.log('Salons available:', salons.length);
    }
  }, [userLocation, salons]);

  // Pagination logic
  const totalPages = Math.ceil(salons.length / perPage);
  const startIndex = (currentPage - 1) * perPage;
  const paginatedSalons = salons.slice(startIndex, startIndex + perPage);

  const handleSearch = () => {
    setCurrentPage(1);
    onSearch?.(selectedCounty, selectedTown, searchTerm);
  };

  const handleCountySelect = (county) => {
    setSelectedCounty(county);
    setSelectedTown('');
    setCurrentPage(1);
    onCountyChange?.(county);
  };

  const handleTownSelect = (town) => {
    setSelectedTown(town);
    setCurrentPage(1);
    onTownChange?.(town);
  };

  const handleQuickFilter = (county, town = '') => {
    setSelectedCounty(county);
    setSelectedTown(town);
    setSearchTerm('');
    setCurrentPage(1);
    onSearch?.(county, town, '');
  };

  const handleReset = () => {
    setSearchTerm('');
    setSelectedCounty('');
    setSelectedTown('');
    setCurrentPage(1);
    onSearch?.('', '', '');
  };

  return (
    <div className="salon-finder-profile-container">
      <div className="profile-container">
        {/* Optional: Floating Effects */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">✨</span>
          <span className="auth-sparkle">💫</span>
          <span className="auth-sparkle">⭐</span>
        </div>

        {/* Header Section */}
        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">
              <FaSearch />
            </div>
          </div>
          <h1 className="profile-title">Find Your Perfect Salon</h1>
          <p className="profile-subtitle">
            {userLocation
              ? `📍 Showing salons near your location (${userLocation.source === 'gps' ? 'GPS detected' : 'IP detected'}) with real-time distances`
              : 'Discover top-rated salons in your area with advanced filtering and real-time availability'
            }
          </p>
        </div>

        {/* Content Area */}
        <div className="profile-content">

          {/* Search Section */}
          <div className="profile-section">
            <h3 className="section-title">
              <FaSearch /> Search & Filters
            </h3>

            {/* Main Search Bar */}
            <div className="form-group">
              <label className="form-label">Search Salons</label>
              <div className="search-input-group">
                <FaSearch className="search-icon" />
                <input
                  type="text"
                  placeholder="Search salons, services, or locations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input search-input"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center', flexWrap: 'wrap' }}>
                <button
                  className="btn-primary search-button"
                  onClick={handleSearch}
                  disabled={loading}
                >
                  {loading ? 'Searching...' : 'Search'}
                </button>
                <button
                  className="btn-secondary"
                  onClick={handleReset}
                  disabled={loading}
                >
                  Reset
                </button>
              </div>
            </div>

            {/* Filters */}
            <div className="form-group">
              <label className="form-label">
                <FaFilter /> Location Filters
              </label>
              <div className="filter-grid">
                <div className="filter-item">
                  <label className="filter-label">County</label>
                  <select
                    value={selectedCounty}
                    onChange={(e) => handleCountySelect(e.target.value)}
                    className="form-input filter-select"
                  >
                    <option value="">All Counties</option>
                    {counties.map(county => (
                      <option key={county} value={county}>{county}</option>
                    ))}
                  </select>
                </div>

                <div className="filter-item">
                  <label className="filter-label">Town</label>
                  <select
                    value={selectedTown}
                    onChange={(e) => handleTownSelect(e.target.value)}
                    className="form-input filter-select"
                    disabled={!selectedCounty}
                  >
                    <option value="">All Towns</option>
                    {towns.map(town => (
                      <option key={town} value={town}>{town}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Quick Filters */}
            <div className="form-group">
              <label className="form-label">Quick Filters</label>
              <div className="quick-filter-buttons">
                {userLocation && (
                  <button
                    className="btn-primary near-me-btn"
                    onClick={() => {
                      // Sort salons by distance and show nearest first
                      console.log('🎯 Showing nearest salons to user location');
                    }}
                    title="Show salons nearest to your current location"
                  >
                    📍 Near Me
                  </button>
                )}
                <button
                  className="btn-secondary quick-filter-btn"
                  onClick={() => handleQuickFilter('Nairobi County', 'Nairobi CBD')}
                >
                  <FaStar />
                  CBD Salons
                </button>
                <button
                  className="btn-secondary quick-filter-btn"
                  onClick={() => handleQuickFilter('Nairobi County', 'Westlands')}
                >
                  <FaHeart />
                  Westlands
                </button>
                <button
                  className="btn-secondary quick-filter-btn"
                  onClick={() => handleQuickFilter('Nairobi County', 'Karen')}
                >
                  <FaStar />
                  Karen
                </button>
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="profile-section">
            <h3 className="section-title">
              <FaMapMarkerAlt /> Salon Results
            </h3>

            {loading ? (
              <div className="loading-state">
                <div className="loading-spinner"></div>
                <p>Finding perfect salons for you...</p>
              </div>
            ) : salons.length === 0 ? (
              <div className="no-results">
                <FaSearch className="no-results-icon" />
                <h3>No salons found</h3>
                <p>Try adjusting your search criteria or explore different areas</p>
              </div>
            ) : (
              <>
                <div className="results-header">
                  <h4 className="results-title">
                    {salons.length} salon{salons.length !== 1 ? 's' : ''} found
                  </h4>
                  <p className="results-subtitle">
                    Showing {startIndex + 1}-{Math.min(startIndex + perPage, salons.length)} of {salons.length}
                  </p>
                </div>

                <div className="salon-grid">
                  {paginatedSalons.map(salon => (
                    <div key={salon.id} className="salon-card">
                      <div className="salon-image">
                        <img
                          src={salon.imageUrl || salon.image_url || 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format'}
                          alt={salon.name}
                          onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format';
                          }}
                        />
                        <div className="salon-badge">
                          <FaStar />
                          {salon.rating || '4.8'}
                        </div>
                      </div>
                      <div className="salon-info">
                        <h4 className="salon-name">{salon.name}</h4>
                        <p className="salon-location">
                          <FaMapMarkerAlt />
                          {salon.location}
                        </p>
                        <div className="salon-meta">
                          <span className="salon-status">Open Now</span>
                          <span className="salon-distance">
                            📍 {salonDistances[salon.id] !== undefined ? `${salonDistances[salon.id]} km` : (userLocation ? 'Calculating...' : 'Location needed')}
                          </span>
                        </div>
                        <button className="btn-primary view-details-btn">
                          View Details →
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="pagination-wrapper">
                    <ModernPagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={setCurrentPage}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernSalonFinder;
