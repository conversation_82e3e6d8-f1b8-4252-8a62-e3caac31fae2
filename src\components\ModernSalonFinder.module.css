/* Modern Salon Finder - Enterprise Grade, GenZ Appeal, Mobile First */

.salonFinder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
}

.salonFinder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Header */
.header {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
  text-align: center;
}

.headerContent {
  flex: 1;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 2rem;
  font-weight: 800;
  color: #fff;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.titleIcon {
  font-size: 1.75rem;
  color: #ffd700;
}

.subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.resetButton {
  padding: 0.75rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  align-self: center;
}

.resetButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.resetButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Search Interface */
.searchInterface {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 3rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.mainSearch {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.searchInputGroup {
  position: relative;
  flex: 1;
}

.searchIcon {
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 1.125rem;
  z-index: 2;
}

.searchInput {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 3.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 1rem;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
}

.searchInput:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.searchButton {
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16px;
  color: #fff;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.searchButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.searchButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Filter Toggle (Mobile) */
.filterToggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
}

.filterToggle:hover {
  background: #f1f5f9;
  border-color: #cbd5e0;
}

/* Filters */
.filters {
  display: none;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.filters.filtersOpen {
  display: grid;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.filterSelect {
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #fff;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  outline: none;
}

.filterSelect:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filterSelect:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

/* Quick Filters */
.quickFilters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.quickFiltersLabel {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.quickFilterButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.quickFilterBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 20px;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quickFilterBtn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* Near Me button special styling */
.nearMeBtn {
  background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%) !important;
  color: white !important;
  border-color: #ff8c42 !important;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(255, 140, 66, 0.3);
}

.nearMeBtn:hover {
  background: linear-gradient(135deg, #ff6b35 0%, #ff5722 100%) !important;
  border-color: #ff6b35 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 140, 66, 0.4);
}

/* Results */
.results {
  min-height: 400px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #fff;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #fff;
  text-align: center;
}

.noResultsIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.resultsHeader {
  margin-bottom: 2rem;
  text-align: center;
}

.resultsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 0.5rem 0;
}

.resultsSubtitle {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Salon Grid */
.salonGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 3rem;
}

.salonCard {
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.salonCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.salonImage {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.salonImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.salonCard:hover .salonImage img {
  transform: scale(1.05);
}

.salonBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 215, 0, 0.95);
  color: #1a1a1a;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 700;
  backdrop-filter: blur(10px);
}

.salonInfo {
  padding: 1.5rem;
}

.salonName {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
}

.salonLocation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.salonMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.salonStatus {
  padding: 0.25rem 0.75rem;
  background: #10b981;
  color: #fff;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.salonDistance {
  color: #666;
  font-size: 0.875rem;
  font-weight: 600;
}

.viewDetailsBtn {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: #fff;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.viewDetailsBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* Pagination */
.pagination {
  margin-top: 2rem;
}

/* Mobile Optimization - Extra Small Screens */
@media (max-width: 320px) {
  .container {
    padding: 0 0.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .searchInterface {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .searchInput {
    padding: 1rem 1rem 1rem 3rem;
    font-size: 0.9rem;
  }

  .searchButton {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }

  .salonGrid {
    gap: 1rem;
  }

  .salonImage {
    height: 100px;
  }

  .salonInfo {
    padding: 1rem;
  }

  .salonName {
    font-size: 1.1rem;
  }

  .salonLocation {
    font-size: 0.8rem;
  }

  .salonMeta {
    margin-bottom: 0.75rem;
  }

  .salonStatus {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }

  .salonDistance {
    font-size: 0.8rem;
  }

  .viewDetailsBtn {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .salonBadge {
    top: 0.75rem;
    right: 0.75rem;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }
}

/* Tablet Styles */
@media (min-width: 768px) {
  .header {
    flex-direction: row;
    align-items: center;
    text-align: left;
  }
  
  .title {
    justify-content: flex-start;
    font-size: 2.5rem;
  }
  
  .mainSearch {
    flex-direction: row;
    align-items: end;
  }
  
  .filterToggle {
    display: none;
  }
  
  .filters {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  
  .quickFilters {
    flex-direction: row;
    align-items: center;
  }
  
  .salonGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
  
  .title {
    font-size: 3rem;
  }
  
  .searchInterface {
    padding: 3rem;
  }
  
  .filters {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .salonGrid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1200px) {
  .salonGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}
