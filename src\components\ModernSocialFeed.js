// ESLint globally disabled during development
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FaHeart, FaComment, FaFire, FaUserPlus, FaPlus, FaPaperPlane } from 'react-icons/fa';
import { toast, ToastContainer } from 'react-toastify';
import axios from '../api/axiosInstance';
import { useAuth } from '../context/AuthContext';
import FriendSearchModal from './FriendSearchModal';
import ModernPagination from './ModernPagination';
import styles from './ModernSocialFeed.module.css';
import 'react-toastify/dist/ReactToastify.css';

// Modern Activity Card - Mobile First (Memoized for performance)
const ActivityCard = React.memo(({ activity, isFollowing, onFollow, currentUser }) => (
  <div className={styles.card}>
    <div className={styles.cardHeader}>
      <div className={styles.avatar}>
        {activity.friend_name?.charAt(0)?.toUpperCase() || 'U'}
      </div>
      <div className={styles.userInfo}>
        <span className={styles.userName}>{activity.friend_name}</span>
        <span className={styles.timeAgo}>2h ago</span>
      </div>
      {activity.friend_id !== currentUser && (
        <button 
          className={`${styles.followBtn} ${isFollowing ? styles.following : ''}`}
          onClick={() => onFollow(activity.friend_id)}
        >
          {isFollowing ? 'Following' : 'Follow'}
        </button>
      )}
    </div>
    <div className={styles.cardContent}>
      <span className={styles.activityText}>
        Booked <strong>{activity.service_name}</strong> at <strong>{activity.salon_name}</strong>
      </span>
    </div>
  </div>
));

// Comment Input Component
const CommentInput = React.memo(({ postId, onSubmit, placeholder = "Add a comment..." }) => {
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!comment.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSubmit(postId, comment.trim());
      setComment('');
      toast.success('Comment added!');
    } catch (error) {
      toast.error('Failed to add comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={styles.commentForm}>
      <input
        type="text"
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        placeholder={placeholder}
        className={styles.commentInput}
        disabled={isSubmitting}
      />
      <button
        type="submit"
        className={styles.commentSubmit}
        disabled={!comment.trim() || isSubmitting}
      >
        <FaPaperPlane />
      </button>
    </form>
  );
});

// Modern Style Post Card (Memoized for performance)
const StylePostCard = React.memo(({ post, likes, isLiked, onLike, onComment, showComments, comments, onSubmitComment }) => (
  <div className={styles.card}>
    <div className={styles.cardHeader}>
      <div className={styles.avatar}>
        {post.user_name?.charAt(0)?.toUpperCase() || 'U'}
      </div>
      <div className={styles.userInfo}>
        <span className={styles.userName}>{post.user_name}</span>
        <span className={styles.timeAgo}>{new Date(post.created_at).toLocaleDateString()}</span>
      </div>
    </div>
    
    {post.image && (
      <div className={styles.imageContainer}>
        <img 
          src={post.image || 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop&auto=format'} 
          alt="Style post"
          className={styles.postImage}
          loading="lazy"
          onError={(e) => {
            e.target.src = 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop&auto=format';
          }}
        />
      </div>
    )}
    
    <div className={styles.cardContent}>
      <p className={styles.postMessage}>{post.message}</p>
      <p className={styles.serviceInfo}>
        {post.service_name} at <strong>{post.salon_name}</strong>
      </p>
    </div>
    
    <div className={styles.cardActions}>
      <button
        className={`${styles.actionBtn} ${isLiked ? styles.liked : ''}`}
        onClick={() => onLike(post.id)}
      >
        <FaHeart />
        <span>{likes || 0}</span>
      </button>
      <button
        className={styles.actionBtn}
        onClick={() => onComment(post.id)}
      >
        <FaComment />
        <span>{comments?.length || 0}</span>
      </button>
    </div>

    {/* Comments Section */}
    {showComments && (
      <div className={styles.commentsSection}>
        {comments && comments.length > 0 && (
          <div className={styles.commentsList}>
            {comments.slice(0, 3).map(comment => (
              <div key={comment.id} className={styles.comment}>
                <span className={styles.commentUser}>{comment.user_name}</span>
                <span className={styles.commentText}>{comment.text}</span>
              </div>
            ))}
            {comments.length > 3 && (
              <button className={styles.viewMoreComments}>
                View all {comments.length} comments
              </button>
            )}
          </div>
        )}
        <CommentInput
          postId={post.id}
          onSubmit={onSubmitComment}
          placeholder="Add a comment..."
        />
      </div>
    )}
  </div>
));

// Post Creation Modal
const PostModal = React.memo(({ show, onClose, onSubmit }) => {
  const [message, setMessage] = useState('');
  const [image, setImage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!message.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSubmit({ message: message.trim(), image });
      setMessage('');
      setImage('');
      onClose();
      toast.success('Post created!');
    } catch (error) {
      toast.error('Failed to create post');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!show) return null;

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h3>Create Post</h3>
          <button className={styles.closeBtn} onClick={onClose}>&times;</button>
        </div>
        <form onSubmit={handleSubmit} className={styles.postForm}>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="What's on your mind?"
            className={styles.postTextarea}
            rows={4}
            disabled={isSubmitting}
            required
          />
          <input
            type="url"
            value={image}
            onChange={(e) => setImage(e.target.value)}
            placeholder="Image URL (optional)"
            className={styles.postImageInput}
            disabled={isSubmitting}
          />
          <div className={styles.modalActions}>
            <button type="button" onClick={onClose} className={styles.cancelBtn}>
              Cancel
            </button>
            <button
              type="submit"
              className={styles.submitBtn}
              disabled={!message.trim() || isSubmitting}
            >
              {isSubmitting ? 'Posting...' : 'Post'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
});

// Main Modern Social Feed Component
const ModernSocialFeed = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('activity');
  const [activities, setActivities] = useState([]);
  const [stylePosts, setStylePosts] = useState([]);
  const [likes, setLikes] = useState({});
  const [likedPosts, setLikedPosts] = useState({});
  const [comments, setComments] = useState({});
  const [showComments, setShowComments] = useState({});
  const [followStatus, setFollowStatus] = useState({});
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [showPostModal, setShowPostModal] = useState(false);
  const [showFriendModal, setShowFriendModal] = useState(false);

  const ITEMS_PER_PAGE = 5; // Mobile-first pagination

  // Mock data for demo
  const mockActivities = [
    { id: 1, friend_name: 'Sarah Johnson', service_name: 'Hair Styling', salon_name: 'Glamour Salon', friend_id: 2 },
    { id: 2, friend_name: 'Mike Chen', service_name: 'Haircut', salon_name: 'Style Studio', friend_id: 3 },
    { id: 3, friend_name: 'Lisa Rodriguez', service_name: 'Hair Coloring', salon_name: 'Beauty Haven', friend_id: 4 }
  ];

  const mockStylePosts = [
    { 
      id: 1, 
      user_name: 'Emma Wilson', 
      message: 'Love my new summer look!', 
      service_name: 'Hair Styling', 
      salon_name: 'Glamour Salon',
      created_at: '2024-01-15',
      image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=300&fit=crop&auto=format'
    },
    { 
      id: 2, 
      user_name: 'Alex Thompson', 
      message: 'Perfect for the weekend!', 
      service_name: 'Haircut', 
      salon_name: 'Style Studio',
      created_at: '2024-01-14',
      image: null
    }
  ];

  // Fetch real data from API
  const fetchActivities = useCallback(async () => {
    if (!user) {
      setActivities(mockActivities);
      return;
    }

    try {
      const response = await axios.post(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/friends/activity/`, {
        user_id: user.userId || user.id,
        days: 7
      });
      setActivities(response.data.friend_bookings || mockActivities);
    } catch (error) {
      console.log('Using mock activities:', error);
      setActivities(mockActivities);
    }
  }, [user]);

  const fetchStylePosts = useCallback(async () => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/style-posts/`);
      const posts = response.data.results || response.data || mockStylePosts;
      setStylePosts(posts);

      // Initialize likes and comments
      const likesData = {};
      const commentsData = {};
      posts.forEach(post => {
        likesData[post.id] = post.likes_count || 0;
        commentsData[post.id] = post.comments || [];
      });
      setLikes(likesData);
      setComments(commentsData);
    } catch (error) {
      console.log('Using mock posts:', error);
      setStylePosts(mockStylePosts);
      setLikes({ 1: 5, 2: 3 });
    }
  }, []);

  // Fetch follow status for activities
  const fetchFollowStatus = useCallback(async (activities) => {
    if (!user || !activities.length) return;

    const status = {};
    await Promise.all(activities.map(async (activity) => {
      if (!activity.friend_id || activity.friend_id === (user.userId || user.id)) return;

      try {
        const response = await axios.get(`/api/follows/?follower=${user.userId || user.id}&following=${activity.friend_id}`);
        const follows = response.data;
        if (follows.length > 0) {
          status[activity.friend_id] = { isFollowing: true, followId: follows[0].id };
        } else {
          status[activity.friend_id] = { isFollowing: false };
        }
      } catch (error) {
        status[activity.friend_id] = { isFollowing: false };
      }
    }));

    setFollowStatus(status);
  }, [user]);

  useEffect(() => {
    fetchActivities();
    fetchStylePosts();
  }, [fetchActivities, fetchStylePosts]);

  useEffect(() => {
    if (activities.length > 0) {
      fetchFollowStatus(activities);
    }
  }, [activities, fetchFollowStatus]);

  const handleLike = useCallback(async (postId) => {
    if (!user) {
      toast.error('Please log in to like posts');
      return;
    }

    const wasLiked = likedPosts[postId];

    // Optimistic update
    setLikedPosts(prev => ({ ...prev, [postId]: !prev[postId] }));
    setLikes(prev => ({
      ...prev,
      [postId]: (prev[postId] || 0) + (wasLiked ? -1 : 1)
    }));

    try {
      if (wasLiked) {
        await axios.delete(`/api/likes/${postId}/`);
      } else {
        await axios.post('/api/likes/', {
          post_id: postId,
          user_id: user.userId || user.id
        });
      }
    } catch (error) {
      // Revert on error
      setLikedPosts(prev => ({ ...prev, [postId]: wasLiked }));
      setLikes(prev => ({
        ...prev,
        [postId]: (prev[postId] || 0) + (wasLiked ? 1 : -1)
      }));
      toast.error('Failed to update like');
    }
  }, [user, likedPosts]);

  const handleComment = useCallback((postId) => {
    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));
  }, []);

  const handleSubmitComment = useCallback(async (postId, commentText) => {
    if (!user) {
      toast.error('Please log in to comment');
      return;
    }

    try {
      const response = await axios.post('/api/comments/', {
        post_id: postId,
        user_id: user.userId || user.id,
        text: commentText
      });

      // Add comment to local state
      setComments(prev => ({
        ...prev,
        [postId]: [...(prev[postId] || []), response.data]
      }));
    } catch (error) {
      throw new Error('Failed to add comment');
    }
  }, [user]);



  const handleFollow = useCallback(async (friendId) => {
    if (!user) {
      toast.error('Please log in to follow users');
      return;
    }

    const currentStatus = followStatus[friendId];
    const isCurrentlyFollowing = currentStatus?.isFollowing;

    // Optimistic update
    setFollowStatus(prev => ({
      ...prev,
      [friendId]: {
        isFollowing: !isCurrentlyFollowing,
        followId: isCurrentlyFollowing ? null : 'temp'
      }
    }));

    try {
      if (isCurrentlyFollowing) {
        // Unfollow
        await axios.delete(`/api/follows/${currentStatus.followId}/`);
        toast.success('Unfollowed user');
        setFollowStatus(prev => ({
          ...prev,
          [friendId]: { isFollowing: false }
        }));
      } else {
        // Follow
        const response = await axios.post('/api/follows/', {
          follower: user.userId || user.id,
          following: friendId
        });
        toast.success('Now following user!');
        setFollowStatus(prev => ({
          ...prev,
          [friendId]: { isFollowing: true, followId: response.data.id }
        }));
      }

      fetchActivities(); // Refresh activities
    } catch (error) {
      // Revert optimistic update
      setFollowStatus(prev => ({
        ...prev,
        [friendId]: currentStatus || { isFollowing: false }
      }));
      toast.error(isCurrentlyFollowing ? 'Failed to unfollow user' : 'Failed to follow user');
    }
  }, [user, followStatus, fetchActivities]);

  const handleCreatePost = useCallback(async (postData) => {
    if (!user) {
      toast.error('Please log in to create posts');
      return;
    }

    try {
      const response = await axios.post('/api/style-posts/', {
        user_id: user.userId || user.id,
        message: postData.message,
        image: postData.image,
        booking_id: 1 // You might want to select a booking
      });

      // Add new post to local state
      setStylePosts(prev => [response.data, ...prev]);
      fetchStylePosts(); // Refresh to get updated data
    } catch (error) {
      throw new Error('Failed to create post');
    }
  }, [user, fetchStylePosts]);



  // Memoized data with pagination
  const { currentData, totalPages } = useMemo(() => {
    const data = activeTab === 'activity' ? activities : stylePosts;
    const total = Math.ceil(data.length / ITEMS_PER_PAGE);
    const startIndex = (page - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    const current = data.slice(startIndex, endIndex);

    return {
      currentData: current,
      totalPages: total
    };
  }, [activeTab, activities, stylePosts, page]);

  const handlePageChange = useCallback((newPage) => {
    setPage(newPage);
    // Smooth scroll to top of feed
    const feedElement = document.querySelector(`.${styles.feed}`);
    if (feedElement) {
      feedElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  return (
    <>
      <ToastContainer position="top-center" autoClose={3000} />
      <FriendSearchModal
        show={showFriendModal}
        onClose={() => setShowFriendModal(false)}
        onFriendAdded={fetchActivities}
        currentUserId={user?.userId || user?.id || 1}
      />
      <PostModal
        show={showPostModal}
        onClose={() => setShowPostModal(false)}
        onSubmit={handleCreatePost}
      />

      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            <FaFire className={styles.titleIcon} />
            Social Feed
          </h2>
          <div className={styles.headerActions}>
            <div className={styles.tabs}>
              <button
                className={`${styles.tab} ${activeTab === 'activity' ? styles.active : ''}`}
                onClick={() => setActiveTab('activity')}
              >
                Activity
              </button>
              <button
                className={`${styles.tab} ${activeTab === 'posts' ? styles.active : ''}`}
                onClick={() => setActiveTab('posts')}
              >
                Posts
              </button>
            </div>
            <div className={styles.actionButtons}>
              <button
                className={styles.actionButton}
                onClick={() => setShowPostModal(true)}
                title="Create Post"
              >
                <FaPlus />
              </button>
              <button
                className={styles.actionButton}
                onClick={() => setShowFriendModal(true)}
                title="Find Friends"
              >
                <FaUserPlus />
              </button>
            </div>
          </div>
        </div>

      <div className={styles.content}>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
          </div>
        ) : (
          <div className={styles.feed}>
            {activeTab === 'activity' ? (
              activities.map(activity => (
                <ActivityCard
                  key={activity.id}
                  activity={activity}
                  isFollowing={followStatus[activity.friend_id]?.isFollowing || false}
                  onFollow={handleFollow}
                  currentUser={user?.userId || user?.id}
                />
              ))
            ) : (
              stylePosts.map(post => (
                <StylePostCard
                  key={post.id}
                  post={post}
                  likes={likes[post.id]}
                  isLiked={likedPosts[post.id]}
                  onLike={handleLike}
                  onComment={handleComment}
                  showComments={showComments[post.id]}
                  comments={comments[post.id]}
                  onSubmitComment={handleSubmitComment}
                />
              ))
            )}
          </div>
        )}

        {/* Modern Pagination */}
        {totalPages > 1 && (
          <ModernPagination
            currentPage={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className={styles.feedPagination}
          />
        )}
        </div>
      </div>
    </>
  );
};

export default ModernSocialFeed;
