/* Modern Social Feed - Mobile First, Lightweight, Scalable */

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
  background: #fafafa;
  border-radius: 16px;
  min-height: 400px;
}

.header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.headerActions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: stretch;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.actionButton {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.actionButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.titleIcon {
  color: #ff6b6b;
  font-size: 1.25rem;
}

.tabs {
  display: flex;
  background: #f1f3f4;
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
}

.tab {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab.active {
  background: #fff;
  color: #1a1a1a;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content {
  min-height: 300px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f1f3f4;
  border-top: 3px solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.feed {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Card Styles */
.card {
  background: #fff;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid #f1f3f4;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.userInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.userName {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.timeAgo {
  font-size: 0.75rem;
  color: #666;
}

.followBtn {
  padding: 0.5rem 1rem;
  border: 1px solid #e1e5e9;
  background: #fff;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #1a1a1a;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.followBtn:hover {
  background: #f8f9fa;
  border-color: #d1d5db;
}

.followBtn.following {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.cardContent {
  margin-bottom: 0.75rem;
}

.activityText {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.4;
}

.postMessage {
  color: #1a1a1a;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0 0 0.5rem 0;
}

.serviceInfo {
  color: #666;
  font-size: 0.75rem;
  margin: 0;
}

.imageContainer {
  margin: 0.75rem -1rem;
  border-radius: 8px;
  overflow: hidden;
}

.postImage {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.cardActions {
  display: flex;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f3f4;
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionBtn:hover {
  background: #f8f9fa;
  color: #1a1a1a;
}

.actionBtn.liked {
  color: #ff6b6b;
}

.actionBtn.liked:hover {
  background: #fff5f5;
}

/* Load More Button */
.loadMore {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.loadMoreBtn {
  padding: 0.75rem 2rem;
  border: 1px solid #e1e5e9;
  background: #fff;
  border-radius: 24px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
  cursor: pointer;
  transition: all 0.2s ease;
}

.loadMoreBtn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.loadMoreBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .container {
    padding: 1.5rem;
  }
  
  .header {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .tabs {
    width: auto;
    min-width: 200px;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .feed {
    gap: 1.25rem;
  }
  
  .card {
    padding: 1.25rem;
  }
  
  .avatar {
    width: 44px;
    height: 44px;
    font-size: 1rem;
  }
  
  .userName {
    font-size: 1rem;
  }
  
  .postMessage {
    font-size: 1rem;
  }
  
  .activityText {
    font-size: 1rem;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modalContent {
  background: #fff;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0 1.5rem;
}

.modalHeader h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a1a1a;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.closeBtn:hover {
  background: #f1f3f4;
}

.postForm {
  padding: 1.5rem;
}

.postTextarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  outline: none;
  transition: border-color 0.2s ease;
  margin-bottom: 1rem;
}

.postTextarea:focus {
  border-color: #667eea;
}

.postImageInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease;
  margin-bottom: 1.5rem;
}

.postImageInput:focus {
  border-color: #667eea;
}

.modalActions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.cancelBtn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #e1e5e9;
  background: #fff;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelBtn:hover {
  background: #f8f9fa;
  border-color: #d1d5db;
}

.submitBtn {
  padding: 0.75rem 1.5rem;
  border: none;
  background: #667eea;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submitBtn:hover:not(:disabled) {
  background: #5a67d8;
}

.submitBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .container {
    max-width: 800px;
    padding: 2rem;
  }

  .title {
    font-size: 2rem;
  }

  .postImage {
    height: 240px;
  }

  .headerActions {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .actionButtons {
    justify-content: flex-end;
  }
}

/* Feed Pagination */
.feedPagination {
  margin-top: 2rem;
  margin-bottom: 1rem;
}

/* Mobile Optimization - Extra Small Screens */
@media (max-width: 320px) {
  .container {
    padding: 0.75rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .actionButton {
    width: 36px;
    height: 36px;
  }

  .card {
    padding: 0.75rem;
    border-radius: 12px;
  }

  .avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .userName {
    font-size: 0.9rem;
  }

  .timestamp {
    font-size: 0.7rem;
  }

  .postImage {
    height: 120px;
  }

  .cardActions {
    padding: 0.75rem;
    gap: 1rem;
  }

  .actionBtn {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }

  .commentInput {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .commentSubmit {
    width: 32px;
    height: 32px;
  }

  .comment {
    font-size: 0.8rem;
  }

  .commentUser {
    font-size: 0.8rem;
  }

  .commentText {
    font-size: 0.75rem;
  }
}

/* Load More Button */
.loadMore {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.loadMoreBtn {
  padding: 0.75rem 2rem;
  border: 1px solid #e1e5e9;
  background: #fff;
  border-radius: 24px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
  cursor: pointer;
  transition: all 0.2s ease;
}

.loadMoreBtn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.loadMoreBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Comments Section */
.commentsSection {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f3f4;
}

.commentsList {
  margin-bottom: 0.75rem;
}

.comment {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.commentUser {
  font-weight: 600;
  color: #1a1a1a;
  flex-shrink: 0;
}

.commentText {
  color: #374151;
  line-height: 1.4;
}

.viewMoreComments {
  background: none;
  border: none;
  color: #666;
  font-size: 0.75rem;
  cursor: pointer;
  padding: 0;
  text-decoration: underline;
}

.commentForm {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.commentInput {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 20px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.commentInput:focus {
  border-color: #667eea;
}

.commentSubmit {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: #667eea;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.commentSubmit:hover:not(:disabled) {
  background: #5a67d8;
  transform: scale(1.05);
}

.commentSubmit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
