/* Nearby Salons Section - Mobile First Design */

.nearby-salons-section {
  padding: 2rem 1rem;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 142, 83, 0.1));
  border-radius: 20px;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}

.nearby-salons-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.01)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.nearby-salons-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.salon-emoji-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #bdbdbd;
}

.salon-emoji {
  font-size: 40px;
  line-height: 1;
}

@media (min-width: 600px) {
  .salon-emoji-fallback {
    height: 160px;
  }
  .salon-emoji {
    font-size: 48px;
  }
}

/* Section Header */
.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
  font-size: 1rem;
  color: #bdc3c7;
  margin: 0;
  font-weight: 400;
}

.location-button {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  border: none;
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.location-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.location-icon {
  font-size: 1.1rem;
}

/* Radius Selector */
.radius-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.radius-label {
  font-size: 0.9rem;
  color: #ecf0f1;
  font-weight: 500;
  margin: 0;
}

.radius-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.radius-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.radius-select:focus {
  outline: none;
  border-color: #ff6b9d;
  box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

.radius-select option {
  background: #2c3e50;
  color: white;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #ff6b9d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1rem;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.error-text {
  color: #e74c3c;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

.retry-button {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: none;
  border-radius: 20px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Modern Salons Grid - Modest & Unique Cards */
.salons-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

/* Salon Card */
.salon-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
  height: 300px;
}

.salon-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 107, 157, 0.3);
}

.card-image-container {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.salon-card:hover .card-image {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), transparent);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
}

.distance-badge {
  background: rgba(255, 255, 255, 0.9);
  color: #2c3e50;
  padding: 0.5rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
}

.distance-icon {
  font-size: 0.9rem;
}

.card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: calc(100% - 120px);
  min-height: 180px;
}

.salon-name {
  font-size: 1rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.salon-location {
  color: #bdc3c7;
  font-size: 0.8rem;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.salon-location::before {
  content: '📍';
  font-size: 0.8rem;
}

.salon-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: auto;
  margin-top: 0.5rem;
  gap: 0.5rem;
  flex-shrink: 0;
}

.salon-rating {
  color: #f39c12;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.salon-status {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.65rem;
  font-weight: 600;
  flex-shrink: 0;
}

.view-details-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  width: 100%;
  margin-top: 0.75rem;
  flex-shrink: 0;
}

.view-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
  color: white;
  text-decoration: none;
}

.btn-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.view-details-btn:hover .btn-icon {
  transform: translateX(3px);
}

/* Modern Salon Cards - Modest & Unique Design */
.salon-card-modern {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  height: 300px;
  display: flex;
  flex-direction: column;
}

.salon-card-modern:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.15);
  border-color: rgba(255, 107, 157, 0.3);
}

.distance-badge-modern {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(255, 107, 157, 0.9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 2;
  backdrop-filter: blur(10px);
}

.distance-icon {
  font-size: 0.7rem;
}

.distance-text {
  font-size: 0.75rem;
  font-weight: 700;
}

.salon-image-container {
  position: relative;
  height: 100px;
  overflow: hidden;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
}

.salon-image-modern {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.salon-card-modern:hover .salon-image-modern {
  transform: scale(1.05);
}

.salon-image-fallback {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
}

.salon-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.salon-info-modern {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.salon-name-modern {
  font-size: 0.9rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-transform: none;
}

.salon-location-modern {
  font-size: 0.75rem;
  color: #bdc3c7;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.salon-meta-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: auto;
  margin-top: 0.5rem;
  gap: 0.5rem;
}

.salon-rating-modern {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  flex-shrink: 0;
}

.rating-stars {
  font-size: 0.65rem;
}

.rating-value {
  font-weight: 600;
  color: #f39c12;
}

.salon-status-modern {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.6rem;
  font-weight: 600;
  flex-shrink: 0;
}

.salon-cta-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  text-align: center;
  margin-top: auto;
  flex-shrink: 0;
}

.salon-cta-link:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
  color: white;
  text-decoration: none;
}

.cta-arrow {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.salon-cta-link:hover .cta-arrow {
  transform: translateX(2px);
}

/* Carousel Controls */
.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  font-size: 1.2rem;
}

.carousel-btn:hover {
  background: rgba(255, 107, 157, 0.2);
  border-color: #ff6b9d;
  transform: scale(1.1);
}

.carousel-indicators {
  display: flex;
  gap: 0.5rem;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #ff6b9d;
  transform: scale(1.2);
}

/* Show More Button */
.show-more-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.show-more-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
  border-radius: 25px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.show-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
}

.show-more-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.show-more-btn:hover .show-more-icon {
  transform: translateY(2px);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
}

.empty-state h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.empty-state p {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

.expand-radius-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border: none;
  border-radius: 20px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.expand-radius-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
}

/* Auth Prompt */
.auth-prompt {
  display: flex;
  justify-content: center;
  padding: 2rem 1rem;
}

.auth-prompt-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 400px;
}

.auth-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.auth-prompt-content h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.auth-prompt-content p {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.auth-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.location-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.location-btn.primary {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.location-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.auth-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover, .btn-secondary:hover {
  transform: translateY(-2px);
  text-decoration: none;
  color: white;
}

.btn-primary:hover {
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

/* Footer Note */
.section-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-note {
  color: #95a5a6;
  font-size: 0.8rem;
  margin: 0;
  font-style: italic;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .nearby-salons-section {
    padding: 3rem 2rem;
  }

  .section-header {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .radius-selector {
    flex-direction: row;
    justify-content: center;
  }

  .salons-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 0;
  }

  .carousel-controls {
    display: none;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .nearby-salons-section {
    padding: 4rem 3rem;
  }

  .section-title {
    font-size: 3rem;
  }

  .salons-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .card-image-container {
    height: 220px;
  }
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .nearby-salons-container {
    max-width: 1400px;
  }

  .salons-grid {
    gap: 2rem;
  }
}

/* Mobile Optimization - Extra Small Screens */
@media (max-width: 320px) {
  .nearby-salons-section {
    padding: 1.5rem 0.5rem;
  }

  .salons-grid {
    gap: 0.75rem;
    padding: 0 0.25rem;
  }

  .salon-image-container {
    height: 80px;
  }

  .salon-card-modern {
    border-radius: 12px;
  }

  .salon-info-modern {
    padding: 0.75rem;
  }

  .salon-name-modern {
    font-size: 0.9rem;
    line-height: 1.2;
  }

  .salon-location-modern {
    font-size: 0.75rem;
  }

  .salon-meta-modern {
    margin-bottom: 0.75rem;
  }

  .salon-rating-modern,
  .salon-status-modern {
    font-size: 0.7rem;
  }

  .salon-status-modern {
    padding: 0.15rem 0.5rem;
  }

  .salon-cta-link {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .distance-badge-modern {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.2rem 0.6rem;
    font-size: 0.7rem;
  }
}

/* Landscape Mode */
@media (max-height: 500px) and (orientation: landscape) {
  .nearby-salons-section {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .card-image-container {
    height: 150px;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .salon-card {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .salon-card,
  .view-details-btn,
  .location-button,
  .show-more-btn,
  .retry-button,
  .expand-radius-btn {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }

  .card-image {
    transition: none;
  }

  .btn-icon {
    transition: none;
  }
}

/* Focus States for Accessibility */
.salon-card:focus-within {
  outline: 3px solid rgba(255, 107, 157, 0.5);
  outline-offset: 2px;
}

.view-details-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.location-button:focus,
.show-more-btn:focus,
.retry-button:focus,
.expand-radius-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .salon-card:hover {
    transform: none;
  }

  .view-details-btn:hover,
  .location-button:hover,
  .show-more-btn:hover,
  .retry-button:hover,
  .expand-radius-btn:hover {
    transform: none;
  }

  .carousel-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .indicator {
    min-width: 12px;
    min-height: 12px;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .salons-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 0 0.25rem;
  }

  .salon-card-modern {
    border-radius: 16px !important;
    height: 280px !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .salon-image-container {
    height: 100px !important;
  }

  .salon-info-modern {
    padding: 0.75rem !important;
    min-height: 160px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .salon-name-modern {
    font-size: 0.8rem !important;
    margin-bottom: 0.4rem !important;
    line-height: 1.2 !important;
    text-transform: none !important;
  }

  .salon-location-modern {
    font-size: 0.7rem !important;
    margin-bottom: 0.4rem !important;
  }

  .salon-meta-modern {
    margin-top: 0.4rem !important;
    gap: 0.4rem !important;
  }

  .salon-rating-modern {
    font-size: 0.65rem !important;
  }

  .salon-status-modern {
    font-size: 0.55rem !important;
    padding: 0.15rem 0.4rem !important;
  }

  .salon-cta-link {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.7rem !important;
    margin-top: auto !important;
  }
}

@media (max-width: 480px) {
  .salon-card-modern {
    height: 260px !important;
  }

  .salon-image-container {
    height: 90px !important;
  }

  .salon-info-modern {
    padding: 0.6rem !important;
    min-height: 150px !important;
  }

  .salon-name-modern {
    font-size: 0.75rem !important;
    margin-bottom: 0.3rem !important;
  }

  .salon-location-modern {
    font-size: 0.65rem !important;
    margin-bottom: 0.3rem !important;
  }

  .salon-meta-modern {
    margin-top: 0.3rem !important;
    gap: 0.3rem !important;
  }

  .salon-rating-modern {
    font-size: 0.6rem !important;
  }

  .salon-status-modern {
    font-size: 0.5rem !important;
    padding: 0.1rem 0.3rem !important;
  }

  .salon-cta-link {
    padding: 0.5rem 0.7rem !important;
    font-size: 0.65rem !important;
    margin-top: 0.4rem !important;
  }
}

/* Sleek Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
}

.pagination-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 1rem;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 107, 157, 0.1);
  border-color: rgba(255, 107, 157, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.2);
}

.pagination-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  color: #bdc3c7;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0 1rem;
  text-align: center;
  min-width: 120px;
}

.page-numbers {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.page-number {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 0.85rem;
  font-weight: 500;
}

.page-number:hover {
  background: rgba(255, 107, 157, 0.1);
  border-color: rgba(255, 107, 157, 0.3);
  transform: translateY(-1px);
}

.page-number.active {
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  border-color: transparent;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.page-ellipsis {
  color: #bdc3c7;
  font-size: 0.9rem;
  padding: 0 0.5rem;
}

/* Mobile Pagination */
@media (max-width: 768px) {
  .pagination-container {
    gap: 0.75rem;
    margin: 1.5rem 0;
    padding: 0.75rem;
  }

  .pagination-btn {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }

  .pagination-info {
    font-size: 0.8rem;
    min-width: 100px;
    padding: 0 0.5rem;
  }

  .page-number {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .pagination-container {
    gap: 0.5rem !important;
    margin: 1rem 0 !important;
    padding: 0.5rem 0.5rem 0.5rem 0.5rem !important;
  }
  .pagination-btn {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.8rem !important;
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }

  .pagination-info {
    font-size: 0.75rem;
    min-width: 80px;
  }

  .page-number {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .page-numbers {
    gap: 0.25rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .nearby-salons-section {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.15), rgba(255, 142, 83, 0.15));
  }
}