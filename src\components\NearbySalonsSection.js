// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { getCurrentLocation, updateUserLocation, getNearbySalons, getLocationWithGPSPriority, getCachedGPSLocation } from '../services/geolocationService';
import './NearbySalonsSection.css';
import UniversalPagination from './UniversalPagination';

const NearbySalonsSection = () => {
  const { user, authFetch, token } = useAuth();
  const [nearbySalons, setNearbySalons] = useState([]);
  const [showManualLocation, setShowManualLocation] = useState(false);
  const [didAttemptLocation, setDidAttemptLocation] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [locationPermission, setLocationPermission] = useState(false);
  const [radius, setRadius] = useState(5);
  const [currentPage, setCurrentPage] = useState(1);
  const getPerPage = () => (window.innerWidth <= 480 ? 2 : 4);
  const [perPage, setPerPage] = useState(getPerPage());

  useEffect(() => {
    const handleResize = () => setPerPage(getPerPage());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalPages = Math.ceil(nearbySalons.length / perPage);
  const paginatedSalons = nearbySalons.slice((currentPage - 1) * perPage, currentPage * perPage);
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i += 1) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i += 1) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  const fetchNearbySalons = useCallback(async () => {
    if (!user) return;
    setDidAttemptLocation(true);
    setLoading(true);
    setError(null);

    console.log(`🎯 Fetching salons within ${radius}km radius...`);

    try {
      // SURGICAL FIX: Enhanced location detection with caching
      let locationUsed = null;

      try {
        console.log('🎯 Starting GPS-FIRST location detection...');
        const location = await getLocationWithGPSPriority();
        locationUsed = {
          latitude: location.latitude,
          longitude: location.longitude,
          county: 'Auto-detected',
          town: 'GPS Location',
          source: location.source || 'gps',
          accuracy: location.accuracy,
          timestamp: location.timestamp
        };
        console.log('✅ Location obtained via', location.source + ':', locationUsed);
        if (location.source === 'gps') {
          console.log('📱 GPS accuracy:', location.accuracy + ' meters');
        }
        setLocationPermission(true);
      } catch (gpsErr) {
        console.log('🔄 GPS failed, trying IP-based location...');

        // Enhanced IP fallback with multiple services
        const { getUserLocation } = await import('../services/geolocationService');
        const ipLocation = await getUserLocation((city) => {
          setShowManualLocation(false);
          console.log('Manual location entered:', city);
        });

        if (ipLocation && ipLocation.latitude && ipLocation.longitude) {
          locationUsed = {
            latitude: ipLocation.latitude,
            longitude: ipLocation.longitude,
            county: ipLocation.region || 'Nairobi County',
            town: ipLocation.city || 'Nairobi CBD',
            source: ipLocation.source || 'ip',
            accuracy: 'estimated'
          };
          console.log('✅ IP location obtained:', locationUsed);
          setLocationPermission(true);
        } else {
          throw new Error('All location methods failed');
        }
      }

      // Update user location in backend
      if (locationUsed) {
        await updateUserLocation(authFetch, locationUsed);
        console.log(`📍 User location updated: ${locationUsed.source} (${locationUsed.latitude}, ${locationUsed.longitude})`);
      }

      // Fetch nearby salons with enhanced backend logic
      const data = await getNearbySalons(authFetch, radius);
      const salons = data.nearby_salons || [];

      console.log(`🏪 Found ${salons.length} salons within ${radius}km:`);
      salons.forEach(salon => {
        console.log(`  • ${salon.name}: ${salon.distance_km}km away`);
        if (salon.user_location && salon.salon_location) {
          console.log(`    User: (${salon.user_location.lat}, ${salon.user_location.lon})`);
          console.log(`    Salon: (${salon.salon_location.lat}, ${salon.salon_location.lon})`);
        }
      });

      setNearbySalons(salons);

    } catch (err) {
      console.error('❌ All location methods failed:', err);
      setError('We couldn\'t detect your location. Please enable GPS/location services for accurate results, or enter your city manually.');
      setLocationPermission(false);
      setNearbySalons([]);
      if (didAttemptLocation) {
        setShowManualLocation(true);
      }
    } finally {
      setLoading(false);
    }
  }, [user, radius, authFetch, didAttemptLocation]);

  useEffect(() => {
    if (user) {
      fetchNearbySalons();
    }
  }, [user, fetchNearbySalons]);

  // Hide modal when location is successfully obtained
  useEffect(() => {
    if (locationPermission && showManualLocation) {
      setShowManualLocation(false);
      import('../services/geolocationService').then(m => m.hideLocationModalIfVisible());
    }
  }, [locationPermission, showManualLocation]);

  const requestLocationPermission = () => {
    setDidAttemptLocation(true);
    fetchNearbySalons();
  };

  const handleRadiusChange = (newRadius) => {
    setRadius(newRadius);
  };

  if (!user) {
    return (
      <section className="nearby-salons-section">
        <div className="nearby-salons-container">
          <div className="section-header">
            <h2 className="section-title">Salons Near You</h2>
            <p className="section-subtitle">Discover amazing salons in your area</p>
          </div>
          <div className="auth-prompt">
            <div className="auth-prompt-content">
              <div className="auth-icon">📍</div>
              <h3>Enable Location Services</h3>
              <p>Please log in and enable location services to find salons near you</p>
              <div className="auth-actions">
                <button
                  className="location-btn primary"
                  onClick={() => window.location.assign('/login')}
                >
                  Login to Enable Location
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="nearby-salons-section">
      <div className="nearby-salons-container">
        {/* Section Header */}
        <div className="section-header">
          <div className="header-content">
            <h2 className="section-title">Salons Near You</h2>
            <p className="section-subtitle">Discover amazing salons in your area</p>

          </div>
          {!locationPermission && (
            <button
              className="location-button"
              onClick={requestLocationPermission}
              type="button"
            >
              <span className="location-icon">📍</span>
              Enable Location
            </button>
          )}
        </div>

        {/* Radius Selector */}
        <div className="radius-selector">
          <label htmlFor="radiusSelector" className="radius-label">
            Show salons within
          </label>
          <div className="radius-controls">
            <select
              id="radiusSelector"
              className="radius-select"
              value={radius}
              onChange={(e) => handleRadiusChange(Number(e.target.value))}
            >
              <option value={2}>2 km</option>
              <option value={5}>5 km</option>
              <option value={10}>10 km</option>
              <option value={20}>20 km</option>
            </select>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="loading-state">
            <div className="loading-spinner" />
            <p className="loading-text">Finding salons near you...</p>
          </div>
        )}

        {/* Salons Grid */}
        {!loading && (
          <div className="salons-grid">
            {paginatedSalons.map((salon) => (
              <div key={salon.id} className="salon-card-modern">
                {/* Distance Badge */}
                <div className="distance-badge-modern">
                  <span className="distance-icon">📍</span>
                  <span className="distance-text">
                    {salon.distance_km ? `${salon.distance_km}km` : 'N/A'}
                  </span>
                </div>

                {/* Salon Image */}
                <div className="salon-image-container">
                  {salon.image_url && salon.image_url.trim() !== '' ? (
                    <img
                      src={salon.image_url}
                      alt={salon.name || 'Salon'}
                      className="salon-image-modern"
                      loading="lazy"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : salon.imageUrl && salon.imageUrl.trim() !== '' ? (
                    <img
                      src={salon.imageUrl}
                      alt={salon.name || 'Salon'}
                      className="salon-image-modern"
                      loading="lazy"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div className="salon-image-fallback">
                    <span className="salon-icon">💇‍♀️</span>
                  </div>
                </div>

                {/* Salon Info */}
                <div className="salon-info-modern">
                  <h3 className="salon-name-modern">
                    {salon.name || 'Unnamed Salon'}
                  </h3>
                  <p className="salon-location-modern">
                    {salon.town || 'Location not specified'}
                  </p>

                  {/* Rating and Status */}
                  <div className="salon-meta-modern">
                    <div className="salon-rating-modern">
                      <span className="rating-stars">⭐</span>
                      <span className="rating-value">4.8</span>
                    </div>
                    <div className="salon-status-modern">Open</div>
                  </div>

                  {/* CTA Link */}
                  <Link
                    to={`/salon/${salon.id}`}
                    className="salon-cta-link"
                  >
                    View Details
                    <span className="cta-arrow">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Universal Pagination */}
        <UniversalPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          variant="default"
          className="nearby-salons-pagination"
        />

        {/* Empty State */}
        {!loading && !error && nearbySalons.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">🏪</div>
            <h3>No Salons Found</h3>
            <p>
              No salons found within
              {radius}
              km of your location.
            </p>
            <button
              className="expand-radius-btn"
              type="button"
              onClick={() => handleRadiusChange(radius + 5)}
            >
              Expand Search Area
            </button>
          </div>
        )}

        {/* Footer Note */}
        <div className="section-footer">
          <p className="footer-note">
            Distances are calculated as straight-line from your current location
          </p>
        </div>
      </div>
    </section>
  );
};

export default NearbySalonsSection;
