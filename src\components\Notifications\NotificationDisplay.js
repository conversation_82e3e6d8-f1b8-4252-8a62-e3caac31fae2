import React from 'react';
import { useNotification } from '../../context/NotificationContext';

const NotificationDisplay = () => {
  const { notification } = useNotification();

  if (!notification) {
    return null;
  }

  // Use a pastel/gradient background and rounded corners
  const alertClass = 'alert fixed-top mx-auto mt-3 w-50 border-0 shadow';
  const style = {
    background: 'linear-gradient(90deg, #f8fafc 0%, #e0c3fc 100%)',
    color: '#7c3aed',
    borderRadius: 16,
    fontWeight: 600,
    zIndex: 1050,
    fontSize: 18,
    textAlign: 'center',
  };

  return (
    <div className={alertClass} role="alert" style={style}>
      {notification.message}
    </div>
  );
};

export default NotificationDisplay;
