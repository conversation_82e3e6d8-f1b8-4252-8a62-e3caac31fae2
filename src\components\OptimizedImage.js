import React, { useState, useRef, useEffect } from 'react';
import { createImageObserver, getOptimizedImageUrl, isMobileDevice } from '../utils/performanceUtils';

const OptimizedImage = ({ 
  src, 
  alt, 
  className = '', 
  style = {},
  width,
  height,
  quality = 80,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTJMMTIgOEg0TDggMTJaIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykiLz4KPC9zdmc+Cjwvc3ZnPgo=',
  ...props 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    // Create intersection observer for lazy loading
    observerRef.current = createImageObserver((target) => {
      setIsInView(true);
      if (observerRef.current) {
        observerRef.current.unobserve(target);
      }
    });

    if (observerRef.current) {
      observerRef.current.observe(img);
    } else {
      // Fallback for browsers without IntersectionObserver
      setIsInView(true);
    }

    return () => {
      if (observerRef.current && img) {
        observerRef.current.unobserve(img);
      }
    };
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  // Get optimized image URL based on device and requirements
  const optimizedSrc = getOptimizedImageUrl(src, {
    width: width || (isMobileDevice() ? 400 : 800),
    height: height || (isMobileDevice() ? 300 : 600),
    quality
  });

  return (
    <div 
      ref={imgRef}
      className={`optimized-image-container ${className}`}
      style={{
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        ...style
      }}
    >
      {/* Placeholder/Loading state */}
      {!isLoaded && (
        <img
          src={placeholder}
          alt=""
          className="optimized-image-placeholder"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            filter: 'blur(5px)',
            transition: 'opacity 0.3s ease'
          }}
        />
      )}
      
      {/* Actual image */}
      {isInView && (
        <img
          src={hasError ? placeholder : optimizedSrc}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`optimized-image ${isLoaded ? 'loaded' : 'loading'}`}
          style={{
            position: isLoaded ? 'static' : 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            opacity: isLoaded ? 1 : 0,
            transition: 'opacity 0.3s ease',
            ...props.style
          }}
          loading="lazy"
          decoding="async"
          {...props}
        />
      )}
      
      {/* Loading spinner for mobile */}
      {!isLoaded && isInView && isMobileDevice() && (
        <div 
          className="optimized-image-spinner"
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '24px',
            height: '24px',
            border: '2px solid rgba(255, 215, 0, 0.3)',
            borderTop: '2px solid #FFD700',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
