import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaExclamationTriangle, FaRedo, FaHome, FaPhone } from 'react-icons/fa';
import './PaymentFailed.css';

const PaymentFailed = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [bookingData, setBookingData] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    // Get booking data from location state
    const data = location.state?.bookingData;
    if (data) {
      setBookingData(data);
    }
  }, [location.state]);

  const handleRetryPayment = async () => {
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    try {
      // Simulate retry delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate back to payment page
      navigate(`/payment/${bookingData?.paymentMethod || 'mpesa'}`, {
        state: { 
          bookingData,
          isRetry: true,
          retryCount: retryCount + 1
        }
      });
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleTryDifferentMethod = () => {
    navigate('/booking-confirm', {
      state: { bookingData }
    });
  };

  const handleContactSupport = () => {
    // Open email client or contact form
    window.open('mailto:<EMAIL>?subject=Payment Issue - Booking ID: ' + (bookingData?.id || 'Unknown'), '_blank');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  if (!bookingData) {
    return (
      <div className="payment-failed-container">
        <div className="payment-failed-card">
          <div className="error-message">
            <h3>Payment Information Not Found</h3>
            <p>We couldn't find your payment details. Please try booking again.</p>
            <button className="btn-primary" onClick={handleGoHome}>
              Return to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="payment-failed-container">
      <div className="payment-failed-card">
        {/* Header */}
        <div className="failed-header">
          <div className="failed-icon">
            <FaExclamationTriangle />
          </div>
          <h1 className="failed-title">Payment Failed</h1>
          <p className="failed-subtitle">
            We couldn't process your payment. Don't worry, your booking is still reserved.
          </p>
        </div>

        {/* Booking Details */}
        <div className="booking-details">
          <h3>📋 Booking Details</h3>
          <div className="details-grid">
            <div className="detail-item">
              <span className="detail-label">Booking ID:</span>
              <span className="detail-value">{bookingData.id}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Service:</span>
              <span className="detail-value">{bookingData.service?.name || 'Selected Service'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Date:</span>
              <span className="detail-value">{bookingData.date}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Time:</span>
              <span className="detail-value">{bookingData.time}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Amount:</span>
              <span className="detail-value">KSh {bookingData.total_amount || 'N/A'}</span>
            </div>
          </div>
        </div>

        {/* Common Issues */}
        <div className="common-issues">
          <h3>🔍 Common Issues</h3>
          <div className="issues-list">
            <div className="issue-item">
              <span className="issue-icon">💳</span>
              <div className="issue-content">
                <h4>Insufficient Funds</h4>
                <p>Check your account balance and try again</p>
              </div>
            </div>
            <div className="issue-item">
              <span className="issue-icon">📱</span>
              <div className="issue-content">
                <h4>Network Issues</h4>
                <p>Ensure you have a stable internet connection</p>
              </div>
            </div>
            <div className="issue-item">
              <span className="issue-icon">🔒</span>
              <div className="issue-content">
                <h4>Security Block</h4>
                <p>Your bank may have blocked the transaction</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons">
          <button 
            className="btn-retry" 
            onClick={handleRetryPayment}
            disabled={isRetrying || retryCount >= 3}
          >
            {isRetrying ? (
              <>
                <div className="loading-spinner"></div>
                Retrying...
              </>
            ) : (
              <>
                <FaRedo />
                Try Again
              </>
            )}
          </button>
          
          <button className="btn-different-method" onClick={handleTryDifferentMethod}>
            Try Different Payment Method
          </button>
          
          <button className="btn-support" onClick={handleContactSupport}>
            <FaPhone />
            Contact Support
          </button>
          
          <button className="btn-home" onClick={handleGoHome}>
            <FaHome />
            Return to Home
          </button>
        </div>

        {/* Support Information */}
        <div className="support-info">
          <h4>Need Help?</h4>
          <p>
            Our support team is available 24/7 to help you resolve any payment issues.
            You can also call us at <strong>+254 700 000 000</strong> for immediate assistance.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailed; 