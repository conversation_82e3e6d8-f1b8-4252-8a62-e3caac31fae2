import React from 'react';

const BankTransferPayment = () => (
  <div className="container mt-5">
    <div className="glam-card p-4">
      <h2 className="card-title text-center mb-4 text-light">Bank Transfer Payment</h2>
      <div className="card-body text-center text-light">
        <p className="lead">Please use the following bank details to complete your transfer:</p>
        <div className="alert alert-info mt-3 bg-dark text-light border-light" role="alert">
          <h4 className="alert-heading text-light">Bank Details:</h4>
          <p>
            <strong>Bank Name:</strong>
            {' '}
            Example Bank PLC
          </p>
          <p>
            <strong>Account Name:</strong>
            {' '}
            SalonGenz Ltd
          </p>
          <p>
            <strong>Account Number:</strong>
            {' '}
            **********
          </p>
          <p>
            <strong>SWIFT/BIC:</strong>
            {' '}
            EXABICXX
          </p>
          <p>
            <strong>Reference:</strong>
            {' '}
            Your Booking ID (Placeholder)
          </p>
        </div>
        <p className="text-light mt-4">Please allow 1-3 business days for the transfer to be processed and your booking confirmed.</p>
        {/* Bank transfer details will go here */}
      </div>
    </div>
  </div>
);

export default BankTransferPayment;
