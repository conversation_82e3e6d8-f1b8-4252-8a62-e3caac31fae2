import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const MpesaPayment = () => {
  const [paymentData, setPaymentData] = useState({
    amount: 1000,
    phoneNumber: '254',
    email: '<EMAIL>',
    reference: '',
    description: 'Salon Service Payment'
  });
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState('');
  const [transactionRef, setTransactionRef] = useState('');
  
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const amount = urlParams.get('amount');
    const days = urlParams.get('days');
    
    if (amount) {
      setPaymentData(prev => ({
        ...prev,
        amount: parseFloat(amount),
        description: days ? `Salon Subscription - ${days} days` : 'Salon Service Payment',
        reference: `SALON_${Date.now()}`
      }));
    }
  }, [location]);

  const initiatePaystackMpesaPayment = async () => {
    setLoading(true);
    setPaymentStatus('Initiating payment...');
    
    try {
      const paystackPublicKey = process.env.REACT_APP_PAYSTACK_PUBLIC_KEY || 'pk_live_22d10798ff283975b275fc96824e6325ca901a0c';
      
      if (typeof window.PaystackPop === 'undefined') {
        throw new Error('Paystack script not loaded');
      }
      
      const handler = window.PaystackPop.setup({
        key: paystackPublicKey,
        email: paymentData.email,
        amount: paymentData.amount * 100,
        currency: 'KES',
        ref: paymentData.reference,
        metadata: {
          phone: paymentData.phoneNumber,
          description: paymentData.description
        },
        channels: ['mobile_money'],
        callback: function(response) {
          setPaymentStatus('Payment successful!');
          setTransactionRef(response.reference);
          console.log('Payment successful:', response);
          
          setTimeout(() => {
            navigate('/booking-success', { 
              state: { 
                paymentReference: response.reference,
                amount: paymentData.amount,
                method: 'M-Pesa via Paystack'
              }
            });
          }, 2000);
        },
        onClose: function() {
          setPaymentStatus('Payment cancelled');
          setLoading(false);
        }
      });
      
      handler.openIframe();
      
    } catch (error) {
      console.error('Payment error:', error);
      setPaymentStatus(`Payment failed: ${error.message}`);
      setLoading(false);
    }
  };

  return (
    <div className="container mt-5">
      <div className="card bg-dark text-light">
        <div className="card-header">
          <h2 className="card-title text-center mb-0">M-Pesa Payment via Paystack</h2>
        </div>
        <div className="card-body">
          
          <div className="row mb-4">
            <div className="col-md-6">
              <div className="bg-secondary p-3 rounded">
                <h5 className="mb-3">Payment Details</h5>
                <p><strong>Amount:</strong> KSh {paymentData.amount.toLocaleString()}</p>
                <p><strong>Phone:</strong> {paymentData.phoneNumber}</p>
                <p><strong>Reference:</strong> {paymentData.reference}</p>
                <p><strong>Description:</strong> {paymentData.description}</p>
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="bg-secondary p-3 rounded">
                <h5 className="mb-3">Update Payment Info</h5>
                <div className="mb-3">
                  <label className="form-label">Phone Number</label>
                  <input 
                    type="text" 
                    className="form-control bg-dark text-light border-light"
                    value={paymentData.phoneNumber}
                    onChange={(e) => setPaymentData(prev => ({...prev, phoneNumber: e.target.value}))}
                    placeholder="254712345678"
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label">Amount (KSh)</label>
                  <input 
                    type="number" 
                    className="form-control bg-dark text-light border-light"
                    value={paymentData.amount}
                    onChange={(e) => setPaymentData(prev => ({...prev, amount: parseFloat(e.target.value) || 0}))}
                  />
                </div>
              </div>
            </div>
          </div>

          {paymentStatus && (
            <div className={`alert ${paymentStatus.includes('successful') ? 'alert-success' : paymentStatus.includes('failed') || paymentStatus.includes('cancelled') ? 'alert-danger' : 'alert-info'} mb-4`}>
              <h6>Payment Status:</h6>
              <p className="mb-0">{paymentStatus}</p>
              {transactionRef && <p className="mb-0"><strong>Transaction Reference:</strong> {transactionRef}</p>}
            </div>
          )}

          <div className="text-center mb-4">
            <button 
              className="btn btn-primary btn-lg px-5" 
              onClick={initiatePaystackMpesaPayment}
              disabled={loading}
            >
              {loading ? 'Processing...' : 'Pay with Paystack M-Pesa'}
            </button>
            <div className="mt-2">
              <small className="text-muted">Secure payment via Paystack</small>
            </div>
          </div>

          <div className="alert alert-info">
            <h6 className="alert-heading">Payment Instructions:</h6>
            <p className="mb-1">1. Enter your M-Pesa phone number (e.g., 254712345678)</p>
            <p className="mb-1">2. Confirm the payment amount</p>
            <p className="mb-1">3. Click "Pay with Paystack M-Pesa" to open secure payment popup</p>
            <p className="mb-1">4. Select M-Pesa and complete the payment</p>
            <p className="mb-0">5. <strong>Live Mode:</strong> Real money will be charged</p>
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default MpesaPayment;
