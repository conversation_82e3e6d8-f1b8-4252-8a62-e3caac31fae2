/* Payment Pages Common Styles */
.payment-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%);
  padding: 2rem 1rem;
  position: relative;
  overflow-x: hidden;
}

.payment-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(255, 20, 147, 0.1);
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.payment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 215, 0, 0.6),
    rgba(255, 20, 147, 0.6),
    transparent);
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Header */
.payment-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.payment-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff1493, #ff6b6b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.payment-title {
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.payment-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.step-number.active {
  background: linear-gradient(135deg, #ff1493, #ff6b6b);
  border-color: rgba(255, 20, 147, 0.5);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 20, 147, 0.3);
}

.step-number.completed {
  background: linear-gradient(135deg, #00ff88, #00cc6a);
  border-color: rgba(0, 255, 136, 0.5);
  color: white;
}

.step-line {
  width: 60px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 1px;
}

/* Payment Form */
.payment-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.input-group {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-group:focus-within {
  border-color: rgba(255, 215, 0, 0.5);
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1);
}

.input-prefix {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 1rem 1rem 1rem 1.5rem;
  font-weight: 600;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.form-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  outline: none;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-help {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

/* Payment Summary */
.payment-summary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.payment-summary h3 {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item.total {
  border-top: 2px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  margin-top: 0.5rem;
}

.summary-item span:first-child {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
}

.summary-item span:last-child {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: right;
}

.amount {
  color: #00ff88 !important;
}

.total-amount {
  color: #ffd700 !important;
  font-size: 1.1rem !important;
  font-weight: 700 !important;
}

/* Action Buttons */
.payment-actions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  flex: 1;
}

.btn-cancel:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-pay {
  background: linear-gradient(135deg, #ff1493, #ff6b6b);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex: 2;
  position: relative;
  overflow: hidden;
}

.btn-pay:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 20, 147, 0.4);
}

.btn-pay:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Processing State */
.processing-state {
  text-align: center;
  padding: 2rem 0;
}

.processing-icon {
  margin-bottom: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #ff1493;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-spinner.large {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-state h2 {
  color: #ffffff;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.processing-state p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin-bottom: 2rem;
}

.processing-steps {
  display: grid;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.processing-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  flex-shrink: 0;
}

.step-icon.completed {
  background: #00ff88;
  color: white;
}

.step-icon.processing {
  background: #ffd700;
  animation: pulse 1.5s ease-in-out infinite;
}

.step-icon.pending {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
}

.processing-step span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Success State */
.success-state {
  text-align: center;
  padding: 2rem 0;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #00ff88, #00cc6a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 2.5rem;
  color: white;
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.success-state h2 {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.success-state p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.success-details {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.success-details .detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.success-details .detail-item:last-child {
  border-bottom: none;
}

.success-details .detail-item span:first-child {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
}

.success-details .detail-item span:last-child {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: right;
}

.transaction-id {
  color: #00ff88 !important;
  font-family: monospace;
  font-size: 0.8rem !important;
}

.redirect-message {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  font-style: italic;
}

/* Failed State */
.failed-state {
  text-align: center;
  padding: 2rem 0;
}

.failed-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff1493, #ff6b6b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 2.5rem;
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

.failed-state h2 {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.failed-state p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.retry-limit-warning {
  background: rgba(255, 20, 147, 0.1);
  border: 1px solid rgba(255, 20, 147, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.retry-limit-warning p {
  color: #ff1493;
  font-size: 0.9rem;
  margin: 0;
}

.failed-actions {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.btn-retry {
  background: linear-gradient(135deg, #ff1493, #ff6b6b);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-retry:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 20, 147, 0.4);
}

.btn-different-method {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.9);
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-different-method:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-failed-home {
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 12px;
  color: #ffd700;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-failed-home:hover {
  background: rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.5);
  transform: translateY(-2px);
}

/* Security Notice */
.security-notice {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin-top: 2rem;
}

.security-icon {
  color: #00ff88;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.security-content h4 {
  color: #00ff88;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.security-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

/* Paystack Inline Payment Styles */
.payment-method-selection {
  margin-bottom: 2rem;
}

.payment-method-selection h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.payment-method-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.payment-method-btn:hover {
  border-color: #4B0082;
  background: #f8f9ff;
  transform: translateY(-2px);
}

.payment-method-btn.active {
  border-color: #4B0082;
  background: linear-gradient(135deg, #4B0082, #6a0dad);
  color: white;
  box-shadow: 0 4px 15px rgba(75, 0, 130, 0.3);
}

.payment-method-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.payment-method-btn svg {
  font-size: 1.5rem;
}

.payment-method-info {
  background: #f8f9ff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.payment-method-info h4 {
  margin: 0 0 0.5rem 0;
  color: #4B0082;
  font-size: 1rem;
  font-weight: 600;
}

.payment-method-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.supported-methods {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.supported-methods h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.method-icons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.method-icon {
  font-size: 1.5rem;
  opacity: 0.8;
}

.supported-methods p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-container {
    padding: 1rem;
  }

  .payment-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .payment-title {
    font-size: 2rem;
  }

  .payment-subtitle {
    font-size: 1rem;
  }

  .payment-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .step-indicator {
    gap: 0.5rem;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .step-line {
    width: 40px;
  }

  .payment-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-pay {
    width: 100%;
  }

  .processing-steps {
    gap: 0.75rem;
  }

  .processing-step {
    padding: 0.75rem;
  }

  .payment-methods {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .payment-method-btn {
    padding: 1rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .payment-method-btn svg {
    font-size: 1.25rem;
  }
  
  .method-icons {
    gap: 0.75rem;
  }
  
  .method-icon {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .payment-card {
    padding: 1rem;
    border-radius: 12px;
  }

  .payment-title {
    font-size: 1.8rem;
  }

  .payment-subtitle {
    font-size: 0.9rem;
  }

  .step-number {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .step-line {
    width: 30px;
  }

  .payment-summary,
  .success-details {
    padding: 1rem;
  }

  .summary-item {
    padding: 0.5rem 0;
  }

  .processing-step {
    padding: 0.5rem;
  }

  .security-notice {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .payment-method-selection h3 {
    font-size: 1rem;
  }
  
  .payment-method-info {
    padding: 0.75rem;
  }
  
  .payment-method-info h4 {
    font-size: 0.9rem;
  }
  
  .payment-method-info p {
    font-size: 0.8rem;
  }
  
  .supported-methods h4 {
    font-size: 0.9rem;
  }
  
  .supported-methods p {
    font-size: 0.8rem;
  }
} 