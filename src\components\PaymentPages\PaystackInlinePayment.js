import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaCreditCard, FaShieldAlt, FaCheckCircle, FaExclamationTriangle, FaMobile, FaUniversity } from 'react-icons/fa';
import { toast } from 'react-toastify';
import './PaymentPages.css';

const PaystackInlinePayment = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [bookingData, setBookingData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [amount, setAmount] = useState(0);
  const [accessCode, setAccessCode] = useState('');
  const [popup, setPopup] = useState(null);

  useEffect(() => {
    const data = location.state?.bookingData;
    
    if (data) {
      setBookingData(data);
      setEmail(data.email || '<EMAIL>');
      setPhoneNumber(data.phoneNumber || '');
      setAmount(data.total_amount || data.pricingData?.breakdown.total || 0);
    } else {
      // Redirect if no booking data
      navigate('/book');
    }

    // Initialize Paystack popup
    if (window.PaystackPop) {
      const paystackPopup = new window.PaystackPop();
      setPopup(paystackPopup);
    }
  }, [location.state, navigate]);

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value;
    // Only allow numbers and limit to 10 digits
    if (/^\d{0,10}$/.test(value)) {
      setPhoneNumber(value);
    }
  };

  const handlePayment = async () => {
    if (!email || !email.includes('@')) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsProcessing(true);

    try {
      // Step 1: Initialize transaction from backend
      const initResponse = await fetch('http://127.0.0.1:8000/api/paystack/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          amount: Math.round(amount * 100), // Convert to kobo
          currency: 'KES',
          callback_url: `${window.location.origin}/checkout-success`,
          metadata: {
            booking_id: bookingData.id,
            customer_name: bookingData.customerName || bookingData.name,
            service_name: bookingData.service?.name || 'Selected Service',
            salon_name: bookingData.salon?.name || 'Selected Salon',
            phone_number: phoneNumber,
            payment_method: 'paystack_inline'
          }
        })
      });

      if (!initResponse.ok) {
        throw new Error('Failed to initialize payment');
      }

      const initData = await initResponse.json();
      
      if (!initData.success) {
        throw new Error(initData.message || 'Payment initialization failed');
      }

      // Store access code
      setAccessCode(initData.data.access_code);

      // Step 2: Complete transaction using Paystack v2
      if (popup && initData.data.access_code) {
        popup.resumeTransaction(initData.data.access_code);
        
        // Listen for payment completion
        popup.onComplete = (response) => {
          console.log('Payment completed:', response);
          
          if (response.status === 'success') {
            toast.success('Payment completed successfully! 🎉');
            
            // Navigate to success page
            navigate('/checkout-success', {
              state: {
                bookingData: {
                  ...bookingData,
                  transactionId: response.reference,
                  paymentStatus: 'completed',
                  paymentMethod: 'paystack_inline'
                }
              }
            });
          } else {
            toast.error('Payment failed. Please try again.');
            setIsProcessing(false);
          }
        };

        popup.onClose = () => {
          console.log('Payment modal closed');
          setIsProcessing(false);
          toast.info('Payment was cancelled');
        };

      } else {
        throw new Error('Paystack popup not available');
      }

    } catch (error) {
      console.error('Paystack payment error:', error);
      toast.error(error.message || 'Payment initialization failed');
      setIsProcessing(false);
    }
  };

  const handleCancel = () => {
    navigate('/booking-confirm', {
      state: { bookingData }
    });
  };

  if (!bookingData) {
    return (
      <div className="payment-container">
        <div className="payment-card">
          <div className="loading-spinner"></div>
          <p>Loading payment details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="payment-container">
      <div className="payment-card">
        {/* Header */}
        <div className="payment-header">
          <div className="payment-icon">
            <FaCreditCard />
          </div>
          <h1 className="payment-title">Paystack Payment</h1>
          <p className="payment-subtitle">
            Complete your payment securely via Paystack
          </p>
        </div>

        {/* Payment Form */}
        <div className="payment-form">
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              placeholder="<EMAIL>"
              className="form-input"
              disabled={isProcessing}
              required
            />
            <small className="form-help">
              Payment receipt will be sent to this email
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="phoneNumber" className="form-label">
              Phone Number (Optional)
            </label>
            <div className="input-group">
              <span className="input-prefix">+254</span>
              <input
                type="tel"
                id="phoneNumber"
                value={phoneNumber}
                onChange={handlePhoneNumberChange}
                placeholder="700000000"
                className="form-input"
                maxLength="10"
                disabled={isProcessing}
              />
            </div>
            <small className="form-help">
              For mobile money payments (M-Pesa, etc.)
            </small>
          </div>

          {/* Payment Summary */}
          <div className="payment-summary">
            <h3>💰 Payment Summary</h3>
            <div className="summary-item">
              <span>Service:</span>
              <span>{bookingData.service?.name || 'Selected Service'}</span>
            </div>
            <div className="summary-item">
              <span>Salon:</span>
              <span>{bookingData.salon?.name || 'Selected Salon'}</span>
            </div>
            <div className="summary-item">
              <span>Amount:</span>
              <span className="amount">KSh {amount.toLocaleString()}</span>
            </div>
            <div className="summary-item total">
              <span>Total:</span>
              <span className="total-amount">KSh {amount.toLocaleString()}</span>
            </div>
          </div>

          {/* Payment Method Info */}
          <div className="payment-method-info">
            <h4>Payment Method: Paystack (All Methods)</h4>
            <p>
              You'll be redirected to Paystack's secure payment page where you can choose from:
              Credit/Debit Cards, Mobile Money (M-Pesa), Bank Transfers, USSD, and QR Codes.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="payment-actions">
            <button
              className="btn-cancel"
              onClick={handleCancel}
              disabled={isProcessing}
            >
              Cancel
            </button>
            <button
              className="btn-pay"
              onClick={handlePayment}
              disabled={isProcessing || !email}
            >
              {isProcessing ? (
                <>
                  <div className="loading-spinner"></div>
                  Processing...
                </>
              ) : (
                <>
                  <FaShieldAlt />
                  Pay KSh {amount.toLocaleString()}
                </>
              )}
            </button>
          </div>
        </div>

        {/* Security Notice */}
        <div className="security-notice">
          <FaShieldAlt className="security-icon" />
          <div className="security-content">
            <h4>Secure Payment</h4>
            <p>Your payment is processed securely through Paystack's encrypted system</p>
          </div>
        </div>

        {/* Supported Payment Methods */}
        <div className="supported-methods">
          <h4>Supported Payment Methods</h4>
          <div className="method-icons">
            <span className="method-icon">💳</span>
            <span className="method-icon">📱</span>
            <span className="method-icon">🏦</span>
            <span className="method-icon">📟</span>
            <span className="method-icon">📱</span>
          </div>
          <p>Cards, Mobile Money, Bank Transfer, USSD, QR Codes</p>
        </div>
      </div>
    </div>
  );
};

export default PaystackInlinePayment; 
