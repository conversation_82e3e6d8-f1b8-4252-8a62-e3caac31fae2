import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { 
  getPaystackBanks, 
  resolvePaystackAccount, 
  chargePaystackCard, 
  chargePaystackBank, 
  generatePaystackReference 
} from '../../services/PaystackService';
import './PaymentPages.css';

const PaystackPayment = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const bookingData = location.state?.bookingData;

  const [step, setStep] = useState(1);
  const [loading, setIsLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [banks, setBanks] = useState([]);
  const [selectedBank, setSelectedBank] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [accountName, setAccountName] = useState('');
  const [cardData, setCardData] = useState({
    cardNumber: '',
    cvv: '',
    expiryMonth: '',
    expiryYear: '',
    pin: ''
  });
  const [transactionData, setTransactionData] = useState(null);

  useEffect(() => {
    if (!bookingData) {
      navigate('/booking-confirm');
      return;
    }
    loadBanks();
  }, [bookingData, navigate]);

  const loadBanks = async () => {
    try {
      const result = await getPaystackBanks();
      if (result.success) {
        setBanks(result.data);
      }
    } catch (error) {
      console.error('Failed to load banks:', error);
    }
  };

  const handleCardInputChange = (field, value) => {
    setCardData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAccountNumberChange = async (value) => {
    setAccountNumber(value);
    if (value.length === 10 && selectedBank) {
      try {
        const result = await resolvePaystackAccount(value, selectedBank);
        if (result.success) {
          setAccountName(result.data.account_name);
        }
      } catch (error) {
        console.error('Failed to resolve account:', error);
      }
    }
  };

  const validateCardData = () => {
    const { cardNumber, cvv, expiryMonth, expiryYear, pin } = cardData;
    
    if (!cardNumber || cardNumber.length < 13 || cardNumber.length > 19) {
      toast.error('Please enter a valid card number');
      return false;
    }
    
    if (!cvv || cvv.length < 3 || cvv.length > 4) {
      toast.error('Please enter a valid CVV');
      return false;
    }
    
    if (!expiryMonth || !expiryYear) {
      toast.error('Please enter card expiry date');
      return false;
    }
    
    if (!pin || pin.length !== 4) {
      toast.error('Please enter a 4-digit PIN');
      return false;
    }
    
    return true;
  };

  const validateBankData = () => {
    if (!selectedBank) {
      toast.error('Please select a bank');
      return false;
    }
    
    if (!accountNumber || accountNumber.length !== 10) {
      toast.error('Please enter a valid 10-digit account number');
      return false;
    }
    
    if (!accountName) {
      toast.error('Please verify your account number');
      return false;
    }
    
    return true;
  };

  const processCardPayment = async () => {
    if (!validateCardData()) return;

    setIsLoading(true);
    setStep(2);

    try {
      const reference = generatePaystackReference('SALON');
      const paymentData = {
        ...cardData,
        amount: bookingData.total_amount || bookingData.pricingData?.breakdown.total,
        email: bookingData.customer_email,
        reference,
        booking_id: bookingData.id,
        customer_name: bookingData.customer_name,
        service_name: bookingData.service_name,
        salon_name: bookingData.salon_name
      };

      const result = await chargePaystackCard(paymentData);

      if (result.success) {
        setTransactionData(result);
        setStep(3);
        toast.success('Payment processed successfully! 🎉');
        
        // Navigate to success page after delay
        setTimeout(() => {
          navigate('/checkout-success', {
            state: {
              bookingData: {
                ...bookingData,
                transactionId: result.reference,
                paymentStatus: 'completed'
              }
            }
          });
        }, 3000);
      } else {
        setStep(4);
        toast.error(result.message || 'Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setStep(4);
      toast.error('Payment processing failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const processBankPayment = async () => {
    if (!validateBankData()) return;

    setIsLoading(true);
    setStep(2);

    try {
      const reference = generatePaystackReference('SALON');
      const paymentData = {
        account_number: accountNumber,
        bank_code: selectedBank,
        amount: bookingData.total_amount || bookingData.pricingData?.breakdown.total,
        email: bookingData.customer_email,
        reference,
        booking_id: bookingData.id,
        customer_name: bookingData.customer_name,
        service_name: bookingData.service_name,
        salon_name: bookingData.salon_name
      };

      const result = await chargePaystackBank(paymentData);

      if (result.success) {
        setTransactionData(result);
        setStep(3);
        toast.success('Bank payment initiated successfully! 🎉');
        
        // Navigate to success page after delay
        setTimeout(() => {
          navigate('/checkout-success', {
            state: {
              bookingData: {
                ...bookingData,
                transactionId: result.reference,
                paymentStatus: 'completed'
              }
            }
          });
        }, 3000);
      } else {
        setStep(4);
        toast.error(result.message || 'Bank payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setStep(4);
      toast.error('Payment processing failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePayment = () => {
    if (paymentMethod === 'card') {
      processCardPayment();
    } else if (paymentMethod === 'bank') {
      processBankPayment();
    }
  };

  const handleRetry = () => {
    setStep(1);
    setCardData({
      card_number: '',
      cvv: '',
      expiry_month: '',
      expiry_year: '',
      pin: ''
    });
    setAccountNumber('');
    setAccountName('');
    setSelectedBank('');
  };

  const handleTryDifferentMethod = () => {
    navigate('/booking-confirm', {
      state: { bookingData }
    });
  };

  const handleCancel = () => {
    navigate('/booking-confirm', {
      state: { bookingData }
    });
  };

  if (!bookingData) {
    return (
      <div className="payment-container">
        <div className="payment-card">
          <div className="loading-spinner"></div>
          <p>Loading payment details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="payment-container">
      <div className="payment-card">
        {/* Header */}
        <div className="payment-header">
          <div className="payment-logo">
            <span className="logo-icon">💳</span>
            <h2>Paystack Payment</h2>
          </div>
          <p className="payment-subtitle">Secure payment powered by Paystack</p>
        </div>

        {/* Step 1: Payment Method Selection */}
        {step === 1 && (
          <div className="payment-step">
            <div className="step-indicator">
              <div className="step active">1</div>
              <div className="step-line"></div>
              <div className="step">2</div>
              <div className="step-line"></div>
              <div className="step">3</div>
            </div>

            <div className="payment-methods">
              <h3>Select Payment Method</h3>
              
              <div className="method-options">
                <label className={`method-option ${paymentMethod === 'card' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="card"
                    checked={paymentMethod === 'card'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                  />
                  <div className="method-content">
                    <span className="method-icon">💳</span>
                    <div className="method-details">
                      <h4>Credit/Debit Card</h4>
                      <p>Visa, Mastercard, Verve</p>
                    </div>
                  </div>
                </label>

                <label className={`method-option ${paymentMethod === 'bank' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="bank"
                    checked={paymentMethod === 'bank'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                  />
                  <div className="method-content">
                    <span className="method-icon">🏦</span>
                    <div className="method-details">
                      <h4>Bank Transfer</h4>
                      <p>Direct bank account transfer</p>
                    </div>
                  </div>
                </label>
              </div>

              {/* Card Payment Form */}
              {paymentMethod === 'card' && (
                <div className="payment-form">
                  <h4>Card Details</h4>
                  
                  <div className="form-group">
                    <label>Card Number</label>
                    <input
                      type="text"
                      placeholder="1234 5678 9012 3456"
                      value={cardData.card_number}
                      onChange={(e) => handleCardInputChange('card_number', e.target.value)}
                      maxLength="19"
                    />
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Expiry Month</label>
                      <select
                        value={cardData.expiry_month}
                        onChange={(e) => handleCardInputChange('expiry_month', e.target.value)}
                      >
                        <option value="">MM</option>
                        {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                          <option key={month} value={month.toString().padStart(2, '0')}>
                            {month.toString().padStart(2, '0')}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label>Expiry Year</label>
                      <select
                        value={cardData.expiry_year}
                        onChange={(e) => handleCardInputChange('expiry_year', e.target.value)}
                      >
                        <option value="">YYYY</option>
                        {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i).map(year => (
                          <option key={year} value={year}>
                            {year}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label>CVV</label>
                      <input
                        type="password"
                        placeholder="123"
                        value={cardData.cvv}
                        onChange={(e) => handleCardInputChange('cvv', e.target.value)}
                        maxLength="4"
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label>PIN</label>
                    <input
                      type="password"
                      placeholder="4-digit PIN"
                      value={cardData.pin}
                      onChange={(e) => handleCardInputChange('pin', e.target.value)}
                      maxLength="4"
                    />
                  </div>
                </div>
              )}

              {/* Bank Transfer Form */}
              {paymentMethod === 'bank' && (
                <div className="payment-form">
                  <h4>Bank Account Details</h4>
                  
                  <div className="form-group">
                    <label>Select Bank</label>
                    <select
                      value={selectedBank}
                      onChange={(e) => setSelectedBank(e.target.value)}
                    >
                      <option value="">Choose your bank</option>
                      {banks.map(bank => (
                        <option key={bank.code} value={bank.code}>
                          {bank.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Account Number</label>
                    <input
                      type="text"
                      placeholder="**********"
                      value={accountNumber}
                      onChange={(e) => handleAccountNumberChange(e.target.value)}
                      maxLength="10"
                    />
                  </div>

                  {accountName && (
                    <div className="account-verification">
                      <span className="verification-icon">✅</span>
                      <span className="account-name">{accountName}</span>
                    </div>
                  )}
                </div>
              )}

              <div className="payment-actions">
                <button
                  className="btn-secondary"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  className="btn-primary"
                  onClick={handlePayment}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="loading-spinner"></div>
                      Processing...
                    </>
                  ) : (
                    `Pay ${bookingData.total_amount ? `KSh ${bookingData.total_amount.toLocaleString()}` : 'Now'}`
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Processing */}
        {step === 2 && (
          <div className="payment-step">
            <div className="processing-state">
              <div className="processing-spinner"></div>
              <h3>Processing Payment</h3>
              <p>Please wait while we process your payment...</p>
              <div className="processing-details">
                <p>Amount: KSh {bookingData.total_amount?.toLocaleString()}</p>
                <p>Method: {paymentMethod === 'card' ? 'Card Payment' : 'Bank Transfer'}</p>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Success */}
        {step === 3 && (
          <div className="payment-step">
            <div className="success-state">
              <div className="success-icon">✅</div>
              <h3>Payment Successful!</h3>
              <p>Your payment has been processed successfully.</p>
              
              {transactionData && (
                <div className="transaction-details">
                  <p><strong>Reference:</strong> {transactionData.reference}</p>
                  <p><strong>Amount:</strong> KSh {transactionData.amount?.toLocaleString()}</p>
                  <p><strong>Status:</strong> {transactionData.status}</p>
                </div>
              )}
              
              <p className="redirect-message">Redirecting to confirmation page...</p>
            </div>
          </div>
        )}

        {/* Step 4: Error */}
        {step === 4 && (
          <div className="payment-step">
            <div className="error-state">
              <div className="error-icon">❌</div>
              <h3>Payment Failed</h3>
              <p>Sorry, your payment could not be processed.</p>
              
              <div className="error-actions">
                <button className="btn-secondary" onClick={handleRetry}>
                  Try Again
                </button>
                <button className="btn-primary" onClick={handleTryDifferentMethod}>
                  Try Different Method
                </button>
              </div>
              
              <div className="error-help">
                <h4>Need Help?</h4>
                <p>Contact support if you continue to experience issues.</p>
                <p>Email: <EMAIL></p>
                <p>Phone: +254 700 000 000</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaystackPayment;
