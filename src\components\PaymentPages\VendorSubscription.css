/* Mobile-First Vendor Subscription Payment Styles */
.vendor-payment {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.payment-container {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.payment-header {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  padding: 1.5rem 1rem;
  text-align: center;
  color: white;
}

.payment-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.payment-subtitle {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.payment-content {
  padding: 1.5rem 1rem;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.details-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
}

.details-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.details-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.9rem;
  color: #64748b;
  border-bottom: 1px solid #e2e8f0;
}

.details-item:last-child {
  border-bottom: none;
}

.details-label {
  font-weight: 500;
}

.details-value {
  font-weight: 600;
  color: #1e293b;
}

.payment-summary {
  background: #ecfdf5;
  border: 1px solid #10b981;
  border-radius: 12px;
  padding: 1rem;
  margin: 1.5rem 0;
}

.summary-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #065f46;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  font-size: 0.95rem;
  color: #047857;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0 0 0;
  margin-top: 1rem;
  border-top: 2px solid #10b981;
  font-size: 1.2rem;
  font-weight: 700;
  color: #065f46;
}

.status-alert {
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin: 1.5rem 0;
  font-size: 0.95rem;
  font-weight: 500;
}

.status-success {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border: 2px solid #10b981;
  color: #065f46;
}

.status-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border: 2px solid #ef4444;
  color: #dc2626;
}

.status-info {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 2px solid #3b82f6;
  color: #1d4ed8;
}

.payment-button {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin: 1rem 0;
  min-height: 48px;
  touch-action: manipulation;
}

.payment-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
}

.payment-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.security-note {
  text-align: center;
  font-size: 0.85rem;
  color: #64748b;
  margin: 0.5rem 0 1.5rem 0;
}



.benefits-card {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 2px solid #3b82f6;
  border-radius: 16px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.benefits-title {
  font-size: 1rem;
  font-weight: 700;
  color: #1d4ed8;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefits-item {
  padding: 0.5rem 0;
  font-size: 0.9rem;
  color: #1e40af;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: #64748b;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
  text-align: center;
}

.back-button:hover:not(:disabled) {
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
}

.error-container {
  max-width: 600px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #dc2626;
  margin: 0 0 1rem 0;
}

.error-message {
  font-size: 1rem;
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.error-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* Desktop enhancements */
@media (min-width: 768px) {
  .vendor-payment {
    padding: 2rem 1rem;
  }

  .payment-container {
    max-width: 800px;
    border-radius: 24px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .payment-header {
    padding: 2rem;
  }

  .payment-content {
    padding: 2rem;
  }

  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .payment-title {
    font-size: 1.75rem;
  }

  .details-card {
    padding: 1.5rem;
    border-radius: 16px;
    border-width: 2px;
  }

  .payment-summary,
  .benefits-card {
    border-radius: 16px;
    border-width: 2px;
  }

  .payment-button {
    border-radius: 12px;
  }
}
