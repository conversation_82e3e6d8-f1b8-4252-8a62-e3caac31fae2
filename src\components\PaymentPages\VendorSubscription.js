import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import './VendorSubscription.css';

const VendorSubscription = () => {
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState('');
  const [transactionRef, setTransactionRef] = useState('');
  const [showBypassOption, setShowBypassOption] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const { subscriptionData } = location.state || {};

  useEffect(() => {
    if (!subscriptionData) {
      navigate('/register-vendor');
    }
  }, [subscriptionData, navigate]);

  const initiateVendorPayment = async () => {
    setLoading(true);
    setPaymentStatus('Initiating vendor subscription payment...');

    try {
      // Check if Paystack script is loaded
      if (typeof window.PaystackPop === 'undefined') {
        throw new Error('Paystack script not loaded. Please refresh the page.');
      }
      
      const paystackPublicKey = process.env.REACT_APP_PAYSTACK_PUBLIC_KEY || 'pk_live_22d10798ff283975b275fc96824e6325ca901a0c';
      
      const handler = window.PaystackPop.setup({
        key: paystackPublicKey,
        email: subscriptionData.email,
        amount: Math.round(subscriptionData.totalAmount * 100), // Convert to kobo/cents
        currency: 'KES',
        ref: `VENDOR_SUB_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        metadata: {
          vendor_name: subscriptionData.salonName,
          vendor_email: subscriptionData.email,
          vendor_phone: subscriptionData.phone,
          subscription_plan: subscriptionData.duration || subscriptionData.subscriptionPlan,
          listing_tier: subscriptionData.listingTier,
          plan_duration: subscriptionData.durationDetails?.days || 1,
          payment_type: 'vendor_subscription'
        },
        channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money'], // All payment methods
        callback: function (response) {
          console.log('Vendor subscription payment successful:', response);
          setPaymentStatus('Payment successful! Setting up your vendor account...');
          setTransactionRef(response.reference);
          
          // TODO: Register vendor in backend with subscription details
          setTimeout(() => {
            navigate('/vendor-dashboard', {
              state: {
                vendorData: subscriptionData,
                paymentReference: response.reference,
                subscriptionActive: true
              }
            });
          }, 2000);
        },
        onClose: function () {
          setPaymentStatus('Payment cancelled');
          setLoading(false);
        }
      });
      
      handler.openIframe();
      
    } catch (error) {
      console.error('Vendor payment error:', error);
      setPaymentStatus(`Payment failed: ${error.message}`);
      setLoading(false);
    }
  };

  if (!subscriptionData) {
    return (
      <div className="vendor-payment">
        <div className="error-container">
          <h4 className="error-title">No subscription data found</h4>
          <p className="error-message">Please complete the vendor registration form first.</p>
          <button className="error-button" onClick={() => navigate('/register-vendor')}>
            Go to Vendor Registration
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="vendor-payment">
      <div className="payment-container">
        <div className="payment-header">
          <h1 className="payment-title">Vendor Subscription Payment</h1>
          <p className="payment-subtitle">Complete your vendor registration</p>
        </div>
        <div className="payment-content">

          {/* Vendor Details */}
          <div className="details-grid">
            <div className="details-card">
              <h3 className="details-title">
                <span>📋</span>
                Vendor Details
              </h3>
              <div className="details-item">
                <span className="details-label">Salon Name:</span>
                <span className="details-value">{subscriptionData.salonName}</span>
              </div>
              <div className="details-item">
                <span className="details-label">Location:</span>
                <span className="details-value">{subscriptionData.town}</span>
              </div>
              <div className="details-item">
                <span className="details-label">Email:</span>
                <span className="details-value">{subscriptionData.email}</span>
              </div>
              <div className="details-item">
                <span className="details-label">Phone:</span>
                <span className="details-value">{subscriptionData.phone}</span>
              </div>
            </div>

            <div className="details-card">
              <h3 className="details-title">
                <span>💰</span>
                Subscription Details
              </h3>
              <div className="details-item">
                <span className="details-label">Platform Plan:</span>
                <span className="details-value">{subscriptionData.durationDetails?.label || subscriptionData.duration}</span>
              </div>
              <div className="details-item">
                <span className="details-label">Duration:</span>
                <span className="details-value">{subscriptionData.durationDetails?.days || 1} day(s)</span>
              </div>
              <div className="details-item">
                <span className="details-label">Listing Tier:</span>
                <span className="details-value">{subscriptionData.tierDetails?.label || subscriptionData.listingTier}</span>
              </div>
              <div className="details-item">
                <span className="details-label">Platform Cost:</span>
                <span className="details-value">KSh {subscriptionData.platformCost || 0}</span>
              </div>
            </div>
          </div>

          {/* Payment Breakdown */}
          <div className="payment-summary">
            <h3 className="summary-title">
              <span>💳</span>
              Payment Breakdown
            </h3>
            <div className="summary-item">
              <span>Platform Access ({subscriptionData.durationDetails?.label || subscriptionData.duration})</span>
              <span>KSh {subscriptionData.platformCost || 0}</span>
            </div>
            <div className="summary-item">
              <span>Listing Placement ({subscriptionData.tierDetails?.label || subscriptionData.listingTier})</span>
              <span>KSh {subscriptionData.listingCost || 0}</span>
            </div>
            <div className="summary-total">
              <span>Total Amount</span>
              <span>KSh {subscriptionData.totalAmount}</span>
            </div>
          </div>

          {/* Payment Status */}
          {paymentStatus && (
            <div className={`status-alert ${paymentStatus.includes('successful') ? 'status-success' : paymentStatus.includes('failed') || paymentStatus.includes('cancelled') ? 'status-error' : 'status-info'}`}>
              <div><strong>Payment Status:</strong></div>
              <div>{paymentStatus}</div>
              {transactionRef && <div><strong>Transaction Reference:</strong> {transactionRef}</div>}
            </div>
          )}

          {/* Payment Button */}
          <div>
            <button
              className="payment-button"
              onClick={initiateVendorPayment}
              disabled={loading}
            >
              {loading ? 'Processing...' : `Pay KSh ${subscriptionData.totalAmount} - Start Subscription`}
            </button>
            <div className="security-note">
              🔒 Secure payment via Paystack
            </div>
          </div>

          {/* Benefits */}
          <div className="benefits-card">
            <h3 className="benefits-title">
              <span>🎯</span>
              What You Get
            </h3>
            <ul className="benefits-list">
              <li className="benefits-item">
                <span>✅</span>
                Professional salon listing on SalonGenz platform
              </li>
              <li className="benefits-item">
                <span>✅</span>
                Premium listing placement and visibility
              </li>
              <li className="benefits-item">
                <span>✅</span>
                Customer booking management system
              </li>
              <li className="benefits-item">
                <span>✅</span>
                Direct payment processing for your services
              </li>
              <li className="benefits-item">
                <span>✅</span>
                Analytics and reporting dashboard
              </li>
              <li className="benefits-item">
                <span>✅</span>
                24/7 customer support
              </li>
            </ul>
          </div>

          {/* Back Button */}
          <button
            className="back-button"
            onClick={() => navigate('/register-vendor')}
            disabled={loading}
          >
            ← Back to Registration
          </button>
          
          {/* Back Button */}
          <button
            className="back-button"
            onClick={() => navigate('/register-vendor')}
            disabled={loading}
          >
            ← Back to Registration
          </button>

        </div>
      </div>
    </div>
  );
};

export default VendorSubscription;
