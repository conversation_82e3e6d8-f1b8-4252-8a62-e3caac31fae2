// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './PopularSalonsSection.css';

const PopularSalonsSection = () => {
  const { authFetch } = useAuth();
  const [popularSalons, setPopularSalons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const getPerPage = () => (window.innerWidth <= 768 ? 2 : 4);
  const [perPage, setPerPage] = useState(getPerPage());
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    const handleResize = () => setPerPage(getPerPage());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalPages = Math.ceil(popularSalons.length / perPage);
  const paginatedSalons = popularSalons.slice((currentPage - 1) * perPage, currentPage * perPage);
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i += 1) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i += 1) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  useEffect(() => {
    const fetchPopular = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await authFetch('/api/recommendations/?type=popular');
        if (!res.ok) throw new Error('Failed to fetch popular salons');
        const data = await res.json();
        const salonIds = data.map(rec => rec.object_id);
        const salonDetailsPromises = salonIds.map(id => authFetch(`/api/salons/${id}/`).then(resp => resp.json()));
        const salonDetails = await Promise.all(salonDetailsPromises);
        setPopularSalons(salonDetails);
      } catch (err) {
        setError('Could not load popular salons.');
      }
      setLoading(false);
    };
    fetchPopular();
  }, [authFetch]);

  return (
    <section className="popular-salons-section">
      <div className="popular-salons-container">

        {/* Section Header */}
        <div className="section-header">
          <div className="header-content">
            <h2 className="section-title">Most Popular Salons</h2>
            <p className="section-subtitle">Trending salons loved by our community</p>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="loading-state">
            <div className="loading-spinner" />
            <p className="loading-text">Loading popular salons...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="error-state">
            <div className="error-icon">⚠️</div>
            <p className="error-text">{error}</p>
          </div>
        )}

        {/* Salons Grid */}
        {!loading && !error && paginatedSalons.length > 0 && (
          <div className="salons-grid">
            {paginatedSalons.map(salon => (
              <div key={salon.id} className="salon-card popular-card">
                <div className="card-image-container">
                  <img
                    src={salon.imageUrl || salon.image_url || 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format'}
                    className="card-image"
                    alt={`${salon.name} - Popular Salon`}
                    loading="lazy"
                    onError={(e) => {
                      e.target.src = 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format';
                    }}
                    sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 25vw"
                  />
                </div>
                <div className="card-content">
                  <div className="salon-badge-banner">
                    <div className="popular-badge-banner">
                      <span className="popular-icon">🔥</span>
                      Popular
                    </div>
                  </div>
                  <h3 className="salon-name">{salon.name}</h3>
                  <p className="salon-location">
                    {salon.town}
                    ,
                    {' '}
                    {salon.address}
                  </p>
                  <div className="salon-meta">
                    <span className="salon-rating">⭐ 4.9</span>
                    <span className="salon-status popular-status">Trending</span>
                  </div>
                  <Link to={`/salon/${salon.id || salon.salonId}`} className="view-details-btn popular-btn">
                    View Salon
                    <span className="btn-icon">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="pagination-container">
            {perPage === 2 ? (
              <div className="mobile-pagination">
                <button className="pagination-btn" type="button" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                  <span className="pagination-icon">←</span>
                </button>
                <span className="pagination-text">
                  Page
                  {currentPage}
                  {' '}
                  of
                  {totalPages}
                </span>
                <button className="pagination-btn" type="button" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>
                  <span className="pagination-icon">→</span>
                </button>
              </div>
            ) : (
              <div className="desktop-pagination">
                <button className="pagination-btn" type="button" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                  <span className="pagination-icon">←</span>
                </button>
                <div className="page-numbers">
                  {getPageNumbers().map((num) => (num === 'ellipsis-left' || num === 'ellipsis-right' ? (
                    <span key={num} className="page-ellipsis">...</span>
                  ) : (
                    <button
                      key={num}
                      type="button"
                      className={`page-number ${currentPage === num ? 'active' : ''}`}
                      onClick={() => setCurrentPage(num)}
                    >
                      {num}
                    </button>
                  )))}
                </div>
                <button className="pagination-btn" type="button" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>
                  <span className="pagination-icon">→</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && popularSalons.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">🏆</div>
            <h3>No Popular Salons</h3>
            <p>Check back later for trending salons in your area.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default PopularSalonsSection;
