/* Compact Mobile-First Premium Ticker */

/* Large screens - show 4 cards */
@media (min-width: 1024px) {
  .premium-ticker {
    width: calc(100% / 4 * 4);
  }
  .ticker-item {
    min-width: calc((100vw - 2rem) / 4);
    max-width: calc((100vw - 2rem) / 4);
  }
}

/* Tablet/iPad - show 2 cards */
@media (min-width: 768px) and (max-width: 1023px) {
  .premium-ticker {
    width: calc(100% / 2 * 2);
  }
  .ticker-item {
    min-width: calc((100vw - 2rem) / 2);
    max-width: calc((100vw - 2rem) / 2);
  }
}

/* Mobile - show 1 card */
@media (max-width: 767px) {
  .premium-ticker {
    width: calc(100% / 1 * 1);
  }
  .ticker-item {
    min-width: calc(100vw - 2rem);
    max-width: calc(100vw - 2rem);
  }
}

.premium-ticker-container {
  background: linear-gradient(135deg,
    rgba(15, 15, 23, 0.95) 0%,
    rgba(25, 25, 35, 0.92) 25%,
    rgba(20, 20, 30, 0.94) 50%,
    rgba(30, 30, 40, 0.91) 75%,
    rgba(15, 15, 23, 0.95) 100%);
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(15px);
  overflow: hidden;
  margin: 0;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
}

.premium-ticker-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.6) 50%,
    transparent 100%);
  z-index: 1;
}

.premium-ticker-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 12px 20px;
  background: rgba(255, 215, 0, 0.05);
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
  position: relative;
  z-index: 2;
}

.ticker-label {
  font-size: 0.9rem;
  font-weight: 800;
  background: linear-gradient(45deg, #FFD700 0%, #FFA500 50%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.ticker-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  opacity: 0.85;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.premium-ticker {
  height: 90px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg,
    rgba(15, 15, 23, 0.8) 0%,
    rgba(25, 25, 35, 0.7) 50%,
    rgba(15, 15, 23, 0.8) 100%);
  width: 100%;
  border-top: 1px solid rgba(255, 215, 0, 0.1);
}

.ticker-content {
  display: flex;
  animation: scroll-left 40s linear infinite;
  height: 100%;
  align-items: center;
  gap: 2rem;
  padding: 0 1.5rem;
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  width: 200%;
  transform-style: preserve-3d;
  position: relative;
}

/* Ensure smooth infinite loop without text overlap */
.ticker-content::before,
.ticker-content::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background: transparent;
  z-index: 10;
  pointer-events: none;
}

.premium-ticker.paused .ticker-content {
  animation-play-state: paused;
}

/* Smooth transitions for all elements */
.ticker-item * {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Prevent text flickering during animations */
.salon-name,
.tier-label-prominent {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Clicking state to prevent distortion */
.ticker-item.clicking {
  transform: scale(0.98) !important;
  transition: transform 0.1s ease !important;
  animation-play-state: paused !important;
  pointer-events: none;
}

.ticker-item.clicking .salon-image {
  transform: none !important;
  transition: none !important;
}

.ticker-item.clicking .salon-name {
  transform: none !important;
  transition: none !important;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.ticker-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 12px;
  text-decoration: none !important;
  color: white !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 240px;
  max-width: 260px;
  flex-shrink: 0;
  border: 1px solid rgba(255, 215, 0, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  height: auto;
  min-height: 75px;
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
  overflow: hidden;
  isolation: isolate;
  contain: layout style paint;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.ticker-item:hover {
  transform: translateY(-3px) scale(1.02);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 215, 0, 0.1) 50%,
    rgba(255, 255, 255, 0.15) 100%);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.4);
  text-decoration: none !important;
  color: white !important;
}

.ticker-item:hover .salon-image {
  transform: translateY(-0.2rem);
  margin-top: 0.1rem;
}

.ticker-item:visited,
.ticker-item:focus {
  text-decoration: none !important;
  color: white !important;
}

.ticker-item:active {
  text-decoration: none !important;
  color: white !important;
  transform: translateY(0px) scale(0.98) !important;
  transition: transform 0.1s ease !important;
}

/* Enterprise Elite Tier Styling */
.ticker-item.elite-tier {
  border: 2px solid rgba(255, 215, 0, 0.6);
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.25) 0%,
    rgba(255, 165, 0, 0.2) 30%,
    rgba(255, 20, 147, 0.15) 70%,
    rgba(138, 43, 226, 0.1) 100%
  );
  animation: elite-glow 4s ease-in-out infinite;
  position: relative;
}

.ticker-item.elite-tier::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B9D, #8A2BE2);
  border-radius: 14px;
  z-index: -1;
  opacity: 0.7;
  animation: elite-border 3s ease-in-out infinite;
}

@keyframes elite-glow {
  0%, 100% {
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
    border-color: rgba(255, 215, 0, 0.6);
  }
  50% {
    box-shadow: 0 10px 40px rgba(255, 215, 0, 0.7);
    border-color: rgba(255, 215, 0, 0.8);
  }
}

@keyframes elite-border {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.ticker-item.elite-tier:hover {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.35) 0%,
    rgba(255, 165, 0, 0.3) 30%,
    rgba(255, 20, 147, 0.25) 70%,
    rgba(138, 43, 226, 0.2) 100%
  );
  box-shadow: 0 12px 45px rgba(255, 215, 0, 0.6);
  transform: translateY(-5px) scale(1.08);
}

.ticker-item.spotlight-tier {
  border: 2px solid rgba(255, 20, 147, 0.5);
  background: linear-gradient(135deg,
    rgba(255, 20, 147, 0.22) 0%,
    rgba(138, 43, 226, 0.18) 50%,
    rgba(255, 107, 157, 0.15) 100%
  );
  animation: spotlight-pulse 3s ease-in-out infinite;
  position: relative;
}

.ticker-item.spotlight-tier::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #FF1493, #8A2BE2, #FF6B9D);
  border-radius: 14px;
  z-index: -1;
  opacity: 0.6;
  animation: spotlight-border 2.5s ease-in-out infinite;
}

@keyframes spotlight-pulse {
  0%, 100% {
    box-shadow: 0 6px 25px rgba(255, 20, 147, 0.4);
    border-color: rgba(255, 20, 147, 0.5);
  }
  50% {
    box-shadow: 0 10px 35px rgba(255, 20, 147, 0.6);
    border-color: rgba(255, 20, 147, 0.7);
  }
}

@keyframes spotlight-border {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.9; }
}

.ticker-item.spotlight-tier:hover {
  background: linear-gradient(135deg,
    rgba(255, 20, 147, 0.32) 0%,
    rgba(138, 43, 226, 0.28) 50%,
    rgba(255, 107, 157, 0.25) 100%
  );
  box-shadow: 0 12px 40px rgba(255, 20, 147, 0.6);
  transform: translateY(-5px) scale(1.06);
}

.ticker-item.featured-tier {
  border: 2px solid rgba(52, 152, 219, 0.5);
  background: linear-gradient(135deg,
    rgba(52, 152, 219, 0.2) 0%,
    rgba(155, 89, 182, 0.15) 50%,
    rgba(102, 126, 234, 0.12) 100%
  );
  animation: featured-wave 3.5s ease-in-out infinite;
  position: relative;
}

.ticker-item.featured-tier::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #3498db, #9b59b6, #667eea);
  border-radius: 14px;
  z-index: -1;
  opacity: 0.5;
  animation: featured-border 3s ease-in-out infinite;
}

@keyframes featured-wave {
  0%, 100% {
    box-shadow: 0 6px 25px rgba(52, 152, 219, 0.3);
    border-color: rgba(52, 152, 219, 0.5);
  }
  50% {
    box-shadow: 0 10px 35px rgba(52, 152, 219, 0.5);
    border-color: rgba(52, 152, 219, 0.7);
  }
}

@keyframes featured-border {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.ticker-item.featured-tier:hover {
  background: linear-gradient(135deg,
    rgba(52, 152, 219, 0.3) 0%,
    rgba(155, 89, 182, 0.25) 50%,
    rgba(102, 126, 234, 0.22) 100%
  );
  box-shadow: 0 12px 40px rgba(52, 152, 219, 0.5);
  transform: translateY(-5px) scale(1.06);
}

.salon-image {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  align-self: center;
  margin-top: 0.2rem;
  transition: none;
}

.salon-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Prominent Tier Label */
.tier-label-prominent {
  font-size: 0.45rem;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  margin-bottom: 0.1rem;
  padding: 1px 4px;
  border-radius: 3px;
  display: inline-block;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1;
  white-space: nowrap;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Tier-specific styling */
.elite-tier .tier-label-prominent {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #333;
  font-weight: 900;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.spotlight-tier .tier-label-prominent {
  background: linear-gradient(45deg, #FF1493, #FF6B9D);
  color: white;
}

.featured-tier .tier-label-prominent {
  background: linear-gradient(45deg, #00FFFF, #1E90FF);
  color: #333;
  font-weight: 900;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.salon-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.05rem;
  text-align: center;
  margin: 0 0.5rem;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.salon-name {
  font-size: 0.65rem;
  font-weight: 700;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1.1;
  max-width: 100%;
  position: relative;
  z-index: 3;
}

.salon-location {
  font-size: 0.55rem;
  margin: 0;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1;
}

.special-offer {
  font-size: 0.5rem;
  margin: 0;
  color: #FFD700;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1;
}

.cta-arrow-left,
.cta-arrow-right {
  font-size: 1rem;
  font-weight: bold;
  opacity: 0.7;
  transition: all 0.3s ease;
  flex-shrink: 0;
  align-self: center;
}

.ticker-item:hover .cta-arrow-left {
  opacity: 1;
  transform: translateX(-3px);
}

.ticker-item:hover .cta-arrow-right {
  opacity: 1;
  transform: translateX(3px);
}

/* Loading state */
.premium-ticker.loading {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ticker-loading {
  color: #FFD700;
  font-size: 0.9rem;
  font-weight: 600;
}

/* GenZ Mobile Experience */
@media (max-width: 768px) {
  .premium-ticker-container {
    top: 60px;
    background: linear-gradient(135deg,
      rgba(255, 20, 147, 0.15),
      rgba(138, 43, 226, 0.1),
      rgba(255, 215, 0, 0.1)
    );
    border-bottom: 2px solid rgba(255, 20, 147, 0.3);
    box-shadow: 0 4px 20px rgba(255, 20, 147, 0.2);
  }

  .premium-ticker-header {
    padding: 0.6rem 1rem;
    background: linear-gradient(90deg,
      rgba(255, 20, 147, 0.1),
      rgba(138, 43, 226, 0.1)
    );
    border-bottom: 1px solid rgba(255, 20, 147, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ticker-label {
    font-size: 0.9rem;
    font-weight: 800;
    background: linear-gradient(45deg, #FF1493, #FFD700, #8A2BE2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    letter-spacing: 0.5px;
  }

  .ticker-subtitle {
    font-size: 0.7rem;
    color: #FFD700;
    font-weight: 600;
    opacity: 0.9;
  }

  .premium-ticker {
    height: 100px;
    background: rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .ticker-content {
    animation: scroll-left-mobile 35s linear infinite;
    gap: 1rem;
    padding: 0.75rem 1rem;
    width: 200%;
  }

  @keyframes scroll-left-mobile {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
  }

  .ticker-item {
    min-width: 200px;
    max-width: 220px;
    padding: 0.5rem 0.7rem;
    gap: 0.4rem;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.05)
    );
    border: 1.5px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    min-height: 75px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .ticker-item:hover {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.25),
      rgba(255, 255, 255, 0.1)
    );
    box-shadow: 0 8px 25px rgba(255, 20, 147, 0.3);
  }

  .ticker-item:hover .salon-image {
    transform: translateY(-0.15rem);
    margin-top: 0.1rem;
  }

  .ticker-item:active {
    transform: translateY(-1px) scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  .salon-image {
    width: 35px;
    height: 35px;
    border: 1px solid rgba(255, 215, 0, 0.5);
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
    border-radius: 4px;
    align-self: center;
    margin-top: 0.15rem;
  }

  .tier-badge {
    background: linear-gradient(45deg, #FF1493, #FFD700);
    color: #000;
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .salon-name {
    font-size: 0.6rem;
    font-weight: 800;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    line-height: 1.1;
  }

  .tier-label-prominent {
    font-size: 0.4rem;
    padding: 1px 3px;
    margin-bottom: 0.05rem;
    border-radius: 2px;
  }

  .salon-location {
    font-size: 0.5rem;
    line-height: 1;
  }

  .special-offer {
    font-size: 0.45rem;
    line-height: 1;
  }

  .salon-location {
    font-size: 0.7rem;
    color: #FFD700;
    font-weight: 600;
    opacity: 0.9;
  }

  .special-offer {
    font-size: 0.65rem;
    background: linear-gradient(45deg, #FF1493, #FFD700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .cta-arrow-left,
  .cta-arrow-right {
    font-size: 1.4rem;
    background: linear-gradient(45deg, #FF1493, #FFD700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(255, 20, 147, 0.5));
  }
}

@media (max-width: 480px) {
  .premium-ticker-container {
    background: linear-gradient(135deg,
      rgba(255, 20, 147, 0.2),
      rgba(138, 43, 226, 0.15),
      rgba(255, 215, 0, 0.15),
      rgba(0, 255, 255, 0.1)
    );
    border-bottom: 2px solid rgba(255, 20, 147, 0.4);
  }

  .premium-ticker-header {
    padding: 0.5rem 0.75rem;
    flex-direction: row;
    justify-content: space-between;
  }

  .ticker-label {
    font-size: 0.8rem;
    animation: rainbow-pulse 3s ease-in-out infinite;
  }

  @keyframes rainbow-pulse {
    0%, 100% {
      background: linear-gradient(45deg, #FF1493, #FFD700, #8A2BE2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    50% {
      background: linear-gradient(45deg, #00FFFF, #FF1493, #FFD700);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .ticker-subtitle {
    font-size: 0.65rem;
    background: linear-gradient(45deg, #FFD700, #FF1493);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .premium-ticker {
    height: 95px;
  }

  .ticker-content {
    animation-duration: 30s;
    gap: 0.75rem;
    width: 200%;
  }

  .ticker-item {
    min-width: 260px;
    padding: 0.6rem 0.9rem;
    border-radius: 20px;
    border: 2px solid rgba(255, 20, 147, 0.3);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.2),
      rgba(255, 20, 147, 0.1),
      rgba(138, 43, 226, 0.05)
    );
    box-shadow: 0 6px 20px rgba(255, 20, 147, 0.2);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .ticker-item:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 10px 30px rgba(255, 20, 147, 0.4);
    border-color: rgba(255, 215, 0, 0.6);
  }

  .ticker-item:hover .salon-image {
    transform: translateY(-0.25rem);
    margin-top: 0.15rem;
  }

  .ticker-item:active {
    transform: translateY(-1px) scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  .salon-image {
    width: 45px;
    height: 45px;
    border: 2px solid rgba(255, 215, 0, 0.7);
    box-shadow: 0 3px 12px rgba(255, 215, 0, 0.4);
    align-self: center;
    margin-top: 0.25rem;
  }

  /* Tier badge banner removed - tier text now integrated into salon names */

  .salon-name {
    font-size: 0.75rem;
    font-weight: 800;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  .tier-label-prominent {
    font-size: 0.45rem;
    padding: 1px 3px;
    margin-bottom: 0.1rem;
    border-radius: 3px;
  }

  .salon-location {
    font-size: 0.65rem;
    font-weight: 700;
  }

  .special-offer {
    font-size: 0.6rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    animation: offer-pulse 2s ease-in-out infinite;
  }

  @keyframes offer-pulse {
    0%, 100% {
      background: linear-gradient(45deg, #FF1493, #FFD700);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    50% {
      background: linear-gradient(45deg, #FFD700, #8A2BE2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .cta-arrow-left,
  .cta-arrow-right {
    font-size: 1.3rem;
    animation: arrow-bounce 1.5s ease-in-out infinite;
  }

  @keyframes arrow-bounce {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(3px); }
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .ticker-item:hover {
    transform: none !important;
    background: inherit !important;
    box-shadow: inherit !important;
  }

  .ticker-item:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
}

/* Mobile-First Optimizations */
@media (max-width: 768px) {
  .premium-ticker-container {
    margin: 0;
    border-radius: 0;
  }

  .premium-ticker-header {
    padding: 6px 12px;
    gap: 0.4rem;
  }

  .ticker-label {
    font-size: 0.75rem;
  }

  .ticker-subtitle {
    font-size: 0.65rem;
  }

  .premium-ticker {
    height: 70px;
  }

  .ticker-content {
    gap: 1rem;
    padding: 0 0.5rem;
    animation: scroll-left 35s linear infinite;
    width: 200%;
  }

  .ticker-item {
    min-width: 160px;
    max-width: 180px;
    padding: 0.3rem 0.5rem;
    gap: 0.4rem;
    min-height: 70px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .salon-image {
    width: 30px;
    height: 30px;
    border-radius: 3px;
    align-self: center;
    margin-top: 0.1rem;
  }

  .salon-name {
    font-size: 0.55rem;
    font-weight: 700;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.1px;
    line-height: 1;
  }

  .tier-label-prominent {
    font-size: 0.35rem;
    padding: 0.5px 2px;
    margin-bottom: 0.02rem;
    border-radius: 2px;
  }

  .salon-location {
    font-size: 0.45rem;
    line-height: 1;
  }

  .special-offer {
    font-size: 0.4rem;
    line-height: 1;
  }

  .cta-arrow-left,
  .cta-arrow-right {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .premium-ticker-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .premium-ticker {
    height: 65px;
  }

  .ticker-content {
    animation: scroll-left 30s linear infinite;
    gap: 0.8rem;
    width: 200%;
  }

  .ticker-item {
    min-width: 160px;
    padding: 0.25rem 0.5rem;
    flex-shrink: 0;
    min-height: 60px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .salon-image {
    width: 26px;
    height: 26px;
  }

  .salon-name {
    font-size: 0.6rem;
  }

  .salon-location {
    font-size: 0.5rem;
  }

  .special-offer {
    font-size: 0.45rem;
  }

  /* Tier badge banner removed - tier text now integrated into salon names */
}
