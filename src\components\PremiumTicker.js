import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import './PremiumTicker.css';

const PremiumTicker = () => {
  const [premiumSalons, setPremiumSalons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const [randomizedSalons, setRandomizedSalons] = useState([]);
  const [clickingItem, setClickingItem] = useState(null);
  const tickerRef = useRef(null);

  // GenZ Premium salons data (replace with API call)
  const mockPremiumSalons = [
    {
      id: 1,
      name: "GLOW UP STUDIO",
      tier: "elite",
      tierLabel: "💎 ELITE",
      location: "Westlands",
      specialOffer: "20% off glow-up packages ✨",
      image: "https://images.unsplash.com/photo-1560066984-138dadb4c035?w=100&h=100&fit=crop&auto=format"
    },
    {
      id: 2,
      name: "VIBE BEAUTY CO",
      tier: "spotlight",
      tierLabel: "🔥 SPOTLIGHT",
      location: "<PERSON>",
      specialOffer: "free consultation + vibes check 💅",
      image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=100&h=100&fit=crop&auto=format"
    },
    {
      id: 3,
      name: "SLAY STATION",
      tier: "featured",
      tierLabel: "⚡ FEATURED",
      location: "CBD",
      specialOffer: "book 2 slay sessions, get 1 free 🎯",
      image: "https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=100&h=100&fit=crop&auto=format"
    },
    {
      id: 4,
      name: "LUXE VIBES SPA",
      tier: "elite",
      tierLabel: "💎 ELITE",
      location: "Kilimani",
      specialOffer: "VIP main character energy 👑",
      image: "https://images.unsplash.com/photo-1559599101-f09722fb4948?w=100&h=100&fit=crop&auto=format"
    },
    {
      id: 5,
      name: "FRESH CUTS HUB",
      tier: "featured",
      tierLabel: "⚡ FEATURED",
      location: "Eastlands",
      specialOffer: "student discounts + free wifi 📱",
      image: "https://images.unsplash.com/photo-1562322140-8baeececf3df?w=100&h=100&fit=crop&auto=format"
    },
    {
      id: 6,
      name: "AESTHETIC DREAMS",
      tier: "spotlight",
      tierLabel: "🔥 SPOTLIGHT",
      location: "Kileleshwa",
      specialOffer: "pinterest-worthy looks guaranteed 📸",
      image: "https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=100&h=100&fit=crop&auto=format"
    }
  ];

  // Randomization function for fairness
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  useEffect(() => {
    // Simulate API call
    const fetchPremiumSalons = async () => {
      try {
        setLoading(true);
        // Replace with actual API call
        // const response = await fetch('/api/premium-salons/');
        // const data = await response.json();

        // Using mock data for now
        setTimeout(() => {
          setPremiumSalons(mockPremiumSalons);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching premium salons:', error);
        setPremiumSalons(mockPremiumSalons);
        setLoading(false);
      }
    };

    fetchPremiumSalons();
  }, []);

  // Randomize salons every 30 seconds for fairness
  useEffect(() => {
    if (premiumSalons.length > 0) {
      const randomize = () => {
        setRandomizedSalons(shuffleArray(premiumSalons));
      };

      // Initial randomization
      randomize();

      // Re-randomize every 30 seconds
      const interval = setInterval(randomize, 30000);

      return () => clearInterval(interval);
    }
  }, [premiumSalons]);

  // Cleanup clicking state on unmount
  useEffect(() => {
    return () => {
      setClickingItem(null);
      setIsPaused(false);
    };
  }, []);

  const handleSalonClick = async (salon, event) => {
    // Prevent any animation interference during click
    event.preventDefault();
    event.stopPropagation();
    
    // Prevent multiple clicks
    if (clickingItem) return;
    
    setIsPaused(true);
    setClickingItem(salon.id);

    try {
      // Track click for analytics
      await fetch('/api/premium-analytics/click/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          salon_id: salon.id,
          user_ip: 'auto', // Backend will capture real IP
          referrer: window.location.href,
          session_id: sessionStorage.getItem('session_id') || 'anonymous'
        })
      });
    } catch (error) {
      console.error('Error tracking click:', error);
    }

    // Navigate after a brief delay to ensure smooth transition
    setTimeout(() => {
      window.location.href = `/salon/${salon.id}`;
    }, 200);
  };

  const getTierColor = (tier) => {
    switch (tier) {
      case 'elite': return 'elite-tier';
      case 'spotlight': return 'spotlight-tier';
      case 'featured': return 'featured-tier';
      default: return 'featured-tier';
    }
  };

  if (loading) {
    return (
      <div className="premium-ticker loading">
        <div className="ticker-loading">
          <span>Loading featured salons...</span>
        </div>
      </div>
    );
  }

  if (!randomizedSalons.length) {
    return null;
  }

  return (
    <div className="premium-ticker-container">
      <div className="premium-ticker-header">
        <span className="ticker-label">✨ Featured Salons</span>
        <span className="ticker-subtitle">exclusive vibes & deals 🔥</span>
      </div>
      
      <div 
        className={`premium-ticker ${isPaused ? 'paused' : ''}`}
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        ref={tickerRef}
      >
        <div className="ticker-content">
          {/* Duplicate content for seamless loop */}
          {[...randomizedSalons, ...randomizedSalons].map((salon, index) => (
            <Link
              key={`${salon.id}-${index}`}
              to={`/salon/${salon.id}`}
              className={`ticker-item ${getTierColor(salon.tier)} ${clickingItem === salon.id ? 'clicking' : ''}`}
              onClick={(event) => handleSalonClick(salon, event)}
            >
              <div className="cta-arrow-left">←</div>
              
              <div className="salon-image">
                <img
                  src={salon.image}
                  alt={salon.name}
                  loading="lazy"
                />
              </div>

              <div className="salon-info">
                <div className="tier-label-prominent">{salon.tierLabel}</div>
                <h4 className="salon-name">{salon.name}</h4>
              </div>
              
              <div className="cta-arrow-right">→</div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PremiumTicker;
