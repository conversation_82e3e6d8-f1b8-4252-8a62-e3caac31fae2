/* Pricing Display Component Styles */
.pricing-display {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.pricing-display:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.pricing-display.compact {
  padding: 1rem;
}

/* Main Pricing Section */
.pricing-main {
  margin-bottom: 1rem;
}

.service-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.service-info {
  flex: 1;
}

.service-name {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.service-duration {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  font-weight: 400;
}

.service-price {
  color: #00ffff;
  font-size: 1.3rem;
  font-weight: 700;
  text-align: right;
}

/* Cost Breakdown */
.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
}

.item-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.item-amount {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 600;
  text-align: right;
}

.fee-description {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-weight: 400;
}

/* Commission Item */
.commission-item {
  background: rgba(0, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.commission-item .item-label {
  color: #00ffff;
  font-weight: 600;
}

.commission-item .item-amount {
  color: #00ffff;
  font-weight: 700;
}

/* Fee Items */
.fee-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 0.5rem;
  margin: 0.25rem 0;
}

/* Divider */
.breakdown-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  margin: 1rem 0;
}

/* Total Item */
.total-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1rem;
  margin-top: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.total-item .item-label {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

.total-amount {
  color: #00ffff !important;
  font-size: 1.2rem !important;
  font-weight: 700 !important;
}

/* Compact Total */
.compact-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.total-label {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

/* Additional Info */
.pricing-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

.info-icon {
  font-size: 0.9rem;
  flex-shrink: 0;
}

.info-text {
  line-height: 1.3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pricing-display {
    padding: 1rem;
    border-radius: 12px;
  }

  .service-pricing {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .service-price {
    text-align: left;
    font-size: 1.1rem;
  }

  .breakdown-item {
    flex-direction: column;
    gap: 0.25rem;
  }

  .item-amount {
    text-align: left;
  }

  .compact-total {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .pricing-info {
    gap: 0.75rem;
  }

  .info-item {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .pricing-display {
    padding: 0.75rem;
    border-radius: 10px;
  }

  .service-name {
    font-size: 1rem;
  }

  .service-price {
    font-size: 1rem;
  }

  .total-amount {
    font-size: 1.1rem !important;
  }

  .breakdown-item {
    padding: 0.4rem 0;
  }

  .commission-item,
  .fee-item {
    padding: 0.6rem;
  }

  .total-item {
    padding: 0.8rem;
  }
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
  .pricing-display {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    border-color: rgba(255, 255, 255, 0.15);
  }
}

/* Animation for price updates */
.pricing-display {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects for interactive elements */
.breakdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: background 0.2s ease;
}

.info-item:hover {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.2s ease;
} 