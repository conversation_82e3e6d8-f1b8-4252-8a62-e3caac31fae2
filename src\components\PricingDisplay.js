import React from 'react';
import { calculateBookingCost, formatCurrency } from '../utils/pricingCalculator';
import './PricingDisplay.css';

const PricingDisplay = ({ service, options = {}, showBreakdown = true, compact = false }) => {
  if (!service || !service.price) {
    return null;
  }

  const costBreakdown = calculateBookingCost(service, options);
  const { breakdown } = costBreakdown;

  return (
    <div className={`pricing-display ${compact ? 'compact' : ''}`}>
      {/* Main Pricing Section */}
      <div className="pricing-main">
        <div className="service-pricing">
          <div className="service-info">
            <h4 className="service-name">{service.name}</h4>
            <span className="service-duration">{service.duration} min</span>
          </div>
          <div className="service-price">
            {formatCurrency(breakdown.service.price)}
          </div>
        </div>

        {/* Cost Breakdown */}
        {showBreakdown && (
          <div className="cost-breakdown">
            {/* Service Price */}
            <div className="breakdown-item">
              <span className="item-label">Service Price</span>
              <span className="item-amount">{formatCurrency(breakdown.service.price)}</span>
            </div>

            {/* Additional Fees */}
            {breakdown.additionalFees.amount > 0 && (
              <>
                {breakdown.additionalFees.breakdown.map((fee, index) => (
                  <div key={index} className="breakdown-item fee-item">
                    <span className="item-label">
                      {fee.name}
                      <span className="fee-description">{fee.description}</span>
                    </span>
                    <span className="item-amount">{formatCurrency(fee.amount)}</span>
                  </div>
                ))}
              </>
            )}

            {/* Platform Commission */}
            <div className="breakdown-item commission-item">
              <span className="item-label">
                Platform Fee ({breakdown.commission.percentage}%)
                <span className="fee-description">{breakdown.commission.description}</span>
              </span>
              <span className="item-amount">{formatCurrency(breakdown.commission.amount)}</span>
            </div>

            {/* Divider */}
            <div className="breakdown-divider"></div>

            {/* Total */}
            <div className="breakdown-item total-item">
              <span className="item-label">Total Amount</span>
              <span className="item-amount total-amount">{formatCurrency(breakdown.total)}</span>
            </div>
          </div>
        )}

        {/* Compact View - Just Total */}
        {!showBreakdown && (
          <div className="compact-total">
            <span className="total-label">Total</span>
            <span className="total-amount">{formatCurrency(breakdown.total)}</span>
          </div>
        )}
      </div>

      {/* Additional Info */}
      <div className="pricing-info">
        <div className="info-item">
          <span className="info-icon">💳</span>
          <span className="info-text">Secure payment processing</span>
        </div>
        <div className="info-item">
          <span className="info-icon">🔄</span>
          <span className="info-text">Free cancellation up to 24h before</span>
        </div>
        <div className="info-item">
          <span className="info-icon">⭐</span>
          <span className="info-text">Platform fee helps maintain quality service</span>
        </div>
      </div>
    </div>
  );
};

export default PricingDisplay; 