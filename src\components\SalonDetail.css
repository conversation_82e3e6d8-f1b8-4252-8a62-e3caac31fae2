/* Modern Salon Detail Page Styles */
.salon-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%);
  padding: 0;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #333;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 193, 7, 0.3);
  border-top: 3px solid #ffc107;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #333;
  padding: 2rem;
}

.error-state h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

/* Header */
.salon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 193, 7, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 193, 7, 0.1);
  transform: translateX(-2px);
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 193, 7, 0.1);
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background: rgba(255, 193, 7, 0.2);
  transform: scale(1.1);
}

.action-button.favorited {
  background: #e74c3c;
  color: white;
}

/* Hero Section */
.salon-hero {
  padding: 2rem 1.5rem;
  background: white;
  margin-bottom: 1rem;
}

.salon-image-container {
  position: relative;
  width: 100%;
  height: 250px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.salon-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.salon-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 193, 7, 0.95);
  color: #333;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.salon-info {
  text-align: center;
}

.salon-name {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.salon-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.salon-description {
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 1.5rem;
}

/* Quick Info Card for Mobile */
.quick-info-mobile {
  display: none;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.quick-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffc107;
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.2rem;
}

/* Contact Section */
.salon-contact {
  background: white;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.contact-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background: none;
  border: none;
  padding: 1rem 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.contact-toggle h3 {
  color: #333;
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.toggle-icon {
  color: #ffc107;
  transition: transform 0.3s ease;
}

.contact-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.contact-content.expanded {
  max-height: 200px;
  padding-top: 1rem;
}

.contact-grid {
  display: grid;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 193, 7, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.contact-item svg {
  color: #ffc107;
  font-size: 1.2rem;
}

.contact-label {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.2rem;
}

.contact-value {
  display: block;
  font-weight: 600;
  color: #333;
}

/* Services Section */
.salon-services {
  background: white;
  padding: 1.5rem 1.5rem;
  margin-bottom: 1rem;
}

.services-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.services-header h3 {
  color: #333;
  margin: 0;
  font-weight: 600;
}

.services-count {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.toggle-services-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 1rem;
  margin-top: 1rem;
  background: rgba(255, 193, 7, 0.1);
  border: 2px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-services-btn:hover {
  background: rgba(255, 193, 7, 0.2);
  transform: translateY(-1px);
}

.toggle-services-btn svg {
  color: #ffc107;
  transition: transform 0.3s ease;
}

.services-grid {
  display: grid;
  gap: 1rem;
}

.service-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 193, 7, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 193, 7, 0.2);
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
}

.service-info h4 {
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.service-description {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.service-price {
  text-align: right;
}

.price-label {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.2rem;
}

.price-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffc107;
}

.no-services {
  text-align: center;
  padding: 2rem;
  color: #666;
}

/* CTA Section */
.salon-cta {
  padding: 2rem 1.5rem;
  background: white;
  margin-bottom: 1rem;
}

.book-button {
  display: block;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ffc107, #ff8f00);
  color: #333;
  text-decoration: none;
  border-radius: 15px;
  text-align: center;
  font-weight: 700;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.book-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
  color: #333;
  text-decoration: none;
}

.book-button span:first-child {
  display: block;
  font-size: 1.2rem;
  margin-bottom: 0.2rem;
}

.book-subtitle {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Bookings Section */
.salon-bookings {
  background: white;
  padding: 2rem 1.5rem;
  margin-bottom: 1rem;
}

.salon-bookings h3 {
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 600;
}

.bookings-list {
  display: grid;
  gap: 1rem;
}

.booking-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.booking-info h4 {
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.booking-info p {
  color: #666;
  margin-bottom: 0.3rem;
}

.booking-date {
  font-size: 0.8rem;
  color: #999;
}

.booking-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.booking-status.confirmed {
  background: #d4edda;
  color: #155724;
}

.booking-status.pending {
  background: #fff3cd;
  color: #856404;
}

.no-bookings {
  text-align: center;
  padding: 2rem;
  color: #666;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .salon-header {
    padding: 1rem;
  }

  .salon-hero {
    padding: 1.5rem 1rem;
  }

  .salon-image-container {
    height: 200px;
  }

  .salon-name {
    font-size: 1.5rem;
  }

  .salon-meta {
    align-items: center;
  }

  .quick-info-mobile {
    display: grid;
  }

  .salon-contact,
  .salon-services,
  .salon-cta,
  .salon-bookings {
    padding: 1rem;
  }

  /* Mobile Contact Toggle */
  .contact-toggle {
    padding: 0.75rem 0;
  }

  .contact-toggle h3 {
    font-size: 1rem;
  }

  .contact-content.expanded {
    max-height: 150px;
  }

  /* Mobile Services */
  .services-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .services-count {
    align-self: flex-end;
  }

  .service-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }

  .service-price {
    text-align: left;
    align-self: flex-end;
  }

  .toggle-services-btn {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 320px) {
  .salon-header {
    padding: 0.75rem;
  }
  
  .salon-hero {
    padding: 1rem 0.75rem;
  }
  
  .salon-image-container {
    height: 180px;
  }
  
  .salon-name {
    font-size: 1.3rem;
  }
  
  .salon-contact,
  .salon-services,
  .salon-cta,
  .salon-bookings {
    padding: 1rem 0.75rem;
  }
}
