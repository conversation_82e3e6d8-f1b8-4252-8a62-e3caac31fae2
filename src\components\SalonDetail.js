// ESLint globally disabled during development
import React, { useEffect, useState } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaMapMarkerAlt, FaPhone, FaEnvelope, FaStar, FaClock, FaHeart, FaShare, FaInstagram, FaWhatsapp, FaCut } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import './SalonFinderProfile.css';
import './SalonDetailProfile.css';
import './SalonDetail.css';
import './SalonDetailModern.css';

const SalonDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { authFetch, user } = useAuth();
  console.log('SalonDetail: id param =', id);
  const [salon, setSalon] = useState(null);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const fetchSalonAndBookings = async () => {
      setLoading(true);
      setError(null);
      try {
        const salonRes = await authFetch(`/api/salons/${id}/`);
        console.log(`Fetching /api/salons/${id}/`, 'Status:', salonRes.status);
        if (!salonRes.ok) throw new Error('Failed to fetch salon');
        const salonData = await salonRes.json();
        console.log('Salon data:', salonData);
        setSalon(salonData);

        const bookingsRes = await authFetch(`/api/bookings/?salon=${id}`);
        if (!bookingsRes.ok) throw new Error('Failed to fetch bookings');
        const bookingsData = await bookingsRes.json();
        setBookings(bookingsData);
      } catch (err) {
        setError('Failed to load salon details or bookings.');
      }
      setLoading(false);
    };
    fetchSalonAndBookings();
  }, [id, authFetch]);

  // Handle responsive pagination
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      setCurrentPage(1); // Reset to first page on resize
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Smart pagination logic
  const getServicesPerPage = () => isMobile ? 2 : 3;
  const servicesPerPage = getServicesPerPage();
  const totalServices = salon?.services?.length || 0;
  const totalPages = Math.ceil(totalServices / servicesPerPage);

  const getCurrentPageServices = () => {
    if (!salon?.services) return [];
    const startIndex = (currentPage - 1) * servicesPerPage;
    const endIndex = startIndex + servicesPerPage;
    return salon.services.slice(startIndex, endIndex);
  };

  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages, prev + 1));
  };

  // Handle favorite functionality
  const handleFavorite = async () => {
    if (!user) {
      alert('Please login to add favorites');
      return;
    }

    try {
      const method = isFavorite ? 'DELETE' : 'POST';
      const response = await authFetch(`/api/favorites/`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ salon_id: salon.id })
      });

      if (response.ok) {
        setIsFavorite(!isFavorite);
      }
    } catch (error) {
      console.error('Error updating favorite:', error);
    }
  };

  // Handle share functionality
  const handleShare = async () => {
    const shareData = {
      title: salon.name,
      text: `Check out ${salon.name} - ${salon.description || 'Amazing salon services!'}`,
      url: window.location.href
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing:', error);
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (clipboardError) {
        console.error('Clipboard error:', clipboardError);
      }
    }
  };

  if (loading) {
    return (
      <div className="salon-detail-container">
        <div className="loading-state genz-loading">
          <div className="loading-emoji">✨</div>
          <p>loading the vibes...</p>
          <div className="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !salon) {
    return (
      <div className="salon-detail-container">
        <div className="error-state genz-error">
          <div className="error-emoji">😔</div>
          <h3>salon not found</h3>
          <p>{error || "this salon might be taking a glow up break"}</p>
          <button className="back-button genz-btn" onClick={() => navigate(-1)}>
            <FaArrowLeft />
            back to exploring ✨
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="salon-finder-profile-container salon-detail-theme">
      <div className="profile-container">
        {/* GenZ Sparkles */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">✨</span>
          <span className="auth-sparkle">💅</span>
          <span className="auth-sparkle">💫</span>
        </div>

        {/* Header Section */}
        <div className="profile-header salon-detail-header">
          <div className="header-actions-top">
            <button className="btn-secondary back-button" onClick={() => navigate(-1)}>
              <FaArrowLeft /> Back
            </button>
            <div className="action-buttons">
              <button
                className={`btn-secondary ${isFavorite ? 'favorited' : ''}`}
                onClick={handleFavorite}
                title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
              >
                <FaHeart />
              </button>
              <button
                className="btn-secondary"
                onClick={handleShare}
                title="Share this salon"
              >
                <FaShare />
              </button>
            </div>
          </div>

          <div className="auth-icon-wrapper salon-icon">
            <div className="auth-icon">
              <FaCut />
            </div>
          </div>
          <h1 className="profile-title">{salon.name}</h1>
          <p className="profile-subtitle">
            where your main character era begins ✨
          </p>
        </div>

        {/* Content Area */}
        <div className="profile-content">

          {/* Salon Details Section */}
          <div className="profile-section">
            <h3 className="section-title">
              ✨ Salon Vibes
            </h3>

            <div className="salon-hero-card">
              <div className="salon-image-container">
                <img
                  src={salon.imageUrl || salon.image_url || 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=800&h=600&fit=crop&auto=format'}
                  alt={salon.name}
                  className="salon-hero-image"
                  onError={(e) => {
                    e.target.src = 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=800&h=600&fit=crop&auto=format';
                  }}
                />
                <div className="salon-badge genz-badge">
                  <span className="badge-emoji">⭐</span>
                  <span>4.8</span>
                  <span className="badge-text">vibes</span>
                </div>
              </div>

              <div className="salon-info-card">
                <div className="salon-meta genz-meta">
                  <div className="meta-item">
                    <span className="meta-emoji">📍</span>
                    <span>{salon.town}, {salon.address}</span>
                  </div>
                  <div className="meta-item">
                    <span className="meta-emoji">🕐</span>
                    <span>open now • closes 8:00 PM</span>
                  </div>
                </div>
                <p className="salon-description genz-description">
                  {salon.description || "where your main character era begins ✨"}
                </p>

                {/* Quick Stats */}
                <div className="quick-stats-grid">
                  <div className="quick-stat">
                    <span className="stat-number">{salon.services?.length || 0}</span>
                    <span className="stat-label">Services</span>
                  </div>
                  <div className="quick-stat">
                    <span className="stat-number">4.8</span>
                    <span className="stat-label">Rating</span>
                  </div>
                  <div className="quick-stat">
                    <span className="stat-number">2.1km</span>
                    <span className="stat-label">Away</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="profile-section">
            <h3 className="section-title">
              ✨ Get In Touch
            </h3>
            <div className="contact-grid">
              <a href={`tel:${salon.phone}`} className="btn-secondary contact-btn">
                <FaPhone />
                <span>Call Now</span>
              </a>
              <a href={`mailto:${salon.email}`} className="btn-secondary contact-btn">
                <FaEnvelope />
                <span>Email Us</span>
              </a>
              <a href={`https://wa.me/${salon.phone?.replace(/\D/g, '')}`} className="btn-secondary contact-btn">
                <FaWhatsapp />
                <span>WhatsApp</span>
              </a>
              <a href={`https://instagram.com/${salon.name?.toLowerCase().replace(/\s+/g, '')}`} className="btn-secondary contact-btn">
                <FaInstagram />
                <span>Follow Us</span>
              </a>
            </div>
          </div>

          {/* Services Section */}
          <div className="profile-section">
            <h3 className="section-title">
              💅 Our Services
            </h3>

        {salon.services && salon.services.length > 0 ? (
          <>
            <div className="services-grid">
              {getCurrentPageServices().map(service => (
                <div key={service.id} className="service-card">
                  <h4 className="service-name">{service.name}</h4>
                  <p className="service-description">{service.description || 'Professional service'}</p>
                  <div className="service-price">
                    From <strong>Ksh {service.price}</strong>
                  </div>
                </div>
              ))}
            </div>


          </>
        ) : (
          <div className="no-services">
            <p>No services listed for this salon yet.</p>
          </div>
            )}
          </div>

          {/* CTA Section */}
          <div className="profile-section">
            <h3 className="section-title">🚀 Ready for Your Glow Up?</h3>
            <div className="cta-buttons">
              <Link to={`/book?salonId=${salon.id}`} className="btn-primary cta-primary">
                ✨ Book Your Appointment
              </Link>
              <button className="btn-secondary cta-secondary" onClick={() => window.open(`tel:${salon.phone}`)}>
                📞 Call Now
              </button>
            </div>

            {/* Services Pagination */}
            {totalPages > 1 && (
              <div className="services-pagination">
                <button
                  className="btn-secondary pagination-btn"
                  onClick={handlePrevPage}
                  disabled={currentPage === 1}
                >
                  ← Previous
                </button>

                <div className="pagination-info">
                  Page {currentPage} of {totalPages}
                  <br />
                  <small>({totalServices} services total)</small>
                </div>

                <button
                  className="btn-secondary pagination-btn"
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                >
                  Next →
                </button>
              </div>
            )}
          </div>

          {/* Admin Bookings Section */}
          {user && user.username === salon.vendor_username && (
            <div className="profile-section">
              <h3 className="section-title">Recent Bookings</h3>
              {bookings.length > 0 ? (
                <div className="bookings-grid">
                  {bookings.map(booking => (
                    <div key={booking.id} className="booking-card">
                      <div className="booking-info">
                        <h4>{booking.userName}</h4>
                        <p>{booking.service_name} with {booking.staff_name}</p>
                        <span className="booking-date">{booking.date} at {booking.time}</span>
                      </div>
                      <div className={`booking-status ${booking.status.toLowerCase()}`}>
                        {booking.status}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="no-results">
                  <p>No bookings yet for this salon.</p>
                </div>
              )}
            </div>
          )}

          {/* Floating Action Button */}
          <div className="floating-action">
            <Link to={`/book?salonId=${salon.id}`} className="btn-primary floating-book-btn" title="Book Appointment">
              📅 Book Now
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalonDetail;
