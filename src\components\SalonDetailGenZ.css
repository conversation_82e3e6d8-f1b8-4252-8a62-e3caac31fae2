/* GenZ Salon Detail Page Styles */
.salon-detail-genz {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(255, 20, 147, 0.1), 
    rgba(138, 43, 226, 0.05), 
    rgba(255, 215, 0, 0.05),
    rgba(0, 255, 255, 0.05)
  );
  color: white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Hero Section */
.salon-hero {
  position: relative;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.6), 
    rgba(255, 20, 147, 0.3),
    rgba(138, 43, 226, 0.2)
  );
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
  text-align: center;
  max-width: 600px;
  padding: 2rem;
}

/* Tier Badges */
.tier-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  border: 2px solid;
}

.elite-badge {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));
  border-color: #FFD700;
  color: #FFD700;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
}

.spotlight-badge {
  background: linear-gradient(45deg, rgba(255, 20, 147, 0.2), rgba(138, 43, 226, 0.1));
  border-color: #FF1493;
  color: #FF1493;
  box-shadow: 0 4px 20px rgba(255, 20, 147, 0.3);
}

.featured-badge {
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(52, 152, 219, 0.1));
  border-color: #00FFFF;
  color: #00FFFF;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
}

.salon-name {
  font-size: 3rem;
  font-weight: 900;
  margin: 0 0 1rem 0;
  background: linear-gradient(45deg, #FFFFFF, #FFD700, #FF1493);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.salon-description {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  font-weight: 500;
}

.salon-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.stat-emoji {
  font-size: 1.2rem;
}

.stat-value {
  font-weight: 700;
}

.stat-label {
  opacity: 0.8;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.book-now-btn, .call-btn {
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: lowercase;
}

.book-now-btn.primary {
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
  box-shadow: 0 4px 15px rgba(255, 20, 147, 0.4);
}

.book-now-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 20, 147, 0.6);
}

.call-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.call-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Navigation Tabs */
.detail-nav {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 1rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 140px;
  z-index: 100;
}

.nav-tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: lowercase;
  backdrop-filter: blur(5px);
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.nav-tab.active {
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
  box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
}

/* Content Sections */
.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.detail-content h2 {
  font-size: 2.5rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 3rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: lowercase;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.service-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(255, 20, 147, 0.2);
}

.service-emoji {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.service-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-transform: lowercase;
}

.service-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.service-price {
  color: #FFD700;
  font-size: 1.2rem;
}

.service-duration {
  opacity: 0.8;
}

.add-service-btn {
  background: linear-gradient(45deg, #FF1493, #8A2BE2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: lowercase;
}

.add-service-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 20, 147, 0.4);
}

/* Loading and Error States */
.salon-detail-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(255, 20, 147, 0.1), 
    rgba(138, 43, 226, 0.05)
  );
}

.loading-animation {
  text-align: center;
  color: white;
}

.loading-emoji {
  font-size: 4rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.salon-not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(255, 20, 147, 0.1), 
    rgba(138, 43, 226, 0.05)
  );
}

.not-found-content {
  text-align: center;
  color: white;
}

.not-found-emoji {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.back-home-btn {
  display: inline-block;
  margin-top: 2rem;
  padding: 1rem 2rem;
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 700;
  transition: all 0.3s ease;
}

.back-home-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .salon-name {
    font-size: 2rem;
  }
  
  .salon-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .detail-nav {
    top: 60px;
    padding: 1rem 0.5rem;
    gap: 0.25rem;
  }
  
  .nav-tab {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .service-card {
    padding: 1.5rem;
  }
}
