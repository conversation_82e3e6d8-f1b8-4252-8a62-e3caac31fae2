/* ===== GEN Z MODERN SALON DETAILS PAGE ===== */

/* Foundation & Variables */
:root {
  --genz-primary: #FF1493;
  --genz-secondary: #8A2BE2;
  --genz-accent: #FFD700;
  --genz-cyan: #00FFFF;
  --genz-dark: #0f0f17;
  --genz-glass: rgba(255, 255, 255, 0.1);
  --genz-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --genz-blur: blur(16px);
}

/* Mobile-First Container */
.salon-detail-container {
  background: linear-gradient(135deg,
    rgba(15, 15, 23, 0.98) 0%,
    rgba(25, 25, 35, 0.96) 25%,
    rgba(20, 20, 30, 0.97) 50%,
    rgba(30, 30, 40, 0.95) 75%,
    rgba(15, 15, 23, 0.98) 100%) !important;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

/* Animated Background Elements */
.salon-detail-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
  animation: float-bg 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes float-bg {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

/* Mobile-First Header */
.salon-header {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 100%) !important;
  backdrop-filter: var(--genz-blur) saturate(180%);
  border-bottom: 1px solid rgba(255, 215, 0, 0.15);
  padding: 0.75rem 1rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-button {
  background: linear-gradient(135deg, var(--genz-primary), var(--genz-secondary)) !important;
  border: none !important;
  color: white !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 25px !important;
  font-weight: 700 !important;
  font-size: 0.9rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.back-button:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: 0 8px 25px rgba(255, 20, 147, 0.4) !important;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.action-button {
  background: var(--genz-glass) !important;
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  font-size: 1.1rem !important;
}

.action-button:hover {
  background: rgba(255, 215, 0, 0.2) !important;
  transform: translateY(-2px) scale(1.1) !important;
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3) !important;
}

.action-button.favorited {
  background: linear-gradient(135deg, var(--genz-primary), #ff6b9d) !important;
  color: white !important;
}

/* Mobile-First Hero Section */
.salon-hero {
  padding: 1rem !important;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(245, 245, 250, 0.02) 0%,
    rgba(240, 240, 245, 0.03) 50%,
    rgba(235, 235, 240, 0.02) 100%);
}

.salon-image-container {
  position: relative;
  height: 200px !important;
  border-radius: 16px !important;
  overflow: hidden;
  margin-bottom: 1rem;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.4),
    0 4px 12px rgba(255, 20, 147, 0.2) !important;
}

.salon-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.salon-image-container:hover .salon-hero-image {
  transform: scale(1.05);
}

/* Glass Morphism Badge */
.salon-badge {
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.9) 0%,
    rgba(255, 179, 71, 0.9) 100%) !important;
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--genz-dark) !important;
  padding: 0.75rem 1.25rem !important;
  border-radius: 20px !important;
  font-weight: 800 !important;
  font-size: 0.9rem !important;
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.badge-emoji {
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

/* Enhanced Salon Info */
.salon-info {
  text-align: center;
  position: relative;
}

.salon-name {
  font-size: 1.8rem !important;
  font-weight: 900 !important;
  background: linear-gradient(135deg,
    var(--genz-primary) 0%,
    var(--genz-secondary) 50%,
    var(--genz-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem !important;
  line-height: 1.2 !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Mobile-First Meta Information */
.salon-meta {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.75rem !important;
  margin-bottom: 1rem !important;
}

.meta-item {
  background: var(--genz-glass);
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 0.9rem !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.meta-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.2);
}

.meta-emoji {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Mobile-First Description */
.salon-description {
  background: var(--genz-glass);
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.85) !important;
  font-size: 0.95rem !important;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.salon-description::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--genz-accent) 50%,
    transparent 100%);
}

/* Mobile-First Contact Section */
.salon-contact {
  background: var(--genz-glass) !important;
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  border-radius: 16px !important;
  padding: 1.25rem 1rem !important;
  margin: 1rem !important;
  position: relative;
  overflow: hidden;
}

.salon-contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    var(--genz-primary) 0%,
    var(--genz-secondary) 50%,
    var(--genz-accent) 100%);
}

.salon-contact h3 {
  color: white !important;
  font-size: 1.2rem !important;
  font-weight: 800 !important;
  margin-bottom: 1rem !important;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.contact-item {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.85rem;
  text-align: center;
}

.contact-item:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.2);
  color: white;
  text-decoration: none;
}

.contact-item i {
  font-size: 1.2rem;
  color: var(--genz-accent);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Mobile-First Services Section */
.salon-services {
  background: var(--genz-glass) !important;
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  border-radius: 16px !important;
  padding: 1.25rem 1rem !important;
  margin: 1rem !important;
  position: relative;
  overflow: hidden;
}

.salon-services::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    var(--genz-cyan) 0%,
    var(--genz-primary) 50%,
    var(--genz-secondary) 100%);
}

.salon-services h3 {
  color: white !important;
  font-size: 1.2rem !important;
  font-weight: 800 !important;
  margin-bottom: 1rem !important;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Mobile-First Service Cards - 2 per row on mobile */
.services-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.service-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100px;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg,
    var(--genz-primary) 0%,
    var(--genz-accent) 100%);
}

.service-card:hover {
  transform: translateY(-4px) scale(1.02);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.1) 100%);
  box-shadow: 0 12px 30px rgba(255, 20, 147, 0.3);
  border-color: rgba(255, 215, 0, 0.3);
}

.service-name {
  color: white !important;
  font-size: 0.75rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.2rem !important;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  line-height: 1.1;
}

.service-description {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 0.65rem !important;
  margin-bottom: 0.3rem !important;
  line-height: 1.2;
  flex: 1;
}

.service-price {
  color: var(--genz-accent) !important;
  font-size: 0.7rem !important;
  font-weight: 800 !important;
  text-align: center;
  margin-top: auto;
}

/* Compact Pagination Controls for CTA Section */
.services-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.pagination-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 15px !important;
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.3px !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  min-width: 80px;
}

.pagination-btn:disabled {
  background: rgba(255, 255, 255, 0.05) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  min-width: 100px;
  line-height: 1.3;
}

.pagination-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.pagination-btn:hover::before {
  left: 100%;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--genz-accent) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

/* Mobile-First CTA Section */
.salon-cta {
  background: linear-gradient(135deg,
    rgba(255, 20, 147, 0.12) 0%,
    rgba(138, 43, 226, 0.12) 100%) !important;
  backdrop-filter: var(--genz-blur);
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  border-radius: 16px !important;
  padding: 1.5rem 1rem !important;
  margin: 1rem !important;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.salon-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    var(--genz-primary) 0%,
    var(--genz-accent) 50%,
    var(--genz-cyan) 100%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.salon-cta h3 {
  color: white !important;
  font-size: 1.3rem !important;
  font-weight: 900 !important;
  margin-bottom: 1rem !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-buttons {
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.cta-primary {
  background: linear-gradient(135deg,
    var(--genz-accent) 0%,
    #ffb347 100%) !important;
  border: none !important;
  color: var(--genz-dark) !important;
  padding: 0.75rem 1.25rem !important;
  border-radius: 20px !important;
  font-weight: 800 !important;
  font-size: 0.85rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.3px !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  flex: 1;
  min-width: 140px;
}

.cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.cta-primary:hover::before {
  left: 100%;
}

.cta-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
}

.cta-secondary {
  background: var(--genz-glass) !important;
  backdrop-filter: var(--genz-blur);
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  padding: 0.75rem 1.25rem !important;
  border-radius: 20px !important;
  font-weight: 700 !important;
  font-size: 0.85rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.3px !important;
  transition: all 0.3s ease !important;
  flex: 1;
  min-width: 120px;
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--genz-accent) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.2);
}

/* Floating Action Button */
.floating-book-btn {
  position: fixed;
  bottom: 100px;
  right: 20px;
  background: linear-gradient(135deg,
    var(--genz-primary) 0%,
    var(--genz-secondary) 100%);
  border: none;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 1.5rem;
  box-shadow: 0 8px 25px rgba(255, 20, 147, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
  animation: pulse-float 3s ease-in-out infinite;
}

@keyframes pulse-float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    box-shadow: 0 8px 25px rgba(255, 20, 147, 0.4);
  }
  50% {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 20, 147, 0.6);
  }
}

.floating-book-btn:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 15px 40px rgba(255, 20, 147, 0.6);
}

/* Desktop Optimizations - Better Space Utilization */
@media (min-width: 768px) {
  .salon-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .salon-header {
    padding: 1rem 2rem;
    border-radius: 0 0 16px 16px;
  }

  .salon-hero {
    padding: 2rem !important;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
  }

  .salon-image-container {
    height: 350px !important;
    margin-bottom: 0;
    border-radius: 20px !important;
  }

  .salon-info {
    text-align: left;
  }

  .salon-name {
    font-size: 2.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .salon-meta {
    flex-direction: row !important;
    gap: 1rem !important;
    margin-bottom: 1.5rem !important;
  }

  .meta-item {
    flex: 1;
    padding: 1rem;
    font-size: 0.95rem !important;
  }

  .salon-description {
    font-size: 1.1rem !important;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  /* Two-column layout for contact and services */
  .content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
  }

  .salon-contact,
  .salon-services {
    margin: 0 !important;
    padding: 2rem !important;
    border-radius: 20px !important;
  }

  .contact-info {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .contact-item {
    padding: 1rem;
    font-size: 0.9rem;
  }

  .services-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .service-card {
    padding: 1rem;
    min-height: 130px;
  }

  .service-name {
    font-size: 0.9rem !important;
    margin-bottom: 0.4rem !important;
  }

  .service-description {
    font-size: 0.8rem !important;
    margin-bottom: 0.6rem !important;
  }

  .service-price {
    font-size: 0.85rem !important;
  }

  .salon-cta h3 {
    font-size: 1.6rem !important;
  }

  .cta-primary,
  .cta-secondary {
    font-size: 0.95rem !important;
    padding: 0.9rem 1.5rem !important;
  }

  .salon-cta {
    margin: 2rem 0 !important;
    padding: 2rem !important;
    border-radius: 20px !important;
  }

  .cta-buttons {
    flex-direction: row;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
  }

  .floating-book-btn {
    bottom: 30px;
    right: 30px;
    width: 65px;
    height: 65px;
    font-size: 1.5rem;
  }
}

/* Large Desktop Optimizations */
@media (min-width: 1200px) {
  .salon-detail-container {
    max-width: 1400px;
  }

  .salon-hero {
    padding: 3rem !important;
    gap: 3rem;
  }

  .salon-image-container {
    height: 400px !important;
  }

  .salon-name {
    font-size: 3rem !important;
  }

  .content-grid {
    gap: 3rem;
    margin: 3rem 0;
  }

  .salon-contact,
  .salon-services {
    padding: 2.5rem !important;
  }

  .services-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .salon-name {
    font-size: 1.8rem !important;
  }

  .salon-image-container {
    height: 200px !important;
    border-radius: 16px !important;
  }

  .salon-contact,
  .salon-services,
  .salon-cta {
    border-radius: 16px !important;
    margin: 1rem 0.75rem !important;
    padding: 1.25rem !important;
  }

  .back-button {
    padding: 0.6rem 1.2rem !important;
    font-size: 0.8rem !important;
  }

  .action-button {
    width: 42px !important;
    height: 42px !important;
    font-size: 1rem !important;
  }

  .floating-book-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    bottom: 80px;
  }
}
