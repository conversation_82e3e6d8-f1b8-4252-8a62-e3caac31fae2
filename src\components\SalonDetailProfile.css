/* Salon Detail Page - GenZ Theme with Profile Design Pattern */

/* Salon Detail Theme Override */
.salon-finder-profile-container.salon-detail-theme {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 0.5rem;
}

.salon-finder-profile-container.salon-detail-theme::before {
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="salon-grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23salon-grain)"/></svg>');
  opacity: 0.3;
}

/* Salon Detail Header */
.salon-finder-profile-container.salon-detail-theme .profile-header.salon-detail-header {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  padding: 1rem;
  position: relative;
}

.salon-finder-profile-container.salon-detail-theme .header-actions-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.salon-finder-profile-container.salon-detail-theme .action-buttons {
  display: flex;
  gap: 0.5rem;
}

.salon-finder-profile-container.salon-detail-theme .back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.salon-finder-profile-container.salon-detail-theme .favorited {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%) !important;
  color: white !important;
}

.salon-finder-profile-container.salon-detail-theme .auth-icon-wrapper.salon-icon {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%);
  box-shadow: 0 4px 16px rgba(255, 107, 157, 0.4);
}

/* GenZ Sparkles */
.salon-finder-profile-container.salon-detail-theme .auth-sparkle:nth-child(1) {
  color: #ff6b9d;
}

.salon-finder-profile-container.salon-detail-theme .auth-sparkle:nth-child(2) {
  color: #ffd700;
}

.salon-finder-profile-container.salon-detail-theme .auth-sparkle:nth-child(3) {
  color: #667eea;
}

/* Salon Hero Card */
.salon-finder-profile-container.salon-detail-theme .salon-hero-card {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.salon-finder-profile-container.salon-detail-theme .salon-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.salon-finder-profile-container.salon-detail-theme .salon-hero-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
}

.salon-finder-profile-container.salon-detail-theme .genz-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 4px 16px rgba(255, 107, 157, 0.3);
}

.salon-finder-profile-container.salon-detail-theme .salon-info-card {
  padding: 1.5rem;
}

.salon-finder-profile-container.salon-detail-theme .genz-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.salon-finder-profile-container.salon-detail-theme .meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.salon-finder-profile-container.salon-detail-theme .meta-emoji {
  font-size: 1.2rem;
}

.salon-finder-profile-container.salon-detail-theme .genz-description {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-style: italic;
}

/* Quick Stats Grid */
.salon-finder-profile-container.salon-detail-theme .quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.salon-finder-profile-container.salon-detail-theme .quick-stat {
  text-align: center;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.salon-finder-profile-container.salon-detail-theme .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.salon-finder-profile-container.salon-detail-theme .stat-label {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Contact Grid */
.salon-finder-profile-container.salon-detail-theme .contact-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.salon-finder-profile-container.salon-detail-theme .contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.salon-finder-profile-container.salon-detail-theme .contact-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Services Grid */
.salon-finder-profile-container.salon-detail-theme .services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.salon-finder-profile-container.salon-detail-theme .service-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.salon-finder-profile-container.salon-detail-theme .service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

/* Bookings Grid */
.salon-finder-profile-container.salon-detail-theme .bookings-grid {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.salon-finder-profile-container.salon-detail-theme .booking-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(102, 126, 234, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Floating Action */
.salon-finder-profile-container.salon-detail-theme .floating-action {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.salon-finder-profile-container.salon-detail-theme .floating-book-btn {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.salon-finder-profile-container.salon-detail-theme .floating-book-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(255, 107, 157, 0.5);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .salon-finder-profile-container.salon-detail-theme .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .salon-finder-profile-container.salon-detail-theme .quick-stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }
  
  .salon-finder-profile-container.salon-detail-theme .quick-stat {
    padding: 0.75rem 0.5rem;
  }
  
  .salon-finder-profile-container.salon-detail-theme .stat-number {
    font-size: 1.2rem;
  }
  
  .salon-finder-profile-container.salon-detail-theme .floating-action {
    bottom: 1rem;
    right: 1rem;
  }
}

@media (max-width: 480px) {
  .salon-finder-profile-container.salon-detail-theme .salon-image-container {
    height: 150px;
  }
  
  .salon-finder-profile-container.salon-detail-theme .salon-info-card {
    padding: 1rem;
  }
  
  .salon-finder-profile-container.salon-detail-theme .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .salon-finder-profile-container.salon-detail-theme .salon-hero-card,
  .salon-finder-profile-container.salon-detail-theme .service-card,
  .salon-finder-profile-container.salon-detail-theme .booking-card {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(102, 126, 234, 0.3);
  }
}
