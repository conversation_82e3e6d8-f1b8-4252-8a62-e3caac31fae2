/* SalonGenz Salon Finder Profile Design Pattern */

/* CRITICAL: Override global responsive image styles with maximum specificity */
.salon-finder-profile-container .profile-container .profile-section .salon-grid .salon-card .salon-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
  min-height: 140px !important;
  max-height: 140px !important;
  aspect-ratio: 16/9 !important;
}

/* Main Container */
.salon-finder-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: relative;
  overflow: hidden;
}

.salon-finder-profile-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

/* Profile Container */
.salon-finder-profile-container .profile-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

/* Floating Effects */
.salon-finder-profile-container .auth-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.salon-finder-profile-container .auth-sparkle {
  position: absolute;
  font-size: 1.5rem;
  animation: sparkleFloat 6s ease-in-out infinite;
  opacity: 0.7;
}

.salon-finder-profile-container .auth-sparkle:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.salon-finder-profile-container .auth-sparkle:nth-child(2) {
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.salon-finder-profile-container .auth-sparkle:nth-child(3) {
  bottom: 15%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes sparkleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

/* Header */
.salon-finder-profile-container .profile-header {
  text-align: center;
  padding: 1.5rem 1rem 1rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 24px 24px 0 0;
  position: relative;
  z-index: 1;
}

.salon-finder-profile-container .auth-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.salon-finder-profile-container .auth-icon {
  color: white;
  font-size: 1.5rem;
}

.salon-finder-profile-container .profile-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.salon-finder-profile-container .profile-subtitle {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* Content */
.salon-finder-profile-container .profile-content {
  padding: 1rem;
}

/* Sections */
.salon-finder-profile-container .profile-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.salon-finder-profile-container .section-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Form Elements */
.salon-finder-profile-container .form-group {
  margin-bottom: 1.5rem;
}

.salon-finder-profile-container .form-label {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
}

.salon-finder-profile-container .form-input {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.salon-finder-profile-container .form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Search Input Group */
.salon-finder-profile-container .search-input-group {
  position: relative;
  margin-bottom: 1rem;
}

.salon-finder-profile-container .search-icon {
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 1.2rem;
}

.salon-finder-profile-container .search-input {
  padding-left: 4rem;
}

/* Buttons */
.salon-finder-profile-container .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  align-self: flex-start;
}

.salon-finder-profile-container .btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.salon-finder-profile-container .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.salon-finder-profile-container .btn-secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2px solid rgba(102, 126, 234, 0.2);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.salon-finder-profile-container .btn-secondary:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* Near Me Button Special Styling */
.salon-finder-profile-container .near-me-btn {
  background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%) !important;
  color: white !important;
  border-color: #ff8c42 !important;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(255, 140, 66, 0.3);
}

.salon-finder-profile-container .near-me-btn:hover {
  background: linear-gradient(135deg, #ff6b35 0%, #ff5722 100%) !important;
  border-color: #ff6b35 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 140, 66, 0.4);
}

/* Filter Grid */
.salon-finder-profile-container .filter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.salon-finder-profile-container .filter-item {
  display: flex;
  flex-direction: column;
}

.salon-finder-profile-container .filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #555;
}

/* Quick Filter Buttons */
.salon-finder-profile-container .quick-filter-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.4rem;
}

.salon-finder-profile-container .quick-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Loading State */
.salon-finder-profile-container .loading-state {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.salon-finder-profile-container .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No Results */
.salon-finder-profile-container .no-results {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.salon-finder-profile-container .no-results-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 1rem;
}

/* Results Header */
.salon-finder-profile-container .results-header {
  margin-bottom: 2rem;
  text-align: center;
}

.salon-finder-profile-container .results-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.salon-finder-profile-container .results-subtitle {
  color: #666;
  font-size: 1rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .salon-finder-profile-container {
    padding: 0.25rem;
  }

  .salon-finder-profile-container .profile-header {
    padding: 1.25rem 0.75rem 1rem;
  }

  .salon-finder-profile-container .profile-title {
    font-size: 1.6rem;
  }

  .salon-finder-profile-container .profile-subtitle {
    font-size: 0.85rem;
  }

  .salon-finder-profile-container .profile-content {
    padding: 0.75rem;
  }

  .salon-finder-profile-container .profile-section {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }

  .salon-finder-profile-container .filter-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .salon-finder-profile-container .quick-filter-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.3rem;
  }

  .salon-finder-profile-container .form-input {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .salon-finder-profile-container .search-input {
    padding-left: 3rem;
  }
}

@media (max-width: 480px) {
  .salon-finder-profile-container .auth-icon-wrapper {
    width: 60px;
    height: 60px;
  }
  
  .salon-finder-profile-container .auth-icon {
    font-size: 1.5rem;
  }
  
  .salon-finder-profile-container .profile-title {
    font-size: 1.8rem;
  }
  
  .salon-finder-profile-container .profile-subtitle {
    font-size: 1rem;
  }
}

/* Salon Grid */
.salon-finder-profile-container .salon-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.salon-finder-profile-container .salon-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  min-height: 280px;
}

.salon-finder-profile-container .salon-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.salon-finder-profile-container .salon-image {
  position: relative;
  height: 140px;
  overflow: hidden;
  flex-shrink: 0;
}

.salon-finder-profile-container .salon-image img {
  width: 100% !important;
  height: 140px !important;
  object-fit: cover !important;
  transition: transform 0.3s ease;
  display: block !important;
  min-height: 140px !important;
  max-height: 140px !important;
  aspect-ratio: auto !important;
}

.salon-finder-profile-container .salon-card:hover .salon-image img {
  transform: scale(1.05);
}

.salon-finder-profile-container .salon-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.salon-finder-profile-container .salon-info {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
}

.salon-finder-profile-container .salon-name {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.salon-finder-profile-container .salon-location {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.salon-finder-profile-container .salon-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.salon-finder-profile-container .salon-status {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 50px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
}

.salon-finder-profile-container .salon-distance {
  background: rgba(255, 140, 66, 0.1);
  color: #ff8c42;
  padding: 0.2rem 0.5rem;
  border-radius: 50px;
  font-size: 0.7rem;
  font-weight: 600;
  border: 1px solid rgba(255, 140, 66, 0.2);
  white-space: nowrap;
  flex-shrink: 0;
}

.salon-finder-profile-container .view-details-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
  margin-top: auto;
}

.salon-finder-profile-container .view-details-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* Pagination */
.salon-finder-profile-container .pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* Responsive Salon Grid */
@media (min-width: 576px) {
  .salon-finder-profile-container .salon-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

@media (min-width: 768px) {
  .salon-finder-profile-container .salon-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 992px) {
  .salon-finder-profile-container .salon-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 1200px) {
  .salon-finder-profile-container .salon-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .salon-finder-profile-container .salon-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .salon-finder-profile-container .salon-image {
    height: 120px !important;
  }

  .salon-finder-profile-container .salon-image img {
    height: 120px !important;
    min-height: 120px !important;
    max-height: 120px !important;
  }

  .salon-finder-profile-container .salon-info {
    padding: 0.75rem;
  }

  .salon-finder-profile-container .salon-name {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }

  .salon-finder-profile-container .salon-location {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }

  .salon-finder-profile-container .salon-meta {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
  }

  .salon-finder-profile-container .salon-status {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .salon-finder-profile-container .salon-distance {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .salon-finder-profile-container .view-details-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .salon-finder-profile-container .salon-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .salon-finder-profile-container .salon-status,
  .salon-finder-profile-container .salon-distance {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
  }
}
