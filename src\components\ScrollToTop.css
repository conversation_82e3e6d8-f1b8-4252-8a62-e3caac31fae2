/* Sleek Scroll to Top - Gen Z Elegant Design */

.scroll-to-top {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 998;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(100px) scale(0.8);
  opacity: 0;
  pointer-events: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.scroll-to-top.visible {
  transform: translateY(0) scale(1);
  opacity: 1;
  pointer-events: all;
}

.scroll-to-top:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 40px rgba(255, 215, 0, 0.3);
}

.scroll-to-top:active {
  transform: translateY(0) scale(0.95);
}

/* Progress Ring */
.progress-ring {
  position: absolute;
  top: 0;
  left: 0;
  transform: rotate(-90deg);
  transition: all 0.3s ease;
}

.progress-ring-background {
  opacity: 0.3;
}

.progress-ring-progress {
  transition: stroke-dashoffset 0.3s ease;
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.5));
}

/* Arrow Icon */
.arrow-icon {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.scroll-to-top:hover .arrow-icon {
  transform: translateY(-2px);
  color: #ffd700;
}

.arrow-icon svg {
  transition: all 0.3s ease;
}

.scroll-to-top:hover .arrow-icon svg {
  transform: scale(1.1);
}

/* Glow Effect */
.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.3s ease;
  pointer-events: none;
}

.scroll-to-top:hover .glow-effect {
  transform: translate(-50%, -50%) scale(1.5);
}

/* Pulse animation when first appearing */
.scroll-to-top.visible {
  animation: pulse-appear 0.6s ease-out;
}

@keyframes pulse-appear {
  0% {
    transform: translateY(100px) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateY(-5px) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Floating animation */
.scroll-to-top.visible {
  animation: float-gentle 3s ease-in-out infinite;
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-3px) scale(1);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 120px; /* Above mobile nav */
    right: 16px;
    width: 44px;
    height: 44px;
  }
  
  .progress-ring {
    width: 44px;
    height: 44px;
  }
  
  .progress-ring-background,
  .progress-ring-progress {
    cx: 22;
    cy: 22;
    r: 18;
  }
  
  .arrow-icon svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    bottom: 110px;
    right: 12px;
    width: 40px;
    height: 40px;
  }
  
  .progress-ring {
    width: 40px;
    height: 40px;
  }
  
  .progress-ring-background,
  .progress-ring-progress {
    cx: 20;
    cy: 20;
    r: 16;
  }
  
  .arrow-icon svg {
    width: 12px;
    height: 12px;
  }
}

/* Landscape mode adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .scroll-to-top {
    bottom: 80px;
    right: 12px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .scroll-to-top {
    background: #000;
    border: 2px solid #fff;
    color: #fff;
  }
  
  .progress-ring-progress {
    stroke: #fff;
  }
  
  .scroll-to-top:hover {
    background: #333;
    box-shadow: 0 0 0 2px #fff;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .scroll-to-top {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  .scroll-to-top.visible {
    animation: none;
  }
  
  .scroll-to-top:hover {
    transform: none;
  }
  
  .arrow-icon,
  .arrow-icon svg,
  .glow-effect {
    transition: none;
  }
}

/* Dark theme optimization */
@media (prefers-color-scheme: dark) {
  .scroll-to-top {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .scroll-to-top:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

/* Focus styles for accessibility */
.scroll-to-top:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.5);
}

.scroll-to-top:focus:not(:focus-visible) {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Smooth entrance when scrolling back up */
.scroll-to-top:not(.visible) {
  transition-delay: 0.1s;
}
