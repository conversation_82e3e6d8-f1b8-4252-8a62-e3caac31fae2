/* Search Results - Kenya-First Mobile Design */
.search-results-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
  padding: 0.75rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.search-header {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.search-form {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-summary {
  background: white;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #667eea;
}

.search-summary p {
  margin: 0;
  color: #64748b;
}

.search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-results {
  max-width: 1200px;
  margin: 0 auto;
}

.results-section {
  margin-bottom: 2rem;
}

.results-section h2 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
  padding: 0 0.5rem;
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.result-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.salon-card {
  display: flex;
  flex-direction: column;
}

.card-image {
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.result-card:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-content h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.location {
  color: #667eea;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
}

.price {
  color: #10b981;
  font-weight: 700;
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
}

.duration {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.description {
  color: #64748b;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  flex: 1;
}

.view-button {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.view-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.no-results {
  text-align: center;
  padding: 3rem 1rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.no-results p {
  color: #64748b;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.browse-button {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.browse-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Tablet */
@media (min-width: 768px) {
  .search-results-container {
    padding: 2rem 1rem;
  }
  
  .search-header {
    padding: 2rem;
  }
  
  .search-header h1 {
    font-size: 2rem;
  }
  
  .search-form {
    flex-wrap: nowrap;
  }
  
  .results-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .salon-card {
    flex-direction: row;
  }
  
  .salon-card .card-image {
    width: 200px;
    height: auto;
    min-height: 150px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .results-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  .salon-card {
    flex-direction: column;
  }
  
  .salon-card .card-image {
    width: 100%;
    height: 200px;
  }
}
