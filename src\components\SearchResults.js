import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './SearchResults.css';

const SearchResults = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const navigate = useNavigate();
  const { authFetch } = useAuth();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const query = params.get('q');
    if (query) {
      setSearchQuery(query);
      performSearch(query);
    } else {
      setLoading(false);
    }
  }, [location.search]);

  const performSearch = async (query) => {
    try {
      setLoading(true);
      
      // Search salons
      const salonResponse = await fetch(`/api/salons/?search=${encodeURIComponent(query)}`);
      const salons = salonResponse.ok ? await salonResponse.json() : [];
      
      // Search services
      const serviceResponse = await fetch(`/api/services/?search=${encodeURIComponent(query)}`);
      const services = serviceResponse.ok ? await serviceResponse.json() : [];
      
      setResults({
        salons: Array.isArray(salons) ? salons : salons.results || [],
        services: Array.isArray(services) ? services : services.results || [],
        query
      });
    } catch (error) {
      console.error('Search error:', error);
      setResults({ salons: [], services: [], query });
    } finally {
      setLoading(false);
    }
  };

  const handleNewSearch = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const newQuery = formData.get('search');
    if (newQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(newQuery.trim())}`);
    }
  };

  if (loading) {
    return (
      <div className="search-results-container">
        <div className="search-loading">
          <div className="loading-spinner"></div>
          <p>Searching...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="search-results-container">
      <div className="search-header">
        <h1>Search Results</h1>
        <form onSubmit={handleNewSearch} className="search-form">
          <input
            type="text"
            name="search"
            placeholder="Search salons, services, locations..."
            defaultValue={searchQuery}
            className="search-input"
          />
          <button type="submit" className="search-button">Search</button>
        </form>
      </div>

      {searchQuery && (
        <div className="search-summary">
          <p>Results for: <strong>"{searchQuery}"</strong></p>
        </div>
      )}

      <div className="search-results">
        {results.salons && results.salons.length > 0 && (
          <div className="results-section">
            <h2>Salons ({results.salons.length})</h2>
            <div className="results-grid">
              {results.salons.map((salon) => (
                <div key={salon.id} className="result-card salon-card">
                  <div className="card-image">
                    <img 
                      src={salon.image || salon.imageUrl || '/placeholder-salon.jpg'} 
                      alt={salon.name}
                      onError={(e) => {
                        e.target.src = '/placeholder-salon.jpg';
                      }}
                    />
                  </div>
                  <div className="card-content">
                    <h3>{salon.name}</h3>
                    <p className="location">{salon.town}</p>
                    <p className="description">{salon.description}</p>
                    <button 
                      onClick={() => navigate(`/salon/${salon.id}`)}
                      className="view-button"
                    >
                      View Salon
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {results.services && results.services.length > 0 && (
          <div className="results-section">
            <h2>Services ({results.services.length})</h2>
            <div className="results-grid">
              {results.services.map((service) => (
                <div key={service.id} className="result-card service-card">
                  <div className="card-content">
                    <h3>{service.name}</h3>
                    <p className="price">KSh {service.price}</p>
                    <p className="duration">{service.duration} minutes</p>
                    <p className="description">{service.description}</p>
                    <button 
                      onClick={() => navigate(`/service/${service.id}`)}
                      className="view-button"
                    >
                      Book Service
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {(!results.salons || results.salons.length === 0) && 
         (!results.services || results.services.length === 0) && 
         searchQuery && (
          <div className="no-results">
            <div className="no-results-icon">🔍</div>
            <h3>No results found</h3>
            <p>Try searching with different keywords or browse our featured salons.</p>
            <button 
              onClick={() => navigate('/')}
              className="browse-button"
            >
              Browse Featured Salons
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchResults;
