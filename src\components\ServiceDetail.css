/* Modern Service Detail Page Styles */
.service-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(41, 128, 185, 0.1) 100%);
  padding: 0;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #333;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(52, 152, 219, 0.3);
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #333;
  padding: 2rem;
}

.error-state h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

/* Header */
.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(52, 152, 219, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(52, 152, 219, 0.1);
  transform: translateX(-2px);
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(52, 152, 219, 0.1);
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: scale(1.1);
}

.action-button.favorited {
  background: #e74c3c;
  color: white;
}

/* Hero Section */
.service-hero {
  padding: 2rem 1.5rem;
  background: white;
  margin-bottom: 1rem;
}

.service-image-container {
  position: relative;
  width: 100%;
  height: 250px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.service-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(52, 152, 219, 0.95);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.service-info {
  text-align: center;
}

.service-name {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.service-price-display {
  margin-bottom: 1.5rem;
}

.price-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.3rem;
}

.price-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #3498db;
}

.service-description {
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Service Details */
.service-details {
  background: white;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.details-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background: none;
  border: none;
  padding: 1rem 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.details-toggle h3 {
  color: #333;
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.toggle-icon {
  color: #3498db;
  transition: transform 0.3s ease;
}

.details-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.details-content.expanded {
  max-height: 200px;
  padding-top: 1rem;
}

.details-grid {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.detail-item svg {
  color: #3498db;
  font-size: 1.2rem;
}

.detail-label {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.2rem;
}

.detail-value {
  display: block;
  font-weight: 600;
  color: #333;
}

.salon-link {
  color: #3498db;
  text-decoration: none;
  font-weight: 600;
}

.salon-link:hover {
  text-decoration: underline;
}

/* CTA Section */
.service-cta {
  padding: 2rem 1.5rem;
  background: white;
  margin-bottom: 1rem;
}

.book-button {
  display: block;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  text-decoration: none;
  border-radius: 15px;
  text-align: center;
  font-weight: 700;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.book-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(52, 152, 219, 0.4);
  color: white;
  text-decoration: none;
}

.book-button span:first-child {
  display: block;
  font-size: 1.2rem;
  margin-bottom: 0.2rem;
}

.book-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .service-header {
    padding: 1rem;
  }
  
  .service-hero {
    padding: 1.5rem 1rem;
  }
  
  .service-image-container {
    height: 200px;
  }
  
  .service-name {
    font-size: 1.5rem;
  }
  
  .price-value {
    font-size: 1.5rem;
  }
  
  .service-details,
  .service-cta {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 320px) {
  .service-header {
    padding: 0.75rem;
  }
  
  .service-hero {
    padding: 1rem 0.75rem;
  }
  
  .service-image-container {
    height: 180px;
  }
  
  .service-name {
    font-size: 1.3rem;
  }
  
  .price-value {
    font-size: 1.3rem;
  }
  
  .service-details,
  .service-cta {
    padding: 1rem 0.75rem;
  }
}
