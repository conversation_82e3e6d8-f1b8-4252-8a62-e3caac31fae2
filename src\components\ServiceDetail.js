// ESLint globally disabled during development
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { FaArrowLeft, FaMapMarkerAlt, FaClock, FaTag, FaHeart, FaShare } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import './ServiceDetail.css';

const ServiceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { authFetch } = useAuth();
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const fetchService = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await authFetch(`/api/services/${id}/`);
        if (!res.ok) throw new Error('Failed to fetch service details');
        const data = await res.json();
        setService(data);
      } catch (err) {
        setError('Could not load service details.');
      }
      setLoading(false);
    };
    fetchService();
  }, [id, authFetch]);

  if (loading) {
    return (
      <div className="service-detail-container">
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading service details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="service-detail-container">
        <div className="error-state">
          <h3>Service not found</h3>
          <p>{error}</p>
          <button className="back-button" onClick={() => navigate(-1)}>
            <FaArrowLeft />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!service) return null;

  return (
    <div className="service-detail-container">
      {/* Header with Back Button */}
      <div className="service-header">
        <button className="back-button" onClick={() => navigate(-1)}>
          <FaArrowLeft />
          Back
        </button>
        <div className="header-actions">
          <button
            className={`action-button ${isFavorite ? 'favorited' : ''}`}
            onClick={() => setIsFavorite(!isFavorite)}
          >
            <FaHeart />
          </button>
          <button className="action-button">
            <FaShare />
          </button>
        </div>
      </div>

      {/* Hero Section */}
      <div className="service-hero">
        <div className="service-image-container">
          <img
            src={service.imageUrl || service.image_url || 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=800&h=600&fit=crop&auto=format'}
            alt={service.name}
            className="service-hero-image"
            onError={(e) => {
              e.target.src = 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=800&h=600&fit=crop&auto=format';
            }}
          />
          <div className="service-badge">
            <FaTag />
            <span>Popular</span>
          </div>
        </div>

        <div className="service-info">
          <h1 className="service-name">{service.name}</h1>
          <div className="service-price-display">
            <span className="price-label">Starting from</span>
            <span className="price-value">Ksh {service.price}</span>
          </div>
          <p className="service-description">{service.description || 'Professional beauty service with expert care.'}</p>
        </div>
      </div>

      {/* Service Details */}
      <div className="service-details">
        <button
          className="details-toggle"
          onClick={() => setShowDetails(!showDetails)}
        >
          <h3>Service Information</h3>
          <FaArrowLeft
            className={`toggle-icon ${showDetails ? 'expanded' : ''}`}
            style={{ transform: showDetails ? 'rotate(-90deg)' : 'rotate(90deg)' }}
          />
        </button>

        <div className={`details-content ${showDetails ? 'expanded' : ''}`}>
          <div className="details-grid">
            <div className="detail-item">
              <FaClock />
              <div>
                <span className="detail-label">Duration</span>
                <span className="detail-value">{service.duration || '60 minutes'}</span>
              </div>
            </div>
            <div className="detail-item">
              <FaMapMarkerAlt />
              <div>
                <span className="detail-label">Available at</span>
                <span className="detail-value">
                  {service.salon_name ? (
                    <Link to={`/salon/${service.salon}`} className="salon-link">
                      {service.salon_name}
                    </Link>
                  ) : 'Multiple locations'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="service-cta">
        <Link to={`/book?serviceId=${service.id}`} className="book-button">
          <span>Book This Service</span>
          <span className="book-subtitle">Professional care awaits</span>
        </Link>
      </div>
    </div>
  );
};

export default ServiceDetail; 
