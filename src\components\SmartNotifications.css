/* ===== Smart Notifications - Enterprise Gen Z Design ===== */

.smart-notifications-page {
  min-height: 100vh;
  background: #0d1117;
  color: #f0f6fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.notifications-background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.notifications-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: notificationsFloat 16s ease-in-out infinite;
}

.notifications-orb-1 {
  width: 280px;
  height: 280px;
  background: linear-gradient(135deg, #8a2be2, #9b59b6);
  top: 20%;
  left: -18%;
  animation-delay: 0s;
}

.notifications-orb-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  top: 75%;
  right: -15%;
  animation-delay: 7s;
}

.notifications-orb-3 {
  width: 240px;
  height: 240px;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  bottom: 30%;
  left: 50%;
  animation-delay: 12s;
}

@keyframes notificationsFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  12.5% { transform: translateY(-35px) rotate(45deg) scale(1.2); }
  25% { transform: translateY(25px) rotate(90deg) scale(0.8); }
  37.5% { transform: translateY(-20px) rotate(135deg) scale(1.1); }
  50% { transform: translateY(30px) rotate(180deg) scale(0.9); }
  62.5% { transform: translateY(-25px) rotate(225deg) scale(1.15); }
  75% { transform: translateY(15px) rotate(270deg) scale(0.85); }
  87.5% { transform: translateY(-30px) rotate(315deg) scale(1.05); }
}

.container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Back Button */
.back-button-container {
  margin-bottom: 2rem;
}

.back-button-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  color: #f0f6fc;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.back-button-modern:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: #f0f6fc;
  border-color: rgba(138, 43, 226, 0.3);
}

.back-button-modern .button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-button-modern:hover .button-glow {
  left: 100%;
}

/* Modern Header */
.notifications-header-modern {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.notifications-header-content {
  max-width: 800px;
  margin: 0 auto;
}

.notifications-header-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(138, 43, 226, 0.1);
  border: 1px solid rgba(138, 43, 226, 0.3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #8a2be2;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.notifications-title-modern {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.title-gradient {
  background: linear-gradient(135deg, #8a2be2, #667eea, #ffeaa7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: notificationsGradientShift 8s ease-in-out infinite;
}

.title-accent {
  display: inline-block;
  animation: notificationsSparkle 4s ease-in-out infinite;
  margin-left: 0.5rem;
}

@keyframes notificationsGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes notificationsSparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.7) rotate(180deg); opacity: 0.3; }
}

.notifications-subtitle-modern {
  font-size: 1.2rem;
  color: #8b949e;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Modern Stats */
.notification-stats-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(138, 43, 226, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.05), rgba(102, 126, 234, 0.05));
  opacity: 0.5;
}

.stat-icon {
  font-size: 2rem;
  position: relative;
  z-index: 2;
}

.stat-content {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 900;
  color: #f0f6fc;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: #8b949e;
  font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .notifications-orb-1, .notifications-orb-2, .notifications-orb-3 {
    width: 160px;
    height: 160px;
    filter: blur(40px);
  }

  .container {
    padding: 16px;
  }

  .notifications-title-modern {
    font-size: 2.5rem;
  }

  .notifications-subtitle-modern {
    font-size: 1rem;
  }

  .notification-stats-modern {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1.25rem;
  }

  .stat-icon {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .notifications-title-modern {
    font-size: 2rem;
  }

  .notifications-header-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }

  .stat-card {
    padding: 1rem;
    gap: 0.75rem;
  }
}

.back-button:hover {
  background: rgba(138, 43, 226, 0.3);
  transform: translateY(-2px);
  text-decoration: none;
  color: #8a2be2;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.back-icon {
  font-size: 1.1rem;
  font-weight: bold;
}

.back-text {
  font-size: 0.95rem;
}

.notifications-header {
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.notifications-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.notifications-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 20px 0;
}

.notifications-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Controls */
.notifications-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.generate-btn, .clear-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.generate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #333;
  border: 2px solid #e1e5e9;
}

.clear-btn:hover {
  background: rgba(255, 107, 107, 0.1);
  border-color: #ff6b6b;
  color: #ff6b6b;
}

/* Notifications Container */
.notifications-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 30px;
}

.no-notifications {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-notifications-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.no-notifications h3 {
  color: #333;
  margin-bottom: 10px;
}

/* Notifications List */
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notification-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-card.unread::before {
  opacity: 1;
}

.notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.notification-card.read {
  opacity: 0.7;
}

.notification-header {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.notification-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px;
  color: white;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.notification-message {
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.notification-meta {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.notification-time {
  font-size: 0.8rem;
  color: #999;
}

.personalized-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.priority-high {
  background: #ffebee;
  color: #c62828;
}

.priority-badge.priority-medium {
  background: #fff3e0;
  color: #f57c00;
}

.priority-badge.priority-low {
  background: #e8f5e8;
  color: #2e7d32;
}

.notification-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.book-now {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.view-details {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.action-btn.view-booking {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.action-btn.view-style {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.action-btn.view-offer {
  background: rgba(255, 87, 34, 0.1);
  color: #ff5722;
}

.action-btn.view-trend {
  background: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}

.action-btn.book-maintenance {
  background: rgba(0, 188, 212, 0.1);
  color: #00bcd4;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.mark-read-btn, .delete-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.mark-read-btn:hover {
  background: rgba(76, 175, 80, 0.1);
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.1);
}

/* Priority Colors */
.notification-card.priority-high {
  border-left: 4px solid #f44336;
}

.notification-card.priority-medium {
  border-left: 4px solid #ff9800;
}

.notification-card.priority-low {
  border-left: 4px solid #4caf50;
}

/* Insights Section */
.notifications-insights {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notifications-insights h3 {
  text-align: center;
  color: #333;
  margin-bottom: 25px;
  font-size: 1.8rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.insight-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.insight-card h4 {
  color: #667eea;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.insight-card p {
  color: #666;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-notifications {
    padding: 15px;
  }
  
  .notifications-header {
    padding: 20px;
  }
  
  .notifications-header h2 {
    font-size: 2rem;
  }
  
  .notifications-stats {
    gap: 15px;
  }
  
  .stat-item {
    padding: 10px 15px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .notifications-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .generate-btn, .clear-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .notifications-container {
    padding: 20px;
  }
  
  .notification-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .notification-actions {
    justify-content: center;
    width: 100%;
  }
  
  .notification-meta {
    justify-content: center;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .notifications-header h2 {
    font-size: 1.8rem;
  }
  
  .notifications-header p {
    font-size: 1rem;
  }
  
  .notification-card {
    padding: 15px;
  }
  
  .notification-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }
  
  .notification-title {
    font-size: 1rem;
  }
  
  .insight-card {
    padding: 15px;
  }
} 