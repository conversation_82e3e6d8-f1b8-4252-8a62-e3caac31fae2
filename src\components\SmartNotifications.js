// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import smartNotificationsService from '../services/smartNotificationsService';
import './SmartNotifications.css';

const SmartNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState({
    profile: {
      name: '<PERSON>',
      age: 22,
      stylePreferences: ['trendy', 'natural', 'low maintenance']
    },
    bookingHistory: [
      {
        date: '2024-01-15',
        service: 'Haircut & Style',
        salonName: 'Trendy Cuts',
        price: 75,
        time: '14:00'
      },
      {
        date: '2024-02-01',
        service: 'Color Treatment',
        salonName: 'Trendy Cuts',
        price: 120,
        time: '15:30'
      },
      {
        date: '2024-02-20',
        service: 'Trim & Style',
        salonName: 'Beauty Haven',
        price: 60,
        time: '10:00'
      }
    ],
    preferences: ['trendy', 'natural', 'low maintenance'],
    searchHistory: ['bob haircut', 'natural waves', 'trendy styles'],
    favoriteSalons: ['Trendy Cuts', 'Beauty Haven']
  });

  useEffect(() => {
    generateSmartNotifications();
  }, []);

  const generateSmartNotifications = async () => {
    setLoading(true);
    try {
      const newNotifications = [];
      // Generate different types of smart notifications
      const notificationTypes = [
        'maintenance_reminder',
        'style_trending',
        'salon_discount',
        'new_style_available',
        'booking_reminder'
      ];

      for (const type of notificationTypes) {
        const notification = await smartNotificationsService.generateSmartNotification({
          type,
          userProfile: userData.profile,
          salonData: { name: 'Trendy Cuts' },
          bookingData: { service: 'Haircut & Style' },
          preferences: userData.preferences,
          previousInteractions: userData.bookingHistory
        });

        newNotifications.push({
          id: Date.now() + Math.random(),
          ...notification,
          timestamp: new Date(),
          read: false
        });
      }

      setNotifications(newNotifications);
    } catch (error) {
      console.error('Error generating notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = (notificationId) => {
    setNotifications(prev => prev.map(notif => (notif.id === notificationId 
      ? { ...notif, read: true }
      : notif)));
  };

  const deleteNotification = (notificationId) => {
    setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'priority-high';
      case 'medium': return 'priority-medium';
      case 'low': return 'priority-low';
      default: return 'priority-medium';
    }
  };

  const getActionButton = (action, notification) => {
    switch (action) {
      case 'book_now':
        return <button className="action-btn book-now">Book Now</button>;
      case 'view_details':
        return <button className="action-btn view-details">View Details</button>;
      case 'view_booking':
        return <button className="action-btn view-booking">View Booking</button>;
      case 'view_style':
        return <button className="action-btn view-style">View Style</button>;
      case 'view_offer':
        return <button className="action-btn view-offer">View Offer</button>;
      case 'view_trend':
        return <button className="action-btn view-trend">View Trend</button>;
      case 'book_maintenance':
        return <button className="action-btn book-maintenance">Book Maintenance</button>;
      default:
        return <button className="action-btn view-details">View Details</button>;
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="smart-notifications-page">
      <div className="notifications-background-effects">
        <div className="notifications-gradient-orb notifications-orb-1"></div>
        <div className="notifications-gradient-orb notifications-orb-2"></div>
        <div className="notifications-gradient-orb notifications-orb-3"></div>
      </div>

      <div className="container">
        {/* Back Button */}
        <div className="back-button-container">
          <Link to="/ai-features" className="back-button-modern">
            <span className="back-icon">←</span>
            <span className="back-text">Back to AI Features</span>
            <div className="button-glow"></div>
          </Link>
        </div>

        <div className="notifications-header-modern">
          <div className="notifications-header-content">
            <div className="notifications-header-badge">
              <span className="badge-icon">🧠</span>
              <span className="badge-text">AI NOTIFICATIONS</span>
            </div>
            <h1 className="notifications-title-modern">
              <span className="title-gradient">Smart Alerts</span>
              <span className="title-accent">🔔</span>
            </h1>
            <p className="notifications-subtitle-modern">
              AI that knows what matters to you
            </p>

            <div className="notification-stats-modern">
              <div className="stat-card">
                <div className="stat-icon">📊</div>
                <div className="stat-content">
                  <span className="stat-number">{notifications.length}</span>
                  <span className="stat-label">Total Alerts</span>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">🔥</div>
                <div className="stat-content">
                  <span className="stat-number">{unreadCount}</span>
                  <span className="stat-label">Unread</span>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">✨</div>
                <div className="stat-content">
                  <span className="stat-number">{notifications.filter(n => n.personalized).length}</span>
                  <span className="stat-label">Personalized</span>
                </div>
              </div>
            </div>
          </div>
        </div>

      <div className="notifications-controls">
        <button 
          className="generate-btn"
          onClick={generateSmartNotifications}
          disabled={loading}
        >
          {loading ? '🧠 AI is thinking...' : '🧠 Generate Smart Notifications'}
        </button>
        
        <button 
          className="clear-btn"
          onClick={() => setNotifications([])}
        >
          Clear All
        </button>
      </div>

      <div className="notifications-container">
        {notifications.length === 0 ? (
          <div className="no-notifications">
            <div className="no-notifications-icon">🔔</div>
            <h3>No notifications yet</h3>
            <p>Generate some smart notifications to see AI in action!</p>
          </div>
        ) : (
          <div className="notifications-list">
            {notifications.map(notification => (
              <div 
                key={notification.id} 
                className={`notification-card ${getPriorityColor(notification.priority)} ${notification.read ? 'read' : 'unread'}`}
              >
                <div className="notification-header">
                  <div className="notification-icon">
                    {notification.emoji}
                  </div>
                  <div className="notification-content">
                    <h4 className="notification-title">{notification.title}</h4>
                    <p className="notification-message">{notification.message}</p>
                    <div className="notification-meta">
                      <span className="notification-time">
                        {notification.timestamp.toLocaleTimeString()}
                      </span>
                      {notification.personalized && (
                        <span className="personalized-badge">✨ Personalized</span>
                      )}
                      <span className={`priority-badge ${getPriorityColor(notification.priority)}`}>
                        {notification.priority}
                      </span>
                    </div>
                  </div>
                  <div className="notification-actions">
                    {getActionButton(notification.action, notification)}
                    <button 
                      className="mark-read-btn"
                      onClick={() => markAsRead(notification.id)}
                      title={notification.read ? 'Mark as unread' : 'Mark as read'}
                    >
                      {notification.read ? '👁️' : '✓'}
                    </button>
                    <button 
                      className="delete-btn"
                      onClick={() => deleteNotification(notification.id)}
                      title="Delete notification"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="notifications-insights">
        <h3>🧠 AI Insights</h3>
        <div className="insights-grid">
          <div className="insight-card">
            <h4>📊 User Behavior Analysis</h4>
            <p>AI analyzes your booking patterns, preferences, and interactions to create personalized notifications.</p>
          </div>
          <div className="insight-card">
            <h4>🎯 Contextual Intelligence</h4>
            <p>Notifications are generated based on your style preferences, budget, and maintenance schedule.</p>
          </div>
          <div className="insight-card">
            <h4>⏰ Smart Timing</h4>
            <p>AI determines the best time to send notifications based on your activity patterns and preferences.</p>
          </div>
          <div className="insight-card">
            <h4>🎨 Style Matching</h4>
            <p>Notifications are tailored to match your style preferences and trending interests.</p>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default SmartNotifications; 
