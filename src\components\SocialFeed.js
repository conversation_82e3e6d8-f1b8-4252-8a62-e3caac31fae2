import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { toast, ToastContainer } from 'react-toastify';
import { FaHeart, FaComment, FaUserPlus, FaFire } from 'react-icons/fa';
import axios from '../api/axiosInstance';
import { useAuth } from '../context/AuthContext';
import styles from './SocialFeed.module.css';
import 'react-toastify/dist/ReactToastify.css';

// Modular components
const FriendActivity = ({ friendActivity, followStatus, onFollow, onUnfollow, currentUser }) => (
  <div className="col-md-6">
    <div className={styles.socialFeedCard}>
      <div className="card-body p-0">
        <h5 className="card-title mb-3" style={{ color: '#7c3aed', fontWeight: 700 }}>
          <i className="bi bi-people me-2" />
          Friends' Recent Activity
        </h5>
        {friendActivity.length > 0 ? (
          <div className="list-group list-group-flush">
            {friendActivity.slice(0, 5).map(booking => (
              <div key={booking.id} className="list-group-item bg-transparent border-0 px-0 py-2 d-flex align-items-center justify-content-between flex-wrap gap-2">
                <div className="flex-grow-1" style={{ fontSize: 15 }}>
                  <b className={styles.socialFeedPostUser}>{booking.friend_name}</b>
                  {' '}
                  booked{' '}
                  <b>
                    {booking.service_name}
                  </b>
                  {' '}
                  at{' '}
                  <b>
                    {booking.salon_name}
                  </b>
                </div>
                {booking.friend_id !== currentUser && (
                  followStatus[booking.friend_id] && followStatus[booking.friend_id].isFollowing ? (
                    <button className="btn btn-outline-danger btn-sm rounded-pill px-3 ms-2 w-100 w-md-auto" style={{ fontWeight: 600, minWidth: 90, fontSize: 14, padding: '0.375rem 1rem', cursor: 'pointer' }} onClick={() => onUnfollow(booking.friend_id, followStatus[booking.friend_id].followId)}>
                      Unfollow
                    </button>
                  ) : (
                    <button className="btn btn-outline-primary btn-sm rounded-pill px-3 ms-2 w-100 w-md-auto" style={{ fontWeight: 600, color: '#7c3aed', borderColor: '#7c3aed', minWidth: 90, fontSize: 14, padding: '0.375rem 1rem', cursor: 'pointer' }} onClick={() => onFollow(booking.friend_id)}>
                      Follow
                    </button>
                  )
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-muted">No recent activity</p>
        )}
      </div>
    </div>
  </div>
);

FriendActivity.propTypes = {
  friendActivity: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.number.isRequired,
    friend_name: PropTypes.string.isRequired,
    service_name: PropTypes.string.isRequired,
    salon_name: PropTypes.string.isRequired,
    friend_id: PropTypes.number.isRequired,
  })).isRequired,
  followStatus: PropTypes.objectOf(PropTypes.shape({
    isFollowing: PropTypes.bool.isRequired,
    followId: PropTypes.number,
  })).isRequired,
  onFollow: PropTypes.func.isRequired,
  onUnfollow: PropTypes.func.isRequired,
  currentUser: PropTypes.number,
};

const StylePost = ({
  post,
  likeCount,
  onLike,
  liked,
  comments,
  onComment,
  commentInput,
  setCommentInput,
  onSubmitComment,
  reviewSummary,
}) => (
  <div className={styles.socialFeedPost}>
    <div className="d-flex align-items-start flex-column flex-md-row gap-3">
      <div className="flex-grow-1">
        <h6 className={`${styles.socialFeedPostUser} mb-1`}>{post.user_name}</h6>
        <p className="mb-1" style={{ fontSize: 16 }}>{post.message}</p>
        <p className="mb-1 text-muted" style={{ fontSize: 15 }}>
          {post.service_name}
          {' '}
          at
          <b>{post.salon_name}</b>
        </p>
        <small className="text-muted">
          {new Date(post.created_at).toLocaleDateString()}
        </small>
        {post.image && (
          <img
            src={post.image || '/assets/fallback.jpg'}
            onError={e => (e.target.src = '/assets/fallback.jpg')}
            alt="Style"
            className={`${styles.socialFeedPostImage} img-fluid rounded mt-2`}
          />
        )}
        {/* Like and Comment buttons */}
        <div className="mt-3 d-flex gap-3 flex-wrap">
          <button
            className={`${styles.socialFeedButtonLike} btn btn-outline-primary btn-sm rounded-pill px-3${liked ? ` ${styles.socialFeedButtonLikeActive}` : ''}`}
            title="Like"
            onClick={liked ? undefined : onLike}
            disabled={liked}
            style={{ cursor: liked ? 'not-allowed' : 'pointer' }}
          >
            <i className="bi bi-heart" />
            {' '}
            Like
            {likeCount > 0 ? (
              <>
                {' ('}
                {likeCount}
                )
              </>
            ) : null}
          </button>
          <button
            className={`${styles.socialFeedButtonComment} btn btn-outline-secondary btn-sm rounded-pill px-3`}
            title="Comment"
            onClick={onComment}
          >
            <i className="bi bi-chat" />
            {' '}
            Comment
            {comments.length > 0 ? (
              <>
                {' ('}
                {comments.length}
                )
              </>
            ) : null}
          </button>
        </div>
        {/* Comment input */}
        {commentInput !== undefined && (
          <form className="mt-2 d-flex gap-2" onSubmit={onSubmitComment}>
            <label htmlFor="comment-input" className="visually-hidden">Add a comment</label>
            <input
              id="comment-input"
              type="text"
              className={`${styles.socialFeedCommentInput} form-control form-control-sm`}
              placeholder="Add a comment..."
              value={commentInput}
              onChange={e => setCommentInput(e.target.value)}
              maxLength={200}
              required
            />
            <button className="btn btn-primary btn-sm px-3" type="submit" style={{ cursor: commentInput ? 'pointer' : 'not-allowed' }} disabled={!commentInput}>Post</button>
          </form>
        )}
        {/* Comments list */}
        {comments.length > 0 && (
          <div className="mt-2">
            {comments.map((c, idx) => (
              <div key={c.id || idx} className={`${styles.socialFeedCommentCard} border rounded p-2 mb-1 bg-light`}>
                <b>{c.user_id}</b>
                : {c.text}
              </div>
            ))}
          </div>
        )}
        {/* Review display (real data) */}
        <div className="mt-2">
          {reviewSummary && (
            <span className={styles.socialFeedBadge}>
              <i className="bi bi-star-fill" />
              {' '}
              {reviewSummary.avgRating.toFixed(1)}
              {' ('}
              {reviewSummary.count}
              {' reviews)'}
            </span>
          )}
        </div>
      </div>
    </div>
  </div>
);

StylePost.propTypes = {
  post: PropTypes.shape({
    user_name: PropTypes.string.isRequired,
    message: PropTypes.string.isRequired,
    service_name: PropTypes.string.isRequired,
    salon_name: PropTypes.string.isRequired,
    created_at: PropTypes.string.isRequired,
    image: PropTypes.string,
  }).isRequired,
  likeCount: PropTypes.number.isRequired,
  onLike: PropTypes.func.isRequired,
  liked: PropTypes.bool.isRequired,
  comments: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.number, // id might be optional for new comments
    user_id: PropTypes.number.isRequired,
    text: PropTypes.string.isRequired,
  })).isRequired,
  onComment: PropTypes.func.isRequired,
  commentInput: PropTypes.string, // Can be undefined
  setCommentInput: PropTypes.func.isRequired,
  onSubmitComment: PropTypes.func.isRequired,
  reviewSummary: PropTypes.shape({
    avgRating: PropTypes.number.isRequired,
    count: PropTypes.number.isRequired,
  }),
};

StylePost.defaultProps = {
  commentInput: '',
  reviewSummary: { avgRating: 0, count: 0 },
};

const StylePostsFeed = ({
  stylePosts,
  likes,
  onLike,
  likedPosts,
  comments,
  onComment,
  commentInputs,
  setCommentInputs,
  onSubmitComment,
  reviewSummaries,
}) => (
  <div className="col-md-6">
    <div className={styles.socialFeedCard}>
      <div className="card-body p-0">
        <h5 className="card-title mb-3" style={{ color: '#7c3aed', fontWeight: 700 }}>
          <i className="bi bi-camera me-2" />
          Style Posts
        </h5>
        {stylePosts.length > 0 ? (
          <div className="list-group list-group-flush">
            {stylePosts.slice(0, 5).map(post => (
              <StylePost
                key={post.id}
                post={post}
                likeCount={likes[post.id] || 0}
                onLike={() => onLike(post.id)}
                liked={likedPosts[post.id]}
                comments={comments[post.id] || []}
                onComment={() => onComment(post.id)}
                commentInput={commentInputs[post.id]}
                setCommentInput={val => setCommentInputs(inputs => ({ ...inputs, [post.id]: val }))}
                onSubmitComment={e => onSubmitComment(e, post.id)}
                reviewSummary={reviewSummaries[post.salon_id]}
              />
            ))}
          </div>
        ) : (
          <p className="text-muted">No style posts yet</p>
        )}
      </div>
    </div>
  </div>
);

StylePostsFeed.propTypes = {
  stylePosts: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.number.isRequired,
    user_name: PropTypes.string.isRequired,
    message: PropTypes.string.isRequired,
    service_name: PropTypes.string.isRequired,
    salon_name: PropTypes.string.isRequired,
    created_at: PropTypes.string.isRequired,
    image: PropTypes.string,
  })).isRequired,
  likes: PropTypes.objectOf(PropTypes.number).isRequired,
  onLike: PropTypes.func.isRequired,
  likedPosts: PropTypes.objectOf(PropTypes.bool).isRequired,
  comments: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.number,
    user_id: PropTypes.number.isRequired,
    text: PropTypes.string.isRequired,
  }))).isRequired,
  onComment: PropTypes.func.isRequired,
  commentInputs: PropTypes.objectOf(PropTypes.string).isRequired,
  setCommentInputs: PropTypes.func.isRequired,
  onSubmitComment: PropTypes.func.isRequired,
  reviewSummaries: PropTypes.objectOf(PropTypes.shape({
    avgRating: PropTypes.number.isRequired,
    count: PropTypes.number.isRequired,
  })).isRequired,
};

StylePostsFeed.defaultProps = {
  reviewSummaries: {},
  commentInputs: {},
};

const SocialFeed = () => {
  const { user } = useAuth();
  const [friendActivity, setFriendActivity] = useState([]);
  const [stylePosts, setStylePosts] = useState([]);
  const [likes, setLikes] = useState({});
  const [likedPosts, setLikedPosts] = useState({});
  const [comments, setComments] = useState({});
  const [showCommentInput, setShowCommentInput] = useState({});
  const [commentInputs, setCommentInputs] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [followStatus, setFollowStatus] = useState({});
  const [reviewSummaries, setReviewSummaries] = useState({});
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [showFriendModal, setShowFriendModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const perPage = 5;
  const stylePostsSafe = Array.isArray(stylePosts) ? stylePosts : [];
  const totalPages = Math.ceil(stylePostsSafe.length / perPage);
  const paginatedPosts = stylePostsSafe.slice((currentPage - 1) * perPage, currentPage * perPage);

  // Mock data for when API is not available
  const mockFriendActivity = [
    {
      id: 1,
      friend_name: 'Sarah Johnson',
      service_name: 'Hair Styling',
      salon_name: 'Glamour Salon',
      friend_id: 2
    },
    {
      id: 2,
      friend_name: 'Mike Chen',
      service_name: 'Haircut',
      salon_name: 'Style Studio',
      friend_id: 3
    },
    {
      id: 3,
      friend_name: 'Lisa Rodriguez',
      service_name: 'Hair Coloring',
      salon_name: 'Beauty Haven',
      friend_id: 1
    }
  ];

  const mockStylePosts = [
    {
      id: 1,
      user_name: 'Emma Wilson',
      message: 'Love my new summer look!',
      service_name: 'Hair Styling',
      salon_name: 'Glamour Salon',
      created_at: '2024-01-15',
      image: null
    },
    {
      id: 2,
      user_name: 'Alex Thompson',
      message: 'Perfect for the weekend!',
      service_name: 'Haircut',
      salon_name: 'Style Studio',
      created_at: '2024-01-14',
      image: null
    }
  ];

  const fetchFriendActivity = useCallback(async () => {
    try {
      const response = await axios.post(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/friends/activity/`, {
        user_id: user?.userId || 1,
        days: 7
      });

      if (!response.ok) {
        // Use mock data if API fails
        console.log('Using mock friend activity data');
        setFriendActivity(mockFriendActivity);
        return;
      }

      const data = await response.json();
      setFriendActivity(data.friend_bookings || mockFriendActivity);
    } catch (err) {
      console.log('Error fetching friend activity, using mock data:', err.message);
      setFriendActivity(mockFriendActivity);
    }
  }, [user]);

  const fetchStylePosts = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/api/style-posts/`);
      if (!response.ok) {
        // Use mock data if API fails
        console.log('Using mock style posts data');
        setStylePosts(mockStylePosts);
        return;
      }

      const data = await response.json();
      setStylePosts(data || mockStylePosts);
    } catch (err) {
      console.log('Error fetching style posts, using mock data:', err.message);
      setStylePosts(mockStylePosts);
    } finally {
      setLoading(false);
    }
  };

  // Fetch likes for all posts
  const fetchLikes = useCallback(async (posts) => {
    const likeCounts = {};
    const liked = {};
    await Promise.all(posts.map(async (post) => {
      try {
        const res = await fetch(`/api/likes/?style_post=${post.id}`);
        if (res.ok) {
          const data = await res.json();
          likeCounts[post.id] = data.length;
          liked[post.id] = data.some(like => like.user_id === user?.userId);
        }
      } catch { }
    }));
    setLikes(likeCounts);
    setLikedPosts(liked);
  }, [user]);

  // Like action
  const handleLike = async (postId) => {
    if (!user) return;
    try {
      const res = await fetch('/api/likes/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: user.userId, style_post: postId })
      });
      if (res.ok) {
        setLikes(l => ({ ...l, [postId]: (l[postId] || 0) + 1 }));
        setLikedPosts(lp => ({ ...lp, [postId]: true }));
        toast.success('Liked!');
      } else {
        toast.error('Failed to like.');
      }
    } catch {
      toast.error('Failed to like.');
    }
  };

  // Fetch comments for all posts
  const fetchComments = useCallback(async (posts) => {
    const allComments = {};
    await Promise.all(posts.map(async (post) => {
      try {
        const res = await fetch(`/api/comments/?style_post=${post.id}`);
        if (res.ok) {
          const data = await res.json();
          allComments[post.id] = data;
        }
      } catch { }
    }));
    setComments(allComments);
  }, []);

  // Show/hide comment input
  const handleComment = (postId) => {
    setShowCommentInput(inputs => ({ ...inputs, [postId]: !inputs[postId] }));
    setCommentInputs(inputs => ({ ...inputs, [postId]: '' }));
  };

  // Submit comment
  const handleSubmitComment = async (e, postId) => {
    e.preventDefault();
    if (!user || !commentInputs[postId]) return;
    setCommentInputs(inputs => ({ ...inputs, [postId]: '' }));
    try {
      const res = await fetch('/api/comments/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: user.userId,
          style_post: postId,
          text: commentInputs[postId],
        }),
      });
      if (res.ok) {
        setComments(c => ({
          ...c,
          [postId]: [
            ...(c[postId] || []),
            { user_id: user.userId, text: commentInputs[postId] },
          ],
        }));
      }
    } catch { }
  };

  // Fetch follow status for each friend in activity
  const fetchFollowStatus = useCallback(async (activity) => {
    if (!user) return;
    const status = {};
    await Promise.all(activity.map(async (booking) => {
      if (!booking.friend_id || booking.friend_id === user.userId) return;
      try {
        const res = await fetch(`/api/follows/?follower=${user.userId}&following=${booking.friend_id}`);
        if (res.ok) {
          const data = await res.json();
          if (data.length > 0) {
            status[booking.friend_id] = { isFollowing: true, followId: data[0].id };
          } else {
            status[booking.friend_id] = { isFollowing: false };
          }
        }
      } catch { }
    }));
    setFollowStatus(status);
  }, [user]);

  // Follow action
  const handleFollow = async (friendId) => {
    if (!user) return;
    try {
      const res = await fetch('/api/follows/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ follower: user.userId, following: friendId })
      });
      if (res.ok) {
        const data = await res.json();
        setFollowStatus(s => ({ ...s, [friendId]: { isFollowing: true, followId: data.id } }));
      }
    } catch { }
  };

  // Unfollow action
  const handleUnfollow = async (friendId, followId) => {
    if (!user || !followId) return;
    try {
      const res = await fetch(`/api/follows/${followId}/`, { method: 'DELETE' });
      if (res.ok || res.status === 204) {
        setFollowStatus(s => ({ ...s, [friendId]: { isFollowing: false } }));
      }
    } catch { }
  };

  // Fetch review summaries for all salons in style posts
  const fetchReviewSummaries = useCallback(async (posts) => {
    const summaries = {};
    await Promise.all(posts.map(async (post) => {
      if (!post.salon_id) return;
      try {
        const res = await fetch(`/api/reviews/?salon=${post.salon_id}`);
        if (res.ok) {
          const data = await res.json();
          if (Array.isArray(data) && data.length > 0) {
            const avg = data.reduce((sum, r) => sum + (r.rating || 0), 0) / data.length;
            summaries[post.salon_id] = { avgRating: avg, count: data.length };
          } else {
            summaries[post.salon_id] = { avgRating: 0, count: 0 };
          }
        }
      } catch { }
    }));
    setReviewSummaries(summaries);
  }, []);

  useEffect(() => {
    if (user) {
      fetchFriendActivity();
      fetchStylePosts().then(() => {
        fetchLikes(stylePosts);
        fetchComments(stylePosts);
      });
    } else {
      // Show mock data even without user
      setFriendActivity(mockFriendActivity);
      setStylePosts(mockStylePosts);
      setLoading(false);
    }
  }, [user, fetchFriendActivity]);

  useEffect(() => {
    if (stylePosts.length > 0) {
      fetchLikes(stylePosts);
      fetchComments(stylePosts);
      fetchReviewSummaries(stylePosts);
    }
  }, [stylePosts, fetchLikes, fetchComments, fetchReviewSummaries]);

  useEffect(() => {
    if (user && friendActivity.length > 0) {
      fetchFollowStatus(friendActivity);
    }
  }, [user, friendActivity, fetchFollowStatus]);

  useEffect(() => {
    setLoading(true);
    axios.get(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/style-posts/?page=${page}`)
      .then(res => {
        const newPosts = Array.isArray(res.data.results) ? res.data.results : [];
        setStylePosts(prev => [
          ...(Array.isArray(prev) ? prev : []),
          ...newPosts
        ]);
        setHasMore(newPosts.length > 0);
      })
      .catch(() => setError('Could not load feed'))
      .finally(() => setLoading(false));
  }, [page]);

  // Show social feed even without user (with mock data)
  // if (!user) {
  //   return null;
  // }

  if (error) return <div className="text-center text-red-500">{error}</div>;
  if (!loading && stylePosts.length === 0) return <div className="text-center text-gray-500">No style posts yet. Be the first!</div>;

  return (
    <>
      <ToastContainer position="top-center" autoClose={2000} />
      <FriendSearchModal
        show={showFriendModal}
        onClose={() => setShowFriendModal(false)}
        onFriendAdded={() => { fetchFriendActivity(); }}
        currentUserId={user?.userId || user?.id}
      />
      <div className={styles.socialFeedContainer}>
        <h2 className="text-center mb-4 d-block d-md-none display-6">Social Feed</h2>
        <h2 className="text-center mb-4 d-none d-md-block display-5">Social Feed</h2>
        {loading ? (
          <div className="text-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : error ? (
          <div className="alert alert-warning text-center">{error}</div>
        ) : (
          <div className="row g-2 g-md-4 flex-column flex-md-row" style={{ display: 'flex', flexWrap: 'wrap' }}>
            <FriendActivity
              friendActivity={friendActivity}
              followStatus={followStatus}
              onFollow={handleFollow}
              onUnfollow={handleUnfollow}
              currentUser={user ? user.userId : null}
            />
            <StylePostsFeed
              stylePosts={stylePosts}
              likes={likes}
              onLike={handleLike}
              likedPosts={likedPosts}
              comments={comments}
              onComment={handleComment}
              commentInputs={Object.fromEntries(Object.entries(showCommentInput).filter(([k, v]) => v).map(([k]) => [k, commentInputs[k] || '']))}
              setCommentInputs={setCommentInputs}
              onSubmitComment={handleSubmitComment}
              reviewSummaries={reviewSummaries}
            />
          </div>
        )}
        <div className={styles.fabRowResponsive}>
          <button
            className={`${styles.socialFeedFab} ${styles.fabLeft}`}
            onClick={() => alert('New post coming soon!')}
            style={{ background: '#7c3aed', color: '#fff' }}
            title="Add Post"
          >
            <FaPlus size={20} />
          </button>
          <button
            className={`${styles.socialFeedFab} ${styles.fabRight}`}
            onClick={() => setShowFriendModal(true)}
            style={{ background: '#f472b6', color: '#fff' }}
            title="Find Friends"
          >
            <FaUserPlus size={20} />
          </button>
        </div>
        <div className={styles.paginationContainer}>
          <button
            className={styles.paginationBtn}
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
          >
            ←
          </button>
          <span className={styles.paginationText}>
            Page {currentPage} of {totalPages}
          </span>
          <button
            className={styles.paginationBtn}
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            →
          </button>
        </div>
      </div>
    </>
  );
};

export default SocialFeed; 
