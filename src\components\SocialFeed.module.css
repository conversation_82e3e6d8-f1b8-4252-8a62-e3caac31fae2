/* SocialFeed.module.css */

.socialFeedContainer {
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f8a5c2 0%, #a18cd1 100%);
  border-radius: 20px;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
  min-height: 400px;
}

.socialFeedTitleDesktop {
  color: #7c3aed;
  font-weight: 700;
}

.socialFeedTitleMobile {
  color: #7c3aed;
  font-weight: 700;
  font-size: 28px;
}

.socialFeedCard {
  background: rgba(255,255,255,0.15);
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(160, 140, 209, 0.08);
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255,255,255,0.12);
  backdrop-filter: blur(8px);
}

.socialFeedPost {
  background: rgba(255,255,255,0.10);
  border-radius: 14px;
  padding: 1.2rem 1rem;
  margin-bottom: 1.2rem;
  box-shadow: 0 2px 8px rgba(160, 140, 209, 0.06);
  color: #3d246c;
}

.socialFeedPostUser {
  color: #7c3aed;
  font-weight: 700;
  font-size: 1.1rem;
}

.socialFeedPostImage {
  max-width: 100%;
  border-radius: 12px;
  margin-top: 0.7rem;
  box-shadow: 0 2px 8px rgba(248,165,194,0.13);
}

.socialFeedButtonLike {
  color: #7c3aed;
  border-color: #7c3aed;
  background: rgba(124,58,237,0.07);
  font-weight: 600;
}

.socialFeedButtonLikeActive {
  background: #f472b6;
  color: #fff;
  border-color: #f472b6;
}

.socialFeedButtonComment {
  color: #a18cd1;
  border-color: #a18cd1;
  background: rgba(161,140,209,0.07);
  font-weight: 600;
}

.socialFeedCommentCard {
  background: rgba(255,255,255,0.18);
  color: #3d246c;
}

.socialFeedCommentInput {
  background: #fff0fa;
  border: 1px solid #f8a5c2;
  color: #3d246c;
}

.socialFeedBadge {
  background: linear-gradient(90deg, #f8a5c2 60%, #a18cd1 100%);
  color: #fff;
  border-radius: 12px;
  padding: 0.2rem 0.7rem;
  font-size: 0.95rem;
  font-weight: 600;
  margin-right: 0.5rem;
}

.socialFeedFab {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  z-index: 1200;
  width: 54px;
  height: 54px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7c3aed 60%, #f472b6 100%);
  color: #fff;
  border: none;
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(160, 140, 209, 0.18);
  transition: background 0.2s;
  cursor: pointer;
}

.socialFeedFab:hover {
  background: linear-gradient(135deg, #f472b6 60%, #7c3aed 100%);
}

.fabRow {
  position: absolute;
  bottom: 2.2rem;
  right: 2.2rem;
  display: flex;
  gap: 0.7rem;
  z-index: 1200;
}
@media (max-width: 600px) {
  .fabRow {
    bottom: 1.1rem;
    right: 1.1rem;
    gap: 0.4rem;
  }
  .socialFeedFab {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}
.paginationContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 1.5rem;
}
.paginationBtn {
  background: linear-gradient(90deg, #7c3aed 60%, #f472b6 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.4rem 1.1rem;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px #f472b633;
}
.paginationBtn:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
}
.paginationText {
  color: #7c3aed;
  font-weight: 600;
  font-size: 1.1rem;
}

.fabRowResponsive {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  margin-bottom: 1.2rem;
  z-index: 1200;
}
.fabLeft {
  margin-left: 0;
}
.fabRight {
  margin-right: 0;
}
@media (max-width: 600px) {
  .fabRowResponsive {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0.7rem;
    width: 100%;
    justify-content: space-between;
    padding: 0 0.7rem;
    margin-bottom: 0;
    background: linear-gradient(135deg, #f8a5c2 0%, #a18cd1 100%);
    border-radius: 0 0 18px 18px;
    z-index: 1202;
    pointer-events: none;
  }
  .fabLeft, .fabRight {
    pointer-events: auto;
  }
  .socialFeedFab {
    width: 38px;
    height: 38px;
    font-size: 1.1rem;
    min-width: 38px;
    min-height: 38px;
    box-shadow: 0 2px 8px #f472b633;
    background: linear-gradient(135deg, #7c3aed 60%, #f472b6 100%);
    border: none;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 
