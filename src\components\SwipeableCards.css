/* Swipeable Cards - Mobile-First Interactive Component */

.swipeable-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px 0;
}

/* Progress Indicators */
.swipe-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #ffd700;
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

/* Swipe Wrapper */
.swipe-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 200px;
}

.swipe-card {
  width: 100%;
  height: 100%;
  cursor: grab;
  user-select: none;
  touch-action: pan-y;
  position: relative;
  z-index: 1;
}

.swipe-card.dragging {
  cursor: grabbing;
}

.swipe-card:active {
  cursor: grabbing;
}

/* Default Card Styling */
.default-card {
  padding: 24px;
  text-align: center;
  color: white;
}

.default-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #ffd700;
}

.default-card p {
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

/* Swipe Hints */
.swipe-hints {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 0;
}

.swipe-hint {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.6);
  animation: pulse-hint 2s ease-in-out infinite;
}

.swipe-hint.left {
  left: 12px;
}

.swipe-hint.right {
  right: 12px;
}

@keyframes pulse-hint {
  0%, 100% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}

/* Navigation */
.swipe-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 0 8px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.nav-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
  color: #ffd700;
  transform: scale(1.05);
}

.nav-btn:active:not(.disabled) {
  transform: scale(0.95);
}

.nav-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-counter {
  font-size: 0.85rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Touch Feedback */
.swipe-card:active {
  transform: scale(0.98);
}

/* Responsive Design */
@media (max-width: 480px) {
  .swipeable-container {
    padding: 16px 0;
  }
  
  .swipe-wrapper {
    min-height: 180px;
  }
  
  .default-card {
    padding: 20px;
  }
  
  .default-card h3 {
    font-size: 1.1rem;
  }
  
  .default-card p {
    font-size: 0.85rem;
  }
  
  .nav-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
  
  .swipe-hint {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
}

@media (min-width: 768px) {
  .swipeable-container {
    max-width: 500px;
  }
  
  .swipe-wrapper {
    min-height: 240px;
  }
  
  .default-card {
    padding: 32px;
  }
  
  .default-card h3 {
    font-size: 1.4rem;
  }
  
  .default-card p {
    font-size: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .indicator,
  .nav-btn,
  .swipe-card {
    transition: none;
  }
  
  .swipe-hint {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .swipe-wrapper {
    border: 2px solid white;
    background: rgba(0, 0, 0, 0.8);
  }
  
  .indicator {
    background: white;
  }
  
  .indicator.active {
    background: yellow;
  }
  
  .nav-btn {
    border-color: white;
    background: black;
  }
}
