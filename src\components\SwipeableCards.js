import React, { useState, useRef, useEffect } from 'react';
import './SwipeableCards.css';

const SwipeableCards = ({ items = [], onSwipe, renderCard }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [currentX, setCurrentX] = useState(0);
  const [translateX, setTranslateX] = useState(0);
  const cardRef = useRef(null);

  // Touch event handlers
  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartX(e.touches[0].clientX);
    setCurrentX(e.touches[0].clientX);
    
    // Haptic feedback
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    
    setCurrentX(e.touches[0].clientX);
    const deltaX = currentX - startX;
    setTranslateX(deltaX);
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    
    setIsDragging(false);
    const deltaX = currentX - startX;
    const threshold = 100; // Minimum swipe distance
    
    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0 && currentIndex > 0) {
        // Swipe right - previous
        setCurrentIndex(prev => prev - 1);
        onSwipe?.('previous', currentIndex - 1);
        
        // Haptic feedback for successful swipe
        if (navigator.vibrate) {
          navigator.vibrate([50, 10, 50]);
        }
      } else if (deltaX < 0 && currentIndex < items.length - 1) {
        // Swipe left - next
        setCurrentIndex(prev => prev + 1);
        onSwipe?.('next', currentIndex + 1);
        
        // Haptic feedback for successful swipe
        if (navigator.vibrate) {
          navigator.vibrate([50, 10, 50]);
        }
      }
    }
    
    // Reset position
    setTranslateX(0);
    setStartX(0);
    setCurrentX(0);
  };

  // Mouse event handlers for desktop
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.clientX);
    setCurrentX(e.clientX);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    setCurrentX(e.clientX);
    const deltaX = currentX - startX;
    setTranslateX(deltaX);
  };

  const handleMouseUp = () => {
    handleTouchEnd();
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowLeft' && currentIndex > 0) {
        setCurrentIndex(prev => prev - 1);
        onSwipe?.('previous', currentIndex - 1);
      } else if (e.key === 'ArrowRight' && currentIndex < items.length - 1) {
        setCurrentIndex(prev => prev + 1);
        onSwipe?.('next', currentIndex + 1);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentIndex, items.length, onSwipe]);

  if (!items.length) return null;

  return (
    <div className="swipeable-container">
      {/* Progress Indicators */}
      <div className="swipe-indicators">
        {items.map((_, index) => (
          <div
            key={index}
            className={`indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>

      {/* Swipeable Cards */}
      <div className="swipe-wrapper">
        <div
          ref={cardRef}
          className={`swipe-card ${isDragging ? 'dragging' : ''}`}
          style={{
            transform: `translateX(${translateX}px)`,
            transition: isDragging ? 'none' : 'transform 0.3s ease-out'
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {renderCard ? renderCard(items[currentIndex], currentIndex) : (
            <div className="default-card">
              <h3>{items[currentIndex].title}</h3>
              <p>{items[currentIndex].description}</p>
            </div>
          )}
        </div>

        {/* Swipe Hints */}
        <div className="swipe-hints">
          {currentIndex > 0 && (
            <div className="swipe-hint left">
              <span>←</span>
            </div>
          )}
          {currentIndex < items.length - 1 && (
            <div className="swipe-hint right">
              <span>→</span>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="swipe-nav">
        <button
          className={`nav-btn prev ${currentIndex === 0 ? 'disabled' : ''}`}
          onClick={() => currentIndex > 0 && setCurrentIndex(prev => prev - 1)}
          disabled={currentIndex === 0}
        >
          ‹
        </button>
        <span className="nav-counter">
          {currentIndex + 1} / {items.length}
        </span>
        <button
          className={`nav-btn next ${currentIndex === items.length - 1 ? 'disabled' : ''}`}
          onClick={() => currentIndex < items.length - 1 && setCurrentIndex(prev => prev + 1)}
          disabled={currentIndex === items.length - 1}
        >
          ›
        </button>
      </div>
    </div>
  );
};

export default SwipeableCards;
