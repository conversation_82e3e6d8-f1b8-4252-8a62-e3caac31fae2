// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useIsMobile } from '../context/ResponsiveContext'; // Added import
import './TrendingHairstylesSection.css';

const TrendingHairstylesSection = () => {
  const [trendingHairstyles, setTrendingHairstyles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isMobile = useIsMobile(); // Added
  const perPage = isMobile ? 2 : 4; // Simplified perPage logic
  const [currentPage, setCurrentPage] = useState(1);

  // Removed useEffect for perPage

  const totalPages = Math.ceil(trendingHairstyles.length / perPage);
  const paginatedHairstyles = trendingHairstyles.slice((currentPage - 1) * perPage, currentPage * perPage);
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i++) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  useEffect(() => {
    const fetchTrendingHairstyles = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/recommendations/?type=hairstyles');
        if (!res.ok) throw new Error('Failed to fetch trending hairstyles');
        const data = await res.json();
        setTrendingHairstyles(data);
      } catch (err) {
        setError('Could not load trending hairstyles.');
      }
      setLoading(false);
    };
    fetchTrendingHairstyles();
  }, []);

  return (
    <section className="trending-hairstyles-section">
      <div className="trending-hairstyles-container">

        {/* Section Header */}
        <div className="section-header">
          <div className="header-content">
            <h2 className="section-title">Trending Hairstyles</h2>
            <p className="section-subtitle">Latest styles everyone's trying</p>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="loading-state">
            <div className="loading-spinner" />
            <p className="loading-text">Loading trending hairstyles...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="error-state">
            <div className="error-icon">⚠️</div>
            <p className="error-text">{error}</p>
          </div>
        )}

        {/* Hairstyles Grid */}
        {!loading && !error && paginatedHairstyles.length > 0 && (
          <div className="hairstyles-grid">
            {paginatedHairstyles.map(hairstyle => (
              <div key={hairstyle.id} className="hairstyle-card trending-card">
                <div className="card-image-container">
                  <img 
                    src={hairstyle.imageUrl || 'https://via.placeholder.com/300x400?text=Hairstyle'} 
                    className="card-image" 
                    alt={hairstyle.name}
                    loading="lazy"
                  />
                  <div className="card-overlay">
                    <div className="trending-badge">
                      <span className="trending-icon">💇‍♀️</span>
                      {' '}
                      Trending
                    </div>
                  </div>
                </div>
                <div className="card-content">
                  <h3 className="hairstyle-name">{hairstyle.name}</h3>
                  <p className="hairstyle-description">{hairstyle.description || 'Latest trending style'}</p>
                  <div className="hairstyle-meta">
                    <span className="hairstyle-rating">⭐ 4.8</span>
                    <span className="hairstyle-status trending-status">Hot</span>
                  </div>
                  <Link to={`/hairstyle/${hairstyle.id}`} className="view-details-btn trending-btn">
                    View Style 
                    {' '}
                    <span className="btn-icon">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="pagination-container">
            {perPage === 2 ? (
              <div className="mobile-pagination">
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                  <span className="pagination-icon">←</span>
                </button>
                <span className="pagination-text">
                  {`Page ${currentPage} of ${totalPages}`}
                </span>
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>
                  <span className="pagination-icon">→</span>
                </button>
              </div>
            ) : (
              <div className="desktop-pagination">
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                  <span className="pagination-icon">←</span>
                </button>
                <div className="page-numbers">
                  {getPageNumbers().map((num, idx) => (num === 'ellipsis-left' || num === 'ellipsis-right' ? (
                    <span key={num + idx} className="page-ellipsis">...</span>
                  ) : (
                    <button 
                      key={num} 
                      className={`page-number ${currentPage === num ? 'active' : ''}`}
                      onClick={() => setCurrentPage(num)}
                    >
                      {num}
                    </button>
                  )))}
                </div>
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>
                  <span className="pagination-icon">→</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && trendingHairstyles.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">💇‍♀️</div>
            <h3>No Trending Hairstyles</h3>
            <p>Check back later for the latest trending styles.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default TrendingHairstylesSection;
