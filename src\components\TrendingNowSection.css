/* Trending Now Section - Mobile First Design */

.trending-now-section {
  padding: 2rem 1rem 0 1rem;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
  border-radius: 20px;
  margin: 2rem 0 0 0;
  position: relative;
  overflow: hidden;
  min-height: auto;
}

.trending-now-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.01)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.trending-now-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 0;
}

/* Section Header */
.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
  font-size: 1rem;
  color: #bdc3c7;
  margin: 0;
  font-weight: 400;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #e74c3c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1rem;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.error-text {
  color: #e74c3c;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

/* Items Grid - Modest & Sleek */
.items-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

/* Item Card */
.item-card {
  background: rgba(231, 76, 60, 0.08);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(231, 76, 60, 0.15);
  position: relative;
}

.trending-card {
  border-color: rgba(231, 76, 60, 0.3);
}

.item-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.trending-card:hover {
  border-color: rgba(231, 76, 60, 0.5);
  box-shadow: 0 10px 30px rgba(231, 76, 60, 0.2);
}

.card-image-container {
  position: relative;
  height: 100px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.item-card:hover .card-image {
  transform: scale(1.05);
}

.service-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #343a40;
}

.service-icon {
  font-size: 3rem;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), transparent);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
}

.trending-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 142, 83, 0.9));
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
}

.trending-icon {
  font-size: 0.9rem;
}

.card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  height: calc(100% - 120px);
  min-height: 180px;
}

.item-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-transform: none;
}

.item-location {
  color: #bdc3c7;
  font-size: 0.8rem;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-location::before {
  content: '📍';
  font-size: 0.8rem;
}

.item-price {
  color: #bdc3c7;
  font-size: 0.8rem;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-price::before {
  content: '💰';
  font-size: 0.8rem;
}

/* Badge Row Layout */
.salon-badges-row {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin-bottom: auto;
  margin-top: 0.5rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

/* Individual Badge Styling */
.salon-badge {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.65rem;
  font-weight: 600;
  flex-shrink: 0;
  white-space: nowrap;
}

/* Hot Badge */
.hot-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9) 0%, rgba(255, 142, 83, 0.9) 100%);
  color: white;
  box-shadow: 0 2px 6px rgba(255, 107, 157, 0.3);
}

/* Trending Badge */
.trending-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.8) 0%, rgba(255, 142, 83, 0.8) 100%);
  color: white;
  box-shadow: 0 2px 6px rgba(255, 107, 157, 0.2);
}

/* Rating Badge */
.rating-badge {
  background: rgba(255, 107, 157, 0.1);
  color: #f39c12;
  border: 1px solid rgba(255, 107, 157, 0.2);
}

.view-details-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  width: 100%;
  margin-top: 0.75rem;
  flex-shrink: 0;
}

.trending-btn {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 142, 83, 0.9));
  color: white;
}

.view-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
  color: white;
  text-decoration: none;
}

.trending-btn:hover {
  box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
  color: white;
}

.btn-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.view-details-btn:hover .btn-icon {
  transform: translateX(3px);
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

/* Mobile Pagination */
.mobile-pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-icon {
  font-size: 1.1rem;
}

.pagination-text {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Desktop Pagination */
.desktop-pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-number {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  color: #ffffff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.page-number.active {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.page-ellipsis {
  color: #bdc3c7;
  font-size: 0.9rem;
  padding: 0 0.5rem;
}

/* View All Button */
.view-all-container {
  display: flex;
  justify-content: center;
  margin: 0;
  padding-bottom: 0;
}

.view-all-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  border: none;
  border-radius: 25px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.view-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(243, 156, 18, 0.4);
  color: white;
  text-decoration: none;
}

.view-all-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.view-all-btn:hover .view-all-icon {
  transform: translateX(3px);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
}

.empty-state h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.empty-state p {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .trending-now-section {
    padding: 3rem 2rem;
  }

  .section-header {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 0;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .trending-now-section {
    padding: 4rem 3rem;
  }

  .section-title {
    font-size: 3rem;
  }

  .items-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .card-image-container {
    height: 140px;
  }
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .trending-now-container {
    max-width: 1400px;
  }

  .items-grid {
    gap: 2rem;
  }
}

/* Landscape Mode */
@media (max-height: 500px) and (orientation: landscape) {
  .trending-now-section {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .card-image-container {
    height: 150px;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .item-card {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .item-card,
  .view-details-btn,
  .pagination-btn,
  .page-number,
  .view-all-btn {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }

  .card-image {
    transition: none;
  }

  .btn-icon,
  .pagination-icon,
  .view-all-icon {
    transition: none;
  }
}

/* Mobile Optimization - Extra Small Screens */
@media (max-width: 320px) {
  .trending-now-section {
    padding: 1.5rem 0.5rem;
  }

  .items-grid {
    gap: 0.75rem;
    padding: 0 0.25rem;
  }

  .card-image-container {
    height: 80px;
  }

  .trending-card {
    border-radius: 12px;
  }

  .card-content {
    padding: 0.75rem;
  }

  .item-name {
    font-size: 0.9rem;
    line-height: 1.2;
  }

  .item-description {
    font-size: 0.75rem;
  }

  .item-meta {
    gap: 0.5rem;
    margin: 0.5rem 0;
  }

  .item-price,
  .item-trend {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .view-details-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
}

/* Focus States for Accessibility */
.item-card:focus-within {
  outline: 3px solid rgba(231, 76, 60, 0.5);
  outline-offset: 2px;
}

.view-details-btn:focus,
.pagination-btn:focus,
.page-number:focus,
.view-all-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .item-card:hover {
    transform: none;
  }

  .view-details-btn:hover,
  .pagination-btn:hover,
  .page-number:hover,
  .view-all-btn:hover {
    transform: none;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .trending-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 0 0.25rem;
  }

  .trending-card {
    border-radius: 16px;
  }

  .card-image-container {
    height: 100px;
  }

  .card-content {
    padding: 0.75rem;
    min-height: 160px;
  }

  .item-name {
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
  }

  .item-location, .item-price {
    font-size: 0.75rem;
    margin-bottom: 0.4rem;
  }

  .item-meta {
    margin-top: 0.4rem;
    gap: 0.4rem;
  }

  .item-rating {
    font-size: 0.7rem;
  }

  .item-status {
    font-size: 0.6rem;
    padding: 0.15rem 0.4rem;
  }

  .view-details-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.75rem;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 0.6rem;
    min-height: 150px;
  }

  .item-name {
    font-size: 0.85rem;
  }

  .item-location, .item-price {
    font-size: 0.7rem;
  }

  .view-details-btn {
    padding: 0.5rem 0.7rem;
    font-size: 0.7rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .trending-now-section {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.15), rgba(192, 57, 43, 0.15));
  }
} 