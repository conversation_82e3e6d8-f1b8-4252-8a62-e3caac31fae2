// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { FaChartLine, FaStar, FaMapMarkerAlt } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { useIsMobile } from '../context/ResponsiveContext';
import './SalonFinderProfile.css';
import './TrendingProfile.css';
import UniversalPagination from './UniversalPagination';

const TrendingNowSection = () => {
  const { authFetch } = useAuth();
  const [trendingItems, setTrendingItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const isMobile = useIsMobile();
  const perPage = isMobile ? 2 : 4;

  // Reset to page 1 when screen size changes
  useEffect(() => {
    setCurrentPage(1);
  }, [isMobile]);

  useEffect(() => {
    const fetchTrending = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await authFetch('/api/recommendations/trending/');
        if (!res.ok) throw new Error('Failed to fetch trending items');
        const data = await res.json();
        const salons = (data.trending_salons || []).map(salon => ({ ...salon, type: 'salon' }));
        const services = (data.trending_services || []).map(service => ({ ...service, type: 'service' }));
        const combined = [...salons, ...services].sort(() => 0.5 - Math.random());
        
        // Add mock data for testing pagination if not enough items
        if (combined.length < 6) {
          const mockItems = [
            { id: 'mock1', name: 'Trending Salon 1', type: 'salon', imageUrl: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format', town: 'Nairobi', address: 'Westlands' },
            { id: 'mock2', name: 'Trending Salon 2', type: 'salon', imageUrl: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format', town: 'Nairobi', address: 'Kilimani' },
            { id: 'mock3', name: 'Trending Service 1', type: 'service', price: '2500', salon_name: 'Beauty Hub' },
            { id: 'mock4', name: 'Trending Service 2', type: 'service', price: '1800', salon_name: 'Style Studio' },
            { id: 'mock5', name: 'Trending Salon 3', type: 'salon', imageUrl: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format', town: 'Nairobi', address: 'Lavington' },
            { id: 'mock6', name: 'Trending Service 3', type: 'service', price: '3200', salon_name: 'Elite Salon' },
            { id: 'mock7', name: 'Trending Salon 4', type: 'salon', imageUrl: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format', town: 'Nairobi', address: 'Karen' },
            { id: 'mock8', name: 'Trending Service 4', type: 'service', price: '2100', salon_name: 'Modern Cuts' }
          ];
          setTrendingItems([...combined, ...mockItems]);
        } else {
          setTrendingItems(combined);
        }
      } catch (err) {
        setError('No trending items to display.');
      }
      setLoading(false);
    };
    fetchTrending();
  }, [authFetch]);

  // Pagination logic
  const totalPages = Math.ceil(trendingItems.length / perPage);
  const paginatedItems = trendingItems.slice((currentPage - 1) * perPage, currentPage * perPage);
  
  // Debug logging
  console.log('🔥 TrendingNowSection RENDERING - CHANGES SHOULD BE VISIBLE:', {
    totalItems: trendingItems.length,
    perPage,
    totalPages,
    currentPage,
    isMobile,
    paginatedItemsLength: paginatedItems.length,
    timestamp: new Date().toLocaleTimeString()
  });
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i++) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  return (
    <div className="salon-finder-profile-container trending-theme">
      <div className="profile-container">
        {/* Trending Sparkles */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">🔥</span>
          <span className="auth-sparkle">📈</span>
          <span className="auth-sparkle">⭐</span>
        </div>

        {/* Header Section */}
        <div className="profile-header trending-header">

          <h1 className="profile-title">🔥 Trending Now</h1>
          <p className="profile-subtitle">
            What's hot right now in your area - discover the most popular salons and services
          </p>
        </div>

        {/* Content Area */}
        <div className="profile-content">

          {/* Trending Items Section */}
          <div className="profile-section">
            <h3 className="section-title">
              🔥 Hot Trending Items
            </h3>

            {/* Loading State */}
            {loading && (
              <div className="loading-state">
                <div className="loading-spinner"></div>
                <p>Loading trending items...</p>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="no-results">
                <div className="no-results-icon">⚠️</div>
                <h3>Oops! Something went wrong</h3>
                <p>{error}</p>
              </div>
            )}

            {/* Items Grid */}
            {!loading && !error && paginatedItems.length > 0 && (
              <div className="salon-grid">
                {paginatedItems.map(item => (
                  <div key={item.id} className="salon-card">
                    <div className="salon-image">
                      {item.type === 'salon' ? (
                        <img
                          src={item.imageUrl || 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format'}
                          alt={item.name}
                          onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&auto=format';
                          }}
                        />
                      ) : (
                        <div style={{
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(135deg, #ff6b9d, #ff8e53)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '2rem'
                        }}>
                          ✂️
                        </div>
                      )}
                    </div>
                    <div className="salon-info">
                      <h4 className="salon-name">{item.name}</h4>
                      <p className="salon-location">
                        <FaMapMarkerAlt />
                        {item.type === 'salon' ? `${item.town}, ${item.address}` : `Ksh ${item.price} at ${item.salon_name}`}
                      </p>
                      <div className="salon-badges-row">
                        <span className="salon-badge hot-badge">
                          <span>🔥</span>
                          <span>Hot</span>
                        </span>
                        <span className="salon-badge trending-badge">
                          <span>📈</span>
                          <span>Trending</span>
                        </span>
                        <span className="salon-badge rating-badge">⭐ 4.7</span>
                      </div>
                      <Link
                        to={item.type === 'salon' ? `/salon/${item.id}` : `/service/${item.salon || item.salonId}`}
                        className="btn-primary view-details-btn"
                      >
                        View {item.type === 'salon' ? 'Salon' : 'Service'} →
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination-wrapper">
                <UniversalPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  variant="default"
                />
              </div>
            )}

            {/* View All Button */}
            {!loading && !error && trendingItems.length > 0 && (
              <div style={{ textAlign: 'center', marginTop: '1rem' }}>
                <Link to="/trending" className="btn-secondary">
                  View All Trending →
                </Link>
              </div>
            )}

            {/* Empty State */}
            {!loading && !error && trendingItems.length === 0 && (
              <div className="no-results">
                <div className="no-results-icon">📈</div>
                <h3>No Trending Items</h3>
                <p>Check back later for trending salons and services in your area.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendingNowSection;
