/* Modern Trending Page Styles */
.trending-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%);
  padding: 0;
}

/* Header */
.trending-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(231, 76, 60, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(231, 76, 60, 0.1);
  transform: translateX(-2px);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
}

.fire-icon {
  color: #e74c3c;
  font-size: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.header-title h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.filter-toggle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-toggle:hover {
  background: rgba(231, 76, 60, 0.2);
  transform: scale(1.1);
}

/* Filters Section */
.filters-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(231, 76, 60, 0.2);
  padding: 1rem 1.5rem;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid rgba(231, 76, 60, 0.3);
  background: rgba(231, 76, 60, 0.1);
  color: #333;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.filter-btn:hover {
  background: rgba(231, 76, 60, 0.2);
  transform: translateY(-1px);
}

.filter-btn.active {
  background: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

/* Content */
.trending-content {
  padding: 2rem 1.5rem;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 40vh;
  color: #333;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(231, 76, 60, 0.3);
  border-top: 3px solid #e74c3c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 40vh;
  text-align: center;
  color: #333;
}

.error-state h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 40vh;
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  color: #e74c3c;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Trending Grid */
.trending-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.trending-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.1);
  transition: all 0.3s ease;
}

.trending-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

.card-image-container {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.trending-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.trending-badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background: rgba(231, 76, 60, 0.95);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.card-type-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.card-content {
  padding: 1rem;
}

.item-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.item-location,
.item-price {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #666;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.item-location svg,
.item-price svg {
  color: #e74c3c;
}

.view-button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  text-decoration: none;
  border-radius: 10px;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.view-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
  color: white;
  text-decoration: none;
}

/* Desktop Styles */
@media (min-width: 768px) {
  .trending-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .card-image-container {
    height: 180px;
  }

  .item-name {
    font-size: 1.1rem;
  }

  .item-location,
  .item-price {
    font-size: 0.9rem;
  }
}

/* Mobile Optimization */
@media (max-width: 320px) {
  .trending-header {
    padding: 1rem;
  }

  .trending-content {
    padding: 1rem;
  }

  .trending-grid {
    gap: 0.75rem;
  }

  .card-image-container {
    height: 120px;
  }

  .card-content {
    padding: 0.75rem;
  }

  .item-name {
    font-size: 0.9rem;
  }

  .item-location,
  .item-price {
    font-size: 0.75rem;
  }

  .view-button {
    padding: 0.6rem;
    font-size: 0.8rem;
  }
}
