/* Trending Now Section - Unique Theme with Profile Design Pattern */

/* Trending Theme Override */
.salon-finder-profile-container.trending-theme {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  min-height: auto;
  padding: 1rem 0.5rem;
}

.salon-finder-profile-container.trending-theme::before {
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="trending-grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.15)"/></pattern></defs><rect width="100" height="100" fill="url(%23trending-grain)"/></svg>');
  opacity: 0.4;
}

/* Trending Header */
.salon-finder-profile-container.trending-theme .profile-header.trending-header {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.15) 0%, rgba(192, 57, 43, 0.15) 100%);
  padding: 1.5rem 1rem 1rem;
}

.salon-finder-profile-container.trending-theme .auth-icon-wrapper.trending-icon {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  box-shadow: 0 4px 16px rgba(231, 76, 60, 0.4);
}

.salon-finder-profile-container.trending-theme .profile-title {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.8rem;
}

/* Trending Sparkles */
.salon-finder-profile-container.trending-theme .auth-sparkle:nth-child(1) {
  color: #e74c3c;
}

.salon-finder-profile-container.trending-theme .auth-sparkle:nth-child(2) {
  color: #f39c12;
}

.salon-finder-profile-container.trending-theme .auth-sparkle:nth-child(3) {
  color: #e74c3c;
}

/* Trending Section */
.salon-finder-profile-container.trending-theme .profile-section {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(231, 76, 60, 0.1);
}

.salon-finder-profile-container.trending-theme .section-title {
  color: #e74c3c;
}

/* Trending Salon Cards */
.salon-finder-profile-container.trending-theme .salon-card {
  border: 1px solid rgba(231, 76, 60, 0.2);
  background: #fafafa !important;
}

.salon-finder-profile-container.trending-theme .salon-card:hover {
  box-shadow: 0 8px 24px rgba(231, 76, 60, 0.2);
  transform: translateY(-2px);
}

/* Salon Name - Normal Case */
.salon-finder-profile-container.trending-theme .salon-name {
  text-transform: none !important;
  font-weight: 600;
  font-size: 1.1rem;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

/* Rating Centered */
.salon-finder-profile-container.trending-theme .salon-rating-centered {
  text-align: center;
  color: #f39c12;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

/* Badge Row Layout */
.salon-finder-profile-container.trending-theme .salon-badges-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

/* Individual Badge Styling */
.salon-finder-profile-container.trending-theme .salon-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
  white-space: nowrap;
}

/* Hot Badge */
.salon-finder-profile-container.trending-theme .hot-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9) 0%, rgba(255, 142, 83, 0.9) 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
}

/* Trending Badge */
.salon-finder-profile-container.trending-theme .trending-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.8) 0%, rgba(255, 142, 83, 0.8) 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.2);
}

/* Rating Badge */
.salon-finder-profile-container.trending-theme .rating-badge {
  background: rgba(255, 107, 157, 0.1);
  color: #f39c12;
  border: 1px solid rgba(255, 107, 157, 0.2);
}

/* Trending Status */
.salon-finder-profile-container.trending-theme .salon-status {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

/* Trending Distance/Rating */
.salon-finder-profile-container.trending-theme .salon-distance {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.2);
}

/* Trending Buttons */
.salon-finder-profile-container.trending-theme .btn-primary {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.salon-finder-profile-container.trending-theme .btn-primary:hover {
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.salon-finder-profile-container.trending-theme .btn-secondary {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.2);
}

.salon-finder-profile-container.trending-theme .btn-secondary:hover {
  background: rgba(231, 76, 60, 0.2);
  border-color: rgba(231, 76, 60, 0.3);
}

/* CRITICAL: Fix Image Stretching for Trending */
.salon-finder-profile-container.trending-theme .salon-image img {
  width: 100% !important;
  height: 140px !important;
  object-fit: cover !important;
  display: block !important;
  min-height: 140px !important;
  max-height: 140px !important;
  aspect-ratio: auto !important;
}

/* Mobile Responsive for Trending */
@media (max-width: 768px) {
  .salon-finder-profile-container.trending-theme {
    padding: 0.25rem;
  }
  
  .salon-finder-profile-container.trending-theme .profile-header.trending-header {
    padding: 1.25rem 0.75rem 1rem;
  }
  
  .salon-finder-profile-container.trending-theme .profile-title {
    font-size: 1.6rem;
  }
}

@media (max-width: 480px) {
  .salon-finder-profile-container.trending-theme .salon-image img {
    height: 120px !important;
    min-height: 120px !important;
    max-height: 120px !important;
  }
}

/* Loading and Error States for Trending */
.salon-finder-profile-container.trending-theme .loading-spinner {
  border-top-color: #e74c3c;
}

.salon-finder-profile-container.trending-theme .no-results-icon {
  color: #e74c3c;
}

/* Trending Animation */
@keyframes trendingPulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4);
  }
  50% { 
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
}

.salon-finder-profile-container.trending-theme .auth-icon-wrapper.trending-icon {
  animation: trendingPulse 2s infinite;
}

/* Trending Grid Optimization */
.salon-finder-profile-container.trending-theme .salon-grid {
  gap: 0.75rem;
}

/* Trending Service Placeholder */
.salon-finder-profile-container.trending-theme .salon-image div[style*="linear-gradient"] {
  background: linear-gradient(135deg, #e74c3c, #f39c12) !important;
}

/* Dark Mode Support for Trending */
@media (prefers-color-scheme: dark) {
  .salon-finder-profile-container.trending-theme {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.9) 0%, rgba(192, 57, 43, 0.9) 100%);
  }
  
  .salon-finder-profile-container.trending-theme .profile-section {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(231, 76, 60, 0.3);
  }
  
  .salon-finder-profile-container.trending-theme .salon-card {
    background: rgba(0, 0, 0, 0.7);
    border-color: rgba(231, 76, 60, 0.2);
  }
}

/* Accessibility for Trending */
.salon-finder-profile-container.trending-theme .salon-card:focus-within {
  outline: 3px solid rgba(231, 76, 60, 0.5);
  outline-offset: 2px;
}

/* High DPI Support for Trending */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .salon-finder-profile-container.trending-theme .salon-card {
    border-width: 0.5px;
  }
}
