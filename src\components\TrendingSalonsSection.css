/* Trending Salons Section - Mobile First Design */

.trending-salons-section {
  padding: 2rem 1rem;
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
  border-radius: 20px;
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}

.trending-salons-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.01)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.01)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.trending-salons-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

/* Section Header */
.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
  font-size: 1rem;
  color: #bdc3c7;
  margin: 0;
  font-weight: 400;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #2ecc71;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1rem;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.error-text {
  color: #e74c3c;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

/* Salons Grid */
.salons-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Salon Card */
.salon-card {
  background: rgba(255, 107, 157, 0.08);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 107, 157, 0.15);
  position: relative;
}

.trending-card {
  border-color: rgba(255, 107, 157, 0.3);
}

.salon-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.trending-card:hover {
  border-color: rgba(255, 107, 157, 0.5);
  box-shadow: 0 10px 30px rgba(255, 107, 157, 0.2);
}

.card-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.salon-card:hover .card-image {
  transform: scale(1.05);
}

.salon-badge-banner {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 0.75rem;
  width: 100%;
  overflow: hidden;
}

.trending-badge-banner {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 142, 83, 0.9));
  color: #ffffff;
  padding: 0.6rem 0.8rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  min-height: 1.8rem;
}

.trending-icon {
  font-size: 0.9rem;
}

.card-content {
  padding: 1.5rem;
}

.salon-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  text-transform: none;
}

.salon-location {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.salon-location::before {
  content: '📍';
  font-size: 0.8rem;
}

/* Badge Row Layout */
.salon-badges-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

/* Individual Badge Styling */
.salon-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
  white-space: nowrap;
}

/* Hot Badge */
.hot-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9) 0%, rgba(255, 142, 83, 0.9) 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
}

/* Trending Badge */
.trending-badge {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.8) 0%, rgba(255, 142, 83, 0.8) 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.2);
}

/* Rating Badge */
.rating-badge {
  background: rgba(255, 107, 157, 0.1);
  color: #f39c12;
  border: 1px solid rgba(255, 107, 157, 0.2);
}

.view-details-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #ff6b9d, #ff8e53);
  color: white;
  text-decoration: none;
  padding: 0.875rem 1.25rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  width: 100%;
}

.trending-btn {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 142, 83, 0.9));
  color: white;
}

.view-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
  color: white;
  text-decoration: none;
}

.trending-btn:hover {
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
  color: white;
}

.btn-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.view-details-btn:hover .btn-icon {
  transform: translateX(3px);
}

/* Show More Button */
.show-more-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.show-more-btn {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  border: none;
  border-radius: 25px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
}

.show-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(46, 204, 113, 0.4);
}

.show-more-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.show-more-btn:hover .show-more-icon {
  transform: translateY(2px);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
}

.empty-state h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.empty-state p {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin: 0;
  max-width: 300px;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .trending-salons-section {
    padding: 3rem 2rem;
  }

  .section-header {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .salons-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .trending-salons-section {
    padding: 4rem 3rem;
  }

  .section-title {
    font-size: 3rem;
  }

  .salons-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  .card-image-container {
    height: 220px;
  }
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .trending-salons-container {
    max-width: 1400px;
  }

  .salons-grid {
    gap: 2.5rem;
  }
}

/* Landscape Mode */
@media (max-height: 500px) and (orientation: landscape) {
  .trending-salons-section {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .card-image-container {
    height: 150px;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .salon-card {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .salon-card,
  .view-details-btn,
  .show-more-btn {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }

  .card-image {
    transition: none;
  }

  .btn-icon {
    transition: none;
  }
}

/* Focus States for Accessibility */
.salon-card:focus-within {
  outline: 3px solid rgba(46, 204, 113, 0.5);
  outline-offset: 2px;
}

.view-details-btn:focus,
.show-more-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .salon-card:hover {
    transform: none;
  }

  .view-details-btn:hover,
  .show-more-btn:hover {
    transform: none;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .salon-badge-banner {
    margin-bottom: 0.5rem;
  }

  .trending-badge-banner {
    padding: 0.5rem 0.6rem;
    font-size: 0.65rem;
    gap: 0.2rem;
    letter-spacing: 0.2px;
    min-height: 1.6rem;
  }
}

@media (max-width: 480px) {
  .trending-badge-banner {
    padding: 0.4rem 0.5rem;
    font-size: 0.6rem;
    gap: 0.15rem;
    min-height: 1.4rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .trending-salons-section {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15), rgba(39, 174, 96, 0.15));
  }
} 