// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import './TrendingSalonsSection.css';

const TrendingSalonsSection = () => {
  const [trendingSalons, setTrendingSalons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const getPerPage = () => (window.innerWidth <= 480 ? 2 : 4);
  const [perPage, setPerPage] = useState(getPerPage());
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    const handleResize = () => setPerPage(getPerPage());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalPages = Math.ceil(trendingSalons.length / perPage);
  const paginatedSalons = trendingSalons.slice((currentPage - 1) * perPage, currentPage * perPage);
  const getPageNumbers = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage > 3) {
        pages.push(1);
        if (currentPage > 4) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);
      for (let i = start; i <= end; i++) pages.push(i);
      if (currentPage < totalPages - 2) {
        if (currentPage < totalPages - 3) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  useEffect(() => {
    const fetchTrendingSalons = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/recommendations/trending/');
        if (!res.ok) throw new Error('Failed to fetch trending salons');
        const data = await res.json();
        setTrendingSalons(data.trending_salons);
      } catch (err) {
        setError('No trending salons to display.');
      }
      setLoading(false);
    };
    fetchTrendingSalons();
  }, []);

  return (
    <section className="trending-salons-section">
      <div className="trending-salons-container">

        {/* Section Header */}
        <div className="section-header">
          <div className="header-content">
            <h2 className="section-title">Trending Salons</h2>
            <p className="section-subtitle">Hot salons everyone's talking about</p>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="loading-state">
            <div className="loading-spinner" />
            <p className="loading-text">Loading trending salons...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="error-state">
            <div className="error-icon">⚠️</div>
            <p className="error-text">{error}</p>
          </div>
        )}

        {/* Salons Grid */}
        {!loading && !error && paginatedSalons.length > 0 && (
          <div className="salons-grid">
            {paginatedSalons.map(salon => (
              <div key={salon.id} className="salon-card trending-card">
                <div className="card-image-container">
                  <img
                    src={salon.imageUrl}
                    className="card-image"
                    alt={salon.name}
                    loading="lazy"
                  />
                </div>
                <div className="card-content">
                  <div className="salon-badge-banner">
                    <div className="trending-badge-banner">
                      <span className="trending-icon">🚀</span>
                      Trending
                    </div>
                  </div>
                  <h3 className="salon-name">{salon.name}</h3>
                  <p className="salon-location">
                    {`${salon.town}, ${salon.address}`}
                  </p>
                  <div className="salon-meta">
                    <span className="salon-rating">⭐ 4.7</span>
                    <span className="salon-status trending-status">Hot</span>
                  </div>
                  <Link to={`/salon/${salon.id}`} className="view-details-btn trending-btn">
                    View Salon 
                    {' '}
                    <span className="btn-icon">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="pagination-container">
            {perPage === 2 ? (
              <div className="mobile-pagination">
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                  <span className="pagination-icon">←</span>
                </button>
                <span className="pagination-text">
                  {`Page ${currentPage} of ${totalPages}`}
                </span>
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>
                  <span className="pagination-icon">→</span>
                </button>
              </div>
            ) : (
              <div className="desktop-pagination">
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                  <span className="pagination-icon">←</span>
                </button>
                <div className="page-numbers">
                  {getPageNumbers().map((num, idx) => (num === 'ellipsis-left' || num === 'ellipsis-right' ? (
                    <span key={num + idx} className="page-ellipsis">...</span>
                  ) : (
                    <button 
                      key={num} 
                      className={`page-number ${currentPage === num ? 'active' : ''}`}
                      onClick={() => setCurrentPage(num)}
                    >
                      {num}
                    </button>
                  )))}
                </div>
                <button className="pagination-btn" onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages}>
                  <span className="pagination-icon">→</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && trendingSalons.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">📈</div>
            <h3>No Trending Salons</h3>
            <p>Check back later for trending salons in your area.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default TrendingSalonsSection;
