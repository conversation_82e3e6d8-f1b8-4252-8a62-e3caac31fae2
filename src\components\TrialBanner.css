/* Trial Banner Styles */
.trial-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 12px 20px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.trial-banner-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
}

.trial-banner-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.trial-banner-text {
    flex: 1;
    min-width: 0;
}

.trial-banner-title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
}

.trial-banner-message {
    margin: 0 0 4px 0;
    font-size: 14px;
    line-height: 1.4;
    opacity: 0.9;
}

.trial-banner-time {
    margin: 0;
    font-size: 12px;
    font-weight: 500;
    opacity: 0.8;
}

.trial-banner-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
}

.trial-banner-action-btn,
.trial-banner-start-trial-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.trial-banner-action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    backdrop-filter: blur(10px);
}

.trial-banner-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.trial-banner-start-trial-btn {
    background: #10b981;
    color: white;
    font-weight: 600;
}

.trial-banner-start-trial-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.trial-banner-close {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.1);
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.trial-banner-close:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

/* Status-specific colors */
.trial-banner-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.trial-banner-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.trial-banner-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.trial-banner-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

/* Action button colors for different statuses */
.trial-banner-action-danger {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.trial-banner-action-warning {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.trial-banner-action-info {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.trial-banner-action-success {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .trial-banner {
        padding: 16px;
    }
    
    .trial-banner-content {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .trial-banner-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .trial-banner-action-btn,
    .trial-banner-start-trial-btn {
        width: 100%;
        padding: 12px 16px;
    }
    
    .trial-banner-close {
        position: static;
        align-self: flex-end;
        margin-top: -8px;
    }
}

@media (max-width: 480px) {
    .trial-banner-title {
        font-size: 15px;
    }
    
    .trial-banner-message {
        font-size: 13px;
    }
    
    .trial-banner-time {
        font-size: 11px;
    }
} 