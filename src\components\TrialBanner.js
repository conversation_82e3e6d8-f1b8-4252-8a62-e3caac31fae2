import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useNotification } from '../context/NotificationContext';
import TrialService from '../services/trialService';
import './TrialBanner.css';

const TrialBanner = () => {
    const [trialStatus, setTrialStatus] = useState(null);
    const [trialInfo, setTrialInfo] = useState(null);
    const [isVisible, setIsVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const { user } = useAuth();
    const { showNotification } = useNotification();
    const navigate = useNavigate();

    useEffect(() => {
        // Only show for vendors or users with salons
        if (!user || (!user.is_vendor && !user.user_type === 'vendor')) {
            setIsLoading(false);
            return;
        }

        loadTrialStatus();
    }, [user]);

    const loadTrialStatus = async () => {
        try {
            setIsLoading(true);
            const [statusResponse, infoResponse] = await Promise.all([
                TrialService.getTrialStatus(),
                TrialService.getTrialInfo()
            ]);

            setTrialStatus(statusResponse);
            setTrialInfo(infoResponse);

            // Show banner if needed
            const shouldShow = TrialService.shouldShowTrialBanner(statusResponse);
            setIsVisible(shouldShow);

        } catch (error) {
            console.error('Error loading trial status:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleStartTrial = async () => {
        try {
            const response = await TrialService.startTrial();
            showNotification('Trial started successfully! You have 48 hours to explore premium features.', 'success');
            loadTrialStatus(); // Refresh status
        } catch (error) {
            showNotification('Failed to start trial. Please try again.', 'error');
        }
    };

    const handleActionClick = () => {
        const bannerConfig = TrialService.getTrialBannerConfig(trialStatus);
        if (bannerConfig?.actionUrl) {
            navigate(bannerConfig.actionUrl);
        }
    };

    const handleClose = () => {
        setIsVisible(false);
    };

    if (isLoading || !isVisible || !trialStatus) {
        return null;
    }

    const bannerConfig = TrialService.getTrialBannerConfig(trialStatus);
    if (!bannerConfig) {
        return null;
    }

    const statusColor = TrialService.getTrialStatusColor(trialStatus);
    const timeRemaining = TrialService.formatTimeRemaining(trialStatus);

    return (
        <div className={`trial-banner trial-banner-${statusColor}`}>
            <div className="trial-banner-content">
                <div className="trial-banner-icon">
                    {statusColor === 'danger' && '⏰'}
                    {statusColor === 'warning' && '⚠️'}
                    {statusColor === 'info' && 'ℹ️'}
                    {statusColor === 'success' && '✨'}
                </div>
                
                <div className="trial-banner-text">
                    <h4 className="trial-banner-title">{bannerConfig.title}</h4>
                    <p className="trial-banner-message">{bannerConfig.message}</p>
                    {timeRemaining && (
                        <p className="trial-banner-time">{timeRemaining}</p>
                    )}
                </div>

                <div className="trial-banner-actions">
                    {bannerConfig.actionText && (
                        <button
                          className={`trial-banner-action-btn trial-banner-action-${statusColor}`}
                          onClick={handleActionClick}
                        >
                            {bannerConfig.actionText}
                        </button>
                    )}
                    
                    {!trialInfo?.has_subscription && (
                        <button
                          className="trial-banner-start-trial-btn"
                          onClick={handleStartTrial}
                        >
                            Start Free Trial
                        </button>
                    )}
                </div>

                {bannerConfig.showClose && (
                    <button
                      className="trial-banner-close"
                      onClick={handleClose}
                      aria-label="Close trial banner"
                    >
                        ×
                    </button>
                )}
            </div>
        </div>
    );
};

export default TrialBanner;