/* Trial Start Component Styles */
.trial-start-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.1),
    rgba(52, 211, 153, 0.1),
    rgba(34, 197, 94, 0.1)
  );
  padding: 2rem;
}

.trial-start-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.trial-start-content h2 {
  color: #10B981;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.trial-start-content p {
  color: #374151;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.loading-spinner {
  margin-bottom: 2rem;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(16, 185, 129, 0.2);
  border-top: 4px solid #10B981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.trial-start-content.error {
  border-color: rgba(239, 68, 68, 0.2);
}

.trial-start-content.error h2 {
  color: #EF4444;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.retry-btn,
.back-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0.5rem;
}

.retry-btn {
  background: #10B981;
  color: white;
}

.retry-btn:hover {
  background: #059669;
  transform: translateY(-2px);
}

.back-btn {
  background: #6B7280;
  color: white;
}

.back-btn:hover {
  background: #4B5563;
  transform: translateY(-2px);
}

.server-help {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  text-align: left;
  font-size: 0.9rem;
}

.server-help p {
  margin: 0.5rem 0;
  color: #374151;
}

.server-help code {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .trial-start-container {
    padding: 1rem;
  }
  
  .trial-start-content {
    padding: 2rem 1.5rem;
  }
  
  .trial-start-content h2 {
    font-size: 1.5rem;
  }
  
  .trial-start-content p {
    font-size: 1rem;
  }
}

/* Trial Success Popup */
.trial-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.trial-popup {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.trial-popup-content {
  text-align: center;
}

.trial-popup-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.trial-popup-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.trial-popup-message {
  color: #64748b;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.trial-popup-action {
  color: #3b82f6;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.trial-popup-buttons {
  display: flex;
  gap: 0.75rem;
  flex-direction: column;
}

.trial-popup-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95rem;
}

.trial-popup-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.trial-popup-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.trial-popup-btn.secondary {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.trial-popup-btn.secondary:hover {
  background: #f1f5f9;
  color: #475569;
}