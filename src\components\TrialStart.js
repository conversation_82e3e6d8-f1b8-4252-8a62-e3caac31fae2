import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useNotification } from '../context/NotificationContext';
import TrialService from '../services/trialService';
import './TrialStart.css';

const TrialStart = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  useEffect(() => {
    const startTrial = async () => {
      try {
        setIsLoading(true);
        
        // Check if user is authenticated and has vendor access
        if (!user) {
          setError('Please log in to start a trial');
          return;
        }

        // Allow trial for vendors or users who want to become vendors
        // The backend will handle salon creation if needed

        // Start the trial
        const response = await TrialService.startTrial();
        
        if (response.success) {
          // Show elegant popup instead of notification
          setShowSuccessPopup(true);
        } else {
          setError(response.error || 'Failed to start trial');
        }
      } catch (err) {
        console.error('Error starting trial:', err);
        setError(err.message || 'Failed to start trial. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    startTrial();
  }, [user, navigate, showNotification]);

  if (isLoading) {
    return (
      <div className="trial-start-container">
        <div className="trial-start-content">
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <h2>Starting Your Free Trial...</h2>
          <p>Please wait while we activate your 48-hour trial period</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="trial-start-container">
        <div className="trial-start-content error">
          <div className="error-icon">❌</div>
          <h2>Trial Start Failed</h2>
          <p>{error}</p>
          <button 
            className="retry-btn"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
          <button
            className="back-btn"
            onClick={() => navigate('/vendor/subscription')}
          >
            Back to Subscription
          </button>
          {error.includes('backend server') && (
            <div className="server-help">
              <p>💡 <strong>For Developers:</strong></p>
              <p>Make sure the Django backend server is running on port 8000:</p>
              <code>python manage.py runserver 8000</code>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Success popup
  if (showSuccessPopup) {
    return (
      <div className="trial-popup-overlay">
        <div className="trial-popup">
          <div className="trial-popup-content">
            <div className="trial-popup-icon">✨</div>
            <h2 className="trial-popup-title">Trial Activated!</h2>
            <p className="trial-popup-message">
              Your 48-hour trial has been successfully activated.
              You now have access to all premium features.
            </p>
            <p className="trial-popup-action">
              <strong>Next step:</strong> Update your vendor profile to get the most out of your trial.
            </p>
            <div className="trial-popup-buttons">
              <button
                className="trial-popup-btn primary"
                onClick={() => navigate('/vendor/profile')}
              >
                Update Profile
              </button>
              <button
                className="trial-popup-btn secondary"
                onClick={() => navigate('/vendor/subscription')}
              >
                View Plans
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default TrialStart;