/* Trial Timer - Sleek and Non-Obtrusive */
.trial-timer {
  position: fixed;
  top: 80px;
  right: 1rem;
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 0.75rem 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 120px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.trial-timer:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.trial-timer-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.trial-timer-icon {
  font-size: 1.2rem;
  line-height: 1;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.trial-timer-info {
  flex: 1;
  min-width: 0;
}

.trial-timer-label {
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.7;
  line-height: 1;
  margin-bottom: 0.2rem;
}

.trial-timer-time {
  font-size: 0.9rem;
  font-weight: 700;
  line-height: 1;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.trial-timer-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.trial-timer-progress-bar {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 0 0 16px 16px;
}

/* Color Variants */
.trial-timer-active {
  border-left: 4px solid #10B981;
}

.trial-timer-active .trial-timer-label,
.trial-timer-active .trial-timer-time {
  color: #10B981;
}

.trial-timer-active .trial-timer-progress-bar {
  background: linear-gradient(90deg, #10B981, #059669);
}

.trial-timer-warning {
  border-left: 4px solid #F59E0B;
}

.trial-timer-warning .trial-timer-label,
.trial-timer-warning .trial-timer-time {
  color: #F59E0B;
}

.trial-timer-warning .trial-timer-progress-bar {
  background: linear-gradient(90deg, #F59E0B, #D97706);
}

.trial-timer-critical {
  border-left: 4px solid #EF4444;
  animation: criticalPulse 1s ease-in-out infinite;
}

@keyframes criticalPulse {
  0%, 100% { 
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  50% { 
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
  }
}

.trial-timer-critical .trial-timer-label,
.trial-timer-critical .trial-timer-time {
  color: #EF4444;
}

.trial-timer-critical .trial-timer-progress-bar {
  background: linear-gradient(90deg, #EF4444, #DC2626);
}

.trial-timer-expired {
  border-left: 4px solid #6B7280;
  opacity: 0.8;
}

.trial-timer-expired .trial-timer-label,
.trial-timer-expired .trial-timer-time {
  color: #6B7280;
}

.trial-timer-expired .trial-timer-progress-bar {
  background: #6B7280;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .trial-timer {
    top: 70px;
    right: 0.5rem;
    padding: 0.5rem 0.75rem;
    min-width: 100px;
    font-size: 0.85rem;
  }
  
  .trial-timer-icon {
    font-size: 1rem;
  }
  
  .trial-timer-label {
    font-size: 0.6rem;
  }
  
  .trial-timer-time {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .trial-timer {
    top: 60px;
    right: 0.25rem;
    padding: 0.4rem 0.6rem;
    min-width: 90px;
  }
  
  .trial-timer-content {
    gap: 0.4rem;
  }
  
  .trial-timer-icon {
    font-size: 0.9rem;
  }
  
  .trial-timer-label {
    font-size: 0.55rem;
  }
  
  .trial-timer-time {
    font-size: 0.75rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .trial-timer {
    background: #ffffff;
    border: 2px solid #000000;
  }
  
  .trial-timer-active .trial-timer-label,
  .trial-timer-active .trial-timer-time {
    color: #000000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .trial-timer {
    animation: none;
  }
  
  .trial-timer-icon {
    animation: none;
  }
  
  .trial-timer-critical {
    animation: none;
  }
  
  .trial-timer:hover {
    transform: none;
  }
}
