import React, { useState, useEffect } from 'react';
import TrialService from '../services/trialService';
import { useAuth } from '../context/AuthContext';
import './TrialTimer.css';

const TrialTimer = () => {
  const [trialStatus, setTrialStatus] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    loadTrialStatus();
    
    // Update timer every minute
    const interval = setInterval(loadTrialStatus, 60000);
    
    return () => clearInterval(interval);
  }, [user]);

  const loadTrialStatus = async () => {
    try {
      setIsLoading(true);
      const statusResponse = await TrialService.getTrialStatus();
      
      if (statusResponse && statusResponse.trial_status) {
        setTrialStatus(statusResponse);
        
        // Show timer only if trial is active
        const isTrialActive = ['active', 'expiring_soon', 'expiring_today'].includes(
          statusResponse.trial_status
        );
        setIsVisible(isTrialActive);
        
        if (isTrialActive) {
          calculateTimeRemaining(statusResponse);
        }
      } else {
        setIsVisible(false);
      }
    } catch (error) {
      console.error('Error loading trial status:', error);
      setIsVisible(false);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTimeRemaining = (status) => {
    if (!status.trial_end_date) return;
    
    const endTime = new Date(status.trial_end_date);
    const now = new Date();
    const diff = endTime - now;
    
    if (diff <= 0) {
      setTimeRemaining({ expired: true });
      return;
    }
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    setTimeRemaining({ hours, minutes, expired: false });
  };

  const getTimerColor = () => {
    if (!timeRemaining || timeRemaining.expired) return 'expired';
    if (timeRemaining.hours < 2) return 'critical';
    if (timeRemaining.hours < 6) return 'warning';
    return 'active';
  };

  const formatTimeDisplay = () => {
    if (!timeRemaining) return '';
    if (timeRemaining.expired) return 'Expired';
    
    const { hours, minutes } = timeRemaining;
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (isLoading || !isVisible || !trialStatus) {
    return null;
  }

  const timerColor = getTimerColor();
  const timeDisplay = formatTimeDisplay();

  return (
    <div className={`trial-timer trial-timer-${timerColor}`}>
      <div className="trial-timer-content">
        <div className="trial-timer-icon">
          {timerColor === 'critical' && '🔥'}
          {timerColor === 'warning' && '⚡'}
          {timerColor === 'active' && '✨'}
          {timerColor === 'expired' && '⏰'}
        </div>
        
        <div className="trial-timer-info">
          <div className="trial-timer-label">Trial</div>
          <div className="trial-timer-time">{timeDisplay}</div>
        </div>
        
        <div className="trial-timer-progress">
          <div 
            className={`trial-timer-progress-bar trial-timer-progress-${timerColor}`}
            style={{ 
              width: timeRemaining && !timeRemaining.expired 
                ? `${Math.max(10, ((timeRemaining.hours + (timeRemaining.minutes / 60)) / 48) * 100)}%`
                : '0%'
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default TrialTimer;
