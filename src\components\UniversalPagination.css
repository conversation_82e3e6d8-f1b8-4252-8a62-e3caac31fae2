/* Universal Pagination - Mobile First Design */

.universal-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  margin: 1rem 0;
  padding: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Pagination Buttons - Simple & Elegant */
.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0.25rem;
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 4px;
  background: transparent;
  color: rgba(255, 107, 157, 0.8);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 107, 157, 0.1);
  border-color: rgba(255, 107, 157, 0.5);
  color: rgba(255, 107, 157, 1);
}

.pagination-btn:active:not(:disabled) {
  background: rgba(255, 107, 157, 0.15);
}

.pagination-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  background: transparent;
  color: #ccc;
  border-color: #ddd;
}

.pagination-icon {
  font-size: 1rem;
  line-height: 1;
}

/* Page Numbers */
.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0.25rem;
  border: 1px solid transparent;
  border-radius: 4px;
  background: transparent;
  color: rgba(255, 107, 157, 0.7);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.page-number:hover {
  background: rgba(255, 107, 157, 0.1);
  color: rgba(255, 107, 157, 1);
  border-color: rgba(255, 107, 157, 0.3);
}

.page-number.active {
  background: rgba(255, 107, 157, 0.9);
  color: white;
  border-color: rgba(255, 107, 157, 0.9);
}

.page-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  color: #9ca3af;
  font-size: 0.875rem;
  user-select: none;
}

/* Minimal Variant (Mobile) */
.universal-pagination.minimal {
  gap: 1rem;
  padding: 1rem;
}

.universal-pagination.minimal .pagination-btn {
  min-width: 3rem;
  height: 3rem;
  border-radius: 12px;
  font-size: 1rem;
}

.pagination-info {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 4rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Dots Variant */
.universal-pagination.dots {
  gap: 0.75rem;
  padding: 1rem;
}

.pagination-dot {
  width: 0.75rem;
  height: 0.75rem;
  border: none;
  border-radius: 50%;
  background: #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.pagination-dot:hover {
  background: #9ca3af;
  transform: scale(1.2);
}

.pagination-dot.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scale(1.3);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 480px) {
  .universal-pagination {
    padding: 0.5rem;
    gap: 0.25rem;
  }
  
  .pagination-btn,
  .page-number {
    min-width: 2rem;
    height: 2rem;
    font-size: 0.75rem;
  }
  
  .page-numbers {
    gap: 0.125rem;
  }
  
  /* Hide page numbers on very small screens, show only prev/next */
  .universal-pagination.default .page-numbers {
    display: none;
  }
  
  .universal-pagination.default::after {
    content: attr(data-page-info);
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0 0.5rem;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .universal-pagination {
    gap: 0.375rem;
  }
  
  .pagination-btn,
  .page-number {
    min-width: 2.25rem;
    height: 2.25rem;
  }
}

@media (min-width: 769px) {
  .universal-pagination {
    gap: 0.5rem;
  }
  
  .pagination-btn,
  .page-number {
    min-width: 2.5rem;
    height: 2.5rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pagination-btn {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }
  
  .pagination-btn:hover:not(:disabled) {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .page-number {
    color: #f9fafb;
  }
  
  .page-number:hover {
    background: #4b5563;
    color: #ffffff;
  }
  
  .pagination-info {
    background: rgba(102, 126, 234, 0.2);
    color: #f9fafb;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .pagination-btn,
  .page-number,
  .pagination-dot {
    transition: none;
  }
  
  .pagination-btn:hover:not(:disabled) {
    transform: none;
  }
  
  .pagination-dot:hover,
  .pagination-dot.active {
    transform: none;
  }
}
