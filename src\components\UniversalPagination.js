import React from 'react';
import PropTypes from 'prop-types';
import './UniversalPagination.css';

const UniversalPagination = ({
  currentPage,
  totalPages,
  onPageChange,
  className = '',
  variant = 'default', // 'default', 'minimal', 'dots'
  showPageNumbers = true,
  showFirstLast = false,
  maxVisiblePages = 5
}) => {
  if (totalPages <= 1) return null;

  const getPageNumbers = () => {
    const pages = [];
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) pages.push(i);
        pages.push('ellipsis-right');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('ellipsis-left');
        for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push('ellipsis-left');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
        pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  // Mobile-first: Show simple prev/next on small screens
  const isMobile = window.innerWidth <= 768;

  if (variant === 'minimal' || isMobile) {
    return (
      <div className={`universal-pagination minimal ${className}`}>
        <button
          className="pagination-btn prev"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          aria-label="Previous page"
        >
          <span className="pagination-icon">←</span>
        </button>
        
        <span className="pagination-info">
          {currentPage} of {totalPages}
        </span>
        
        <button
          className="pagination-btn next"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          aria-label="Next page"
        >
          <span className="pagination-icon">→</span>
        </button>
      </div>
    );
  }

  if (variant === 'dots') {
    return (
      <div className={`universal-pagination dots ${className}`}>
        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
          <button
            key={page}
            className={`pagination-dot ${currentPage === page ? 'active' : ''}`}
            onClick={() => handlePageChange(page)}
            aria-label={`Go to page ${page}`}
          />
        ))}
      </div>
    );
  }

  // Desktop: Full pagination with page numbers
  return (
    <div className={`universal-pagination default ${className}`}>
      {showFirstLast && (
        <button
          className="pagination-btn first"
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
          aria-label="First page"
        >
          <span className="pagination-icon">⟪</span>
        </button>
      )}
      
      <button
        className="pagination-btn prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        aria-label="Previous page"
      >
        <span className="pagination-icon">←</span>
      </button>

      {showPageNumbers && (
        <div className="page-numbers">
          {getPageNumbers().map((num, index) => (
            num === 'ellipsis-left' || num === 'ellipsis-right' ? (
              <span key={`${num}-${index}`} className="page-ellipsis">...</span>
            ) : (
              <button
                key={num}
                className={`page-number ${currentPage === num ? 'active' : ''}`}
                onClick={() => handlePageChange(num)}
                aria-label={`Go to page ${num}`}
              >
                {num}
              </button>
            )
          ))}
        </div>
      )}

      <button
        className="pagination-btn next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        aria-label="Next page"
      >
        <span className="pagination-icon">→</span>
      </button>

      {showFirstLast && (
        <button
          className="pagination-btn last"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
          aria-label="Last page"
        >
          <span className="pagination-icon">⟫</span>
        </button>
      )}
    </div>
  );
};

UniversalPagination.propTypes = {
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'minimal', 'dots']),
  showPageNumbers: PropTypes.bool,
  showFirstLast: PropTypes.bool,
  maxVisiblePages: PropTypes.number
};

export default UniversalPagination;
