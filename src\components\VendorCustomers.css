/* VendorCustomers.css - mobile-first, sleek, responsive styles */

.glam-card {
  border-radius: 18px;
  box-shadow: 0 2px 16px rgba(44,62,80,0.08);
  background: #23272f;
}

@media (max-width: 600px) {
  .glam-card {
    padding: 10px 4px !important;
    border-radius: 10px;
  }
  .vendor-customers-title {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem;
  }
  .vendor-customers-table th,
  .vendor-customers-table td {
    font-size: 0.85rem !important;
    padding: 6px 4px !important;
  }
  .vendor-customers-table {
    font-size: 0.9rem;
    min-width: 600px;
  }
  .vendor-customers-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .vendor-customers-btn {
    font-size: 0.85rem !important;
    padding: 4px 8px !important;
    min-width: 0;
    border-radius: 16px !important;
  }
  .vendor-customers-search {
    font-size: 0.95rem !important;
    padding: 6px 8px !important;
    margin-bottom: 10px;
  }
}

.vendor-customers-table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
}
.vendor-customers-table th,
.vendor-customers-table td {
  padding: 10px 8px;
  font-size: 1rem;
  vertical-align: middle;
}
.vendor-customers-actions {
  display: flex;
  gap: 6px;
}
.vendor-customers-btn {
  border-radius: 20px;
  font-size: 1rem;
  padding: 6px 14px;
  min-width: 36px;
  transition: background 0.2s;
}
.vendor-customers-btn:active {
  opacity: 0.85;
}
.vendor-customers-search {
  border-radius: 16px;
  font-size: 1.05rem;
  padding: 8px 14px;
  margin-bottom: 18px;
}

/* Table horizontal scroll for mobile */
@media (max-width: 900px) {
  .vendor-customers-table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Pagination styles */
.pagination .page-link {
  border-radius: 12px;
  margin: 0 2px;
  border: 1px solid #495057;
  transition: all 0.2s ease;
}

.pagination .page-link:hover {
  background-color: #495057;
  border-color: #495057;
  transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
  background-color: #ff6b9d;
  border-color: #ff6b9d;
  box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
}

.pagination .page-item.disabled .page-link {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

/* Mobile pagination specific styles */
@media (max-width: 768px) {
  .pagination {
    margin-bottom: 1rem;
  }
  
  .pagination .page-link {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
  
  /* Mobile pagination info */
  .text-light {
    font-size: 0.9rem;
  }
} 

.back-to-profile-btn {
  border-radius: 20px;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(44, 62, 80, 0.10);
}