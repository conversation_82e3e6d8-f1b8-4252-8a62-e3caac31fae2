// Cleaned by Gemini CLI: ESLint + Prettier compliance (2025-07-09)
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './VendorCustomers.css';

const VendorCustomers = () => {
  const navigate = useNavigate();
  const { authFetch } = useAuth();
  const [customers, setCustomers] = useState([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [showBookingsModal, setShowBookingsModal] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [bookingsLoading, setBookingsLoading] = useState(false);
  const [bookingsError, setBookingsError] = useState('');
  const [bookingsCustomer, setBookingsCustomer] = useState(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    fetchCustomers();
    // Set responsive items per page
    const handleResize = () => {
      setItemsPerPage(window.innerWidth <= 768 ? 5 : 10);
    };
    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const fetchCustomers = async () => {
    setLoading(true);
    setError('');
    try {
      const res = await authFetch('/api/customers/');
      if (!res.ok) throw new Error('Failed to fetch customers');
      const data = await res.json();
      setCustomers(data);
    } catch (err) {
      setError(err.message);
    }
    setLoading(false);
  };

  const filtered = customers.filter(c => {
    const searchTerm = search.toLowerCase();
    return (
      (c.name && c.name.toLowerCase().includes(searchTerm))
      || (c.email && c.email.toLowerCase().includes(searchTerm))
      || (c.phone && c.phone.toLowerCase().includes(searchTerm))
    );
  });

  // Pagination logic
  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedCustomers = filtered.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [search]);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = window.innerWidth <= 768 ? 3 : 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage > Math.ceil(maxVisiblePages / 2)) {
        pages.push(1);
        if (currentPage > Math.ceil(maxVisiblePages / 2) + 1) pages.push('ellipsis-left');
      }
      const start = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
      const end = Math.min(totalPages - 1, currentPage + Math.floor(maxVisiblePages / 2));
      for (let i = start; i <= end; i++) pages.push(i);
      if (currentPage < totalPages - Math.floor(maxVisiblePages / 2)) {
        if (currentPage < totalPages - Math.floor(maxVisiblePages / 2) - 1) pages.push('ellipsis-right');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  const handleExportCSV = () => {
    const headers = ['Name', 'Email', 'Phone', 'Notes', 'Salon', 'User'];
    const rows = filtered.map(c => [c.name, c.email, c.phone, c.notes, c.salon, c.user]);
    let csv = `${headers.join(',')}\n`;
    rows.forEach(row => {
      csv += `${row.map(val => `"${val || ''}"`).join(',')}\n`;
    });
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'customers.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleShowDetail = (customer) => {
    setSelectedCustomer(customer);
    setShowModal(true);
    setIsEdit(false);
  };

  const handleEditCustomer = (customer) => {
    setSelectedCustomer(customer);
    setShowModal(true);
    setIsEdit(true);
  };

  const handleDeleteCustomer = async (customer) => {
    if (!window.confirm('Delete this customer?')) return;
    await authFetch(`/api/customers/${customer.id}/`, { method: 'DELETE' });
    fetchCustomers();
  };

  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setShowModal(true);
    setIsEdit(true);
  };

  const handleSaveCustomer = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const customerData = {
      name: formData.get('name'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      notes: formData.get('notes'),
      salon: formData.get('salon'),
      user: formData.get('user') || null,
    };
    if (selectedCustomer && selectedCustomer.id) {
      await authFetch(`/api/customers/${selectedCustomer.id}/`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
    } else {
      await authFetch('/api/customers/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
    }
    setShowModal(false);
    fetchCustomers();
  };

  const handleViewBookings = async (customer) => {
    setBookingsCustomer(customer);
    setShowBookingsModal(true);
    setBookingsLoading(true);
    setBookingsError('');
    try {
      const res = await authFetch(`/api/vendor/customers/${customer.user || customer.user_id}/bookings/`);
      if (!res.ok) throw new Error('Failed to fetch bookings');
      const data = await res.json();
      setBookings(data);
    } catch (err) {
      setBookingsError(err.message);
      setBookings([]);
    }
    setBookingsLoading(false);
  };

  return (
    <div className="container mt-5">
      <div className="glam-card p-4 mx-auto" style={{ maxWidth: 900 }}>
        <div className="d-flex justify-content-between align-items-center mb-4">
          <button className="btn btn-outline-secondary px-4 py-2 vendor-customers-btn back-to-profile-btn" onClick={() => navigate('/vendor/profile')}>
            <i className="bi bi-arrow-left me-2" />
            Back to Profile
          </button>
          <div>
            <button className="btn btn-success glam-btn me-2 vendor-customers-btn" onClick={handleExportCSV}>
              <i className="bi bi-download me-1" />
              Export CSV
            </button>
            <button className="btn btn-primary glam-btn vendor-customers-btn" onClick={handleAddCustomer}>
              <i className="bi bi-plus-circle me-1" />
              Add Customer
            </button>
          </div>
        </div>
        <input
          type="text"
          className="form-control mb-3 vendor-customers-search"
          placeholder="Search by name, email, or phone..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
        {loading ? (
          <div className="text-light">Loading customers...</div>
        ) : error ? (
          <div className="alert alert-danger">{error}</div>
        ) : (
          <div className="vendor-customers-table-responsive">
            <table className="table table-striped table-bordered text-light vendor-customers-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Phone</th>
                  <th>Notes</th>
                  <th>Salon</th>
                  <th>User</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedCustomers.map(c => (
                  <tr key={c.id}>
                    <td>{c.name}</td>
                    <td>{c.email}</td>
                    <td>{c.phone}</td>
                    <td>{c.notes}</td>
                    <td>{c.salon}</td>
                    <td>{c.user}</td>
                    <td className="vendor-customers-actions">
                      <button className="btn btn-primary btn-sm glam-btn me-2 vendor-customers-btn" style={{ fontWeight: 'bold', background: '#ff6b9d', borderColor: '#ff6b9d' }} onClick={() => handleViewBookings(c)}>
                        <i className="bi bi-calendar-event" /> VIEW BOOKINGS
                      </button>
                      {/* Debug: If button not rendered, show a message */}
                      {!c.user && !c.user_id && <span style={{ color: 'red', fontWeight: 'bold' }}>NO CUSTOMER ID</span>}
                      <button className="btn btn-info btn-sm glam-btn me-2 vendor-customers-btn" onClick={() => handleShowDetail(c)}><i className="bi bi-eye" /></button>
                      <button className="btn btn-warning btn-sm glam-btn me-2 vendor-customers-btn" onClick={() => handleEditCustomer(c)}><i className="bi bi-pencil" /></button>
                      <button className="btn btn-danger btn-sm glam-btn vendor-customers-btn" onClick={() => handleDeleteCustomer(c)}><i className="bi bi-trash" /></button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {filtered.length === 0 && <div className="text-light">No customers found.</div>}

            {/* Pagination Info */}
            {filtered.length > 0 && (
              <div className="d-flex justify-content-between align-items-center mt-3">
                <div className="text-light">
                  {`Showing ${startIndex + 1} to ${Math.min(endIndex, filtered.length)} of ${filtered.length} customers`}
                </div>
              </div>
            )}}

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <nav aria-label="Customer pagination" className="mt-3">

                {window.innerWidth <= 768 ? (
                  // Mobile pagination
                  <div className="d-flex justify-content-center align-items-center gap-3">
                    <button 
                      className="btn btn-outline-primary btn-sm vendor-customers-btn" 
                      onClick={() => handlePageChange(currentPage - 1)} 
                      disabled={currentPage === 1}
                    >
                      <i className="bi bi-chevron-left" />
                    </button>
                    <span className="text-light">Page {currentPage} of {totalPages}</span>
                    <button 
                      className="btn btn-outline-primary btn-sm vendor-customers-btn" 
                      onClick={() => handlePageChange(currentPage + 1)} 
                      disabled={currentPage === totalPages}
                    >
                      <i className="bi bi-chevron-right" />
                    </button>
                  </div>
                ) : (
                  // Desktop pagination
                  <ul className="pagination justify-content-center">
                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link bg-transparent text-light border-light vendor-customers-btn"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        <i className="bi bi-chevron-left" />
                      </button>
                    </li>
                    
                    {getPageNumbers().map((num, idx) => (num === 'ellipsis-left' || num === 'ellipsis-right' ? (
                      <li key={num + idx} className="page-item disabled">
                        <span className="page-link bg-transparent text-light border-light">...</span>
                      </li>
                    ) : (
                      <li key={num} className={`page-item ${currentPage === num ? 'active' : ''}`}>
                        <button
                          className={`page-link vendor-customers-btn ${currentPage === num ? 'bg-primary border-primary' : 'bg-transparent text-light border-light'}`}
                          onClick={() => handlePageChange(num)}
                        >
                          {num}
                        </button>
                      </li>
                    )))}
                    
                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                      <button
                        className="page-link bg-transparent text-light border-light vendor-customers-btn"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        <i className="bi bi-chevron-right" />
                      </button>
                    </li>
                  </ul>
                )}
              </nav>
            )}
          </div>
        )}
        {/* Modal for add/edit/detail customer */}
        {showModal && (
          <div className="modal show d-block" tabIndex="-1" style={{ background: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog">
              <div className="modal-content bg-dark text-light">
                {isEdit ? (
                  <form onSubmit={handleSaveCustomer}>
                    <div className="modal-header">
                      <h5 className="modal-title">
                        {`${selectedCustomer ? 'Edit' : 'Add'} Customer`}
                      </h5>
                      <button type="button" className="btn-close" onClick={() => setShowModal(false)} />
                    </div>
                    <div className="modal-body">
                      <div className="mb-3">
                        <label htmlFor="customer-name" className="form-label">Name</label>
                        <input id="customer-name" type="text" className="form-control" name="name" defaultValue={selectedCustomer?.name || ''} required />
                      </div>
                      <div className="mb-3">
                        <label htmlFor="customer-email" className="form-label">Email</label>
                        <input id="customer-email" type="email" className="form-control" name="email" defaultValue={selectedCustomer?.email || ''} />
                      </div>
                      <div className="mb-3">
                        <label htmlFor="customer-phone" className="form-label">Phone</label>
                        <input id="customer-phone" type="text" className="form-control" name="phone" defaultValue={selectedCustomer?.phone || ''} />
                      </div>
                      <div className="mb-3">
                        <label htmlFor="customer-notes" className="form-label">Notes</label>
                        <textarea id="customer-notes" className="form-control" name="notes" defaultValue={selectedCustomer?.notes || ''} />
                      </div>
                      <div className="mb-3">
                        <label htmlFor="customer-salon" className="form-label">Salon ID</label>
                        <input id="customer-salon" type="number" className="form-control" name="salon" defaultValue={selectedCustomer?.salon || ''} required />
                      </div>
                      <div className="mb-3">
                        <label htmlFor="customer-user" className="form-label">User ID (optional)</label>
                        <input id="customer-user" type="number" className="form-control" name="user" defaultValue={selectedCustomer?.user || ''} />
                      </div>
                    </div>
                    <div className="modal-footer">
                      <button type="button" className="btn btn-secondary" onClick={() => setShowModal(false)}>Cancel</button>
                      <button type="submit" className="btn btn-primary">Save</button>
                    </div>
                  </form>
                ) : (
                  <>
                    <div className="modal-header">
                      <h5 className="modal-title">Customer Details</h5>
                      <button type="button" className="btn-close" onClick={() => setShowModal(false)} />
                    </div>
                    <div className="modal-body">
                      <p><strong>Name:</strong> {selectedCustomer?.name}</p>
                      <p><strong>Email:</strong> {selectedCustomer?.email}</p>
                      <p><strong>Phone:</strong> {selectedCustomer?.phone}</p>
                      <p><strong>Notes:</strong> {selectedCustomer?.notes}</p>
                      <p><strong>Salon:</strong> {selectedCustomer?.salon}</p>
                      <p><strong>User:</strong> {selectedCustomer?.user}</p>
                    </div>
                    <div className="modal-footer">
                      <button type="button" className="btn btn-secondary" onClick={() => setShowModal(false)}>Close</button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
        {/* Bookings Modal */}
        {showBookingsModal && (
          <div className="modal show d-block" tabIndex="-1" style={{ background: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog modal-lg">
              <div className="modal-content bg-dark text-light">
                <div className="modal-header">
                  <h5 className="modal-title">
                    {`Bookings for ${bookingsCustomer?.name}`}
                  </h5>
                  <button type="button" className="btn-close" onClick={() => setShowBookingsModal(false)} />
                </div>
                <div className="modal-body">
                  {bookingsLoading ? (
                    <div>Loading bookings...</div>
                  ) : bookingsError ? (
                    <div className="alert alert-danger">{bookingsError}</div>
                  ) : bookings.length === 0 ? (
                    <div>No bookings found for this customer.</div>
                  ) : (
                    <div className="table-responsive-sm">
                      <table className="table table-striped table-bordered text-light">
                        <thead>
                          <tr>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Status</th>
                            <th>Notes</th>
                          </tr>
                        </thead>
                        <tbody>
                          {bookings.map(b => (
                            <tr key={b.id}>
                              <td>{b.service}</td>
                              <td>{b.date}</td>
                              <td>{b.time}</td>
                              <td>{b.status}</td>
                              <td>{b.notes}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
                <div className="modal-footer">
                  <button type="button" className="btn btn-secondary" onClick={() => setShowBookingsModal(false)}>Close</button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VendorCustomers; 
