/* Enhanced Mobile-First Vendor Subscription Styles */
.vendor-subscription {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(255, 20, 147, 0.15),
    rgba(138, 43, 226, 0.1),
    rgba(255, 215, 0, 0.1),
    rgba(0, 255, 255, 0.05)
  );
  color: white;
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-emojis {
  position: absolute;
  width: 100%;
  height: 100%;
}

.emoji {
  position: absolute;
  font-size: 2rem;
  animation: float 6s ease-in-out infinite;
  opacity: 0.15;
  z-index: 1;
}

.emoji-1 { top: 10%; left: 10%; animation-delay: 0s; }
.emoji-2 { top: 20%; right: 15%; animation-delay: 1s; }
.emoji-3 { top: 60%; left: 20%; animation-delay: 2s; }
.emoji-4 { top: 70%; right: 10%; animation-delay: 3s; }
.emoji-5 { top: 40%; left: 5%; animation-delay: 4s; }
.emoji-6 { top: 30%; right: 25%; animation-delay: 5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(10deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 600px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-badge {
  display: inline-block;
  background: linear-gradient(45deg, rgba(255, 20, 147, 0.3), rgba(255, 215, 0, 0.3));
  border: 2px solid rgba(255, 215, 0, 0.7);
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  font-weight: 600;
  font-size: 0.9rem;
  color: #FFFFFF;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  color: #FFFFFF;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: #FFFFFF;
  opacity: 0.95;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.stats-preview {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #FFD700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: #FFFFFF;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

.subscription-content {
  padding: 2rem 1rem;
}

/* Billing Section */
.billing-section {
  text-align: center;
  margin-bottom: 3rem;
}

.billing-section h2 {
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Tiers Section */
.tiers-section h2 {
  text-align: center;
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Billing Toggle */
.billing-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 0.25rem;
  max-width: 300px;
  margin: 0 auto;
}

.billing-toggle button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: white;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.billing-toggle button.active {
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
}

.save-badge {
  background: #FF1493;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  margin-left: 0.5rem;
}

/* Tiers Grid - Mobile First */
.tiers-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 3rem;
}

.tier-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.popular-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
  padding: 0.5rem 1rem;
  border-radius: 0 0 15px 15px;
  font-size: 0.8rem;
  font-weight: 700;
  z-index: 2;
}

.trial-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: linear-gradient(45deg, #10B981, #34D399);
  color: #000;
  padding: 0.5rem 1rem;
  border-radius: 0 0 15px 15px;
  font-size: 0.8rem;
  font-weight: 700;
  z-index: 2;
}

.savings-badge {
  background: linear-gradient(45deg, #00FF00, #00FFFF);
  color: #000;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 700;
  margin-top: 0.5rem;
}

.tier-card:hover,
.tier-card.selected {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 20, 147, 0.3);
}

.tier-card.featured-tier.selected {
  border-color: #00FFFF;
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

.tier-card.spotlight-tier.selected {
  border-color: #FF1493;
  box-shadow: 0 10px 30px rgba(255, 20, 147, 0.4);
}

.tier-card.elite-tier.selected {
  border-color: #FFD700;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
}

.tier-card.trial-tier.selected {
  border-color: #10B981;
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
}

.tier-card.basic-tier.selected {
  border-color: #3B82F6;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
}

/* Tier Header */
.tier-header {
  text-align: center;
  margin-bottom: 2rem;
}

.tier-emoji {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.tier-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.tier-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
}

.currency {
  font-size: 1rem;
  opacity: 0.8;
}

.amount {
  font-size: 2rem;
  font-weight: 800;
  color: #FFD700;
}

.period {
  font-size: 1rem;
  opacity: 0.8;
}

.trial-price .amount {
  color: #10B981;
  font-size: 2.2rem;
}

.trial-price .period {
  color: #10B981;
  font-weight: 600;
}

/* Features */
.tier-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.feature-check {
  color: #00FF00;
  font-weight: bold;
  font-size: 1rem;
}

/* Subscribe Button */
.subscribe-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
  border: none;
  border-radius: 15px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
}

/* Success Stories */
.success-stories {
  margin-bottom: 3rem;
}

.success-stories h2 {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stories-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.story-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.story-avatar {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.story-content h4 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #FFD700;
}

.story-content p {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.story-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
}

.story-stats span {
  background: rgba(255, 20, 147, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-weight: 600;
}

/* Commission Info */
.commission-info {
  margin-bottom: 3rem;
}

.commission-info h2 {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.commission-intro {
  text-align: center;
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.commission-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.commission-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.commission-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.commission-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.commission-rates {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.rate-item:last-child {
  border-bottom: none;
}

.rate-item span:last-child {
  color: #FFD700;
  font-weight: 600;
}

.commission-note {
  font-size: 0.8rem;
  opacity: 0.8;
  text-align: center;
  margin-top: 1rem;
  font-style: italic;
}

/* FAQ Section */
.faq-section {
  margin-bottom: 3rem;
}

.faq-section h2 {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.faq-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.faq-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.faq-item h4 {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #FFD700;
}

.faq-item p {
  font-size: 0.9rem;
  line-height: 1.4;
  opacity: 0.9;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg,
    rgba(255, 20, 147, 0.2),
    rgba(138, 43, 226, 0.15),
    rgba(255, 215, 0, 0.1)
  );
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 2rem;
}

.cta-content h2 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-content p {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.cta-primary, .cta-secondary {
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-primary {
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: #000;
}

.cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-primary:hover, .cta-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
}

.guarantee-badge {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  border-radius: 15px;
  padding: 0.5rem 1rem;
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Benefits Section */
.benefits-section h2 {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.benefit-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.benefit-emoji {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.benefit-item h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.benefit-item p {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

/* Tablet Responsive */
@media (min-width: 768px) {
  .hero-title {
    font-size: 3.5rem;
  }

  .stats-preview {
    gap: 3rem;
  }

  .subscription-content {
    padding: 3rem 2rem;
  }

  .tiers-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .stories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .commission-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .faq-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .cta-buttons {
    gap: 2rem;
  }
}

/* Desktop Responsive */
@media (min-width: 1024px) {
  .subscription-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 4rem 2rem;
  }

  .hero-title {
    font-size: 4rem;
  }

  .hero-subtitle {
    font-size: 1.3rem;
  }

  .stats-preview {
    gap: 4rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .faq-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .cta-section {
    padding: 4rem 3rem;
  }
}
