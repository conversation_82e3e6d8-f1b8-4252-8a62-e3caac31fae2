import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import './VendorSubscriptionProfile.css';

const VendorSubscription = () => {
  const [selectedTier, setSelectedTier] = useState('basic');
  const [selectedDuration, setSelectedDuration] = useState('monthly');
  const [vendorData, setVendorData] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Platform access plans
  const platformPlans = {
    trial: { label: 'Free Trial', daily: 0, weekly: 0, monthly: 0 },
    basic: { label: 'Basic Access', daily: 50, weekly: 300, monthly: 1100 }
  };

  // Premium listing tiers with duration-based pricing
  const listingTiers = {
    none: { label: 'No Premium Listing', daily: 0, weekly: 0, monthly: 0 },
    trending: { label: 'Trending', daily: 250, weekly: 1500, monthly: 6000 },
    elite: { label: 'Elite', daily: 300, weekly: 1800, monthly: 7200 },
    featured: { label: 'Featured', daily: 400, weekly: 2400, monthly: 9600 },
    spotlight: { label: 'Spotlight', daily: 500, weekly: 3000, monthly: 12000 }
  };

  const subscriptionTiers = {
    trial: {
      name: 'Free Trial',
      emoji: '🎁',
      platformPlan: 'trial',
      listingTier: 'none',
      features: [
        '48-hour full access',
        'all premium features',
        'no payment required',
        'instant activation'
      ],
      color: 'trial-tier'
    },
    basic: {
      name: 'Basic',
      emoji: '🚀',
      platformPlan: 'basic',
      listingTier: 'none',
      features: [
        'salon listing',
        'customer bookings',
        'basic analytics',
        'standard support'
      ],
      color: 'basic-tier'
    },
    featured: {
      name: 'Featured',
      emoji: '⚡',
      platformPlan: 'basic',
      listingTier: 'featured',
      features: [
        'ticker placement',
        'enhanced visibility',
        'priority support',
        'advanced analytics'
      ],
      color: 'featured-tier'
    },
    spotlight: {
      name: 'Spotlight',
      emoji: '🔥',
      platformPlan: 'basic',
      listingTier: 'spotlight',
      features: [
        'priority ticker placement',
        'home service booking',
        'premium analytics',
        'customer insights'
      ],
      color: 'spotlight-tier'
    },
    elite: {
      name: 'Elite',
      emoji: '💎',
      platformPlan: 'basic',
      listingTier: 'elite',
      features: [
        'premium ticker placement',
        'home + event services',
        'priority support',
        'competitor insights'
      ],
      color: 'elite-tier'
    }
  };

  const durations = {
    daily: { label: 'Daily', days: 1 },
    weekly: { label: 'Weekly', days: 7 },
    monthly: { label: 'Monthly', days: 30 }
  };

  const commissionRates = {
    home_service: { booking: 500, lead: 200 },
    event_service: { booking: 2000, lead: 500 },
    advertising: { click: 50 }
  };

  // Check if vendor data is passed from registration
  useEffect(() => {
    if (location.state?.vendorData) {
      setVendorData(location.state.vendorData);
    }
  }, [location.state]);

  const calculatePrice = (tier, duration) => {
    if (tier === 'trial') return 0;

    const tierData = subscriptionTiers[tier];
    const platformCost = platformPlans[tierData.platformPlan][duration] || 0;
    const listingCost = listingTiers[tierData.listingTier][duration] || 0;
    return platformCost + listingCost;
  };

  const handleSubscribe = (tier) => {
    if (tier === 'trial') {
      // Handle trial activation
      window.location.href = '/vendor/trial/start';
      return;
    }

    // If no vendor data, redirect to registration first
    if (!vendorData) {
      navigate('/register-vendor', {
        state: {
          selectedTier: tier,
          selectedDuration: selectedDuration
        }
      });
      return;
    }

    const price = calculatePrice(tier, selectedDuration);
    const tierData = subscriptionTiers[tier];

    // Prepare subscription data for payment using vendor data
    const subscriptionData = {
      ...vendorData,
      duration: selectedDuration,
      durationDetails: durations[selectedDuration],
      listingTier: tierData.listingTier,
      tierDetails: listingTiers[tierData.listingTier],
      platformCost: platformPlans[tierData.platformPlan][selectedDuration],
      listingCost: listingTiers[tierData.listingTier][selectedDuration],
      totalAmount: price
    };

    // Navigate to payment page
    navigate('/payment/vendor-subscription', { state: { subscriptionData } });
  };

  return (
    <div className="vendor-subscription-profile-container">
      <div className="profile-container">
        {/* Floating Effects */}
        <div className="auth-sparkles">
          <span className="auth-sparkle">💎</span>
          <span className="auth-sparkle">✨</span>
          <span className="auth-sparkle">🔥</span>
          <span className="auth-sparkle">⚡</span>
          <span className="auth-sparkle">🚀</span>
          <span className="auth-sparkle">💰</span>
        </div>

        {/* Header Section */}
        <div className="profile-header">
          <div className="auth-icon-wrapper">
            <div className="auth-icon">💎</div>
          </div>
          <h1 className="profile-title">Premium Plans</h1>
          <p className="profile-subtitle">unlock exclusive features that bring more clients and boost your revenue</p>
        </div>

        {/* Content Area */}
        <div className="profile-content">

          {/* Duration Selection Section */}
          <div className="profile-section">
            <h3 className="section-title">💳 Choose Your Duration</h3>
            <div className="billing-toggle">
            <button
              className={selectedDuration === 'daily' ? 'active' : ''}
              onClick={() => setSelectedDuration('daily')}
            >
              daily
            </button>
            <button
              className={selectedDuration === 'weekly' ? 'active' : ''}
              onClick={() => setSelectedDuration('weekly')}
            >
              weekly <span className="save-badge">save 14%</span>
            </button>
            <button
              className={selectedDuration === 'monthly' ? 'active' : ''}
              onClick={() => setSelectedDuration('monthly')}
            >
              monthly <span className="save-badge">save 26%</span>
            </button>
          </div>
          </div>

          {/* Subscription Tiers Section */}
          <div className="profile-section">
            <h3 className="section-title">🎯 Pick Your Vibe</h3>
            <div className="tiers-grid">
            {Object.entries(subscriptionTiers).map(([key, tier]) => (
              <div
                key={key}
                className={`tier-card ${tier.color} ${selectedTier === key ? 'selected' : ''}`}
                onClick={() => setSelectedTier(key)}
              >
                {key === 'trial' && <div className="trial-badge">free trial 🎁</div>}
                {key === 'spotlight' && <div className="popular-badge">most popular 🔥</div>}

                <div className="tier-header">
                  <div className="tier-emoji">{tier.emoji}</div>
                  <h3>{tier.name}</h3>
                  {key === 'trial' ? (
                    <div className="tier-price trial-price">
                      <span className="amount">FREE</span>
                      <span className="period">4 hours</span>
                    </div>
                  ) : (
                    <div className="tier-price">
                      <span className="currency">KSh</span>
                      <span className="amount">{calculatePrice(key, selectedDuration).toLocaleString()}</span>
                      <span className="period">/{selectedDuration}</span>
                    </div>
                  )}
                </div>

                <div className="tier-features">
                  {tier.features.map((feature, index) => (
                    <div key={index} className="feature-item">
                      <span className="feature-check">✓</span>
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                <button
                  className="subscribe-btn"
                  onClick={() => handleSubscribe(key)}
                >
                  {key === 'trial' ? 'start free trial 🎁' : `start ${tier.name} journey ✨`}
                </button>
              </div>
            ))}
          </div>
          </div>

          {/* Success Stories Section */}
          <div className="profile-section">
            <h3 className="section-title">📈 Success Stories</h3>
          <div className="stories-grid">
            <div className="story-card">
              <div className="story-avatar">👩‍💼</div>
              <div className="story-content">
                <h4>Sarah's Salon</h4>
                <p>"went from 20 to 80 bookings per month with spotlight tier! home services are a game changer 🔥"</p>
                <div className="story-stats">
                  <span>+300% bookings</span>
                  <span>elite tier</span>
                </div>
              </div>
            </div>

            <div className="story-card">
              <div className="story-avatar">👨‍💼</div>
              <div className="story-content">
                <h4>Kevin's Cuts</h4>
                <p>"event services brought in KSh 150k last month alone! weddings are where the money is 💰"</p>
                <div className="story-stats">
                  <span>+KSh 150k/month</span>
                  <span>spotlight tier</span>
                </div>
              </div>
            </div>
          </div>
          </div>

          {/* FAQ Section */}
          <div className="profile-section">
            <h3 className="section-title">🤔 Got Questions?</h3>
          <div className="faq-grid">
            <div className="faq-item">
              <h4>can i cancel anytime?</h4>
              <p>yes! cancel anytime with 30 days notice. no hidden fees or long-term contracts.</p>
            </div>
            <div className="faq-item">
              <h4>how do payments work?</h4>
              <p>customers pay you directly via M-Pesa, cash, or bank transfer. we just facilitate bookings.</p>
            </div>
            <div className="faq-item">
              <h4>what if i don't get bookings?</h4>
              <p>we provide dedicated support and marketing tips to help increase your bookings.</p>
            </div>
            <div className="faq-item">
              <h4>can i upgrade my plan?</h4>
              <p>absolutely! upgrade anytime and only pay the difference. downgrade at next billing cycle.</p>
            </div>
          </div>
          </div>

          {/* Call to Action Section */}
          <div className="profile-section">
            <h3 className="section-title">🚀 Ready to Level Up?</h3>
            <div className="cta-content">
            <p>join 500+ salons already growing with premium features</p>
            <div className="cta-buttons">
              <button
                className="cta-primary"
                onClick={() => handleSubscribe(selectedTier)}
              >
                {selectedTier === 'trial' ? 'start free trial 🎁' : `get ${subscriptionTiers[selectedTier].name} plan ✨`}
              </button>
              <button className="cta-secondary">
                schedule demo 📞
              </button>
            </div>
            <div className="guarantee-badge">
              <span>✨ secure payment via paystack</span>
            </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default VendorSubscription;
