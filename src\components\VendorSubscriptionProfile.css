/* Vendor Subscription Profile Design Pattern */
.vendor-subscription-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(255, 20, 147, 0.15),
    rgba(138, 43, 226, 0.1),
    rgba(255, 215, 0, 0.1),
    rgba(0, 255, 255, 0.05)
  );
  padding: 1rem 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

/* Floating Effects */
.auth-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.auth-sparkle {
  position: absolute;
  font-size: 1.5rem;
  opacity: 0.1;
  animation: sparkleFloat 8s ease-in-out infinite;
}

.auth-sparkle:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.auth-sparkle:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
.auth-sparkle:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 2s; }
.auth-sparkle:nth-child(4) { top: 40%; right: 25%; animation-delay: 3s; }
.auth-sparkle:nth-child(5) { bottom: 20%; right: 10%; animation-delay: 4s; }
.auth-sparkle:nth-child(6) { top: 60%; left: 5%; animation-delay: 5s; }

@keyframes sparkleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.1; }
  25% { transform: translateY(-10px) rotate(90deg); opacity: 0.2; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.15; }
  75% { transform: translateY(-10px) rotate(270deg); opacity: 0.2; }
}

/* Header Section */
.profile-header {
  text-align: center;
  padding: 3rem 2rem 2rem;
  background: linear-gradient(135deg, #FF1493, #FFD700);
  color: white;
  position: relative;
  z-index: 2;
}

.auth-icon-wrapper {
  margin-bottom: 1.5rem;
}

.auth-icon {
  display: inline-block;
  font-size: 4rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.profile-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #FFFFFF, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Content Area */
.profile-content {
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.profile-section {
  margin-bottom: 3rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(226, 232, 240, 0.5);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  justify-content: center;
}

/* Billing Toggle */
.billing-toggle {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 0.5rem;
  margin: 0 auto 2rem;
  max-width: 600px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.billing-toggle button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #64748b;
}

.billing-toggle button.active {
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 20, 147, 0.3);
}

.save-badge {
  background: rgba(16, 185, 129, 0.2);
  color: #059669;
  padding: 0.2rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

/* Tier Cards */
.tiers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.tier-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(10px);
}

.tier-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.tier-card.selected {
  border-color: #FF1493;
  box-shadow: 0 8px 25px rgba(255, 20, 147, 0.3);
}

.trial-badge, .popular-badge {
  position: absolute;
  top: -10px;
  right: 1rem;
  background: linear-gradient(45deg, #10B981, #059669);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.popular-badge {
  background: linear-gradient(45deg, #FF1493, #FFD700);
}

.tier-header {
  text-align: center;
  margin-bottom: 2rem;
}

.tier-emoji {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.tier-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.tier-price {
  margin-bottom: 1rem;
}

.currency {
  font-size: 1rem;
  color: #64748b;
}

.amount {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0.2rem;
}

.period {
  font-size: 1rem;
  color: #64748b;
}

.trial-price .amount {
  color: #10B981;
  font-size: 2.2rem;
}

.trial-price .period {
  color: #10B981;
  font-weight: 600;
}

/* Features */
.tier-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  color: #475569;
}

.feature-check {
  color: #10B981;
  font-weight: bold;
  font-size: 1rem;
}

/* Subscribe Button */
.subscribe-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 20, 147, 0.4);
}

/* Success Stories */
.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.story-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #FFD700;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.story-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.story-avatar {
  font-size: 2rem;
}

.story-content h4 {
  color: #1e293b;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.story-content p {
  color: #374151;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  font-weight: 500;
}

.story-stats {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.story-stats span {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: lowercase;
}

.story-name {
  font-weight: 600;
  color: #1e293b;
}

.story-quote {
  font-style: italic;
  color: #475569;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.story-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
}

.stat-highlight {
  background: linear-gradient(45deg, #10B981, #059669);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-weight: 600;
}

/* FAQ Grid */
.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.faq-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.faq-item h4 {
  color: #1e293b;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  text-transform: capitalize;
}

.faq-item p {
  color: #475569;
  margin: 0;
  line-height: 1.5;
}

/* CTA Section */
.cta-content {
  text-align: center;
}

.cta-content p {
  color: #64748b;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.cta-primary, .cta-secondary {
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.cta-primary {
  background: linear-gradient(45deg, #FF1493, #FFD700);
  color: white;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 20, 147, 0.4);
}

.cta-secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;
  border: 2px solid rgba(226, 232, 240, 0.5);
}

.cta-secondary:hover {
  background: rgba(248, 250, 252, 0.9);
  color: #475569;
}

.guarantee-badge {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .vendor-subscription-profile-container {
    padding: 0.5rem;
  }

  .profile-container {
    border-radius: 16px;
  }

  .profile-header {
    padding: 2rem 1rem 1.5rem;
  }

  .auth-icon {
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 2.5rem;
  }

  .profile-title {
    font-size: 2rem;
  }

  .profile-subtitle {
    font-size: 1rem;
  }

  .profile-content {
    padding: 1rem;
  }

  .profile-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .billing-toggle {
    flex-direction: column;
    gap: 0.5rem;
  }

  .tiers-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tier-card {
    padding: 1.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary, .cta-secondary {
    width: 100%;
    max-width: 300px;
  }
}
