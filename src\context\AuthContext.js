import React, { createContext, useState, useContext } from 'react';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('user');
    return storedUser ? JSON.parse(storedUser) : null;
  });
  const [token, setToken] = useState(() => localStorage.getItem('token') || null);
  const [refreshToken, setRefreshToken] = useState(() => localStorage.getItem('refreshToken') || null);

  // Persist user and tokens
  React.useEffect(() => {
    if (user && token && refreshToken) {
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('token', token);
      localStorage.setItem('accessToken', token);
      localStorage.setItem('refreshToken', refreshToken);
    } else {
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
  }, [user, token, refreshToken]);

  // Real login with backend JWT
  const login = async (username, password) => {
    try {
      console.log('Login attempt for username:', username);
      const response = await fetch('http://127.0.0.1:8000/api/token/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });
      console.log('Login response status:', response.status);
      if (!response.ok) {
        const errorData = await response.json();
        console.log('Login error:', errorData);
        return { success: false, message: 'Invalid credentials' };
      }
      const data = await response.json();
      console.log('Login success, token received:', !!data.access);
      setToken(data.access);
      setRefreshToken(data.refresh);
      
      // Fetch user profile information including admin status
      try {
        console.log('Fetching user profile...');
        console.log('Token:', data.access);
        
        // Test if the endpoint is reachable
        const testResponse = await fetch('http://127.0.0.1:8000/api/user/profile/', {
          method: 'GET',
          headers: { 
            Authorization: `Bearer ${data.access}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('Profile response status:', testResponse.status);
        console.log('Profile response headers:', testResponse.headers);
        
        if (testResponse.ok) {
          const profileData = await testResponse.json();
          console.log('User profile data:', profileData);
          console.log('is_superuser:', profileData.is_superuser);
          console.log('is_staff:', profileData.is_staff);
          console.log('is_vendor:', profileData.is_vendor);
          setUser({
            id: profileData.id,
            userId: profileData.userId || profileData.id,
            username: profileData.username,
            email: profileData.email,
            is_superuser: profileData.is_superuser,
            isAdmin: profileData.is_superuser,
            is_staff: profileData.is_staff,
            is_vendor: profileData.is_vendor,
            first_name: profileData.first_name,
            last_name: profileData.last_name
          });
          console.log('User state set with admin data:', {
            username: profileData.username,
            is_superuser: profileData.is_superuser,
            isAdmin: profileData.is_superuser,
            is_staff: profileData.is_staff,
            is_vendor: profileData.is_vendor
          });
        } else {
          console.error('Profile response not ok:', testResponse.status);
          const errorText = await testResponse.text();
          console.error('Profile error response:', errorText);
          
          // Try to get more details about the error
          try {
            const errorJson = JSON.parse(errorText);
            console.error('Profile error JSON:', errorJson);
          } catch (e) {
            console.error('Profile error is not JSON:', errorText);
          }
          
          // Fallback to old method if profile endpoint fails
          console.log('Falling back to old vendor check method...');
          let is_vendor = false;
          try {
            const vendorRes = await fetch(`http://127.0.0.1:8000/api/salons/?vendor=${username}`, {
              headers: { Authorization: `Bearer ${data.access}` }
            });
            if (vendorRes.ok) {
              const salons = await vendorRes.json();
              if (Array.isArray(salons) && salons.length > 0) {
                is_vendor = true;
              }
            }
          } catch (e) { 
            console.error('Vendor check error:', e);
          }
          setUser({
            id: 1, // Fallback ID
            userId: 1, // Fallback ID
            username,
            is_vendor,
            isAdmin: false
          });
        }
      } catch (profileError) {
        console.error('Profile fetch error:', profileError);
        console.error('Profile fetch error details:', {
          message: profileError.message,
          stack: profileError.stack
        });
        // Fallback to old method
        let is_vendor = false;
        try {
          const vendorRes = await fetch(`http://127.0.0.1:8000/api/salons/?vendor=${username}`, {
            headers: { Authorization: `Bearer ${data.access}` }
          });
          if (vendorRes.ok) {
            const salons = await vendorRes.json();
            if (Array.isArray(salons) && salons.length > 0) {
              is_vendor = true;
            }
          }
        } catch (e) { 
          console.error('Vendor check error:', e);
        }
        setUser({
          id: 1, // Fallback ID
          userId: 1, // Fallback ID
          username,
          is_vendor,
          isAdmin: false
        });
      }
      
      return { success: true };
    } catch (err) {
      console.error('Login error:', err);
      return { success: false, message: 'Login failed' };
    }
  };

  // Real signup with backend
  const signup = async (username, password, email = '', role = 'customer', salonName = '', vendorPreference = '') => {
    try {
      console.log('Signup attempt for username:', username, 'email:', email, 'role:', role, 'salonName:', salonName, 'vendorPreference:', vendorPreference);
      const payload = { username, password, email, role };
      if (role === 'vendor') {
        payload.salon_name = salonName;
      }
      if (role === 'customer' && vendorPreference) {
        payload.vendor_preference = vendorPreference;
      }
      const response = await fetch('http://127.0.0.1:8000/api/register/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      console.log('Signup response status:', response.status);
      if (!response.ok) {
        const data = await response.json();
        console.log('Signup error:', data);
        return { success: false, message: data.error || 'Signup failed' };
      }
      const data = await response.json();
      console.log('Signup success, attempting auto-login...');
      // Auto-login after signup
      const loginResult = await login(username, password);
      console.log('Auto-login result:', loginResult);
      return loginResult;
    } catch (err) {
      console.error('Signup error:', err);
      return { success: false, message: `Signup failed: ${err.message}` };
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    setRefreshToken(null);
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  };

  // Helper to refresh access token
  const refreshAccessToken = async () => {
    if (!refreshToken) return false;
    try {
      const response = await fetch('http://127.0.0.1:8000/api/token/refresh/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh: refreshToken })
      });
      if (!response.ok) {
        logout();
        return false;
      }
      const data = await response.json();
      setToken(data.access);
      localStorage.setItem('token', data.access);
      return data.access;
    } catch (err) {
      logout();
      return false;
    }
  };

  // Helper to add auth header and auto-refresh token
  const authFetch = async (url, options = {}) => {
    const fullUrl = url.startsWith('http') ? url : `http://127.0.0.1:8000${url}`;
    const accessToken = token;
    let response = await fetch(fullUrl, {
      ...options,
      headers: {
        ...(options.headers || {}),
        Authorization: accessToken ? `Bearer ${accessToken}` : '',
        'Content-Type': 'application/json',
      },
    });
    // If token expired, try to refresh and retry once
    if (response.status === 401) {
      const errorData = await response.json().catch(() => ({}));
      if (errorData.code === 'token_not_valid' && refreshToken) {
        const newAccessToken = await refreshAccessToken();
        if (newAccessToken) {
          response = await fetch(fullUrl, {
            ...options,
            headers: {
              ...(options.headers || {}),
              Authorization: `Bearer ${newAccessToken}`,
              'Content-Type': 'application/json',
            },
          });
        } else {
          logout();
        }
      } else if (errorData.code === 'user_not_found') {
        logout();
      }
    }
    return response;
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      token, 
      refreshToken,
      login, 
      signup, 
      logout, 
      authFetch,
      isAuthenticated: !!user && !!token 
    }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);

export default AuthContext;
