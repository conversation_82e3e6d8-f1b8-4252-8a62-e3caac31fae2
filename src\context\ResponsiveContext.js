import React, { createContext, useContext, useEffect, useState } from 'react';

const ResponsiveContext = createContext({ isMobile: false });

export const ResponsiveProvider = ({ children }) => {
  const getIsMobile = () => window.matchMedia('(max-width: 767px)').matches;
  const [isMobile, setIsMobile] = useState(getIsMobile);

  useEffect(() => {
    const mql = window.matchMedia('(max-width: 767px)');
    const handleChange = () => setIsMobile(mql.matches);
    mql.addEventListener('change', handleChange);
    return () => {
      mql.removeEventListener('change', handleChange);
    };
  }, []);

  return (
    <ResponsiveContext.Provider value={{ isMobile }}>
      {children}
    </ResponsiveContext.Provider>
  );
};

export const useIsMobile = () => useContext(ResponsiveContext).isMobile; 

export default ResponsiveContext; 
