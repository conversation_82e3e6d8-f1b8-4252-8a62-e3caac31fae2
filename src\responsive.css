/* App.css */

/* Mobile-First Responsive Design - Fixed */

/* ===== CRITICAL MOBILE LOADING OPTIMIZATIONS ===== */
* {
  box-sizing: border-box;
}

html, body {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  scroll-behavior: smooth;
  max-width: 100%;
  overflow-x: hidden;
}

/* Prevent mobile loading issues */
body {
  margin: 0;
  padding: 0;
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* Ensure App loads properly on mobile */
.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

/* Mobile content visibility fixes */
main {
  flex: 1;
  width: 100%;
  overflow-x: hidden;
  position: relative;
  z-index: 1;
}

/* ===== MOBILE FIRST BASE STYLES (320px+) ===== */
:root {
  --mobile-padding: 16px;
  --mobile-margin: 12px;
  --touch-target-size: 44px;
  --mobile-font-size: 16px;
  --mobile-line-height: 1.5;
}

/* CRITICAL MOBILE CONTENT VISIBILITY FIXES */
@media (max-width: 767px) {
  /* Ensure main content is always visible */
  body {
    font-size: var(--mobile-font-size);
    line-height: var(--mobile-line-height);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    overflow-x: hidden;
    position: relative;
    z-index: 1;
  }

  /* Ensure App container doesn't get hidden */
  .App {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    overflow-x: hidden;
  }

  /* Ensure main content is visible and not covered by navigation */
  main {
    position: relative;
    z-index: 1;
    padding: 15px;
    overflow: visible;
    margin-top: 0;
    width: 100%;
    min-height: calc(100vh - 200px); /* Account for header and footer */
  }

  /* Ensure header doesn't cover content */
  header {
    min-height: 48px;
    padding: 6px 0;
    background: rgba(30,34,40,0.92) !important;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.13);
    border-bottom: 1.5px solid rgba(255,255,255,0.08);
    backdrop-filter: blur(10px) saturate(160%);
  }
  header .container {
    padding: 6px 10px;
  }
  header h1, header .logo-link {
    font-size: 1.1rem !important;
    letter-spacing: 0.01em;
    font-weight: 800;
  }
  header .tagline {
    font-size: 0.7rem !important;
    font-weight: 600;
    opacity: 0.7;
    margin-top: 0;
  }

  /* Ensure footer is visible */
  footer {
    font-size: 0.92rem;
    padding: 10px 0 0 0 !important;
    background: rgba(30,34,40,0.93) !important;
    border-top: 1.5px solid rgba(255,255,255,0.08);
    box-shadow: 0 -2px 12px 0 rgba(0,0,0,0.13);
    min-height: 44px;
  }
  footer .container {
    padding: 0 10px;
    flex-direction: column;
    gap: 2px;
  }

  /* Touch-friendly buttons - only for mobile */
  .btn, .btn-sm, .btn-group .btn, .page-link, .glam-btn {
    min-width: 44px;
    min-height: 44px;
    padding: 12px 16px;
    font-size: 16px;
    line-height: 1.2;
    border-radius: 8px;
  }
  .btn i, .page-link i {
    font-size: 1.2em;
    vertical-align: middle;
  }

  /* Mobile card improvements - Fix truncation */
  .glam-card, .card, .card.h-100, .card.shadow-sm {
    background: rgba(255,255,255,0.13) !important;
    backdrop-filter: blur(8px) saturate(160%);
    border-radius: 18px !important;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.13), 0 1.5px 6px 0 rgba(255,215,0,0.04);
    border: 1.5px solid rgba(255,255,255,0.18) !important;
    padding: 22px 18px !important;
    margin-bottom: 18px;
    transition: box-shadow 0.2s, background 0.2s;
  }
  .glam-card .card-title, .card .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--accent-color, #ffd700);
    margin-bottom: 10px;
    letter-spacing: 0.01em;
  }
  .glam-card .card-text, .card .card-text {
    font-size: 1.05rem;
    color: #fff;
    line-height: 1.6;
    font-weight: 500;
    margin-bottom: 8px;
  }

  /* Fix carousel card truncation - Our Salons in Nairobi Section */
  .glam-carousel .glam-card {
    height: 280px !important;
    min-height: 280px !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .glam-carousel .glam-card .card-body {
    padding: 0.75rem !important;
    height: auto !important;
    overflow: visible !important;
    display: flex !important;
    flex-direction: column !important;
    flex: 1 !important;
  }

  .glam-carousel .glam-card img {
    max-width: 100% !important;
    height: 100px !important;
    object-fit: cover !important;
    border-radius: 8px !important;
    margin-bottom: 0.5rem !important;
  }

  .glam-carousel .glam-card .card-title {
    font-size: 0.8rem !important;
    margin-bottom: 0.4rem !important;
    text-transform: none !important;
    line-height: 1.2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .glam-carousel .glam-card .card-text {
    font-size: 0.7rem !important;
    margin-bottom: auto !important;
    line-height: 1.2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .glam-carousel .glam-card .glam-btn {
    font-size: 0.7rem !important;
    padding: 0.5rem 0.75rem !important;
    margin-top: auto !important;
    text-transform: none !important;
    min-height: auto !important;
    border-radius: 8px !important;
  }

  /* Enhanced Mobile-First Pagination for Home Page */
  .pagination-container {
    margin: 1.5rem 0 !important;
    padding: 1rem !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    z-index: 10 !important;
    background: rgba(255, 255, 255, 0.02) !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px) !important;
  }

  .mobile-pagination {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    background: rgba(255, 255, 255, 0.08) !important;
    padding: 0.75rem 1.25rem !important;
    border-radius: 25px !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  }

  .desktop-pagination {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    background: rgba(255, 255, 255, 0.08) !important;
    padding: 1rem 1.5rem !important;
    border-radius: 30px !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1) !important;
  }

  .pagination-btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    cursor: pointer !important;
  }

  .pagination-btn:hover:not(:disabled) {
    background: rgba(255, 179, 71, 0.2) !important;
    border-color: rgba(255, 179, 71, 0.4) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(255, 179, 71, 0.3) !important;
  }

  .pagination-btn:disabled {
    opacity: 0.3 !important;
    cursor: not-allowed !important;
    transform: none !important;
  }

  .pagination-text {
    color: white !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    min-width: 100px !important;
    text-align: center !important;
  }

  .pagination-icon {
    font-size: 1.1rem !important;
    font-weight: bold !important;
  }

  .page-numbers {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .page-number {
    width: 36px !important;
    height: 36px !important;
    border-radius: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
  }

  .page-number:hover {
    background: rgba(255, 179, 71, 0.2) !important;
    border-color: rgba(255, 179, 71, 0.4) !important;
    transform: translateY(-1px) !important;
  }

  .page-number.active {
    background: linear-gradient(135deg, #ff6b9d, #ff8e53) !important;
    border-color: transparent !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3) !important;
  }

  .page-ellipsis {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 0.9rem !important;
    padding: 0 0.5rem !important;
  }

  .page-number {
    width: 36px !important;
    height: 36px !important;
    border-radius: 8px !important;
    font-size: 0.85rem !important;
    font-weight: 500 !important;
  }

  .page-number:hover {
    transform: translateY(-1px) !important;
  }

  .pagination-info {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    min-width: 80px !important;
    text-align: center !important;
  }

  .page-ellipsis {
    font-size: 0.9rem !important;
    padding: 0 0.5rem !important;
  }

  /* Fix grid layout issues */
  .row .col-12,
  .row .col-sm-6,
  .row .col-md-3,
  .row .col-md-4,
  .row .col-lg-4 {
    margin-bottom: 20px;
    padding-left: 10px;
    padding-right: 10px;
  }

  /* Mobile form improvements */
  .form-control, .form-select {
    background: rgba(255,255,255,0.13) !important;
    border-radius: 14px !important;
    border: 2px solid rgba(255,255,255,0.18) !important;
    color: #fff !important;
    font-size: 1.08rem !important;
    padding: 18px 14px !important;
    margin-bottom: 18px !important;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.08);
    transition: border 0.2s, box-shadow 0.2s;
  }
  .form-control:focus, .form-select:focus {
    border-color: var(--accent-color, #ffd700) !important;
    box-shadow: 0 0 0 3px rgba(255,215,0,0.13);
    background: rgba(255,255,255,0.18) !important;
    color: #fff !important;
  }
  .form-label {
    font-size: 1.08rem;
    font-weight: 700;
    color: var(--accent-color, #ffd700);
    margin-bottom: 7px;
    letter-spacing: 0.01em;
  }

  /* Mobile table improvements */
  .table-responsive, .table-responsive-sm {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    display: block;
  }

  table {
    min-width: 600px;
  }

  .table-responsive th,
  .table-responsive td {
    padding: 12px 8px;
    white-space: normal; /* Allow text to wrap */
    word-wrap: break-word;
  }

  /* Mobile jumbotron improvements */
  .glam-jumbotron {
    padding: 60px var(--mobile-padding);
    margin: var(--mobile-margin);
    border-radius: 20px;
    overflow: visible !important;
    position: relative;
    z-index: 1;
  }

    .glam-jumbotron h1 {
    font-size: 2.2rem;
    line-height: 1.2;
    margin-bottom: 20px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    overflow: visible;
  }

    .glam-jumbotron p {
        font-size: 1.1rem;
    line-height: 1.5;
    margin-bottom: 24px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    overflow: visible;
  }

  .glam-jumbotron .btn {
    width: 100%;
    max-width: 280px;
    padding: 16px 24px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 50px;
  }

  /* Mobile grid improvements */
  .row {
    margin-left: calc(-1 * var(--mobile-margin));
    margin-right: calc(-1 * var(--mobile-margin));
  }

  .col-12, .col-md-6, .col-lg-4 {
    padding-left: var(--mobile-margin);
    padding-right: var(--mobile-margin);
    margin-bottom: var(--mobile-margin);
  }

  /* Mobile container adjustments */
  .container {
    padding-left: var(--mobile-padding);
    padding-right: var(--mobile-padding);
    max-width: 100% !important;
    overflow: visible !important;
    min-height: 80vh;
    position: relative;
    z-index: 1;
  }

  /* Fix filter section layout */
  .d-flex.justify-content-between.align-items-center.mb-4 {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .d-flex.justify-content-between.align-items-center.mb-4 h3 {
    text-align: center;
    margin-bottom: 10px;
  }

  .d-flex.justify-content-between.align-items-center.mb-4 .btn {
    width: 100%;
    max-width: none;
  }

  /* Fix row spacing */
  .row.mb-3 {
    margin-bottom: 1rem !important;
  }

  .row.mb-3 > div {
    margin-bottom: 1rem;
  }

  /* Fix input group layout */
  .input-group {
    flex-direction: column;
  }

  .input-group .form-control {
    border-radius: 12px;
    margin-bottom: 8px;
  }

  .input-group .btn {
    border-radius: 12px;
    margin-top: 0;
  }

  /* Fix carousel slides */
  .slick-slide {
    padding: 0 5px;
  }

  .slick-slide .glam-card {
    height: auto !important;
    margin: 0;
  }

  /* Fix display classes for mobile */
  .display-3, .display-4, .display-5 {
    font-size: 2rem;
    line-height: 1.2;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Fix contact card */
  .glam-contact-card {
    padding: 30px 15px;
    margin: 20px 0;
  }

  .glam-contact-card h2 {
    font-size: 2rem;
    word-wrap: break-word;
  }

  .glam-contact-card p {
    font-size: 1.1rem;
    word-wrap: break-word;
  }

  /* Fix alerts */
  .alert {
    margin: 15px 0;
    padding: 15px;
    border-radius: 12px;
    word-wrap: break-word;
  }

  h1, .display-3 {
    font-size: 2rem !important;
  }
  h2, .display-4 {
    font-size: 1.5rem !important;
  }
  h3, .display-5 {
    font-size: 1.2rem !important;
  }

  .mobile-bottom-nav {
    width: 100vw !important;
    max-width: none !important;
    left: 0;
    right: 0;
    margin: 0 !important;
    border-radius: 0 0 0 0 !important;
    position: fixed;
    bottom: 0;
    z-index: 1200;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: rgba(30, 34, 40, 0.85);
    backdrop-filter: blur(16px) saturate(180%);
    box-shadow: 0 -4px 24px 0 rgba(0,0,0,0.18);
    padding: 6px 0 2px 0;
    height: 58px;
  }
  .mobile-bottom-nav button {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 1.6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1 1 0;
    padding: 0 2px;
    border-radius: 12px;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    min-width: 44px;
    min-height: 44px;
    position: relative;
  }
  .mobile-bottom-nav button.active,
  .mobile-bottom-nav button:focus {
    color: var(--accent-color, #ffd700);
    background: rgba(255,255,255,0.08);
    box-shadow: 0 2px 8px 0 rgba(255,215,0,0.08);
  }
  .mobile-bottom-nav button i {
    font-size: 1.7em;
    margin-bottom: 2px;
    pointer-events: none;
  }
  .mobile-bottom-nav button span {
    font-size: 0.8em;
    font-weight: 600;
    letter-spacing: 0.01em;
    margin-top: 0px;
    pointer-events: none;
  }
  .mobile-bottom-nav button:active {
    background: rgba(255,255,255,0.13);
    color: var(--accent-color, #ffd700);
    transform: scale(0.97);
  }
  /* Prevent footer overlap */
  footer .text-end {
    min-width: 0 !important;
    flex-wrap: wrap;
    white-space: normal;
    word-break: break-word;
  }
  footer {
    margin-bottom: 70px !important;
  }

  .mobile-fab {
    position: fixed;
    right: 24px;
    bottom: 80px;
    z-index: 1300;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255,255,255,0.18);
    backdrop-filter: blur(12px) saturate(180%);
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.18), 0 1.5px 6px 0 rgba(255,215,0,0.08);
    border: 2px solid rgba(255,255,255,0.25);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color, #ffd700);
    font-size: 2.2rem;
    transition: background 0.2s, box-shadow 0.2s, color 0.2s, transform 0.15s;
    cursor: pointer;
    outline: none;
    animation: fadeInUp 0.5s cubic-bezier(0.4,0,0.2,1);
  }
  .mobile-fab:active, .mobile-fab:focus {
    background: rgba(255,255,255,0.28);
    color: #fff;
    transform: scale(0.96);
    box-shadow: 0 2px 8px 0 rgba(255,215,0,0.18);
  }
  .mobile-fab i {
    pointer-events: none;
  }

  .glam-carousel {
    overflow: visible !important;
  }
  .glam-card {
    overflow: visible !important;
  }

  /* --- NUCLEAR MOBILE VISIBILITY FIX --- */
  .container, .glam-jumbotron, .glam-card, .glam-carousel, main, body, #root, .App {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
    max-width: 100vw !important;
    min-height: 60vh !important;
    z-index: 1 !important;
  }
}

/* ===== ENHANCED GEN Z MOBILE BOTTOM NAVIGATION ===== */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg,
    rgba(15, 15, 23, 0.95) 0%,
    rgba(25, 25, 35, 0.95) 50%,
    rgba(20, 20, 30, 0.95) 100%);
  backdrop-filter: blur(24px) saturate(180%);
  border-top: 1px solid rgba(255, 215, 0, 0.2);
  box-shadow:
    0 -8px 32px rgba(0, 0, 0, 0.6),
    0 -2px 16px rgba(255, 215, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 8px 0 max(env(safe-area-inset-bottom), 12px);
  display: flex;
  justify-content: space-around;
  align-items: center;
  min-height: 67px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  opacity: 1;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
}

.mobile-bottom-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.6) 20%,
    rgba(255, 179, 71, 0.8) 50%,
    rgba(255, 215, 0, 0.6) 80%,
    transparent 100%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Gen Z Micro-Interactions */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  }
}

/* Add floating animation to active nav items */
.mobile-bottom-nav button.active {
  animation: float 3s ease-in-out infinite;
}

/* Add subtle glow to footer elements on hover */
.dev-link:hover,
.legal-icon:hover {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* Scroll-aware states */
.mobile-bottom-nav.visible {
  transform: translateY(0);
  opacity: 1;
}

.mobile-bottom-nav.hidden {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}

.mobile-bottom-nav button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.75);
  padding: 8px 12px;
  min-width: 68px;
  min-height: 52px;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.mobile-bottom-nav button::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.08) 0%,
    rgba(255, 179, 71, 0.12) 50%,
    rgba(255, 215, 0, 0.08) 100%);
  border-radius: 14px;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(0.85);
}

.mobile-bottom-nav button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 215, 0, 0.05) 100%);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-bottom-nav button:hover::before,
.mobile-bottom-nav button:focus::before {
  opacity: 1;
  transform: scale(1);
}

.mobile-bottom-nav button:hover::after {
  opacity: 1;
}

.mobile-bottom-nav button:active {
  transform: scale(0.96);
}

.mobile-bottom-nav button i {
  font-size: 24px;
  margin-bottom: 4px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
  position: relative;
  z-index: 2;
}

.mobile-bottom-nav button span {
  font-size: 10px;
  font-weight: 700;
  line-height: 1.1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.8px;
  text-transform: uppercase;
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.mobile-bottom-nav button.active {
  color: #FFD700;
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.15) 0%,
    rgba(255, 179, 71, 0.2) 50%,
    rgba(255, 215, 0, 0.15) 100%);
  border: 1px solid rgba(255, 215, 0, 0.4);
  box-shadow:
    0 4px 16px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.mobile-bottom-nav button.active::before {
  opacity: 1;
  transform: scale(1);
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.2) 0%,
    rgba(255, 179, 71, 0.25) 50%,
    rgba(255, 215, 0, 0.2) 100%);
}

.mobile-bottom-nav button.active i {
  color: #FFD700;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
    transform: scale(1);
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
    transform: scale(1.05);
  }
}

.mobile-bottom-nav button.active i {
  transform: scale(1.15);
  color: var(--accent-color, #FFD700);
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.4));
}

.mobile-bottom-nav button.active span {
  color: var(--accent-color, #FFD700);
  font-weight: 700;
  transform: translateY(-1px);
}

.mobile-bottom-nav button.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--accent-color, #FFD700);
  border-radius: 50%;
  box-shadow: 0 0 12px rgba(255, 215, 0, 0.8);
  animation: pulse-dot 2s ease-in-out infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(1.2);
    opacity: 0.8;
  }
}

/* Ensure content doesn't get hidden behind bottom nav */
@media (max-width: 767px) {
  body {
    padding-bottom: 0; /* Remove padding since footer is no longer fixed */
  }
  
  main {
    padding-bottom: 0; /* Remove extra padding to eliminate gap */
  }

  /* Optimize layout for Gen Z mobile experience */
  .App {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  main {
    flex: 1;
  }

  .genz-footer {
    margin-top: 0; /* Remove auto margin that creates gap */
  }

  footer {
    margin-bottom: 0 !important; /* Remove bottom margin */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mobile-bottom-nav {
    background: #000;
    border-top: 2px solid white;
  }
  
  .mobile-bottom-nav button {
    color: white;
  }
  
  .mobile-bottom-nav button.active {
    color: var(--accent-color, #FFD700);
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mobile-bottom-nav,
  .mobile-bottom-nav button,
  .mobile-bottom-nav button i,
  .mobile-bottom-nav button span {
    transition: none;
  }
  
  .mobile-bottom-nav button:active {
    transform: none;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-bottom-nav {
    min-height: 50px;
    padding: 6px 0;
  }
  
  .mobile-bottom-nav button {
    min-height: 40px;
    padding: 6px 10px;
  }
  
  .mobile-bottom-nav button i {
    font-size: 18px;
    margin-bottom: 2px;
  }
  
  .mobile-bottom-nav button span {
    font-size: 10px;
  }
  
  body {
    padding-bottom: 0; /* Remove padding since footer is no longer fixed */
  }

  footer {
    margin-bottom: 0 !important; /* Remove bottom margin for landscape */
  }
}

/* Very small screens */
@media (max-width: 375px) {
  .mobile-bottom-nav {
    padding: 6px 0;
    min-height: 50px;
  }

  .mobile-bottom-nav button {
    min-width: 50px;
    min-height: 40px;
    padding: 6px 8px;
  }

  .mobile-bottom-nav button i {
    font-size: 18px;
    margin-bottom: 3px;
  }

  .mobile-bottom-nav button span {
    font-size: 10px;
  }
}

/* ===== DOCUMENTED DESIGN FOOTER STYLES ===== */
.genz-footer-container {
  margin-top: auto;
}

.footer-container {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  position: relative;
  overflow: hidden;
}

.mobile-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  padding: 12px 0 max(env(safe-area-inset-bottom), 12px);
  margin: 0;
  position: relative;
  z-index: 999;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.footer-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 0 16px;
  position: relative;
}

.footer-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-section.dev-credit {
  order: 1;
  flex: 1;
  justify-content: flex-start;
}

.footer-section.copyright {
  order: 2;
  flex: 1;
  justify-content: center;
}

.footer-section.legal-icons {
  order: 3;
  flex: 1;
  justify-content: flex-end;
}

.dev-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: #667eea;
  text-decoration: none;
  font-size: 0.7rem;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 6px 10px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(102, 126, 234, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.dev-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 230, 208, 0.2) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.dev-link:hover::before {
  left: 100%;
}

.dev-link:hover {
  color: #4facfe;
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  border-color: rgba(102, 126, 234, 0.4);
}

.dev-link span {
  color: #667eea;
  position: relative;
  z-index: 2;
  font-weight: 600;
}

.dev-link svg {
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.dev-link:hover svg {
  transform: scale(1.1);
}

.copyright {
  color: #1a1a1a;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
}

.copyright span {
  color: #555;
}

.legal-icons {
  display: flex;
  gap: 8px;
}

.legal-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  color: #667eea;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.75rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.legal-icon:hover {
  background: rgba(255, 255, 255, 0.95);
  color: #4facfe;
  transform: translateY(-2px) scale(1.05);
  border-color: rgba(102, 126, 234, 0.4);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.legal-icon:active {
  transform: translateY(-1px) scale(0.98);
}

.legal-icon i {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.legal-icon:hover i {
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.4));
}

.copyright {
  order: 2;
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.2px;
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
}

.copyright::before {
  content: '✨';
  margin-right: 3.6px;
  font-size: 0.6rem;
  opacity: 0.7;
}

.copyright::after {
  content: '✨';
  margin-left: 3.6px;
  font-size: 0.6rem;
  opacity: 0.7;
}

/* Enhanced Desktop Footer */
.desktop-footer {
  margin-top: auto;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.footer-desktop-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section.copyright-section {
  flex: 1;
  text-align: left;
}

.footer-section.dev-section {
  flex: 1;
  text-align: center;
}

.footer-section.legal-section {
  flex: 1;
  text-align: right;
}

.copyright-text {
  color: #1a1a1a;
  font-size: 0.9rem;
  font-weight: 600;
}

.dev-link-desktop {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #667eea;
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(102, 126, 234, 0.2);
  letter-spacing: 0.5px;
}

.dev-link-desktop:hover {
  color: #4facfe;
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  text-decoration: none;
}

.legal-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  margin: 0 8px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.legal-link:hover {
  color: #4facfe;
  text-decoration: none;
}

.legal-link i {
  margin-right: 4px;
}

.separator {
  color: #999;
  margin: 0 4px;
  opacity: 0.5;
}

.desktop-footer .container {
  position: relative;
  z-index: 2;
}

.desktop-footer a {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.desktop-footer a:hover {
  color: #FFD700 !important;
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
  transform: translateY(-1px);
}

/* Enhanced Responsive Adjustments */
@media (max-width: 480px) {
  .mobile-footer {
    padding: 5.4px 0 3.6px 0;
  }
  .footer-content {
    gap: 3px;
    padding: 0 8px;
  }
  .legal-icons {
    gap: 6px;
  }

  .legal-icon {
    width: 18px;
    height: 18px;
    font-size: 0.45rem;
  }

  .dev-link {
    font-size: 0.45rem;
    padding: 3px 4px;
    gap: 2px;
  }

  .copyright {
    font-size: 0.5rem;
  }

  .mobile-bottom-nav {
    min-height: 58px;
    padding: 6px 0 max(env(safe-area-inset-bottom), 8px);
  }

  .mobile-bottom-nav button {
    min-width: 60px;
    min-height: 48px;
    padding: 6px 8px;
  }

  .mobile-bottom-nav button i {
    font-size: 20px;
    margin-bottom: 3px;
  }

  .mobile-bottom-nav button span {
    font-size: 9px;
  }
}

/* ===== TABLET STYLES (768px+) ===== */
@media (min-width: 768px) {
  .App {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    padding: 0;
    width: 100%;
    overflow-x: visible;
    text-align: center;
  }
  main {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    width: 100%;
    min-height: unset;
    overflow-x: visible;
  }
  .container {
    max-width: 1300px;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
  }

  :root {
    --mobile-padding: 24px;
    --mobile-margin: 16px;
    }

    .glam-jumbotron {
    padding: 80px 40px;
    }

  .glam-jumbotron h1 {
    font-size: 2.8rem;
  }

  .glam-jumbotron p {
    font-size: 1.2rem;
  }

  .glam-jumbotron .btn {
    width: auto;
    max-width: none;
  }

    .glam-card {
    margin: var(--mobile-margin);
  }

  .glam-card .card-title {
    font-size: 1.6rem;
  }

  .booking-form-row .col-md-6 {
    margin-bottom: 0;
  }

  .d-flex.justify-content-between.align-items-center.mb-4 {
    flex-direction: row;
  }

  .d-flex.justify-content-between.align-items-center.mb-4 h3 {
    text-align: left;
    margin-bottom: 0;
  }

  .d-flex.justify-content-between.align-items-center.mb-4 .btn {
    width: auto;
    max-width: none;
  }

  .input-group {
    flex-direction: row;
  }

  .input-group .form-control {
    border-radius: 12px 0 0 12px;
  }

  .input-group .btn {
    border-radius: 0 12px 12px 0;
    margin-top: 0;
  }
}

/* ===== DESKTOP STYLES (1024px+) ===== */
@media (min-width: 1024px) {
  :root {
    --mobile-padding: 32px;
    --mobile-margin: 24px;
  }

  .glam-jumbotron {
    padding: 100px 60px;
  }

  .glam-jumbotron h1 {
    font-size: 3.2rem;
  }

  .glam-jumbotron p {
    font-size: 1.3rem;
  }

  .container {
    max-width: 960px;
  }

  /* Three-column layout for cards */
  .salon-grid .col-lg-4 {
    margin-bottom: 0;
  }

  /* Fix carousel on desktop */
  .glam-carousel .glam-card {
    height: 500px;
  }

  .glam-carousel .glam-card img {
    height: 260px !important;
  }
}

/* ===== LARGE DESKTOP STYLES (1200px+) ===== */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .glam-jumbotron h1 {
    font-size: 3.8rem;
    }
}

/* ===== LANDSCAPE MOBILE ORIENTATION ===== */
@media (max-width: 768px) and (orientation: landscape) {
  .glam-jumbotron {
    padding: 40px var(--mobile-padding);
    }

    .glam-jumbotron h1 {
    font-size: 1.8rem;
    }

    .glam-jumbotron p {
        font-size: 1rem;
    }
}

/* ===== MOBILE NAVIGATION FIXES ===== */
/* Mobile navigation styles are now handled in Header.css */
/* Removed conflicting styles to prevent overlay issues */

/* ===== ANIMATIONS ===== */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    }
}

/* ===== LOADING STATES ===== */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
    }

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus indicators for keyboard navigation */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glam-card {
    border: 2px solid white;
    background: rgba(0, 0, 0, 0.9);
    }

  .form-control,
  .form-select {
    border: 2px solid white;
    background: rgba(0, 0, 0, 0.8);
    }
}

/* Responsive image rules for all images */
img, .img-fluid {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Mobile-specific image rules for cards and carousels */
@media (max-width: 767px) {
  .glam-card img, .glam-carousel img, .card-img-top {
    max-width: 100%;
    height: auto !important;
    object-fit: cover;
  }
}

@media (max-width: 480px) {
  /* Extra small mobile - Our Salons in Nairobi Section */
  .glam-carousel .glam-card {
    height: 260px !important;
    min-height: 260px !important;
  }

  .glam-carousel .glam-card img {
    height: 90px !important;
  }

  .glam-carousel .glam-card .card-body {
    padding: 0.6rem !important;
  }

  .glam-carousel .glam-card .card-title {
    font-size: 0.75rem !important;
    margin-bottom: 0.3rem !important;
  }

  .glam-carousel .glam-card .card-text {
    font-size: 0.65rem !important;
    margin-bottom: 0.4rem !important;
  }

  .glam-carousel .glam-card .glam-btn {
    font-size: 0.65rem !important;
    padding: 0.4rem 0.6rem !important;
  }
}

@media (max-width: 375px) {
  .container, .glam-jumbotron {
    padding-left: 12px;
    padding-right: 12px;
  }
  .row {
    margin-left: -6px;
    margin-right: -6px;
  }
  [class*="col-"] {
    padding-left: 6px;
    padding-right: 6px;
  }
}

.container, .glam-jumbotron, .row, [class*="col-"] {
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box;
}

/* --- Desktop grid/layout stability patch (≥992px) --- */
@media (min-width: 992px) {
  .App, main, .dashboard-layout {
    width: 100% !important;
    min-width: 0 !important;
    max-width: none !important;
    position: static !important;
    flex-grow: unset !important;
    flex-shrink: unset !important;
    flex-basis: unset !important;
    overflow-x: visible !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: stretch !important;
    justify-content: flex-start !important;
    padding: 0 !important;
  }
  .container {
    max-width: 1300px !important;
    width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    box-sizing: border-box !important;
  }
  .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .row [class*="col-"] > .glam-card {
    max-width: 100%;
    min-width: 0;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    box-sizing: border-box;
  }
  .glam-card {
    min-width: 220px;
    max-width: 370px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Remove duplicate/conflicting rules and ensure only the reduced height/padding rules are present for .mobile-footer, .footer-content, and .desktop-footer. Place these at the end of the file for highest specificity. */

.mobile-footer {
  padding: 7.2px 0 max(env(safe-area-inset-bottom), 7.2px) !important;
}
@media (max-width: 480px) {
  .mobile-footer {
    padding: 5.4px 0 3.6px 0 !important;
  }
  .footer-content {
    gap: 4.5px !important;
  }
}
.footer-content {
  gap: 4px !important;
  padding: 0 8px !important;
}
.desktop-footer {
  padding-top: 18px !important;
  padding-bottom: 18px !important;
}