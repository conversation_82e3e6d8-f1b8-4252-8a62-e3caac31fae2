// Enhanced Payment Gateway Service for SalonGenz
// Handles payment processing, status tracking, and booking updates

export class PaymentGateway {
  constructor() {
    this.paymentStatuses = {
      PENDING: 'pending',
      PROCESSING: 'processing',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled'
    };
  }

  /**
   * Process payment for a booking
   * @param {Object} bookingData - Booking information
   * @param {string} paymentMethod - Payment method
   * @returns {Promise<Object>} Payment result
   */
  async processPayment(bookingData, paymentMethod) {
    try {
      // Update booking status to processing
      await this.updateBookingStatus(bookingData.id, this.paymentStatuses.PROCESSING);

      // Process payment via backend API
      const response = await fetch('http://127.0.0.1:8000/api/payments/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_id: bookingData.id,
          payment_method: paymentMethod,
          amount: bookingData.total_amount || bookingData.pricingData?.breakdown.total,
          phone_number: bookingData.phoneNumber || bookingData.customer_phone
        })
      });

      if (!response.ok) {
        throw new Error('Payment processing failed');
      }

      const result = await response.json();

      if (result.success) {
        // Update booking status to completed
        await this.updateBookingStatus(bookingData.id, this.paymentStatuses.COMPLETED);
        
        // Send confirmation email
        await this.sendConfirmationEmail(bookingData);
        
        return {
          success: true,
          transactionId: result.transaction_id,
          message: result.message,
          redirectUrl: result.redirect_url || '/checkout-success'
        };
      } else {
        // Update booking status to failed
        await this.updateBookingStatus(bookingData.id, this.paymentStatuses.FAILED);
        
        return {
          success: false,
          message: result.message || 'Payment failed',
          redirectUrl: result.redirect_url || '/payment-failed'
        };
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      
      // Update booking status to failed
      await this.updateBookingStatus(bookingData.id, this.paymentStatuses.FAILED);
      
      return {
        success: false,
        message: 'Payment processing failed. Please try again.',
        redirectUrl: '/payment-failed'
      };
    }
  }

  /**
   * Process payment by specific method
   * @param {Object} bookingData - Booking information
   * @param {string} paymentMethod - Payment method
   * @returns {Promise<Object>} Payment result
   */
  async processPaymentByMethod(bookingData, paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'mpesa':
        return await this.processMpesaPayment(bookingData);
      case 'paypal':
        return await this.processPayPalPayment(bookingData);
      case 'banktransfer':
        return await this.processBankTransfer(bookingData);
      case 'wise':
        return await this.processWisePayment(bookingData);
      case 'visa':
      case 'card':
        return await this.processCardPayment(bookingData);
      default:
        throw new Error('Invalid payment method');
    }
  }

  /**
   * Process M-Pesa payment
   * @param {Object} bookingData - Booking information
   * @returns {Promise<Object>} Payment result
   */
  async processMpesaPayment(bookingData) {
    console.log('Processing M-Pesa payment...', bookingData);
    
    // Simulate M-Pesa API call
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate 95% success rate
        const isSuccess = Math.random() > 0.05;
        
        if (isSuccess) {
          resolve({
            success: true,
            transactionId: `MPESA_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message: 'M-Pesa payment completed successfully'
          });
        } else {
          resolve({
            success: false,
            message: 'M-Pesa payment failed. Please try again.'
          });
        }
      }, 2000); // Simulate 2-second processing time
    });
  }

  /**
   * Process PayPal payment
   * @param {Object} bookingData - Booking information
   * @returns {Promise<Object>} Payment result
   */
  async processPayPalPayment(bookingData) {
    console.log('Processing PayPal payment...', bookingData);
    
    // Simulate PayPal API call
    return new Promise((resolve) => {
      setTimeout(() => {
        const isSuccess = Math.random() > 0.03;
        
        if (isSuccess) {
          resolve({
            success: true,
            transactionId: `PAYPAL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message: 'PayPal payment completed successfully'
          });
        } else {
          resolve({
            success: false,
            message: 'PayPal payment failed. Please try again.'
          });
        }
      }, 3000);
    });
  }

  /**
   * Process bank transfer
   * @param {Object} bookingData - Booking information
   * @returns {Promise<Object>} Payment result
   */
  async processBankTransfer(bookingData) {
    console.log('Processing bank transfer...', bookingData);
    
    // For bank transfer, we provide details and mark as pending
    return {
      success: true,
      transactionId: `BANK_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: 'Bank transfer details provided. Payment will be confirmed within 1-3 business days.',
      requiresConfirmation: true
    };
  }

  /**
   * Process Wise payment
   * @param {Object} bookingData - Booking information
   * @returns {Promise<Object>} Payment result
   */
  async processWisePayment(bookingData) {
    console.log('Processing Wise payment...', bookingData);
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const isSuccess = Math.random() > 0.04;
        
        if (isSuccess) {
          resolve({
            success: true,
            transactionId: `WISE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message: 'Wise payment completed successfully'
          });
        } else {
          resolve({
            success: false,
            message: 'Wise payment failed. Please try again.'
          });
        }
      }, 2500);
    });
  }

  /**
   * Process card payment
   * @param {Object} bookingData - Booking information
   * @returns {Promise<Object>} Payment result
   */
  async processCardPayment(bookingData) {
    console.log('Processing card payment...', bookingData);
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const isSuccess = Math.random() > 0.02;
        
        if (isSuccess) {
          resolve({
            success: true,
            transactionId: `CARD_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message: 'Card payment completed successfully'
          });
        } else {
          resolve({
            success: false,
            message: 'Card payment failed. Please check your card details and try again.'
          });
        }
      }, 1500);
    });
  }

  /**
   * Update booking status
   * @param {string} bookingId - Booking ID
   * @param {string} status - New status
   * @returns {Promise<void>}
   */
  async updateBookingStatus(bookingId, status) {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/bookings/${bookingId}/status/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        console.error('Failed to update booking status');
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  }

  /**
   * Send confirmation email
   * @param {Object} bookingData - Booking information
   */
  async sendConfirmationEmail(bookingData) {
    try {
      const response = await fetch('http://127.0.0.1:8000/api/notifications/send-confirmation/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          booking_id: bookingData.id,
          customer_email: bookingData.customer_email || bookingData.email,
          customer_name: bookingData.customer_name || bookingData.userName
        })
      });

      if (!response.ok) {
        console.error('Failed to send confirmation email');
      }
    } catch (error) {
      console.error('Error sending confirmation email:', error);
    }
  }

  /**
   * Get payment status
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<Object>} Payment status
   */
  async getPaymentStatus(transactionId) {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/payments/${transactionId}/status/`);
      
      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Failed to get payment status');
      }
    } catch (error) {
      console.error('Error getting payment status:', error);
      return { status: 'unknown', message: 'Unable to determine payment status' };
    }
  }

  /**
   * Cancel payment
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<Object>} Cancellation result
   */
  async cancelPayment(transactionId) {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/payments/${transactionId}/cancel/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Failed to cancel payment');
      }
    } catch (error) {
      console.error('Error canceling payment:', error);
      return { success: false, message: 'Unable to cancel payment' };
    }
  }
}

// Export singleton instance
export const paymentGateway = new PaymentGateway();

// Export utility functions for backward compatibility
export const processPayment = (bookingDetails) => {
  return paymentGateway.processPayment(bookingDetails, bookingDetails.paymentMethod);
};

// Legacy functions for backward compatibility
const processMpesaPayment = (bookingDetails) => {
  console.log('Processing M-Pesa payment...', bookingDetails);
  return { success: true, message: 'M-Pesa payment initiated.', redirectUrl: '/payment/mpesa' };
};

const processPayPalPayment = (bookingDetails) => {
  console.log('Processing PayPal payment...', bookingDetails);
  return { success: true, message: 'PayPal payment initiated.', redirectUrl: '/payment/paypal' };
};

const processBankTransfer = (bookingDetails) => {
  console.log('Processing bank transfer...', bookingDetails);
  return { success: true, message: 'Bank transfer details provided.', redirectUrl: '/payment/banktransfer' };
};

const processWisePayment = (bookingDetails) => {
  console.log('Processing Wise payment...', bookingDetails);
  return { success: true, message: 'Wise payment initiated.', redirectUrl: '/payment/wise' };
};

const processVisaPayment = (bookingDetails) => {
  console.log('Processing Visa payment...', bookingDetails);
  return { success: true, message: 'Visa payment initiated.', redirectUrl: '/payment/visa' };
};

// Legacy processPayment function
export const processPaymentLegacy = (bookingDetails) => {
  switch (bookingDetails.paymentMethod) {
    case 'mpesa':
      return processMpesaPayment(bookingDetails);
    case 'paypal':
      return processPayPalPayment(bookingDetails);
    case 'banktransfer':
      return processBankTransfer(bookingDetails);
    case 'wise':
      return processWisePayment(bookingDetails);
    case 'visa':
      return processVisaPayment(bookingDetails);
    default:
      return { success: false, message: 'Invalid payment method.' };
  }
};
