// Paystack Payment Service for SalonGenz
// Handles all Paystack payment integrations

class PaystackService {
  constructor() {
    // Backend API configuration
    this.backendUrl = 'http://127.0.0.1:8000/api';
    
    // Paystack configuration (only public key for frontend)
    this.publicKey = process.env.REACT_APP_PAYSTACK_PUBLIC_KEY || 'pk_test_61f52f12e283a8733e00c3531d1a495f2cd3943f';
    
    // Supported payment methods
    this.supportedMethods = {
      card: 'card',
      bank: 'bank',
      ussd: 'ussd',
      qr: 'qr',
      mobile_money: 'mobile_money',
      bank_transfer: 'bank_transfer'
    };
  }

  /**
   * Initialize a transaction via backend
   * @param {Object} paymentData - Payment information
   * @returns {Promise<Object>} Transaction initialization result
   */
  async initializeTransaction(paymentData) {
    try {
      const {
        amount,
        email,
        reference,
        callbackUrl,
        currency = 'KES',
        channels = ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],
        metadata = {}
      } = paymentData;

      const payload = {
        amount: Math.round(amount * 100), // Convert to kobo (smallest currency unit)
        email,
        reference,
        callback_url: callbackUrl,
        currency,
        channels,
        metadata: {
          ...metadata,
          booking_id: paymentData.booking_id,
          customer_name: paymentData.customer_name,
          service_name: paymentData.service_name,
          salon_name: paymentData.salon_name
        }
      };

      const response = await fetch(`${this.backendUrl}/paystack/initialize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initialize transaction');
      }

      const result = await response.json();
      return {
        success: true,
        authorization_url: result.data.authorization_url,
        access_code: result.data.access_code,
        reference: result.data.reference,
        message: 'Transaction initialized successfully'
      };

    } catch (error) {
      console.error('Paystack initialization error:', error);
      return {
        success: false,
        message: error.message || 'Failed to initialize payment'
      };
    }
  }

  /**
   * Verify a transaction via backend
   * @param {string} reference - Transaction reference
   * @returns {Promise<Object>} Transaction verification result
   */
  async verifyTransaction(reference) {
    try {
      const response = await fetch(`${this.backendUrl}/paystack/verify/${reference}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to verify transaction');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Transaction verified successfully'
      };

    } catch (error) {
      console.error('Paystack verification error:', error);
      return {
        success: false,
        message: error.message || 'Failed to verify payment'
      };
    }
  }

  /**
   * Charge a card via backend
   * @param {Object} cardData - Card payment information
   * @returns {Promise<Object>} Card charge result
   */
  async chargeCard(cardData) {
    try {
      const {
        amount,
        email,
        cardNumber,
        expiryMonth,
        expiryYear,
        cvv,
        reference,
        currency = 'KES',
        metadata = {}
      } = cardData;

      const payload = {
        amount: Math.round(amount * 100),
        email,
        card: {
          number: cardNumber,
          expiry_month: expiryMonth,
          expiry_year: expiryYear,
          cvv
        },
        reference,
        currency,
        metadata
      };

      const response = await fetch(`${this.backendUrl}/paystack/charge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to charge card');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Card charged successfully'
      };

    } catch (error) {
      console.error('Paystack card charge error:', error);
      return {
        success: false,
        message: error.message || 'Failed to charge card'
      };
    }
  }

  /**
   * Charge a bank account via backend
   * @param {Object} bankData - Bank payment information
   * @returns {Promise<Object>} Bank charge result
   */
  async chargeBank(bankData) {
    try {
      const {
        amount,
        email,
        accountNumber,
        bankCode,
        reference,
        currency = 'KES',
        metadata = {}
      } = bankData;

      const payload = {
        amount: Math.round(amount * 100),
        email,
        account_number: accountNumber,
        bank_code: bankCode,
        reference,
        currency,
        metadata
      };

      const response = await fetch(`${this.backendUrl}/paystack/charge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to charge bank account');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Bank account charged successfully'
      };

    } catch (error) {
      console.error('Paystack bank charge error:', error);
      return {
        success: false,
        message: error.message || 'Failed to charge bank account'
      };
    }
  }

  /**
   * Get list of banks via backend
   * @returns {Promise<Object>} Banks list
   */
  async getBanks() {
    try {
      const response = await fetch(`${this.backendUrl}/paystack/banks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get banks');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Banks retrieved successfully'
      };

    } catch (error) {
      console.error('Paystack get banks error:', error);
      return {
        success: false,
        message: error.message || 'Failed to get banks'
      };
    }
  }

  /**
   * Resolve bank account via backend
   * @param {string} accountNumber - Account number
   * @param {string} bankCode - Bank code
   * @returns {Promise<Object>} Account resolution result
   */
  async resolveBankAccount(accountNumber, bankCode) {
    try {
      const response = await fetch(`${this.backendUrl}/paystack/resolve-account`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          account_number: accountNumber,
          bank_code: bankCode
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to resolve account');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Account resolved successfully'
      };

    } catch (error) {
      console.error('Paystack resolve account error:', error);
      return {
        success: false,
        message: error.message || 'Failed to resolve account'
      };
    }
  }

  /**
   * Create transfer recipient via backend
   * @param {Object} recipientData - Recipient information
   * @returns {Promise<Object>} Transfer recipient creation result
   */
  async createTransferRecipient(recipientData) {
    try {
      const {
        type,
        name,
        accountNumber,
        bankCode,
        currency = 'KES',
        metadata = {}
      } = recipientData;

      const payload = {
        type,
        name,
        account_number: accountNumber,
        bank_code: bankCode,
        currency,
        metadata
      };

      const response = await fetch(`${this.backendUrl}/paystack/transferrecipient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create transfer recipient');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Transfer recipient created successfully'
      };

    } catch (error) {
      console.error('Paystack create transfer recipient error:', error);
      return {
        success: false,
        message: error.message || 'Failed to create transfer recipient'
      };
    }
  }

  /**
   * Initiate transfer via backend
   * @param {Object} transferData - Transfer information
   * @returns {Promise<Object>} Transfer initiation result
   */
  async initiateTransfer(transferData) {
    try {
      const {
        source,
        amount,
        recipient,
        reason,
        currency = 'KES',
        metadata = {}
      } = transferData;

      const payload = {
        source,
        amount: Math.round(amount * 100),
        recipient,
        reason,
        currency,
        metadata
      };

      const response = await fetch(`${this.backendUrl}/paystack/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initiate transfer');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Transfer initiated successfully'
      };

    } catch (error) {
      console.error('Paystack initiate transfer error:', error);
      return {
        success: false,
        message: error.message || 'Failed to initiate transfer'
      };
    }
  }

  /**
   * Get transaction status via backend
   * @param {string} reference - Transaction reference
   * @returns {Promise<Object>} Transaction status
   */
  async getTransactionStatus(reference) {
    try {
      const response = await fetch(`${this.backendUrl}/paystack/verify/${reference}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get transaction status');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'Transaction status retrieved successfully'
      };

    } catch (error) {
      console.error('Paystack get transaction status error:', error);
      return {
        success: false,
        message: error.message || 'Failed to get transaction status'
      };
    }
  }

  /**
   * Generate a unique reference
   * @param {string} prefix - Reference prefix
   * @returns {string} Generated reference
   */
  static generateReference(prefix = 'SALON') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`.toUpperCase();
  }

  /**
   * Format amount for display
   * @param {number} amount - Amount in kobo
   * @returns {string} Formatted amount
   */
  static formatAmount(amount) {
    return (amount / 100).toFixed(2);
  }

  /**
   * Parse amount to kobo
   * @param {number} amount - Amount in currency units
   * @returns {number} Amount in kobo
   */
  static parseAmount(amount) {
    return Math.round(amount * 100);
  }

  /**
   * Initialize M-Pesa payment via backend
   * @param {Object} mpesaData - M-Pesa payment information
   * @returns {Promise<Object>} M-Pesa initialization result
   */
  async initializeMpesaPayment(mpesaData) {
    try {
      const {
        amount,
        email,
        phoneNumber,
        callbackUrl,
        reference,
        currency = 'KES',
        metadata = {}
      } = mpesaData;

      const payload = {
        amount: Math.round(amount * 100),
        email,
        callback_url: callbackUrl,
        reference,
        currency,
        channels: ['mobile_money'],
        metadata: {
          ...metadata,
          phone_number: phoneNumber
        }
      };

      const response = await fetch(`${this.backendUrl}/paystack/initialize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initialize M-Pesa payment');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'M-Pesa payment initialized successfully'
      };

    } catch (error) {
      console.error('Paystack M-Pesa initialization error:', error);
      return {
        success: false,
        message: error.message || 'Failed to initialize M-Pesa payment'
      };
    }
  }

  /**
   * Charge M-Pesa payment via backend
   * @param {Object} mpesaData - M-Pesa charge information
   * @returns {Promise<Object>} M-Pesa charge result
   */
  async chargeMpesa(mpesaData) {
    try {
      const {
        amount,
        email,
        phoneNumber,
        reference,
        currency = 'KES',
        metadata = {}
      } = mpesaData;

      const payload = {
        amount: Math.round(amount * 100),
        email,
        phone_number: phoneNumber,
        reference,
        currency,
        channels: ['mobile_money'],
        metadata
      };

      const response = await fetch(`${this.backendUrl}/paystack/charge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to charge M-Pesa payment');
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        message: 'M-Pesa payment charged successfully'
      };

    } catch (error) {
      console.error('Paystack M-Pesa charge error:', error);
      return {
        success: false,
        message: error.message || 'Failed to charge M-Pesa payment'
      };
    }
  }

  /**
   * Get Kenya-specific payment channels
   * @returns {Array} Available channels
   */
  static getKenyaChannels() {
    return ['card', 'bank', 'mobile_money', 'ussd'];
  }

  /**
   * Check if M-Pesa is supported for amount
   * @param {number} amount - Amount in currency units
   * @returns {boolean} Whether M-Pesa is supported
   */
  static isMpesaSupported(amount) {
    return amount >= 10 && amount <= 70000; // M-Pesa limits in KES
  }
}

// Create singleton instance
const paystackService = new PaystackService();

// Export functions for easy use
export const initializePaystackTransaction = (paymentData) => paystackService.initializeTransaction(paymentData);
export const verifyPaystackTransaction = (reference) => paystackService.verifyTransaction(reference);
export const chargePaystackCard = (cardData) => paystackService.chargeCard(cardData);
export const chargePaystackBank = (bankData) => paystackService.chargeBank(bankData);
export const initializePaystackMpesa = (mpesaData) => paystackService.initializeMpesaPayment(mpesaData);
export const chargePaystackMpesa = (mpesaData) => paystackService.chargeMpesa(mpesaData);
export const getPaystackBanks = () => paystackService.getBanks();
export const resolvePaystackAccount = (accountNumber, bankCode) => paystackService.resolveBankAccount(accountNumber, bankCode);
export const generatePaystackReference = (prefix) => PaystackService.generateReference(prefix);
export const getPaystackKenyaChannels = () => PaystackService.getKenyaChannels();
export const isPaystackMpesaSupported = (amount) => PaystackService.isMpesaSupported(amount); 