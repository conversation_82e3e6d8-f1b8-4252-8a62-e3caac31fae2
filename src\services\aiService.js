// AI Service for generating personalized gift messages
// This service can integrate with OpenAI, Groq, or other AI providers

import { API_BASE_URL, getApiUrl, API_ENDPOINTS } from '../utils/apiConfig';

class AIService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.aiEndpoint = getApiUrl('api/ai');
    // Keep fallback for direct API access if needed
    this.apiKey = process.env.REACT_APP_GROQ_API_KEY || process.env.REACT_APP_MISTRAL_API_KEY || process.env.REACT_APP_OPENAI_API_KEY;
    this.directURL = process.env.REACT_APP_GROQ_API_URL || process.env.REACT_APP_MISTRAL_API_URL || process.env.REACT_APP_OPENAI_API_URL || 'https://api.groq.com/openai/v1';

    // Set model based on which API is being used
    if (this.directURL.includes('groq.com')) {
      this.model = 'llama3-8b-8192';
    } else if (this.directURL.includes('mistral.ai')) {
      this.model = 'mistral-small-latest';
    } else if (this.directURL.includes('openai.com')) {
      this.model = 'gpt-3.5-turbo';
    } else {
      this.model = 'llama3-8b-8192'; // Default to Groq
    }
  }

  // Generate personalized gift message using AI with East African localization
  async generateGiftMessage(relationship, occasion, tone, recipientName = '', userProfile = {}) {
    try {
      // Try backend AI service first
      const response = await fetch(`${this.aiEndpoint}/gift-messages/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          relationship,
          occasion,
          tone,
          recipientName,
          userProfile  // Pass user profile for cultural localization
        }),
      });

      if (response.ok) {
        const data = await response.json();

        // If backend returns fallback, use local templates
        if (data.fallback) {
          return this.generateLocalMessage(relationship, occasion, tone, recipientName);
        }

        return data.response || data.message || 'Your thoughtful gift awaits! 💝';
      }

      // Fallback to direct API if backend fails
      return await this.generateDirectMessage(relationship, occasion, tone, recipientName);

    } catch (error) {
      console.warn('Backend AI failed, trying direct API:', error);
      return await this.generateDirectMessage(relationship, occasion, tone, recipientName);
    }
  }

  // Direct API call as fallback
  async generateDirectMessage(relationship, occasion, tone, recipientName = '') {
    try {
      // If no API key is available, use local templates
      if (!this.apiKey) {
        return this.generateLocalMessage(relationship, occasion, tone, recipientName);
      }

      const prompt = this.buildPrompt(relationship, occasion, tone, recipientName);

      const response = await fetch(`${this.directURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'system',
              content: 'You are a Gen-Z friendly AI assistant that creates personalized, fun, and trendy gift messages for salon appointments. Use emojis, modern slang, and make the messages feel authentic and personal.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 150,
          temperature: 0.8,
        }),
      });

      if (!response.ok) {
        throw new Error('AI service unavailable');
      }

      const data = await response.json();
      return data.choices[0].message.content.trim();
    } catch (error) {
      console.warn('Direct AI service failed, using local templates:', error);
      return this.generateLocalMessage(relationship, occasion, tone, recipientName);
    }
  }

  // Build prompt for AI
  buildPrompt(relationship, occasion, tone, recipientName) {
    const relationshipContext = {
      bestie: 'best friend who you share everything with',
      sister: 'sister who you love and support',
      mom: 'amazing mother who deserves pampering',
      girlfriend: 'romantic partner who you adore',
      friend: 'good friend who you care about',
      colleague: 'work colleague who you appreciate',
      other: 'special person in your life'
    };

    const occasionContext = {
      birthday: 'birthday celebration',
      anniversary: 'anniversary or special milestone',
      friendship: 'friendship appreciation',
      thank_you: 'thank you gesture',
      just_because: 'just because they deserve it',
      congratulations: 'congratulations on an achievement',
      get_well: 'get well soon wishes'
    };

    const toneContext = {
      friendly: 'warm and friendly',
      playful: 'fun and playful with emojis',
      romantic: 'romantic and sweet',
      sassy: 'confident and sassy',
      caring: 'caring and supportive'
    };

    return `Create a personalized gift message for a salon appointment. 
    
    Context:
    - Relationship: ${relationshipContext[relationship] || 'special person'}
    - Occasion: ${occasionContext[occasion] || 'special occasion'}
    - Tone: ${toneContext[tone] || 'friendly'}
    - Recipient: ${recipientName || 'your loved one'}
    
    Requirements:
    - Keep it under 100 characters
    - Use relevant emojis
    - Make it Gen-Z friendly and trendy
    - Include salon/beauty references
    - Make it personal and heartfelt
    - Use modern, casual language
    
    Generate a short, personalized message:`;
  }

  // Local message generation as fallback
  generateLocalMessage(relationship, occasion, tone, recipientName) {
    const templates = {
      birthday: [
        '🎉 Happy Birthday! Time to treat yourself to something fabulous! 💅✨',
        '🎂 Another year, another reason to look absolutely stunning! 💁‍♀️✨',
        '🎊 Birthday vibes + salon vibes = pure magic! ✨💫'
      ],
      anniversary: [
        '💕 Celebrating love and beauty! Here\'s to many more years of looking fabulous together! 💑✨',
        '💖 Love is in the air, and so is the glam! 💅💫',
        '💝 Another year of love, another year of looking absolutely stunning! ✨💕'
      ],
      friendship: [
        '👯‍♀️ Bestie, you deserve all the pampering! Time to slay! 💅✨',
        '💖 Because true friends deserve to look as amazing as they are! ✨💫',
        '👭 Friendship goals: looking fabulous together! 💁‍♀️✨'
      ],
      thank_you: [
        '🙏 Thank you for being you! Here\'s a little something to make you feel extra special! ✨💫',
        '💝 Gratitude looks good on you! Time to treat yourself! 💅✨',
        '🙌 You\'re amazing, and you deserve to look it! ✨💫'
      ],
      just_because: [
        '✨ Just because you\'re absolutely fabulous and deserve to feel it! 💅💫',
        '💫 Because every day is a good day to look stunning! ✨💁‍♀️',
        '🌟 You deserve all the good vibes and glam! ✨💅'
      ],
      congratulations: [
        '🎉 Congrats on your achievement! Time to celebrate with some glam! 💅✨',
        '🏆 You did it! Now let\'s make you look as amazing as you are! ✨💫',
        '🎊 Achievement unlocked: fabulous salon time! 💁‍♀️✨'
      ],
      get_well: [
        '💝 Get well soon! A little pampering will help you feel better! ✨💫',
        '🤗 Sending healing vibes and salon magic your way! 💅✨',
        '💕 Feel better soon! You deserve all the self-care! ✨💫'
      ]
    };

    const selectedTemplates = templates[occasion] || templates.just_because;
    let message = selectedTemplates[Math.floor(Math.random() * selectedTemplates.length)];

    // Personalize based on relationship
    if (relationship === 'bestie' && recipientName) {
      message = message.replace('!', ` ${recipientName}!`);
    } else if (relationship === 'mom' && recipientName) {
      message = message.replace('!', ' mom!');
    }

    // Add tone-specific emojis
    const toneEmojis = {
      friendly: '😊',
      playful: '😄',
      romantic: '💕',
      sassy: '💁‍♀️',
      caring: '🤗'
    };

    const toneEmoji = toneEmojis[tone] || '✨';
    message += ` ${toneEmoji}`;

    return message;
  }

  // Generate multiple message options
  async generateMessageOptions(relationship, occasion, tone, recipientName = '', userProfile = {}) {
    try {
      const promises = Array(3).fill().map(() => this.generateGiftMessage(relationship, occasion, tone, recipientName, userProfile));

      const messages = await Promise.all(promises);
      return messages.filter((msg, index, arr) => arr.indexOf(msg) === index); // Remove duplicates
    } catch (error) {
      console.warn('Failed to generate multiple options:', error);
      return [this.generateLocalMessage(relationship, occasion, tone, recipientName)];
    }
  }

  // Analyze message sentiment and suggest improvements
  analyzeMessage(message) {
    const analysis = {
      length: message.length,
      emojiCount: (message.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length,
      hasSalonReference: /salon|beauty|glam|pamper|treat|fabulous|stunning/i.test(message),
      hasPersonalTouch: /you|your|special|deserve|amazing/i.test(message),
      suggestions: []
    };

    if (analysis.length < 20) {
      analysis.suggestions.push('Consider adding more personal details');
    }
    if (analysis.emojiCount < 2) {
      analysis.suggestions.push('Add more emojis for Gen-Z appeal');
    }
    if (!analysis.hasSalonReference) {
      analysis.suggestions.push('Include salon/beauty references');
    }
    if (!analysis.hasPersonalTouch) {
      analysis.suggestions.push('Make it more personal');
    }

    return analysis;
  }

  // Get trending emojis and phrases for Gen-Z
  getTrendingElements() {
    return {
      emojis: ['💅', '✨', '💕', '🎉', '💁‍♀️', '🔥', '💯', '👑', '💎', '🌟', '💫', '😍', '🥰', '🤩'],
      phrases: [
        'slay', 'fabulous', 'stunning', 'gorgeous', 'amazing', 'perfect', 'beautiful',
        'glam', 'pamper', 'treat yourself', 'self-care', 'vibes', 'goals', 'mood'
      ]
    };
  }
}

// Create singleton instance
const aiService = new AIService();

export default aiService; 
