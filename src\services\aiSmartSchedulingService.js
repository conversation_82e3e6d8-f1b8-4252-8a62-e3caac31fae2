// AI Smart Scheduling Service
// Provides intelligent appointment scheduling with optimal time recommendations

class AISmartSchedulingService {
  constructor() {
    this.apiKey = process.env.REACT_APP_OPENAI_API_KEY || process.env.REACT_APP_GROQ_API_KEY;
    this.apiUrl = process.env.REACT_APP_GROQ_API_URL || 'https://api.groq.com/openai/v1/chat/completions';
  }

  // Generate smart scheduling recommendations
  async generateSmartSchedule(userPreferences, salonAvailability, serviceDetails) {
    try {
      // Try Django AI API first
      const response = await fetch('/ai/smart-scheduling/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userPreferences,
          salonAvailability,
          serviceDetails
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (!result.fallback) {
          return result;
        }
      }

      // Fallback to local scheduling if API fails
      console.log('Using fallback smart scheduling');
      return this.getLocalSmartSchedule(userPreferences, salonAvailability, serviceDetails);
    } catch (error) {
      console.error('Smart scheduling error:', error);
      return this.getLocalSmartSchedule(userPreferences, salonAvailability, serviceDetails);
    }
  }

  // Build AI prompt for smart scheduling
  buildSchedulingPrompt(userPreferences, salonAvailability, serviceDetails) {
    const {
      preferredDays,
      preferredTimes,
      travelTime,
      workSchedule,
      urgency,
      budget,
      serviceType
    } = userPreferences;

    return `Generate smart scheduling recommendations for a salon appointment:

User Preferences:
- Preferred Days: ${JSON.stringify(preferredDays)}
- Preferred Times: ${JSON.stringify(preferredTimes)}
- Travel Time: ${travelTime} minutes
- Work Schedule: ${JSON.stringify(workSchedule)}
- Urgency: ${urgency}
- Budget: ${budget}
- Service Type: ${serviceType}

Salon Availability:
${JSON.stringify(salonAvailability)}

Service Details:
${JSON.stringify(serviceDetails)}

Generate optimal scheduling recommendations in this format:
{
  "recommendations": [
    {
      "date": "YYYY-MM-DD",
      "time": "HH:MM",
      "confidence": 0.0-1.0,
      "reasoning": "Why this time is optimal",
      "travelTime": "X minutes",
      "totalTime": "X hours",
      "cost": "$X",
      "availability": "high/medium/low",
      "alternatives": ["time1", "time2"]
    }
  ],
  "optimalTime": {
    "date": "YYYY-MM-DD",
    "time": "HH:MM",
    "reason": "Primary reason for recommendation"
  },
  "schedulingTips": [
    "Tip 1",
    "Tip 2"
  ],
  "conflicts": [
    {
      "type": "work/travel/budget",
      "description": "Potential conflict description",
      "solution": "Suggested solution"
    }
  ]
}`;
  }

  // Call external AI API
  async callAIAPI(prompt) {
    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'llama3-8b-8192',
          messages: [
            {
              role: 'system',
              content: 'You are a smart scheduling assistant specializing in salon appointments and beauty services.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.6,
          max_tokens: 800
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error('AI API call failed');
      }

      const data = await response.json();
      const { content } = data.choices[0].message;
      
      try {
        return JSON.parse(content);
      } catch (error) {
        return this.parseAIResponse(content);
      }
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('AI API call timed out');
      }
      throw error;
    }
  }

  // Parse AI response if not valid JSON
  parseAIResponse(content) {
    const lines = content.split('\n');
    const recommendations = [];
    const optimalTime = {};
    const schedulingTips = [];
    const conflicts = [];

    let currentSection = '';
    for (const line of lines) {
      if (line.includes('recommendations:')) currentSection = 'recommendations';
      else if (line.includes('optimal time:')) currentSection = 'optimal';
      else if (line.includes('tips:')) currentSection = 'tips';
      else if (line.includes('conflicts:')) currentSection = 'conflicts';
      else if (line.includes('date:') && currentSection === 'recommendations') {
        recommendations.push({
          date: line.split('date:')[1]?.trim() || '2024-01-20',
          time: '10:00',
          confidence: 0.8,
          reasoning: 'AI-recommended optimal time',
          travelTime: '15 minutes',
          totalTime: '2 hours',
          cost: '$75',
          availability: 'high',
          alternatives: ['11:00', '14:00']
        });
      }
    }

    return {
      recommendations: recommendations.length > 0 ? recommendations : this.getLocalRecommendations(),
      optimalTime: Object.keys(optimalTime).length > 0 ? optimalTime : { date: '2024-01-20', time: '10:00', reason: 'Best availability and timing' },
      schedulingTips: schedulingTips.length > 0 ? schedulingTips : ['Book early for popular services', 'Consider travel time', 'Plan for service duration'],
      conflicts: conflicts.length > 0 ? conflicts : []
    };
  }

  // Get local smart schedule when AI is not available
  getLocalSmartSchedule(userPreferences, salonAvailability, serviceDetails) {
    // Generate recommendations based on preferences
    const recommendations = this.generateRecommendations(userPreferences, salonAvailability, serviceDetails);
    const optimalTime = this.findOptimalTime(recommendations);
    const conflicts = this.identifyConflicts(userPreferences, serviceDetails);
    const schedulingTips = this.generateSchedulingTips(userPreferences, serviceDetails);

    return {
      recommendations,
      optimalTime,
      schedulingTips,
      conflicts
    };
  }

  // Generate scheduling recommendations
  generateRecommendations(userPreferences, salonAvailability, serviceDetails) {
    const { preferredDays, preferredTimes, travelTime } = userPreferences;
    const recommendations = [];

    // Generate recommendations for next 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
      
      if (preferredDays.includes(dayName) || preferredDays.length === 0) {
        preferredTimes.forEach(time => {
          const availability = this.checkAvailability(date, time, salonAvailability);
          if (availability !== 'unavailable') {
            const confidence = this.calculateConfidence(date, time, userPreferences);
            recommendations.push({
              date: date.toISOString().split('T')[0],
              time,
              confidence,
              reasoning: this.getReasoning(date, time, userPreferences),
              travelTime: `${travelTime} minutes`,
              totalTime: this.calculateTotalTime(serviceDetails),
              cost: this.calculateCost(serviceDetails),
              availability,
              alternatives: this.getAlternatives(date, time, salonAvailability)
            });
          }
        });
      }
    }

    return recommendations.sort((a, b) => b.confidence - a.confidence).slice(0, 5);
  }

  // Find optimal time from recommendations
  findOptimalTime(recommendations) {
    if (recommendations.length === 0) {
      return {
        date: new Date().toISOString().split('T')[0],
        time: '10:00',
        reason: 'Default recommendation'
      };
    }

    const optimal = recommendations[0];
    return {
      date: optimal.date,
      time: optimal.time,
      reason: optimal.reasoning
    };
  }

  // Calculate confidence score for a time slot
  calculateConfidence(date, time, userPreferences) {
    const { preferredDays, preferredTimes, urgency } = userPreferences;
    let confidence = 0.5;

    // Day preference
    const dayName = new Date(date).toLocaleDateString('en-US', { weekday: 'long' });
    if (preferredDays.includes(dayName)) confidence += 0.2;
    if (preferredTimes.includes(time)) confidence += 0.2;

    // Urgency factor
    if (urgency === 'high') confidence += 0.1;
    if (urgency === 'low') confidence -= 0.1;

    // Time of day preference
    const hour = parseInt(time.split(':')[0]);
    if (hour >= 9 && hour <= 17) confidence += 0.1; // Business hours
    if (hour >= 10 && hour <= 14) confidence += 0.1; // Peak hours

    return Math.min(confidence, 1.0);
  }

  // Get reasoning for recommendation
  getReasoning(date, time, userPreferences) {
    const dayName = new Date(date).toLocaleDateString('en-US', { weekday: 'long' });
    const hour = parseInt(time.split(':')[0]);
    
    let reasoning = `Good availability on ${dayName}`;
    
    if (hour >= 9 && hour <= 11) reasoning += ' - Morning slot for fresh start';
    else if (hour >= 12 && hour <= 14) reasoning += ' - Lunch break timing';
    else if (hour >= 15 && hour <= 17) reasoning += ' - Afternoon convenience';
    
    return reasoning;
  }

  // Check availability for a specific date and time
  checkAvailability(date, time, salonAvailability) {
    const dayName = new Date(date).toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const availability = salonAvailability[dayName] || {};
    
    if (availability[time] === 'available') return 'high';
    if (availability[time] === 'limited') return 'medium';
    return 'unavailable';
  }

  // Get alternative times
  getAlternatives(date, time, salonAvailability) {
    const alternatives = [];
    const hour = parseInt(time.split(':')[0]);
    
    // Suggest times around the preferred time
    for (let i = hour - 2; i <= hour + 2; i++) {
      if (i >= 9 && i <= 18 && i !== hour) {
        const altTime = `${i.toString().padStart(2, '0')}:00`;
        if (this.checkAvailability(date, altTime, salonAvailability) !== 'unavailable') {
          alternatives.push(altTime);
        }
      }
    }
    
    return alternatives.slice(0, 3);
  }

  // Calculate total time for service
  calculateTotalTime(serviceDetails) {
    const baseTime = serviceDetails.duration || 60;
    const travelTime = 30; // Default travel time
    return `${Math.round((baseTime + travelTime) / 60)} hours`;
  }

  // Calculate cost for service
  calculateCost(serviceDetails) {
    const baseCost = serviceDetails.price || 50;
    return `$${baseCost}`;
  }

  // Identify potential conflicts
  identifyConflicts(userPreferences, serviceDetails) {
    const conflicts = [];
    const { workSchedule, budget, serviceType } = userPreferences;
    
    // Work schedule conflicts
    if (workSchedule && workSchedule.workDays.includes(new Date().getDay())) {
      conflicts.push({
        type: 'work',
        description: 'Appointment during work hours',
        solution: 'Consider lunch break or early morning appointment'
      });
    }
    
    // Budget conflicts
    if (serviceDetails.price > budget) {
      conflicts.push({
        type: 'budget',
        description: 'Service cost exceeds budget',
        solution: 'Consider alternative services or payment plans'
      });
    }
    
    // Service type conflicts
    if (serviceType === 'hair_coloring' && serviceDetails.duration > 180) {
      conflicts.push({
        type: 'time',
        description: 'Long service duration',
        solution: 'Plan for extended appointment time'
      });
    }
    
    return conflicts;
  }

  // Generate scheduling tips
  generateSchedulingTips(userPreferences, serviceDetails) {
    const tips = [
      'Book appointments 1-2 weeks in advance for popular services',
      'Consider travel time when scheduling',
      'Plan for service duration plus buffer time',
      'Check salon cancellation policies'
    ];
    
    if (userPreferences.urgency === 'high') {
      tips.push('Call salon directly for urgent appointments');
    }
    
    if (serviceDetails.duration > 120) {
      tips.push('Schedule longer services on days with flexible schedule');
    }
    
    return tips;
  }

  // Get local recommendations when AI is not available
  getLocalRecommendations() {
    const today = new Date();
    const recommendations = [];
    
    for (let i = 0; i < 5; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      recommendations.push({
        date: date.toISOString().split('T')[0],
        time: '10:00',
        confidence: 0.8 - (i * 0.1),
        reasoning: `Good availability on ${date.toLocaleDateString('en-US', { weekday: 'long' })}`,
        travelTime: '15 minutes',
        totalTime: '2 hours',
        cost: '$75',
        availability: 'high',
        alternatives: ['11:00', '14:00']
      });
    }
    
    return recommendations;
  }

  // Analyze user's scheduling patterns
  analyzeSchedulingPatterns(userHistory) {
    const patterns = {
      preferredDays: [],
      preferredTimes: [],
      averageDuration: 0,
      commonServices: []
    };
    
    if (userHistory && userHistory.length > 0) {
      // Analyze day preferences
      const dayCounts = {};
      userHistory.forEach(appointment => {
        const day = new Date(appointment.date).toLocaleDateString('en-US', { weekday: 'long' });
        dayCounts[day] = (dayCounts[day] || 0) + 1;
      });
      
      patterns.preferredDays = Object.keys(dayCounts).sort((a, b) => dayCounts[b] - dayCounts[a]);
      
      // Analyze time preferences
      const timeCounts = {};
      userHistory.forEach(appointment => {
        const time = appointment.time.split(':')[0];
        timeCounts[time] = (timeCounts[time] || 0) + 1;
      });
      
      patterns.preferredTimes = Object.keys(timeCounts).sort((a, b) => timeCounts[b] - timeCounts[a]);
      
      // Calculate average duration
      const totalDuration = userHistory.reduce((sum, appointment) => sum + (appointment.duration || 60), 0);
      patterns.averageDuration = totalDuration / userHistory.length;
      
      // Find common services
      const serviceCounts = {};
      userHistory.forEach(appointment => {
        const { service } = appointment;
        serviceCounts[service] = (serviceCounts[service] || 0) + 1;
      });
      
      patterns.commonServices = Object.keys(serviceCounts).sort((a, b) => serviceCounts[b] - serviceCounts[a]);
    }
    
    return patterns;
  }

  // Suggest optimal booking times based on patterns
  suggestOptimalTimes(schedulingPatterns, salonAvailability) {
    const suggestions = [];
    
    schedulingPatterns.preferredDays.forEach(day => {
      schedulingPatterns.preferredTimes.forEach(time => {
        const availability = this.checkAvailability(new Date(), `${time}:00`, salonAvailability);
        if (availability !== 'unavailable') {
          suggestions.push({
            day,
            time: `${time}:00`,
            confidence: 0.9,
            reason: 'Based on your booking history'
          });
        }
      });
    });
    
    return suggestions.slice(0, 5);
  }

  // Calculate booking success probability
  calculateBookingSuccess(date, time, userPreferences, salonAvailability) {
    const availability = this.checkAvailability(date, time, salonAvailability);
    const confidence = this.calculateConfidence(date, time, userPreferences);
    
    let successRate = 0.5;
    
    if (availability === 'high') successRate += 0.3;
    if (availability === 'medium') successRate += 0.1;
    if (confidence > 0.8) successRate += 0.2;
    
    return Math.min(successRate, 1.0);
  }
}

const aiSmartSchedulingService = new AISmartSchedulingService();
export default aiSmartSchedulingService; 
