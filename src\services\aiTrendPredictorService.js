// AI Trend Predictor Service
// Analyzes social media, fashion data, and salon trends to predict upcoming style trends

import { API_BASE_URL, getApiUrl, API_ENDPOINTS } from '../utils/apiConfig';

class AITrendPredictorService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.aiEndpoint = getApiUrl('api/ai');
    this.apiKey = process.env.REACT_APP_OPENAI_API_KEY || process.env.REACT_APP_GROQ_API_KEY;
    this.apiUrl = process.env.REACT_APP_GROQ_API_URL || 'https://api.groq.com/openai/v1/chat/completions';
  }

  // Predict upcoming trends based on current data
  async predictTrends(currentData) {
    try {
      // Try Django AI API first
      const response = await fetch('/ai/trend-predictions/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platformData: currentData
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (!result.fallback) {
          return result;
        }
      }

      // Fallback to local trend predictions if API fails
      console.log('Using fallback trend predictions');
      return this.getLocalTrendPredictions(currentData);
    } catch (error) {
      console.error('Trend prediction error:', error);
      return this.getLocalTrendPredictions(currentData);
    }
  }

  // Build AI prompt for trend prediction
  buildTrendPredictionPrompt(currentData) {
    const {
      socialMediaTrends,
      fashionData,
      salonBookings,
      seasonalFactors,
      celebrityInfluences
    } = currentData;

    return `Analyze this data to predict upcoming salon and beauty trends for Gen-Z:

Current Data:
- Social Media Trends: ${JSON.stringify(socialMediaTrends)}
- Fashion Data: ${JSON.stringify(fashionData)}
- Salon Bookings: ${JSON.stringify(salonBookings)}
- Seasonal Factors: ${JSON.stringify(seasonalFactors)}
- Celebrity Influences: ${JSON.stringify(celebrityInfluences)}

Predict trends for the next 3-6 months in this format:
{
  "emergingTrends": [
    {
      "name": "Trend Name",
      "description": "Detailed description",
      "confidence": 0.0-1.0,
      "timeframe": "3-6 months",
      "targetAudience": "Gen-Z demographic",
      "influencers": ["celebrity1", "celebrity2"],
      "socialMediaHype": "high/medium/low",
      "salonImpact": "high/medium/low",
      "predictionReasoning": "Why this trend will emerge"
    }
  ],
  "decliningTrends": [
    {
      "name": "Declining Trend",
      "description": "Why it's declining",
      "declineRate": "fast/medium/slow",
      "replacementTrend": "What's replacing it"
    }
  ],
  "seasonalPredictions": {
    "spring": ["trend1", "trend2"],
    "summer": ["trend1", "trend2"],
    "fall": ["trend1", "trend2"],
    "winter": ["trend1", "trend2"]
  },
  "businessRecommendations": [
    {
      "category": "Service/Product/Marketing",
      "recommendation": "Specific recommendation",
      "priority": "high/medium/low",
      "expectedROI": "Expected return on investment"
    }
  ],
  "summary": "Overall trend analysis summary"
}`;
  }

  // Call external AI API
  async callAIAPI(prompt) {
    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3-8b-8192',
        messages: [
          {
            role: 'system',
            content: 'You are a fashion and beauty trend analyst specializing in Gen-Z preferences and salon industry trends.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error('AI API call failed');
    }

    const data = await response.json();
    const { content } = data.choices[0].message;
    
    try {
      return JSON.parse(content);
    } catch (error) {
      return this.parseAIResponse(content);
    }
  }

  // Parse AI response if not valid JSON
  parseAIResponse(content) {
    const lines = content.split('\n');
    const emergingTrends = [];
    const decliningTrends = [];
    const seasonalPredictions = {};
    const businessRecommendations = [];
    let summary = '';

    let currentSection = '';
    for (const line of lines) {
      if (line.includes('emerging trends:')) currentSection = 'emerging';
      else if (line.includes('declining trends:')) currentSection = 'declining';
      else if (line.includes('seasonal predictions:')) currentSection = 'seasonal';
      else if (line.includes('business recommendations:')) currentSection = 'business';
      else if (line.includes('summary:')) currentSection = 'summary';
      else if (line.includes('trend:') && currentSection === 'emerging') {
        emergingTrends.push({
          name: line.split('trend:')[1]?.trim() || 'Emerging Trend',
          description: 'AI-predicted trend based on current data',
          confidence: 0.8,
          timeframe: '3-6 months',
          targetAudience: 'Gen-Z',
          influencers: ['Celebrity A', 'Celebrity B'],
          socialMediaHype: 'high',
          salonImpact: 'medium',
          predictionReasoning: 'Based on current social media patterns'
        });
      } else if (line.includes('trend:') && currentSection === 'declining') {
        decliningTrends.push({
          name: line.split('trend:')[1]?.trim() || 'Declining Trend',
          description: 'Trend that is losing popularity',
          declineRate: 'medium',
          replacementTrend: 'New emerging trend'
        });
      } else if (currentSection === 'summary' && line.trim()) {
        summary = line.trim();
      }
    }

    return {
      emergingTrends: emergingTrends.length > 0 ? emergingTrends : this.getLocalTrendPredictions({}).emergingTrends,
      decliningTrends: decliningTrends.length > 0 ? decliningTrends : this.getLocalTrendPredictions({}).decliningTrends,
      seasonalPredictions: Object.keys(seasonalPredictions).length > 0 ? seasonalPredictions : this.getLocalTrendPredictions({}).seasonalPredictions,
      businessRecommendations: businessRecommendations.length > 0 ? businessRecommendations : this.getLocalTrendPredictions({}).businessRecommendations,
      summary: summary || 'Trend analysis shows continued growth in personalized and sustainable beauty services.'
    };
  }

  // Get local trend predictions when AI is not available
  getLocalTrendPredictions(currentData) {
    return {
      emergingTrends: [
        {
          name: 'Sustainable Hair Care',
          description: 'Eco-friendly products and practices gaining popularity',
          confidence: 0.9,
          timeframe: '3-6 months',
          targetAudience: 'Gen-Z',
          influencers: ['Emma Chamberlain', 'Addison Rae'],
          socialMediaHype: 'high',
          salonImpact: 'high',
          predictionReasoning: 'Growing environmental consciousness among Gen-Z'
        },
        {
          name: 'Micro-Hairstyles',
          description: 'Small, detailed styling techniques for social media',
          confidence: 0.85,
          timeframe: '3-6 months',
          targetAudience: 'Gen-Z',
          influencers: ['Charli D\'Amelio', 'Bella Poarch'],
          socialMediaHype: 'very high',
          salonImpact: 'medium',
          predictionReasoning: 'Perfect for TikTok and Instagram content'
        },
        {
          name: 'Color Gradients',
          description: 'Smooth color transitions and ombre effects',
          confidence: 0.8,
          timeframe: '3-6 months',
          targetAudience: 'Gen-Z',
          influencers: ['Billie Eilish', 'Doja Cat'],
          socialMediaHype: 'high',
          salonImpact: 'high',
          predictionReasoning: 'Visual appeal for social media platforms'
        }
      ],
      decliningTrends: [
        {
          name: 'Overly Complex Styles',
          description: 'High-maintenance styles losing popularity',
          declineRate: 'medium',
          replacementTrend: 'Low-maintenance natural looks'
        },
        {
          name: 'Heavy Hair Extensions',
          description: 'Moving towards natural hair acceptance',
          declineRate: 'slow',
          replacementTrend: 'Natural hair care and styling'
        }
      ],
      seasonalPredictions: {
        spring: ['Pastel Colors', 'Light Layers', 'Natural Waves'],
        summer: ['Beach Waves', 'Braids', 'Protective Styles'],
        fall: ['Rich Colors', 'Textured Cuts', 'Warm Tones'],
        winter: ['Deep Conditioning', 'Warm Highlights', 'Layered Styles']
      },
      businessRecommendations: [
        {
          category: 'Service',
          recommendation: 'Add sustainable hair care services',
          priority: 'high',
          expectedROI: '25% increase in bookings'
        },
        {
          category: 'Product',
          recommendation: 'Stock eco-friendly hair products',
          priority: 'medium',
          expectedROI: '15% increase in product sales'
        },
        {
          category: 'Marketing',
          recommendation: 'Focus on social media content creation',
          priority: 'high',
          expectedROI: '30% increase in brand awareness'
        }
      ],
      summary: 'Gen-Z is driving trends towards sustainability, social media-friendly styles, and natural beauty. Salons should adapt by offering eco-friendly services and creating Instagram-worthy looks.'
    };
  }

  // Analyze social media trends
  analyzeSocialMediaTrends() {
    return {
      trendingHashtags: ['#HairTok', '#SalonLife', '#GenZBeauty', '#SustainableHair'],
      popularPlatforms: ['TikTok', 'Instagram', 'Pinterest'],
      viralStyles: ['Wolf Cut', 'Butterfly Cut', 'Space Buns', 'Bubble Ponytail'],
      engagementMetrics: {
        tiktok: { followers: '2.5M', engagement: '8.5%' },
        instagram: { followers: '1.8M', engagement: '6.2%' },
        pinterest: { followers: '950K', engagement: '4.8%' }
      }
    };
  }

  // Analyze fashion industry data
  analyzeFashionData() {
    return {
      runwayTrends: ['Natural Textures', 'Bold Colors', 'Minimal Styling'],
      celebrityInfluences: ['Billie Eilish', 'Doja Cat', 'Olivia Rodrigo'],
      seasonalColors: {
        spring: ['Lavender', 'Mint', 'Peach'],
        summer: ['Coral', 'Turquoise', 'Sunset Orange'],
        fall: ['Burgundy', 'Forest Green', 'Mustard'],
        winter: ['Navy', 'Emerald', 'Ruby']
      },
      styleMovements: ['Y2K Revival', 'Minimalism', 'Sustainable Fashion']
    };
  }

  // Analyze salon booking patterns
  analyzeSalonBookings(bookingData) {
    const serviceTrends = {};
    const seasonalPatterns = {};
    const customerPreferences = {};

    bookingData.forEach(booking => {
      // Service trends
      const { service } = booking;
      serviceTrends[service] = (serviceTrends[service] || 0) + 1;

      // Seasonal patterns
      const month = new Date(booking.date).getMonth();
      const season = this.getSeason(month);
      seasonalPatterns[season] = (seasonalPatterns[season] || 0) + 1;

      // Customer preferences
      const customer = booking.customerId;
      if (!customerPreferences[customer]) {
        customerPreferences[customer] = [];
      }
      customerPreferences[customer].push(service);
    });

    return {
      serviceTrends,
      seasonalPatterns,
      customerPreferences,
      topServices: Object.entries(serviceTrends)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
    };
  }

  // Get season from month
  getSeason(month) {
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'fall';
    return 'winter';
  }

  // Predict trend virality
  predictVirality(trendData) {
    const factors = {
      socialMediaPresence: trendData.socialMediaHype === 'high' ? 0.3 : 0.1,
      celebrityEndorsement: trendData.influencers.length > 0 ? 0.25 : 0.05,
      seasonalRelevance: 0.2,
      easeOfReplication: 0.15,
      costEffectiveness: 0.1
    };

    const viralityScore = Object.values(factors).reduce((sum, factor) => sum + factor, 0);
    
    return {
      score: viralityScore,
      probability: Math.min(viralityScore * 100, 95),
      timeframe: viralityScore > 0.7 ? '1-2 months' : '3-6 months',
      factors
    };
  }

  // Generate trend report
  async generateTrendReport(currentData) {
    const predictions = await this.predictTrends(currentData);
    const socialMedia = this.analyzeSocialMediaTrends();
    const fashion = this.analyzeFashionData();
    const bookings = this.analyzeSalonBookings(currentData.salonBookings || []);

    // Add virality predictions
    const trendsWithVirality = predictions.emergingTrends.map(trend => ({
      ...trend,
      virality: this.predictVirality(trend)
    }));

    return {
      predictions: {
        ...predictions,
        emergingTrends: trendsWithVirality
      },
      analysis: {
        socialMedia,
        fashion,
        bookings
      },
      generatedAt: new Date().toISOString()
    };
  }

  // Get trending styles for specific platform
  getTrendingStyles(platform = 'all') {
    const platformTrends = {
      tiktok: ['Wolf Cut', 'Butterfly Cut', 'Space Buns', 'Bubble Ponytail'],
      instagram: ['Natural Waves', 'Curtain Bangs', 'Layered Shag', 'Textured Bob'],
      pinterest: ['Minimal Styles', 'Sustainable Looks', 'Vintage Revival', 'Modern Classics']
    };

    return platform === 'all' 
      ? [...new Set(Object.values(platformTrends).flat())]
      : platformTrends[platform] || [];
  }

  // Get trend confidence based on multiple factors
  calculateTrendConfidence(trendData) {
    const factors = {
      socialMediaHype: trendData.socialMediaHype === 'high' ? 0.3 : 0.1,
      celebrityInfluence: trendData.influencers.length > 2 ? 0.25 : 0.1,
      seasonalAlignment: 0.2,
      salonDemand: trendData.salonImpact === 'high' ? 0.15 : 0.05,
      marketReadiness: 0.1
    };

    return Object.values(factors).reduce((sum, factor) => sum + factor, 0);
  }
}

export default new AITrendPredictorService(); 
