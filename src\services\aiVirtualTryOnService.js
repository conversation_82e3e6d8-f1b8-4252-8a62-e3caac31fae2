// AI Virtual Try-On Service
// Simulates how different hairstyles would look on users using AI

class AIVirtualTryOnService {
  constructor() {
    this.apiKey = process.env.REACT_APP_OPENAI_API_KEY || process.env.REACT_APP_GROQ_API_KEY;
    this.apiUrl = process.env.REACT_APP_GROQ_API_URL || 'https://api.groq.com/openai/v1/chat/completions';
  }

  // Generate virtual try-on simulation
  async generateVirtualTryOn(userProfile, selectedStyle) {
    try {
      // Try Django AI API first
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/ai/virtual-tryon/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userProfile,
          selectedStyle
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (!result.fallback) {
          return result;
        }
      }

      // Fallback to local try-on if API fails
      console.log('Using fallback virtual try-on');
      return this.getLocalTryOn(userProfile, selectedStyle);
    } catch (error) {
      console.error('Virtual try-on error:', error);
      return this.getLocalTryOn(userProfile, selectedStyle);
    }
  }

  // Build AI prompt for virtual try-on
  buildTryOnPrompt(userProfile, selectedStyle) {
    const {
      faceShape,
      hairType,
      hairColor,
      skinTone,
      age,
      gender,
      preferences
    } = userProfile;

    return `Create a virtual try-on simulation for a hairstyle:

User Profile:
- Face Shape: ${faceShape}
- Hair Type: ${hairType}
- Hair Color: ${hairColor}
- Skin Tone: ${skinTone}
- Age: ${age}
- Gender: ${gender}
- Preferences: ${preferences.join(', ')}

Selected Style: ${selectedStyle.name}

Generate a detailed simulation including:
1. How the style would look on this user
2. Styling recommendations
3. Maintenance requirements
4. Suitability score
5. Alternative suggestions

Format as JSON:
{
  "simulation": {
    "description": "How the style would look",
    "suitabilityScore": 0.0-1.0,
    "confidence": 0.0-1.0,
    "stylingTips": ["Tip 1", "Tip 2"],
    "maintenance": "Low/Medium/High",
    "timeToStyle": "X minutes",
    "products": ["Product 1", "Product 2"],
    "faceShapeCompatibility": "Excellent/Good/Fair/Poor",
    "hairTypeCompatibility": "Excellent/Good/Fair/Poor"
  },
  "alternatives": [
    {
      "name": "Alternative Style",
      "reason": "Why it might work better",
      "suitabilityScore": 0.0-1.0
    }
  ],
  "recommendations": {
    "colorSuggestions": ["Color 1", "Color 2"],
    "stylingVariations": ["Variation 1", "Variation 2"],
    "accessories": ["Accessory 1", "Accessory 2"]
  }
}`;
  }

  // Call external AI API
  async callAIAPI(prompt) {
    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3-8b-8192',
        messages: [
          {
            role: 'system',
            content: 'You are a professional hairstylist and virtual try-on expert. Create realistic simulations of how hairstyles would look on different users.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        max_tokens: 800
      })
    });

    if (!response.ok) {
      throw new Error('AI API call failed');
    }

    const data = await response.json();
    const { content } = data.choices[0].message;
    
    try {
      return JSON.parse(content);
    } catch (error) {
      return this.parseAIResponse(content);
    }
  }

  // Parse AI response if not valid JSON
  parseAIResponse(content) {
    const lines = content.split('\n');
    let description = 'This style would complement your features well.';
    let suitabilityScore = 0.8;
    const confidence = 0.7;
    const stylingTips = ['Use quality products', 'Regular maintenance'];
    const maintenance = 'Medium';
    const timeToStyle = '15-20 minutes';
    const products = ['Styling gel', 'Hair spray'];
    const faceShapeCompatibility = 'Good';
    const hairTypeCompatibility = 'Good';

    for (const line of lines) {
      if (line.includes('description:')) {
        description = line.split('description:')[1]?.trim() || description;
      } else if (line.includes('suitability:')) {
        const score = line.split('suitability:')[1]?.trim();
        if (score) suitabilityScore = parseFloat(score) || 0.8;
      }
    }

    return {
      simulation: {
        description,
        suitabilityScore,
        confidence,
        stylingTips,
        maintenance,
        timeToStyle,
        products,
        faceShapeCompatibility,
        hairTypeCompatibility
      },
      alternatives: this.getLocalAlternatives({}),
      recommendations: this.getLocalRecommendations({})
    };
  }

  // Get local try-on when AI is not available
  getLocalTryOn(userProfile, selectedStyle) {
    const { faceShape, hairType } = userProfile;
    
    // Calculate compatibility scores based on user profile
    const faceShapeScore = this.calculateFaceShapeCompatibility(faceShape, selectedStyle);
    const hairTypeScore = this.calculateHairTypeCompatibility(hairType, selectedStyle);
    const overallScore = (faceShapeScore + hairTypeScore) / 2;

    return {
      simulation: {
        description: `The ${selectedStyle.name} would work well with your ${faceShape} face shape and ${hairType} hair. The style will enhance your natural features and provide a modern look.`,
        suitabilityScore: overallScore,
        confidence: 0.85,
        stylingTips: [
          'Use a heat protectant before styling',
          'Apply texturizing spray for volume',
          'Use a round brush for smooth finish'
        ],
        maintenance: this.getMaintenanceLevel(selectedStyle),
        timeToStyle: this.getStylingTime(selectedStyle),
        products: this.getRecommendedProducts(selectedStyle),
        faceShapeCompatibility: this.getCompatibilityText(faceShapeScore),
        hairTypeCompatibility: this.getCompatibilityText(hairTypeScore)
      },
      alternatives: this.getLocalAlternatives(userProfile),
      recommendations: this.getLocalRecommendations(userProfile)
    };
  }

  // Calculate face shape compatibility
  calculateFaceShapeCompatibility(faceShape, style) {
    const compatibilityMatrix = {
      Oval: { Bob: 0.9, 'Layered Cut': 0.8, 'Pixie Cut': 0.7, 'Long Layers': 0.9 },
      Round: { Bob: 0.7, 'Layered Cut': 0.9, 'Pixie Cut': 0.8, 'Long Layers': 0.8 },
      Square: { Bob: 0.8, 'Layered Cut': 0.9, 'Pixie Cut': 0.9, 'Long Layers': 0.7 },
      Heart: { Bob: 0.8, 'Layered Cut': 0.8, 'Pixie Cut': 0.9, 'Long Layers': 0.9 },
      Diamond: { Bob: 0.7, 'Layered Cut': 0.8, 'Pixie Cut': 0.8, 'Long Layers': 0.9 },
      Rectangle: { Bob: 0.8, 'Layered Cut': 0.9, 'Pixie Cut': 0.7, 'Long Layers': 0.8 }
    };

    return compatibilityMatrix[faceShape]?.[style.name] || 0.7;
  }

  // Calculate hair type compatibility
  calculateHairTypeCompatibility(hairType, style) {
    const compatibilityMatrix = {
      Straight: { Bob: 0.9, 'Layered Cut': 0.8, 'Pixie Cut': 0.9, 'Long Layers': 0.8 },
      Wavy: { Bob: 0.8, 'Layered Cut': 0.9, 'Pixie Cut': 0.8, 'Long Layers': 0.9 },
      Curly: { Bob: 0.7, 'Layered Cut': 0.9, 'Pixie Cut': 0.6, 'Long Layers': 0.9 },
      Coily: { Bob: 0.6, 'Layered Cut': 0.8, 'Pixie Cut': 0.5, 'Long Layers': 0.8 },
      Fine: { Bob: 0.8, 'Layered Cut': 0.9, 'Pixie Cut': 0.9, 'Long Layers': 0.7 },
      Thick: { Bob: 0.9, 'Layered Cut': 0.8, 'Pixie Cut': 0.7, 'Long Layers': 0.9 }
    };

    return compatibilityMatrix[hairType]?.[style.name] || 0.7;
  }

  // Get maintenance level for style
  getMaintenanceLevel(style) {
    const maintenanceLevels = {
      Bob: 'Medium',
      'Layered Cut': 'Low',
      'Pixie Cut': 'High',
      'Long Layers': 'Medium'
    };

    return maintenanceLevels[style.name] || 'Medium';
  }

  // Get styling time for style
  getStylingTime(style) {
    const stylingTimes = {
      Bob: '10-15 minutes',
      'Layered Cut': '15-20 minutes',
      'Pixie Cut': '5-10 minutes',
      'Long Layers': '20-25 minutes'
    };

    return stylingTimes[style.name] || '15-20 minutes';
  }

  // Get recommended products for style
  getRecommendedProducts(style) {
    const products = {
      Bob: ['Heat protectant', 'Round brush', 'Hair spray'],
      'Layered Cut': ['Texturizing spray', 'Sea salt spray', 'Light hold gel'],
      'Pixie Cut': ['Pomade', 'Hair wax', 'Strong hold gel'],
      'Long Layers': ['Volumizing mousse', 'Hair oil', 'Heat protectant']
    };

    return products[style.name] || ['Styling gel', 'Hair spray'];
  }

  // Get compatibility text
  getCompatibilityText(score) {
    if (score >= 0.9) return 'Excellent';
    if (score >= 0.8) return 'Good';
    if (score >= 0.7) return 'Fair';
    return 'Poor';
  }

  // Get local alternatives
  getLocalAlternatives(userProfile) {
    const { faceShape, hairType } = userProfile;
    
    const alternatives = [
      {
        name: 'Textured Bob',
        reason: 'Great for your face shape and hair type',
        suitabilityScore: 0.85
      },
      {
        name: 'Layered Shag',
        reason: 'Adds volume and movement to your hair',
        suitabilityScore: 0.8
      },
      {
        name: 'Curtain Bangs',
        reason: 'Frames your face beautifully',
        suitabilityScore: 0.75
      }
    ];

    return alternatives;
  }

  // Get local recommendations
  getLocalRecommendations(userProfile) {
    const { hairColor, skinTone } = userProfile;
    
    return {
      colorSuggestions: [
        'Warm highlights',
        'Balayage',
        'Natural tones'
      ],
      stylingVariations: [
        'Textured version',
        'Smooth version',
        'Messy version'
      ],
      accessories: [
        'Hair clips',
        'Headbands',
        'Scarves'
      ]
    };
  }

  // Get available styles for try-on
  getAvailableStyles() {
    return [
      {
        name: 'Bob',
        description: 'Classic bob with modern edge',
        difficulty: 'Medium',
        maintenance: 'Medium',
        imageUrl: '/images/styles/bob.jpg'
      },
      {
        name: 'Layered Cut',
        description: 'Textured layers for movement',
        difficulty: 'Low',
        maintenance: 'Low',
        imageUrl: '/images/styles/layered.jpg'
      },
      {
        name: 'Pixie Cut',
        description: 'Short and sassy',
        difficulty: 'High',
        maintenance: 'High',
        imageUrl: '/images/styles/pixie.jpg'
      },
      {
        name: 'Long Layers',
        description: 'Elegant layered look',
        difficulty: 'Medium',
        maintenance: 'Medium',
        imageUrl: '/images/styles/long-layers.jpg'
      },
      {
        name: 'Wolf Cut',
        description: 'Trendy shaggy layers',
        difficulty: 'Medium',
        maintenance: 'Medium',
        imageUrl: '/images/styles/wolf-cut.jpg'
      },
      {
        name: 'Butterfly Cut',
        description: 'Face-framing layers',
        difficulty: 'Low',
        maintenance: 'Low',
        imageUrl: '/images/styles/butterfly-cut.jpg'
      }
    ];
  }

  // Analyze user's current style
  analyzeCurrentStyle(currentStyle, userProfile) {
    const analysis = {
      strengths: [],
      areasForImprovement: [],
      recommendations: [],
      compatibilityScore: 0.7
    };

    // Analyze based on user profile
    const { faceShape, hairType, age } = userProfile;

    if (currentStyle.includes('bob') && faceShape === 'round') {
      analysis.strengths.push('Bob complements your round face shape');
      analysis.compatibilityScore += 0.1;
    }

    if (currentStyle.includes('layers') && hairType === 'wavy') {
      analysis.strengths.push('Layers work well with your wavy hair');
      analysis.compatibilityScore += 0.1;
    }

    if (age < 25 && currentStyle.includes('pixie')) {
      analysis.strengths.push('Pixie cut suits your age group');
      analysis.compatibilityScore += 0.1;
    }

    // Add recommendations
    analysis.recommendations = [
      'Consider adding texture for more dimension',
      'Try face-framing layers',
      'Experiment with different partings'
    ];

    return analysis;
  }

  // Generate style comparison
  async compareStyles(userProfile, style1, style2) {
    const tryOn1 = await this.generateVirtualTryOn(userProfile, style1);
    const tryOn2 = await this.generateVirtualTryOn(userProfile, style2);

    return {
      style1: {
        name: style1.name,
        simulation: tryOn1.simulation,
        alternatives: tryOn1.alternatives
      },
      style2: {
        name: style2.name,
        simulation: tryOn2.simulation,
        alternatives: tryOn2.alternatives
      },
      comparison: {
        winner: tryOn1.simulation.suitabilityScore > tryOn2.simulation.suitabilityScore ? style1.name : style2.name,
        scoreDifference: Math.abs(tryOn1.simulation.suitabilityScore - tryOn2.simulation.suitabilityScore),
        recommendation: this.getComparisonRecommendation(tryOn1, tryOn2)
      }
    };
  }

  // Get comparison recommendation
  getComparisonRecommendation(tryOn1, tryOn2) {
    const score1 = tryOn1.simulation.suitabilityScore;
    const score2 = tryOn2.simulation.suitabilityScore;

    if (score1 > score2 + 0.1) {
      return `${tryOn1.simulation.description.split(' ')[0]} style is significantly better for you.`;
    } if (score2 > score1 + 0.1) {
      return `${tryOn2.simulation.description.split(' ')[0]} style is significantly better for you.`;
    } 
    return 'Both styles work well for you. Choose based on your lifestyle and preferences.';
  }
}

export default new AIVirtualTryOnService(); 
