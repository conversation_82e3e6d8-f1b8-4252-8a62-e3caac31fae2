// AI Voice Integration Service
// Provides voice commands and speech recognition for salon interactions

import { API_BASE_URL, getApiUrl, API_ENDPOINTS } from '../utils/apiConfig';

class AIVoiceService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.aiEndpoint = getApiUrl('api/ai');
    // Keep fallback for direct API access
    this.apiKey = process.env.REACT_APP_GROQ_API_KEY || process.env.REACT_APP_MISTRAL_API_KEY || process.env.REACT_APP_OPENAI_API_KEY;
    this.apiUrl = process.env.REACT_APP_GROQ_API_URL || process.env.REACT_APP_MISTRAL_API_URL || process.env.REACT_APP_OPENAI_API_URL || 'https://api.groq.com/openai/v1/chat/completions';
    this.recognition = null;
    this.synthesis = null;
    this.isListening = false;
    this.onResultCallback = null;

    this.initializeSpeechRecognition();
    this.initializeSpeechSynthesis();
  }

  // Initialize speech recognition
  initializeSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      this.recognition.continuous = false;
      this.recognition.interimResults = false;
      this.recognition.lang = 'en-US';
      
      this.recognition.onresult = (event) => {
        const { transcript } = event.results[0][0];
        this.processVoiceCommand(transcript);
      };
      
      this.recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        this.speak('Sorry, I didn\'t catch that. Please try again.');
      };
      
      this.recognition.onend = () => {
        this.isListening = false;
      };
    }
  }

  // Initialize speech synthesis
  initializeSpeechSynthesis() {
    if ('speechSynthesis' in window) {
      this.synthesis = window.speechSynthesis;
    }
  }

  // Start listening for voice commands
  startListening(callback) {
    if (!this.recognition) {
      this.speak('Speech recognition is not supported in your browser.');
      return false;
    }

    this.onResultCallback = callback;
    this.isListening = true;
    this.recognition.start();
    this.speak('Listening for your command...');
    return true;
  }

  // Stop listening
  stopListening() {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
      this.isListening = false;
    }
  }

  // Process voice command and determine intent
  async processVoiceCommand(transcript) {
    try {
      const intent = await this.analyzeVoiceIntent(transcript);
      this.executeVoiceIntent(intent, transcript);
    } catch (error) {
      console.error('Error processing voice command:', error);
      this.speak('Sorry, I couldn\'t understand that command.');
    }
  }

  // Analyze voice intent using AI
  async analyzeVoiceIntent(transcript) {
    try {
      // Try backend AI service first
      const response = await fetch(`${this.aiEndpoint}/voice-assistant/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          voiceInput: transcript,
          userContext: {
            timestamp: new Date().toISOString(),
            platform: 'web'
          }
        }),
      });

      if (response.ok) {
        const data = await response.json();

        // If backend returns fallback, use local intent
        if (data.fallback) {
          return this.getLocalIntent(transcript);
        }

        return data.response || data;
      }

      // Fallback to direct API if backend fails
      return await this.callDirectAI(transcript);

    } catch (error) {
      console.warn('Backend AI failed, trying direct API:', error);
      return await this.callDirectAI(transcript);
    }
  }

  // Direct AI call as fallback
  async callDirectAI(transcript) {
    const prompt = `Analyze this voice command for a salon app and extract the intent:

Voice Command: "${transcript}"

Extract the following information:
1. Intent (book_appointment, find_salon, style_recommendation, check_booking, etc.)
2. Service type (haircut, color, styling, etc.)
3. Date/time preferences
4. Location preferences
5. Budget preferences
6. Urgency level

Format as JSON:
{
  "intent": "intent_name",
  "service": "service_type",
  "dateTime": "date_time_preference",
  "location": "location_preference",
  "budget": "budget_range",
  "urgency": "high/medium/low",
  "confidence": 0.0-1.0
}`;

    try {
      if (this.apiKey) {
        return await this.callAIAPI(prompt);
      }
      return this.getLocalIntent(transcript);
    } catch (error) {
      return this.getLocalIntent(transcript);
    }
  }

  // Call AI API for intent analysis
  async callAIAPI(prompt) {
    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3-8b-8192',
        messages: [
          {
            role: 'system',
            content: 'You are a voice assistant for a salon app. Analyze voice commands and extract booking intents.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 300
      })
    });

    if (!response.ok) {
      throw new Error('AI API call failed');
    }

    const data = await response.json();
    const { content } = data.choices[0].message;
    
    try {
      return JSON.parse(content);
    } catch (error) {
      return this.parseAIResponse(content);
    }
  }

  // Parse AI response if not valid JSON
  parseAIResponse(content) {
    const lines = content.split('\n');
    let intent = 'unknown';
    let service = 'general';
    let dateTime = '';
    let location = '';
    let budget = 'medium';
    let urgency = 'medium';
    const confidence = 0.7;

    for (const line of lines) {
      if (line.includes('intent:')) {
        intent = line.split('intent:')[1]?.trim() || intent;
      } else if (line.includes('service:')) {
        service = line.split('service:')[1]?.trim() || service;
      } else if (line.includes('dateTime:')) {
        dateTime = line.split('dateTime:')[1]?.trim() || dateTime;
      } else if (line.includes('location:')) {
        location = line.split('location:')[1]?.trim() || location;
      } else if (line.includes('budget:')) {
        budget = line.split('budget:')[1]?.trim() || budget;
      } else if (line.includes('urgency:')) {
        urgency = line.split('urgency:')[1]?.trim() || urgency;
      }
    }

    return { intent, service, dateTime, location, budget, urgency, confidence };
  }

  // Get local intent analysis when AI is not available
  getLocalIntent(transcript) {
    const lowerTranscript = transcript.toLowerCase();
    
    // Intent detection patterns
    const patterns = {
      book_appointment: ['book', 'appointment', 'schedule', 'reserve'],
      find_salon: ['find', 'salon', 'nearby', 'location'],
      style_recommendation: ['style', 'recommend', 'suggestion', 'look'],
      check_booking: ['check', 'booking', 'appointment', 'status'],
      cancel_booking: ['cancel', 'cancel appointment'],
      salon_info: ['info', 'information', 'details', 'about']
    };

    // Service detection patterns
    const services = {
      haircut: ['haircut', 'cut', 'trim'],
      color: ['color', 'dye', 'highlight'],
      styling: ['style', 'styling', 'blow dry'],
      treatment: ['treatment', 'conditioning', 'therapy']
    };

    // Find intent
    let detectedIntent = 'unknown';
    for (const [intent, keywords] of Object.entries(patterns)) {
      if (keywords.some(keyword => lowerTranscript.includes(keyword))) {
        detectedIntent = intent;
        break;
      }
    }

    // Find service
    let detectedService = 'general';
    for (const [service, keywords] of Object.entries(services)) {
      if (keywords.some(keyword => lowerTranscript.includes(keyword))) {
        detectedService = service;
        break;
      }
    }

    // Extract date/time
    const dateTimePatterns = [
      /(today|tomorrow|next week|this week)/i,
      /(\d{1,2}:\d{2}\s*(am|pm))/i,
      /(\d{1,2}\s*(am|pm))/i
    ];

    let detectedDateTime = '';
    for (const pattern of dateTimePatterns) {
      const match = lowerTranscript.match(pattern);
      if (match) {
        detectedDateTime = match[0];
        break;
      }
    }

    return {
      intent: detectedIntent,
      service: detectedService,
      dateTime: detectedDateTime,
      location: '',
      budget: 'medium',
      urgency: 'medium',
      confidence: 0.8
    };
  }

  // Execute voice intent
  executeVoiceIntent(intent, originalTranscript) {
    const response = this.generateVoiceResponse(intent, originalTranscript);
    this.speak(response.message);
    
    if (this.onResultCallback) {
      this.onResultCallback({
        intent: intent.intent,
        service: intent.service,
        dateTime: intent.dateTime,
        location: intent.location,
        budget: intent.budget,
        urgency: intent.urgency,
        confidence: intent.confidence,
        originalTranscript,
        response: response.message,
        action: response.action
      });
    }
  }

  // Generate voice response based on intent
  generateVoiceResponse(intent, originalTranscript) {
    const responses = {
      book_appointment: {
        message: `I'll help you book a ${intent.service} appointment. Let me find available slots for you.`,
        action: 'navigate_to_booking'
      },
      find_salon: {
        message: 'I\'ll help you find salons near you. Let me search for the best options.',
        action: 'navigate_to_salon_search'
      },
      style_recommendation: {
        message: 'I\'ll get you some style recommendations. Let me show you trending looks.',
        action: 'navigate_to_style_advisor'
      },
      check_booking: {
        message: 'I\'ll check your booking status. Let me look that up for you.',
        action: 'navigate_to_bookings'
      },
      cancel_booking: {
        message: 'I\'ll help you cancel your appointment. Let me find your booking details.',
        action: 'navigate_to_bookings'
      },
      salon_info: {
        message: 'I\'ll get you information about our salons. Let me show you the details.',
        action: 'navigate_to_salon_info'
      },
      unknown: {
        message: `I heard you say "${originalTranscript}". Could you please repeat that or try a different command?`,
        action: 'none'
      }
    };

    return responses[intent.intent] || responses.unknown;
  }

  // Speak text using speech synthesis
  speak(text) {
    if (!this.synthesis) {
      console.log('Speech synthesis not supported');
      return;
    }

    // Stop any current speech
    this.synthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1.0;
    utterance.volume = 0.8;

    // Try to use a female voice
    const voices = this.synthesis.getVoices();
    const femaleVoice = voices.find(voice => voice.name.includes('female') || voice.name.includes('Female'));
    if (femaleVoice) {
      utterance.voice = femaleVoice;
    }

    this.synthesis.speak(utterance);
  }

  // Get available voice commands
  getAvailableCommands() {
    return [
      {
        command: 'Book appointment',
        description: 'Book a salon appointment',
        examples: ['Book a haircut', 'Schedule appointment', 'Book color treatment']
      },
      {
        command: 'Find salon',
        description: 'Find nearby salons',
        examples: ['Find salons near me', 'Show nearby salons', 'Find salon locations']
      },
      {
        command: 'Style recommendation',
        description: 'Get style recommendations',
        examples: ['Recommend a style', 'Show me styles', 'What style should I get?']
      },
      {
        command: 'Check booking',
        description: 'Check appointment status',
        examples: ['Check my booking', 'Show my appointments', 'When is my appointment?']
      },
      {
        command: 'Cancel booking',
        description: 'Cancel an appointment',
        examples: ['Cancel my appointment', 'Cancel booking', 'Reschedule appointment']
      },
      {
        command: 'Salon info',
        description: 'Get salon information',
        examples: ['Tell me about salons', 'Salon details', 'What services do you offer?']
      }
    ];
  }

  // Check if voice features are supported
  isVoiceSupported() {
    return !!(this.recognition && this.synthesis);
  }

  // Get voice status
  getVoiceStatus() {
    return {
      recognition: !!this.recognition,
      synthesis: !!this.synthesis,
      isListening: this.isListening,
      supported: this.isVoiceSupported()
    };
  }
}

export default new AIVoiceService(); 
