// Haversine formula for straight-line distance (fallback)
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
    + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180)
    * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in kilometers
  return distance;
};

// Kenya-specific radius optimization based on city/area
export const getOptimalRadius = (city, userLocation) => {
  const cityConfigs = {
    // Major cities with heavy traffic
    'nairobi': { min: 1, max: 3, default: 2, traffic_factor: 2.5 },
    'mombasa': { min: 2, max: 5, default: 3, traffic_factor: 2.0 },
    'kisumu': { min: 3, max: 7, default: 5, traffic_factor: 1.8 },
    'nakuru': { min: 4, max: 8, default: 6, traffic_factor: 1.6 },
    'eldoret': { min: 4, max: 8, default: 6, traffic_factor: 1.5 },
    'thika': { min: 3, max: 6, default: 4, traffic_factor: 1.7 },
    'machakos': { min: 4, max: 8, default: 6, traffic_factor: 1.4 },
    // Default for smaller towns/rural areas
    'default': { min: 5, max: 15, default: 10, traffic_factor: 1.2 }
  };

  const cityKey = city ? city.toLowerCase() : 'default';
  return cityConfigs[cityKey] || cityConfigs.default;
};

// Enhanced distance calculation with road distance estimation
export const calculateEnhancedDistance = async (lat1, lon1, lat2, lon2, city = null) => {
  const straightLineDistance = calculateDistance(lat1, lon1, lat2, lon2);

  // Get Kenya-specific traffic factor
  const cityConfig = getOptimalRadius(city);
  const trafficFactor = cityConfig.traffic_factor;

  // Estimate road distance using traffic factor
  const estimatedRoadDistance = straightLineDistance * trafficFactor;

  // For very short distances, road distance is closer to straight line
  const adjustedDistance = straightLineDistance < 1
    ? straightLineDistance * 1.2
    : estimatedRoadDistance;

  return {
    straight_line_km: Math.round(straightLineDistance * 100) / 100,
    estimated_road_km: Math.round(adjustedDistance * 100) / 100,
    estimated_time_minutes: Math.round(adjustedDistance * 3), // ~3 min per km in traffic
    accuracy: 'estimated',
    city_factor: trafficFactor
  };
};

// Free road distance API integration (OpenRouteService)
export const getRoadDistance = async (lat1, lon1, lat2, lon2) => {
  try {
    // Note: This requires REACT_APP_OPENROUTE_API_KEY in .env
    const apiKey = process.env.REACT_APP_OPENROUTE_API_KEY;

    if (!apiKey) {
      console.warn('OpenRouteService API key missing, using estimated distance');
      return null;
    }

    const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${apiKey}&start=${lon1},${lat1}&end=${lon2},${lat2}`;

    const response = await fetch(url, { timeout: 5000 });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();

    if (data.features && data.features[0]) {
      const route = data.features[0].properties.segments[0];
      return {
        road_distance_km: Math.round(route.distance / 1000 * 100) / 100,
        travel_time_minutes: Math.round(route.duration / 60),
        accuracy: 'road_network',
        source: 'openrouteservice'
      };
    }

    return null;
  } catch (error) {
    console.warn('Road distance API failed:', error.message);
    return null;
  }
};

// Main distance calculation function with fallbacks
export const getAccurateDistance = async (lat1, lon1, lat2, lon2, city = null) => {
  // Try road distance API first
  const roadDistance = await getRoadDistance(lat1, lon1, lat2, lon2);

  if (roadDistance) {
    return {
      distance_km: roadDistance.road_distance_km,
      travel_time_minutes: roadDistance.travel_time_minutes,
      accuracy: roadDistance.accuracy,
      source: roadDistance.source
    };
  }

  // Fallback to enhanced estimation
  const enhancedDistance = await calculateEnhancedDistance(lat1, lon1, lat2, lon2, city);

  return {
    distance_km: enhancedDistance.estimated_road_km,
    travel_time_minutes: enhancedDistance.estimated_time_minutes,
    accuracy: enhancedDistance.accuracy,
    source: 'kenya_traffic_estimation'
  };
};
