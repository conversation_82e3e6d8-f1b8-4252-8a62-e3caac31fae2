// Enhanced modal logic for city/county input
let locationModalCallback = null;

export function showLocationModal(onSubmit) {
  if (typeof document !== 'undefined') {
    const modal = document.getElementById('location-modal');
    if (modal) {
      modal.style.display = 'block';
      // Attach close handler if not already attached
      const closeBtn = document.getElementById('close-location-modal');
      if (closeBtn && !closeBtn.locationModalHandlerAttached) {
        closeBtn.onclick = closeLocationModal;
        closeBtn.locationModalHandlerAttached = true;
      }
      // Attach submit handler for city/county input
      const form = document.getElementById('location-modal-form');
      if (form && !form.locationModalHandlerAttached) {
        form.onsubmit = (e) => {
          e.preventDefault();
          const input = document.getElementById('location-modal-input');
          const value = input ? input.value.trim() : '';
          if (value) {
            closeLocationModal();
            if (typeof onSubmit === 'function') {
              onSubmit(value);
            } else if (typeof locationModalCallback === 'function') {
              locationModalCallback(value);
            }
          }
        };
        form.locationModalHandlerAttached = true;
      }
      // Store callback for later use
      if (typeof onSubmit === 'function') {
        locationModalCallback = onSubmit;
      }
    }
  }
}

function closeLocationModal() {
  if (typeof document !== 'undefined') {
    const modal = document.getElementById('location-modal');
    if (modal) modal.style.display = 'none';
  }
}

export function hideLocationModalIfVisible() {
  if (typeof document !== 'undefined') {
    const modal = document.getElementById('location-modal');
    if (modal && modal.style.display === 'block') {
      modal.style.display = 'none';
    }
  }
}

// Multiple IP geolocation services for better reliability
const ipLocationServices = [
  {
    name: 'geojs',
    url: 'https://get.geojs.io/v1/ip/geo.json',
    parser: (data) => ({
      latitude: parseFloat(data.latitude),
      longitude: parseFloat(data.longitude),
      city: data.city,
      region: data.region,
      country: data.country
    }),
    free: true
  },
  {
    name: 'ipapi-free',
    url: 'https://freeipapi.com/api/json',
    parser: (data) => ({
      latitude: parseFloat(data.latitude),
      longitude: parseFloat(data.longitude),
      city: data.cityName,
      region: data.regionName,
      country: data.countryCode
    }),
    free: true
  },
  {
    name: 'ip-api',
    url: 'https://ipapi.co/json/',
    parser: (data) => ({
      latitude: data.latitude,
      longitude: data.longitude,
      city: data.city,
      region: data.region_code,
      country: data.country_code
    }),
    free: true
  },
  {
    name: 'ipinfo',
    url: 'https://ipinfo.io/json',
    parser: (data) => {
      if (data.loc) {
        const [latitude, longitude] = data.loc.split(',').map(Number);
        return {
          latitude,
          longitude,
          city: data.city,
          region: data.region,
          country: data.country
        };
      }
      return null;
    },
    requiresKey: true,
    keyParam: 'token'
  }
];

// Enhanced location detection with multiple fallbacks - OPTIMIZED FOR PRODUCTION
export const getUserLocation = async (onManualLocation) => {
  console.log('🌍 Starting enhanced location detection...');

  // Try services with timeout and race condition for faster results
  const locationPromises = ipLocationServices.map(async (service) => {
    try {
      let url = service.url;

      // Add API key if required
      if (service.requiresKey) {
        const apiKey = process.env.REACT_APP_IPINFO_API_KEY;
        if (!apiKey) {
          console.warn(`⚠️ ${service.name}: API key missing`);
          return null;
        }
        url += `?${service.keyParam}=${apiKey}`;
      }

      console.log(`🔍 Trying ${service.name}...`);

      // Use AbortController for faster timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // Reduced from 5000ms

      const response = await fetch(url, {
        signal: controller.signal,
        cache: 'no-cache' // Ensure fresh data in production
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      const location = service.parser(data);

      if (location && location.latitude && location.longitude) {
        console.log(`✅ ${service.name} success:`, location);
        return { ...location, source: service.name };
      }

      console.warn(`⚠️ ${service.name}: Invalid location data`);
      return null;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.warn(`⏰ ${service.name} timeout`);
      } else {
        console.warn(`❌ ${service.name} failed:`, error.message);
      }
      return null;
    }
  });

  try {
    // Race for first successful result - faster than Promise.allSettled
    const location = await Promise.race(
      locationPromises.filter(promise => promise !== null)
    );

    if (location) {
      console.log(`🎯 Location detected via ${location.source}:`, location);
      hideLocationModalIfVisible();
      return location;
    }

    // If race fails, wait a bit more for any remaining promises
    const results = await Promise.allSettled(locationPromises);
    const successfulResult = results.find(result =>
      result.status === 'fulfilled' && result.value
    );

    if (successfulResult) {
      const location = successfulResult.value;
      console.log(`🎯 Location detected via ${location.source}:`, location);
      hideLocationModalIfVisible();
      return location;
    }

    // All services failed
    console.error('❌ All IP location services failed');
    showLocationModal(onManualLocation);
    return null;

  } catch (error) {
    console.error('❌ Error in location detection:', error);
    showLocationModal(onManualLocation);
    return null;
  }
};

// GPS-FIRST Enhanced real-time location detection (FREE & ACCURATE)
export const getCurrentLocation = (onManualLocation) => new Promise((resolve, reject) => {
  console.log('🎯 Starting GPS-FIRST location detection...');

  if (!navigator.geolocation) {
    console.warn('❌ GPS not supported, falling back to IP geolocation');
    getUserLocation(onManualLocation).then(resolve).catch(reject);
    return;
  }

  if ('geolocation' in navigator) {
    console.log('📱 Requesting GPS location (FREE & ACCURATE)...');

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          source: 'gps',
          timestamp: new Date().toISOString()
        };

        console.log('✅ GPS location obtained:', location);
        console.log(`🎯 Accuracy: ${position.coords.accuracy} meters`);
        hideLocationModalIfVisible();

        // Cache GPS location for better performance
        localStorage.setItem('lastGPSLocation', JSON.stringify({
          ...location,
          timestamp: Date.now()
        }));

        resolve(location);
      },
      (error) => {
        console.error('❌ GPS failed:', error.message);
        console.log('🔄 Falling back to IP geolocation...');

        // Fallback to IP geolocation if GPS fails
        getUserLocation(onManualLocation)
          .then(ipLocation => {
            if (ipLocation) {
              console.log('✅ IP fallback successful:', ipLocation);
              resolve({
                ...ipLocation,
                source: 'ip_fallback',
                gps_error: error.message
              });
            } else {
              showLocationModal(onManualLocation);
              reject(error);
            }
          })
          .catch(ipError => {
            console.error('❌ Both GPS and IP failed');
            showLocationModal(onManualLocation);
            reject(new Error(`GPS: ${error.message}, IP: ${ipError.message}`));
          });
      },
      {
        enableHighAccuracy: true,  // Use GPS for best accuracy
        timeout: 15000,           // Increased timeout for GPS
        maximumAge: 300000,       // Cache for 5 minutes
      },
    );
  } else {
    console.warn('❌ Geolocation not available, using IP fallback');
    getUserLocation(onManualLocation).then(resolve).catch(reject);
  }
});

// Get cached GPS location if available and recent
export const getCachedGPSLocation = () => {
  try {
    const cached = localStorage.getItem('lastGPSLocation');
    if (cached) {
      const location = JSON.parse(cached);
      const age = Date.now() - location.timestamp;

      // Use cached location if less than 5 minutes old
      if (age < 300000) {
        console.log('📱 Using cached GPS location (age: ' + Math.round(age/1000) + 's)');
        return location;
      } else {
        console.log('⏰ Cached GPS location too old, requesting fresh location');
        localStorage.removeItem('lastGPSLocation');
      }
    }
  } catch (error) {
    console.warn('❌ Error reading cached GPS location:', error);
  }
  return null;
};

// Enhanced location detection with GPS priority and caching
export const getLocationWithGPSPriority = async (onManualLocation) => {
  console.log('🎯 ENHANCED GPS-FIRST LOCATION DETECTION');

  // Check for recent cached GPS location first
  const cached = getCachedGPSLocation();
  if (cached) {
    return cached;
  }

  // Try GPS first (most accurate)
  try {
    const gpsLocation = await getCurrentLocation(onManualLocation);
    console.log('✅ Fresh GPS location obtained');
    return gpsLocation;
  } catch (gpsError) {
    console.log('🔄 GPS failed, trying IP geolocation...');

    // Fallback to IP geolocation
    try {
      const ipLocation = await getUserLocation(onManualLocation);
      if (ipLocation) {
        console.log('✅ IP geolocation successful');
        return {
          ...ipLocation,
          source: 'ip_fallback',
          gps_error: gpsError.message
        };
      }
    } catch (ipError) {
      console.error('❌ Both GPS and IP failed');
    }

    // Final fallback: manual location
    if (onManualLocation) {
      console.log('📍 Requesting manual location input');
      onManualLocation();
    }

    throw new Error('All location detection methods failed');
  }
};

// Update user location on backend (requires authFetch)
export const updateUserLocation = async (authFetch, location) => {
  try {
    console.log('updateUserLocation called with location:', location);
    const response = await authFetch('/api/user/location/', {
      method: 'POST',
      body: JSON.stringify({
        latitude: location.latitude,
        longitude: location.longitude,
        county: location.county,
        town: location.town,
      }),
    });

    console.log('updateUserLocation response status:', response.status);
    if (!response.ok) {
      const errorText = await response.text();
      console.log('updateUserLocation error response:', errorText);
      throw new Error('Failed to update location');
    }

    const result = await response.json();
    console.log('updateUserLocation success:', result);
    return result;
  } catch (error) {
    console.error('Error updating user location:', error);
    throw error;
  }
};

// Get nearby salons based on user location (requires authFetch)
export const getNearbySalons = async (authFetch, radiusKm = 5) => {
  try {
    const response = await authFetch('/api/recommendations/nearby-enhanced/', {
      method: 'POST',
      body: JSON.stringify({
        radius_km: radiusKm,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch nearby salons');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching nearby salons:', error);
    throw error;
  }
};

// Get regional trending data
export const getRegionalTrending = async (county, town = null, period = 'weekly') => {
  try {
    const params = new URLSearchParams({
      county,
      period,
    });
    if (town) {
      params.append('town', town);
    }

    const response = await fetch(`/api/recommendations/regional-trending/?${params}`);

    if (!response.ok) {
      throw new Error('Failed to fetch regional trending');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching regional trending:', error);
    throw error;
  }
};
