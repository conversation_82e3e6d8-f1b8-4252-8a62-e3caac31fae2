// AI Salon Matcher Service\n// Matches users with the perfect salon based on preferences, budget, and location\n\nclass SalonMatcherService {\n  constructor() {\n    this.apiKey = process.env.REACT_APP_OPENAI_API_KEY || process.env.REACT_APP_GROQ_API_KEY;\n    this.baseURL = process.env.REACT_APP_AI_BASE_URL || 'https://api.openai.com/v1';\n    this.model = process.env.REACT_APP_AI_MODEL || 'gpt-3.5-turbo';\n  }\n\n  // Match user with perfect salons\n  async findPerfectSalons(userPreferences, availableSalons) {\n    try {\n      // Try Django AI API first\n      const response = await fetch('/ai/salon-matching/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          userPreferences,\n          availableSalons\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (!result.fallback) {\n          return result.matches || result;\n        }\n      }\n\n      // Fallback to local matches if API fails\n      console.log('Using fallback salon matching');\n      return this.getLocalSalonMatches(userPreferences, availableSalons);\n    } catch (error) {\n      console.warn('AI salon matcher failed, using local matching:', error);\n      return this.getLocalSalonMatches(userPreferences, availableSalons);\n    }\n  }\n\n  // Build prompt for salon matching\n  buildSalonMatchPrompt(userPreferences, availableSalons) {\n    const salonData = availableSalons.map(salon => ({\n      name: salon.name,\n      specialties: salon.specialties || [],\n      price_range: salon.price_range || 'moderate',\n      rating: salon.rating || 4.0,\n      location: salon.location || '',\n      services: salon.services || []\n    }));\n\n    return `Match a user with the best salons from the available options.\n\n    User Preferences:\n    - Style Preference: ${userPreferences.stylePreference || 'trendy'}\n    - Budget Range: ${userPreferences.budget || 'moderate'}\n    - Location: ${userPreferences.location || 'any'}\n    - Hair Type: ${userPreferences.hairType || 'any'}\n    - Preferred Services: ${userPreferences.preferredServices || []}\n    - Special Requirements: ${userPreferences.specialRequirements || 'none'}\n\n    Available Salons:\n    ${JSON.stringify(salonData, null, 2)}\n\n    Requirements:\n    - Recommend top 3 salons with match scores (1-10)\n    - Explain why each salon is a good match\n    - Consider budget, location, and style compatibility\n    - Include any special considerations\n    - Use Gen-Z friendly language\n\n    Format as:\n    1. Salon Name (Score: X/10)\n    - Why it matches\n    - Special considerations\n    - Best for: [style types]`;\n  }\n\n  // Parse AI response into structured matches\n  parseSalonMatches(aiResponse, availableSalons) {\n    try {\n      const matches = [];\n      const sections = aiResponse.split(/\d+\./).filter(section => section.trim());\n      \n      sections.forEach(section => {\n        const lines = section.trim().split('\n').filter(line => line.trim());\n        if (lines.length >= 2) {\n          const salonName = lines[0].split('(')[0].trim();\n          const scoreMatch = lines[0].match(/Score:\s*(\d+)/);\n          const score = scoreMatch ? parseInt(scoreMatch[1]) : 7;\n          \n          const salon = availableSalons.find(s => \n            s.name.toLowerCase().includes(salonName.toLowerCase()) ||\n            salonName.toLowerCase().includes(s.name.toLowerCase())\n          );\n\n          if (salon) {\n            matches.push({\n              salon: salon,\n              matchScore: score,\n              reasoning: lines.slice(1).join(' ').trim(),\n              bestFor: this.extractBestFor(lines.join(' ')),\n              specialConsiderations: this.extractSpecialConsiderations(lines.join(' '))\n            });\n          }\n        }\n      });\n\n      return matches.length > 0 ? matches : this.getLocalSalonMatches({}, availableSalons);\n    } catch (error) {\n      console.warn('Failed to parse AI salon matches:', error);\n      return this.getLocalSalonMatches({}, availableSalons);\n    }\n  }\n\n  // Extract "best for" information\n  extractBestFor(text) {\n    const bestForMatch = text.match(/Best for:\s*\[([^\]]+)\]/i);\n    return bestForMatch ? bestForMatch[1].split(',').map(s => s.trim()) : ['general styling'];\n  }\n\n  // Extract special considerations\n  extractSpecialConsiderations(text) {\n    const considerations = [];\n    if (text.includes('budget')) considerations.push('Budget-friendly');\n    if (text.includes('trendy') || text.includes('modern')) considerations.push('Trendy styles');\n    if (text.includes('classic') || text.includes('traditional')) considerations.push('Classic cuts');\n    if (text.includes('color') || text.includes('dye')) considerations.push('Color services');\n    return considerations.length > 0 ? considerations : ['General services'];\n  }\n\n  // Get local salon matches as fallback\n  getLocalSalonMatches(userPreferences, availableSalons) {\n    const matches = [];\n    \n    // Simple matching logic based on preferences\n    availableSalons.forEach(salon => {\n      let score = 5; // Base score\n      let reasoning = [];\n      let bestFor = [];\n\n      // Budget matching\n      if (userPreferences.budget === 'low' && salon.price_range === 'low') {\n        score += 2;\n        reasoning.push('Great value for money');\n      } else if (userPreferences.budget === 'high' && salon.price_range === 'high') {\n        score += 2;\n        reasoning.push('Premium services available');\n      }\n\n      // Style preference matching\n      if (userPreferences.stylePreference === 'trendy' && \n          (salon.specialties.includes('modern') || salon.specialties.includes('trendy'))) {\n        score += 2;\n        reasoning.push('Specializes in trendy styles');\n        bestFor.push('Trendy cuts');\n      }\n\n      if (userPreferences.stylePreference === 'classic' && \n          (salon.specialties.includes('classic') || salon.specialties.includes('traditional'))) {\n        score += 2;\n        reasoning.push('Expert in classic styles');\n        bestFor.push('Classic cuts');\n      }\n\n      // Rating consideration\n      if (salon.rating >= 4.5) {\n        score += 1;\n        reasoning.push('Highly rated by customers');\n      }\n\n      // Location consideration\n      if (userPreferences.location && salon.location.includes(userPreferences.location)) {\n        score += 1;\n        reasoning.push('Convenient location');\n      }\n\n      // Service matching\n      if (userPreferences.preferredServices && \n          userPreferences.preferredServices.some(service => \n            salon.services.includes(service))) {\n        score += 1;\n        reasoning.push('Offers your preferred services');\n      }\n\n      matches.push({\n        salon: salon,\n        matchScore: Math.min(score, 10),\n        reasoning: reasoning.join('. '),\n        bestFor: bestFor.length > 0 ? bestFor : ['General styling'],\n        specialConsiderations: this.getSpecialConsiderations(salon, userPreferences)\n      });\n    });\n\n    // Sort by match score and return top 3\n    return matches\n      .sort((a, b) => b.matchScore - a.matchScore)\n      .slice(0, 3);\n  }\n\n  // Get special considerations for a salon\n  getSpecialConsiderations(salon, userPreferences) {\n    const considerations = [];\n\n    if (salon.price_range === 'low') {\n      considerations.push('Budget-friendly');\n    } else if (salon.price_range === 'high') {\n      considerations.push('Premium pricing');\n    }\n\n    if (salon.specialties.includes('color')) {\n      considerations.push('Color specialist');\n    }\n\n    if (salon.specialties.includes('curly')) {\n      considerations.push('Curly hair expert');\n    }\n\n    if (salon.rating >= 4.8) {\n      considerations.push('Highly rated');\n    }\n\n    return considerations;\n  }\n\n  // Analyze salon compatibility with user preferences\n  analyzeSalonCompatibility(salon, userPreferences) {\n    const analysis = {\n      overallScore: 0,\n      strengths: [],\n      weaknesses: [],\n      recommendations: []\n    };\n\n    // Budget analysis\n    if (userPreferences.budget === 'low' && salon.price_range === 'high') {\n      analysis.weaknesses.push('May be outside your budget');\n    } else if (userPreferences.budget === 'high' && salon.price_range === 'low') {\n      analysis.strengths.push('Great value for premium services');\n    }\n\n    // Style preference analysis\n    if (userPreferences.stylePreference && salon.specialties.includes(userPreferences.stylePreference)) {\n      analysis.strengths.push(`Specializes in ${userPreferences.stylePreference} styles`);\n    }\n\n    // Location analysis\n    if (userPreferences.location && !salon.location.includes(userPreferences.location)) {\n      analysis.weaknesses.push('May not be in your preferred area');\n    }\n\n    // Rating analysis\n    if (salon.rating >= 4.5) {\n      analysis.strengths.push('Highly rated by customers');\n    } else if (salon.rating < 4.0) {\n      analysis.weaknesses.push('Lower customer ratings');\n    }\n\n    // Calculate overall score\n    analysis.overallScore = Math.max(1, Math.min(10, \n      5 + analysis.strengths.length - analysis.weaknesses.length\n    ));\n\n    return analysis;\n  }\n\n  // Get salon recommendations based on style trends\n  getTrendBasedRecommendations(currentTrends, availableSalons) {\n    const trendSalons = availableSalons.filter(salon => \n      salon.specialties.some(specialty => \n        currentTrends.some(trend => \n          specialty.toLowerCase().includes(trend.toLowerCase())\n        )\n      )\n    );\n\n    return trendSalons.map(salon => ({\n      salon: salon,\n      trendMatch: currentTrends.filter(trend => \n        salon.specialties.some(specialty => \n          specialty.toLowerCase().includes(trend.toLowerCase())\n        )\n      ),\n      reasoning: `Specializes in trending styles: ${salon.specialties.join(', ')}`\n    }));\n  }\n\n  // Calculate distance-based recommendations\n  getDistanceBasedRecommendations(userLocation, availableSalons, maxDistance = 10) {\n    return availableSalons\n      .filter(salon => {\n        // Simple distance calculation (in real app, use proper geolocation)\n        const distance = this.calculateDistance(userLocation, salon.location);\n        return distance <= maxDistance;\n      })\n      .map(salon => ({\n        salon: salon,\n        distance: this.calculateDistance(userLocation, salon.location),\n        reasoning: `Conveniently located ${this.calculateDistance(userLocation, salon.location)}km away`\n      }))\n      .sort((a, b) => a.distance - b.distance);\n  }\n\n  // Simple distance calculation (placeholder)\n  calculateDistance(location1, location2) {\n    // In real implementation, use proper geolocation API\n    return Math.random() * 5 + 1; // Random distance between 1-6km\n  }\n\n  // Get personalized salon insights\n  getPersonalizedInsights(userPreferences, matchedSalons) {\n    const insights = {\n      topMatch: matchedSalons[0],\n      alternativeOptions: matchedSalons.slice(1),\n      recommendations: [],\n      tips: []\n    };\n\n    // Generate personalized recommendations\n    if (userPreferences.budget === 'low') {\n      insights.recommendations.push('Consider booking during off-peak hours for better rates');\n    }\n\n    if (userPreferences.stylePreference === 'trendy') {\n      insights.recommendations.push('Book early for trendy styles as they fill up quickly');\n    }\n\n    if (matchedSalons.some(s => s.salon.rating >= 4.8)) {\n      insights.tips.push('Highly rated salons often require advance booking');\n    }\n\n    return insights;\n  }\n}\n\n// Create singleton instance\nconst salonMatcherService = new SalonMatcherService();\n\nexport default salonMatcherService; \n\n\n
