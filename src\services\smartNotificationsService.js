// Smart Notifications Service\n// Provides intelligent, contextual notifications based on user behavior and preferences\n\nclass SmartNotificationsService {\n  constructor() {\n    this.apiKey = process.env.REACT_APP_OPENAI_API_KEY || process.env.REACT_APP_GROQ_API_KEY;\n    this.apiUrl = process.env.REACT_APP_GROQ_API_URL || 'https://api.groq.com/openai/v1/chat/completions';\n  }\n\n  // Generate personalized notification content\n  async generateSmartNotification(context) {\n    try {\n      // Try Django AI API first\n      const response = await fetch('/ai/smart-notifications/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          userContext: context,\n          salonData: context.salonData || {}\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (!result.fallback) {\n          return result;\n        }\n      }\n\n      // Fallback to local notification if API fails\n      console.log('Using fallback smart notification');\n      return this.getLocalNotification(context);\n    } catch (error) {\n      console.error('Smart notification error:', error);\n      return this.getLocalNotification(context);\n    }\n  }\n\n  // Build AI prompt for smart notifications\n  buildNotificationPrompt(context) {\n    const {\n      type,\n      userProfile,\n      salonData,\n      bookingData,\n      preferences,\n      previousInteractions\n    } = context;\n\n    return `Generate a personalized, Gen-Z style notification for a salon app user.\n\nContext:\n- Notification Type: ${type}\n- User Profile: ${JSON.stringify(userProfile)}\n- Salon Data: ${JSON.stringify(salonData)}\n- Booking Data: ${JSON.stringify(bookingData)}\n- User Preferences: ${JSON.stringify(preferences)}\n- Previous Interactions: ${JSON.stringify(previousInteractions)}\n\nRequirements:\n1. Use Gen-Z language and emojis\n2. Keep it under 100 characters for the title\n3. Make it personal and engaging\n4. Include relevant emojis\n5. Be encouraging but not pushy\n6. Match the user's style preferences\n\nFormat as JSON:\n{\n  "title": "Short engaging title",\n  "message": "Detailed message with personality",\n  "emoji": "ðŸŽ‰",\n  "priority": "high/medium/low",\n  "action": "book_now/view_details/reminder",\n  "personalized": true\n}`;\n  }\n\n  // Call external AI API\n  async callAIAPI(prompt) {\n    const response = await fetch(this.apiUrl, {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${this.apiKey}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        model: 'llama3-8b-8192',\n        messages: [\n          {\n            role: 'system',\n            content: 'You are a Gen-Z notification expert for a salon app. Create engaging, personalized notifications.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.8,\n        max_tokens: 300\n      })\n    });\n\n    if (!response.ok) {\n      throw new Error('AI API call failed');\n    }\n\n    const data = await response.json();\n    const content = data.choices[0].message.content;\n    \n    try {\n      return JSON.parse(content);\n    } catch (error) {\n      return this.parseAIResponse(content);\n    }\n  }\n\n  // Parse AI response if not valid JSON\n  parseAIResponse(content) {\n    const lines = content.split('\n');\n    let title = 'âœ¨ New Update!';\n    let message = 'Check out what\'s new at your favorite salons!';\n    let emoji = 'âœ¨';\n    let priority = 'medium';\n    let action = 'view_details';\n    let personalized = true;\n\n    for (const line of lines) {\n      if (line.includes('title:')) {\n        title = line.split('title:')[1]?.trim() || title;\n      } else if (line.includes('message:')) {\n        message = line.split('message:')[1]?.trim() || message;\n      } else if (line.includes('emoji:')) {\n        emoji = line.split('emoji:')[1]?.trim() || emoji;\n      }\n    }\n\n    return { title, message, emoji, priority, action, personalized };\n  }\n\n  // Get local notification when AI is not available\n  getLocalNotification(context) {\n    const { type, userProfile, salonData, bookingData } = context;\n    \n    const notifications = {\n      booking_reminder: {\n        title: 'â° Your appointment is tomorrow!',\n        message: `Don't forget your ${bookingData?.service || 'appointment'} at ${salonData?.name || 'your salon'}!`,\n        emoji: 'â°',\n        priority: 'high',\n        action: 'view_booking',\n        personalized: true\n      },\n      new_style_available: {\n        title: 'ðŸ’‡â€â™€ï¸ New style alert!',\n        message: `${salonData?.name || 'Your salon'} just added a new style that matches your vibe!`,\n        emoji: 'ðŸ’‡â€â™€ï¸',\n        priority: 'medium',\n        action: 'view_style',\n        personalized: true\n      },\n      salon_discount: {\n        title: 'ðŸ’° Special offer just for you!',\n        message: `${salonData?.name || 'Your favorite salon'} has a discount on services you love!`,\n        emoji: 'ðŸ’°',\n        priority: 'medium',\n        action: 'view_offer',\n        personalized: true\n      },\n      style_trending: {\n        title: 'ðŸ”¥ This style is trending!',\n        message: 'A style you might love is going viral on social media!',\n        emoji: 'ðŸ”¥',\n        priority: 'low',\n        action: 'view_trend',\n        personalized: true\n      },\n      maintenance_reminder: {\n        title: 'ðŸ’… Time for a touch-up?',\n        message: 'Your last style might need some love - book a maintenance session!',\n        emoji: 'ðŸ’…',\n        priority: 'medium',\n        action: 'book_maintenance',\n        personalized: true\n      }\n    };\n\n    return notifications[type] || {\n      title: 'âœ¨ New update!',\n      message: 'Check out what\'s new at your favorite salons!',\n      emoji: 'âœ¨',\n      priority: 'medium',\n      action: 'view_details',\n      personalized: false\n    };\n  }\n\n  // Analyze user behavior for smart suggestions\n  analyzeUserBehavior(userData) {\n    const {\n      bookingHistory,\n      preferences,\n      searchHistory,\n      favoriteSalons,\n      lastVisit\n    } = userData;\n\n    const insights = {\n      preferredServices: this.getPreferredServices(bookingHistory),\n      preferredSalons: this.getPreferredSalons(bookingHistory),\n      preferredTimes: this.getPreferredTimes(bookingHistory),\n      stylePreferences: this.getStylePreferences(preferences, searchHistory),\n      maintenanceSchedule: this.getMaintenanceSchedule(bookingHistory),\n      budgetRange: this.getBudgetRange(bookingHistory),\n      seasonalPreferences: this.getSeasonalPreferences(bookingHistory)\n    };\n\n    return insights;\n  }\n\n  // Get user's preferred services\n  getPreferredServices(bookingHistory) {\n    const serviceCount = {};\n    bookingHistory.forEach(booking => {\n      const service = booking.service;\n      serviceCount[service] = (serviceCount[service] || 0) + 1;\n    });\n\n    return Object.entries(serviceCount)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 3)\n      .map(([service]) => service);\n  }\n\n  // Get user's preferred salons\n  getPreferredSalons(bookingHistory) {\n    const salonCount = {};\n    bookingHistory.forEach(booking => {\n      const salon = booking.salonName;\n      salonCount[salon] = (salonCount[salon] || 0) + 1;\n    });\n\n    return Object.entries(salonCount)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 3)\n      .map(([salon]) => salon);\n  }\n\n  // Get user's preferred booking times\n  getPreferredTimes(bookingHistory) {\n    const timeCount = {};\n    bookingHistory.forEach(booking => {\n      const time = booking.time?.split(':')[0]; // Get hour\n      if (time) {\n        timeCount[time] = (timeCount[time] || 0) + 1;\n      }\n    });\n\n    return Object.entries(timeCount)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 2)\n      .map(([time]) => time);\n  }\n\n  // Get user's style preferences\n  getStylePreferences(userPreferences, searchHistory) {\n    const styleKeywords = [\n      'natural', 'edgy', 'classic', 'trendy', 'minimal', 'bold',\n      'curly', 'straight', 'wavy', 'short', 'long', 'bob', 'layers'\n    ];\n\n    const matchedPreferences = [];\n    styleKeywords.forEach(keyword => {\n      if (userPreferences.includes(keyword) || searchHistory.some(search => \n        search.toLowerCase().includes(keyword))) {\n        matchedPreferences.push(keyword);\n      }\n    });\n\n    return matchedPreferences;\n  }\n\n  // Get maintenance schedule based on booking history\n  getMaintenanceSchedule(bookingHistory) {\n    const lastBooking = bookingHistory[bookingHistory.length - 1];\n    if (!lastBooking) return null;\n\n    const lastDate = new Date(lastBooking.date);\n    const today = new Date();\n    const daysSince = Math.floor((today - lastDate) / (1000 * 60 * 60 * 24));\n\n    // Suggest maintenance based on service type\n    const service = lastBooking.service?.toLowerCase();\n    let suggestedDays = 30; // Default\n\n    if (service?.includes('color') || service?.includes('dye')) {\n      suggestedDays = 45;\n    } else if (service?.includes('cut') || service?.includes('trim')) {\n      suggestedDays = 60;\n    } else if (service?.includes('treatment')) {\n      suggestedDays = 14;\n    }\n\n    return {\n      daysSince,\n      suggestedDays,\n      needsMaintenance: daysSince >= suggestedDays\n    };\n  }\n\n  // Get budget range from booking history\n  getBudgetRange(bookingHistory) {\n    const prices = bookingHistory.map(booking => booking.price).filter(price => price);\n    \n    if (prices.length === 0) return 'medium';\n\n    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;\n    \n    if (avgPrice < 50) return 'low';\n    if (avgPrice > 100) return 'high';\n    return 'medium';\n  }\n\n  // Get seasonal preferences\n  getSeasonalPreferences(bookingHistory) {\n    const seasonalServices = {\n      'summer': ['beach waves', 'braids', 'protective styles'],\n      'winter': ['deep conditioning', 'moisturizing treatments', 'warm tones'],\n      'spring': ['fresh cuts', 'light colors', 'textured styles'],\n      'fall': ['rich colors', 'layered cuts', 'warm highlights']\n    };\n\n    const currentMonth = new Date().getMonth();\n    let currentSeason = 'summer';\n    \n    if (currentMonth >= 2 && currentMonth <= 4) currentSeason = 'spring';\n    else if (currentMonth >= 5 && currentMonth <= 7) currentSeason = 'summer';\n    else if (currentMonth >= 8 && currentMonth <= 10) currentSeason = 'fall';\n    else currentSeason = 'winter';\n\n    return seasonalServices[currentSeason];\n  }\n\n  // Generate contextual notification based on user behavior\n  async generateContextualNotification(userData, context) {\n    const insights = this.analyzeUserBehavior(userData);\n    \n    // Check if user needs maintenance reminder\n    if (insights.maintenanceSchedule?.needsMaintenance) {\n      return await this.generateSmartNotification({\n        type: 'maintenance_reminder',\n        userProfile: userData.profile,\n        salonData: { name: insights.preferredSalons[0] },\n        bookingData: { service: insights.preferredServices[0] },\n        preferences: insights.stylePreferences,\n        previousInteractions: userData.bookingHistory\n      });\n    }\n\n    // Check for seasonal recommendations\n    const seasonalServices = insights.seasonalPreferences;\n    if (seasonalServices.length > 0) {\n      return await this.generateSmartNotification({\n        type: 'style_trending',\n        userProfile: userData.profile,\n        salonData: { name: insights.preferredSalons[0] },\n        bookingData: { service: seasonalServices[0] },\n        preferences: insights.stylePreferences,\n        previousInteractions: userData.bookingHistory\n      });\n    }\n\n    // Default notification\n    return await this.generateSmartNotification({\n      type: 'general_update',\n      userProfile: userData.profile,\n      salonData: { name: insights.preferredSalons[0] },\n      bookingData: { service: insights.preferredServices[0] },\n      preferences: insights.stylePreferences,\n      previousInteractions: userData.bookingHistory\n    });\n  }\n}\n\nexport default new SmartNotificationsService(); \n\n\n
