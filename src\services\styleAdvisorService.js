// AI Style Advisor Service
import { API_BASE_URL, getApiUrl, API_ENDPOINTS } from '../utils/apiConfig';

class StyleAdvisorService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.aiEndpoint = getApiUrl('api/ai');

    // Direct AI API configuration for fallback
    this.apiKey = process.env.REACT_APP_GROQ_API_KEY || process.env.REACT_APP_MISTRAL_API_KEY || process.env.REACT_APP_OPENAI_API_KEY;
    this.apiUrl = process.env.REACT_APP_GROQ_API_URL || process.env.REACT_APP_MISTRAL_API_URL || process.env.REACT_APP_OPENAI_API_URL || 'https://api.groq.com/openai/v1/chat/completions';

    // Determine which provider is being used
    this.provider = 'unknown';
    if (process.env.REACT_APP_GROQ_API_KEY) {
      this.provider = 'Groq';
    } else if (process.env.REACT_APP_MISTRAL_API_KEY) {
      this.provider = 'Mistral';
    } else if (process.env.REACT_APP_OPENAI_API_KEY) {
      this.provider = 'OpenAI';
    }

    console.log('🔧 StyleAdvisorService initialized:');
    console.log('Backend URL:', this.aiEndpoint);
    console.log('Direct API URL:', this.apiUrl);
    console.log('AI Provider:', this.provider);
    console.log('API Key available:', !!this.apiKey);
  }

  // Get AI-powered style recommendations
  async getStyleRecommendations(userProfile) {
    console.log('🚀 Getting style recommendations for:', userProfile);

    // Try direct AI API first if we have an API key
    if (this.apiKey) {
      console.log(`🔄 Trying direct ${this.provider} API first...`);
      try {
        const prompt = this.buildRecommendationPrompt(userProfile);
        const result = await this.callAIAPI(prompt);
        console.log(`✅ ${this.provider} API success:`, result);
        console.log(`🎯 Source: Direct ${this.provider} API (LIVE AI GENERATION)`);
        return this.transformRecommendationStructure({
          ...result,
          source: `ai_api_direct_${this.provider.toLowerCase()}`,
          confidence: 0.9
        });
      } catch (aiError) {
        console.error('❌ Direct AI API failed:', aiError);
        console.log('🔄 Falling back to backend...');
      }
    }

    try {
      // Try Django AI API as fallback
      console.log('📡 Trying backend AI API...');
      const response = await fetch(`${this.aiEndpoint}/style-recommendations/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userProfile
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('📡 Backend API response:', result);
        console.log('🎯 Backend source:', result.source || 'unknown');

        // Check if backend returned fallback flag (old system)
        if (result.fallback) {
          console.log('⚠️ Backend returned fallback flag');
          return {
            response: "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
            confidence: 0.0,
            source: 'service_unavailable',
            isServiceUnavailable: true
          };
        }

        // Check if service is unavailable (new system)
        if (result.source === 'service_unavailable') {
          console.log('⚠️ Backend service unavailable');
          return {
            ...result,
            isServiceUnavailable: true
          };
        }

        // Log the actual source from backend
        if (result.source === 'cache_dynamic') {
          console.log('💾 Backend returned CACHED recommendations (from similar profiles)');
        } else if (result.source && result.source.includes('ai_')) {
          console.log(`🤖 Backend returned LIVE AI recommendations from: ${result.source}`);
        } else {
          console.log(`🎯 Backend source: ${result.source || 'unknown'}`);
        }

        return this.transformRecommendationStructure(result);
      } else {
        // API endpoint error - try direct AI API fallback
        console.log('🔄 Backend API error, trying direct AI API...');

        try {
          if (this.apiKey) {
            const prompt = this.buildRecommendationPrompt(userProfile);
            const result = await this.callAIAPI(prompt);
            return this.transformRecommendationStructure({
              ...result,
              source: 'ai_api_direct'
            });
          }
        } catch (aiError) {
          console.error('Direct AI API also failed:', aiError);
        }

        // Final fallback: friendly message only
        return {
          response: "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
          confidence: 0.0,
          source: 'service_unavailable',
          isServiceUnavailable: true
        };
      }
    } catch (error) {
      console.error('Style advisor error:', error);
      console.log('🔄 Backend unavailable, trying direct AI API...');

      // Fallback to direct AI API if backend is unavailable
      try {
        if (this.apiKey) {
          const prompt = this.buildRecommendationPrompt(userProfile);
          const result = await this.callAIAPI(prompt);
          return this.transformRecommendationStructure({
            ...result,
            source: 'ai_api_direct'
          });
        }
      } catch (aiError) {
        console.error('Direct AI API also failed:', aiError);
      }

      // Final fallback: friendly message only
      return {
        response: "Sorry, our smart engines are still busy now. Please come back in a few minutes.",
        confidence: 0.0,
        source: 'service_unavailable',
        isServiceUnavailable: true
      };
    }
  }

  // Analyze current style with AI
  async analyzeCurrentStyle(currentStyle, userProfile) {
    try {
      if (!this.apiKey) {
        console.warn('[StyleScan] No API key found, using fallback.');
        return this.getLocalStyleAnalysis(currentStyle, userProfile);
      }
      const prompt = this.buildAnalysisPrompt(currentStyle, userProfile);
      const result = await this.callAIAPI(prompt);
      if (result && result.analysis) {
        console.info('[StyleScan] AI analysis used.');
      return result;
      } else {
        console.warn('[StyleScan] AI response missing analysis, using fallback.');
        return this.getLocalStyleAnalysis(currentStyle, userProfile);
      }
    } catch (error) {
      console.error('[StyleScan] Style analysis error, using fallback:', error);
      return this.getLocalStyleAnalysis(currentStyle, userProfile);
    }
  }

  // Get trending styles
  getTrendingStyles() {
    return [
      {
        name: 'Wolf Cut',
        description: 'Edgy layered cut that\'s perfect for Gen Z',
        difficulty: 'Medium',
        popularity: 95
      },
      {
        name: 'Curtain Bangs',
        description: 'Face-framing bangs that suit most face shapes',
        difficulty: 'Easy',
        popularity: 88
      },
      {
        name: 'Money Piece',
        description: 'Face-framing highlights for a fresh look',
        difficulty: 'Hard',
        popularity: 82
      }
    ];
  }
  // Build AI prompt for style recommendations
  buildRecommendationPrompt(userProfile) {
    const {
      hairType,
      faceShape,
      age,
      occasion,
      stylePreferences,
      budget,
      maintenanceLevel
    } = userProfile;

    // Extract age number for more specific recommendations
    const ageNum = age ? parseInt(age.split('-')[0]) : 25;
    const ageGroup = ageNum < 21 ? 'teen' : ageNum < 26 ? 'young adult' : ageNum < 36 ? 'adult' : ageNum < 46 ? 'mature adult' : 'mature';

    return `You are a personal hairstylist speaking directly to your client. Address them as "you" and make it personal and engaging, like you're having a friendly conversation.

YOUR CLIENT'S PROFILE:
🧬 Your Hair: ${hairType} texture
👤 Your Face: ${faceShape} shape
🎂 Your Age: ${age} (${ageGroup})
🎯 Your Lifestyle: ${occasion} focused
💫 Your Style: ${stylePreferences?.join(' + ')} vibe
💰 Your Budget: ${budget} range
⏰ Your Time: ${maintenanceLevel} maintenance

TASK: Create 2-3 PERSONALIZED recommendations speaking directly to them. Use "you," "your," and make it feel like personal advice from a trusted stylist friend.

REQUIRED JSON FORMAT (respond with ONLY this JSON, no extra text):
{
  "recommendations": [
    {
      "style": "Textured Curly Bob",
      "description": "This modern bob will enhance your natural curly texture beautifully",
      "suitabilityScore": 0.9,
      "maintenance": "Low",
      "cost": "Mid-range",
      "stylingTime": "15 minutes",
      "products": ["Curl defining cream", "Light hold gel"],
      "stylingTips": ["Use your diffuser on low heat", "Scrunch while your hair is damp"],
      "outfit": "A flowy midi dress with white sneakers and a denim jacket for a casual, trendy look.",
      "whyRecommended": "Perfect for your ${hairType} hair and ${faceShape} face - fits your ${maintenanceLevel} lifestyle"
    }
  ],
  "analysis": {
    "faceShapeAnalysis": "Your ${faceShape} face shape works beautifully with styles that balance your features",
    "hairTypeAnalysis": "Your ${hairType} hair will respond best to moisture-rich products and gentle styling",
    "lifestyleFit": "These styles are perfect for your ${occasion} lifestyle and ${maintenanceLevel} maintenance preference"
  },
  "alternatives": [
    {
      "style": "Alternative Style",
      "reason": "This could be a great backup option for you",
      "suitabilityScore": 0.7
    }
  ]
}

IMPORTANT:
- Return ONLY the JSON object above
- Be personal and engaging - use "you," "your," "you'll" throughout
- Keep positive tone while being brief and actionable
- Make it feel like personal advice from a trusted stylist friend
- No introductory text, explanations, or markdown formatting
- ALWAYS fill every field in the JSON, including stylingTime, stylingTips, products, and outfit. Never leave any field empty.

Make each recommendation UNIQUE and SPECIFIC to this exact combination of features. Avoid generic advice.`;
  }

  // Build AI prompt for style analysis
  buildAnalysisPrompt(currentStyle, userProfile) {
    const { hairType, faceShape, age, occasion, stylePreferences, budget, maintenanceLevel } = userProfile;

    return `You are a personal hairstylist speaking directly to your client about their current style. Address them as "you" and make it personal, supportive, and engaging.

YOUR CURRENT STYLE: "${currentStyle}"

ABOUT YOU:
- Your Hair: ${hairType}
- Your Face: ${faceShape}
- Your Age: ${age}
- Your Lifestyle: ${occasion}
- Your Style: ${stylePreferences?.join(', ')}
- Your Budget: ${budget}
- Your Preference: ${maintenanceLevel} maintenance

TASK: Give personal, supportive analysis speaking directly to them. Use "you," "your," and make it feel like advice from a trusted stylist friend.

REQUIRED JSON FORMAT (respond with ONLY this JSON):
{
  "analysis": "Personal, positive analysis speaking directly to them about what works and gentle suggestions (2-3 sentences max using 'you' and 'your')",
  "improvements": [
    "Specific improvement suggestion for you",
    "Enhancement that will work with your natural beauty",
    "Budget-friendly option that fits your lifestyle"
  ],
  "stylingTips": [
    "Quick tip that will work for your hair",
    "Easy technique for your texture",
    "Time-saving trick for your routine"
  ],
  "products": [
    "Essential product for your hair type",
    "Budget-friendly option for you",
    "Perfect choice for your maintenance style"
  ],
  "maintenance": "Personal maintenance advice for your ${maintenanceLevel} preference - when you should trim and your daily routine (2 sentences max)"
}

TONE: Personal, positive, encouraging - like a trusted stylist friend. Use "you," "your," "you'll" throughout.

IMPORTANT:
- Be personal and engaging - speak directly to them
- Keep responses brief, actionable, and positive
- Make it feel like personal advice from someone who cares about their style
- Use conversational, friendly language
- Do NOT start your response with 'Your style...' or repeat the user's input or prompt. Speak as a stylist giving advice based on their situation, not describing their request.`;
  }

  // Get local recommendations when AI is not available
  getLocalRecommendations(userProfile) {
    const { hairType, faceShape, stylePreferences } = userProfile;

    const recommendations = [];

    // Generate recommendations based on profile
    if (faceShape === 'round' && hairType === 'straight') {
      recommendations.push({
        name: 'Long Layered Cut',
        description: 'Elongates your face shape with flowing layers',
        costRange: 'Medium',
        maintenance: 'Low',
        stylingTime: '15 mins',
        stylingTips: ['Use volumizing mousse', 'Blow dry with round brush'],
        products: ['Volumizing Mousse', 'Heat Protectant'],
        suitability: 'Perfect for your round face and straight hair'
      });
    }

    if (stylePreferences?.includes('modern')) {
      recommendations.push({
        name: 'Textured Bob',
        description: 'Modern bob with textured layers for movement',
        costRange: 'Medium',
        maintenance: 'Medium',
        stylingTime: '20 mins',
        stylingTips: ['Use texturizing spray', 'Scrunch while drying'],
        products: ['Texturizing Spray', 'Sea Salt Spray'],
        suitability: 'Matches your modern style preference'
      });
    }

    return {
      generalAdvice: `Based on your ${hairType} hair and ${faceShape} face shape, focus on styles that enhance your natural features.`,
      trendingNotes: 'Current trends favor natural textures and low-maintenance styles.',
      recommendations: recommendations.length > 0 ? recommendations : this.getDefaultRecommendations()
    };
  }

  // Get default recommendations
  getDefaultRecommendations() {
    return [
      {
        name: 'Classic Layered Cut',
        description: 'Versatile layers that work with most hair types',
        costRange: 'Medium',
        maintenance: 'Low',
        stylingTime: '15 mins',
        stylingTips: ['Use a round brush when blow drying', 'Apply heat protectant'],
        products: ['Heat Protectant', 'Volumizing Mousse'],
        suitability: 'Universal style that suits most people'
      },
      {
        name: 'Modern Bob',
        description: 'Chic bob cut with contemporary styling',
        costRange: 'Medium',
        maintenance: 'Medium',
        stylingTime: '20 mins',
        stylingTips: ['Use flat iron for sleek look', 'Add texture spray for casual style'],
        products: ['Smoothing Serum', 'Texture Spray'],
        suitability: 'Great for professional and casual looks'
      }
    ];
  }

  // Get local style analysis
  getLocalStyleAnalysis(currentStyle, userProfile) {
    // Use all user profile fields for a more dynamic fallback
    const { hairType, faceShape, age, occasion, stylePreferences, budget, maintenanceLevel } = userProfile || {};
    const stylePrefs = Array.isArray(stylePreferences) ? stylePreferences.join(', ') : stylePreferences || 'your preferences';
    // Supportive intro, never echo the raw prompt
    const analysis = `Let's find a look that fits your vibe, features, and plans! With your ${hairType || 'unique'} hair and ${faceShape || 'face shape'}, and your interest in ${occasion || 'special occasions'}, you have so many great options.`;
    const improvements = [
      `Try adding layers or texture to enhance your ${hairType || 'hair type'}.`,
      `A new parting or fringe could flatter your ${faceShape || 'face shape'}.`,
      `Consider a color update for a fresh, modern vibe.`,
      `Pick styles that fit your ${occasion || 'lifestyle'} and are easy to maintain.`
    ];
    const stylingTips = [
      `Use products designed for ${hairType || 'your hair type'}.`,
      `Schedule trims every 6-8 weeks to keep your style sharp.`,
      `Protect your hair from heat and environmental stress.`
    ];
    const products = [
      hairType ? `Best shampoo for ${hairType} hair` : 'Quality shampoo',
      maintenanceLevel ? `${maintenanceLevel} maintenance styling cream` : 'Styling cream',
      'Heat Protectant',
      'Dry Shampoo'
    ];
    const maintenance = `For your ${maintenanceLevel || 'preferred'} routine, aim for trims every 6-8 weeks and use products that support ${stylePrefs}.`;
    return {
      analysis,
      improvements,
      stylingTips,
      products,
      maintenance,
      compatibilityScore: 75
    };
  }

  async callAIAPI(prompt) {
    const model = 'llama3-8b-8192';
    console.log(`🤖 Calling ${this.provider} API with model: ${model}`);
    console.log(`🌐 API URL: ${this.apiUrl}`);

    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional hairstylist and style advisor. Provide personalized recommendations based on user characteristics and preferences. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      console.error(`❌ ${this.provider} API call failed with status:`, response.status);
      throw new Error(`${this.provider} API call failed`);
    }

    const data = await response.json();
    const { content } = data.choices[0].message;
    console.log(`📝 Raw ${this.provider} response length:`, content.length);
    console.log(`📝 Raw ${this.provider} response preview:`, content.substring(0, 200) + '...');

    try {
      const parsed = this.extractAndParseJSON(content);
      console.log(`✅ Successfully parsed ${this.provider} JSON response`);
      return parsed;
    } catch (error) {
      return {
        greeting: 'Sorry, our smart style advisor is busy right now. Please try again soon!',
        recommendations: [],
        analysis: '',
        alternatives: [],
        signatureTip: 'Check back later for fresh style inspiration!'
      };
    }
  }

  extractAndParseJSON(raw) {
    const jsonMatch = raw.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('No JSON found in AI response');
  }

  // Transform recommendation structure to match frontend expectations
  transformRecommendationStructure(result) {
    if (!result || !result.recommendations) {
      return result;
    }

    // Transform recommendations array to match frontend structure
    const transformedRecommendations = result.recommendations.map(rec => ({
      name: rec.style || rec.name || 'Recommended Style',
      description: rec.description || '',
      costRange: rec.cost || rec.costRange || 'Medium',
      maintenance: rec.maintenance || 'Medium',
      stylingTime: rec.stylingTime || '15 mins',
      stylingTips: rec.stylingTips || ['Use quality products'],
      products: rec.products || ['Styling product'],
      suitability: rec.whyRecommended || rec.suitability || 'Great choice for you'
    }));

    // Handle analysis object - convert to string if it's an object
    let generalAdvice = 'Here are your personalized recommendations';
    if (typeof result.analysis === 'string') {
      generalAdvice = result.analysis;
    } else if (typeof result.analysis === 'object' && result.analysis) {
      // If analysis is an object, combine the relevant parts
      const parts = [];
      if (result.analysis.faceShapeAnalysis) parts.push(result.analysis.faceShapeAnalysis);
      if (result.analysis.hairTypeAnalysis) parts.push(result.analysis.hairTypeAnalysis);
      if (result.analysis.lifestyleFit) parts.push(result.analysis.lifestyleFit);
      generalAdvice = parts.length > 0 ? parts.join(' ') : generalAdvice;
    } else if (result.greeting) {
      generalAdvice = result.greeting;
    }

    return {
      ...result,
      generalAdvice: generalAdvice,
      trendingNotes: result.signatureTip || 'Stay stylish!',
      recommendations: transformedRecommendations
    };
  }

  // Clean AI response to extract JSON
  cleanAIResponse(content) {
    console.log('🧹 Original content length:', content.length);

    // Remove any text before the first {
    let cleaned = content.replace(/^.*?(?=\{)/s, '');

    // Remove any text after the last }
    const lastBrace = cleaned.lastIndexOf('}');
    if (lastBrace !== -1) {
      cleaned = cleaned.substring(0, lastBrace + 1);
    }

    // Remove markdown code blocks
    cleaned = cleaned.replace(/```json\s*/g, '');
    cleaned = cleaned.replace(/```\s*/g, '');

    // Remove common AI response prefixes
    cleaned = cleaned.replace(/^(Here's|Here are|Based on).*?(?=\{)/s, '');

    console.log('🧹 Cleaned content length:', cleaned.length);
    console.log('🧹 Cleaned preview:', cleaned.substring(0, 100) + '...');

    return cleaned.trim();
  }

  // Parse AI response if JSON parsing fails
  parseAIResponse(content) {
    console.log('🔧 Parsing malformed AI response...');

    // Try to extract structured data from the text
    const recommendations = [];

    // Split by style descriptions
    const sections = content.split(/(?=This style)/);

    for (const section of sections) {
      if (section.includes('This style')) {
        const rec = {
          style: this.extractStyleName(section) || 'AI Recommended Style',
          description: this.extractDescription(section) || section.substring(0, 200),
          suitabilityScore: 0.8,
          maintenance: this.extractMaintenance(section) || 'Medium',
          cost: 'Mid-range',
          stylingTime: this.extractStylingTime(section) || '15 minutes',
          products: this.extractProducts(section) || ['Styling product', 'Hair spray'],
          stylingTips: this.extractStylingTips(section) || ['Use quality products'],
          whyRecommended: section.substring(0, 150) + '...'
        };
        recommendations.push(rec);
      }
    }

    return {
      recommendations: recommendations.length > 0 ? recommendations : this.getDefaultRecommendations(),
      analysis: {
        faceShapeAnalysis: "Based on your profile, these styles complement your face shape.",
        hairTypeAnalysis: "These recommendations work well with your hair type.",
        lifestyleFit: "The selected styles fit your lifestyle and maintenance preferences."
      },
      alternatives: []
    };
  }

  // Helper methods for parsing malformed responses
  extractStyleName(text) {
    // Try to find a style name or create one
    if (text.includes('bob')) return 'Textured Bob';
    if (text.includes('updo')) return 'Messy Updo';
    if (text.includes('curls')) return 'Natural Curls';
    return 'Custom Style';
  }

  extractDescription(text) {
    const match = text.match(/This style[^.]*\./);
    return match ? match[0] : text.substring(0, 200);
  }

  extractStylingTime(text) {
    const match = text.match(/(\d+)\s*minutes?/);
    return match ? `${match[1]} minutes` : '15 minutes';
  }

  extractMaintenance(text) {
    if (text.toLowerCase().includes('low')) return 'Low';
    if (text.toLowerCase().includes('high')) return 'High';
    return 'Medium';
  }

  extractProducts(text) {
    const products = [];
    const lines = text.split('\n');
    let inProductsSection = false;

    for (const line of lines) {
      if (line.includes('🛍️ Products Needed:')) {
        inProductsSection = true;
        continue;
      }
      if (inProductsSection && line.trim() && !line.includes('N/A')) {
        if (line.includes('⏱️') || line.includes('💡')) break;
        products.push(line.trim());
      }
    }

    return products.length > 0 ? products : ['Styling cream', 'Hair spray'];
  }

  extractStylingTips(text) {
    const tips = [];
    const lines = text.split('\n');
    let inTipsSection = false;

    for (const line of lines) {
      if (line.includes('💡 Styling Tips:')) {
        inTipsSection = true;
        continue;
      }
      if (inTipsSection && line.trim()) {
        if (line.includes('🛍️') || line.includes('⏱️')) break;
        tips.push(line.trim());
      }
    }

    return tips.length > 0 ? tips : ['Use quality products', 'Style with care'];
  }
}

const styleAdvisorService = new StyleAdvisorService();
export default styleAdvisorService;
