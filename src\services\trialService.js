// Trial Management Service for Frontend
import axiosInstance from '../api/axiosInstance';

class TrialService {
    /**
     * Get current trial status for vendor
     */
    static async getTrialStatus() {
        try {
            const response = await axiosInstance.get('/api/vendor/trial/status/');
            return response.data;
        } catch (error) {
            console.error('Error getting trial status:', error);
            throw error;
        }
    }

    /**
     * Start a trial for vendor
     */
    static async startTrial() {
        try {
            const response = await axiosInstance.post('/api/vendor/trial/start/');
            return response.data;
        } catch (error) {
            console.error('Error starting trial:', error);

            // Handle specific error cases
            if (error.response?.status === 404) {
                throw new Error('Trial service is currently unavailable. Please ensure the backend server is running.');
            } else if (error.response?.status === 401) {
                throw new Error('Please log in to start a trial.');
            } else if (error.response?.status === 400) {
                throw new Error(error.response.data?.error || 'Invalid trial request.');
            } else {
                throw new Error('Failed to start trial. Please check your connection and try again.');
            }
        }
    }

    /**
     * Get comprehensive trial information
     */
    static async getTrialInfo() {
        try {
            const response = await axiosInstance.get('/api/vendor/trial/info/');
            return response.data;
        } catch (error) {
            console.error('Error getting trial info:', error);
            throw error;
        }
    }

    /**
     * Check if vendor should see trial banner
     */
    static shouldShowTrialBanner(trialStatus) {
        if (!trialStatus || !trialStatus.trial_status) {
            return false;
        }

        return trialStatus.trial_status === 'expired' || 
               trialStatus.trial_status === 'expiring_soon' || 
               trialStatus.trial_status === 'expiring_today';
    }

    /**
     * Get trial banner configuration
     */
    static getTrialBannerConfig(trialStatus) {
        if (!trialStatus || !trialStatus.trial_message) {
            return null;
        }

        const message = trialStatus.trial_message;
        
        return {
            type: message.type,
            title: message.title,
            message: message.message,
            actionText: message.action,
            actionUrl: message.action_url,
            showClose: message.type !== 'expired'
        };
    }

    /**
     * Format time remaining for display
     */
    static formatTimeRemaining(trialStatus) {
        if (!trialStatus || !trialStatus.hours_remaining) {
            return '';
        }

        const hours = trialStatus.hours_remaining;
        const minutes = Math.floor((trialStatus.hours_remaining % 1) * 60);

        if (hours >= 1) {
            return `${Math.floor(hours)}h ${minutes}m remaining`;
        } else {
            return `${minutes}m remaining`;
        }
    }

    /**
     * Get trial status color for UI
     */
    static getTrialStatusColor(trialStatus) {
        if (!trialStatus || !trialStatus.trial_status) {
            return 'default';
        }

        switch (trialStatus.trial_status) {
            case 'expired':
                return 'danger';
            case 'expiring_soon':
                return 'warning';
            case 'expiring_today':
                return 'info';
            case 'active':
                return 'success';
            default:
                return 'default';
        }
    }

    /**
     * Check if vendor has active trial or subscription
     */
    static hasActiveAccess(trialInfo) {
        if (!trialInfo) {
            return false;
        }

        return trialInfo.is_active || 
               (trialInfo.is_trial && trialInfo.trial_status !== 'expired');
    }

    /**
     * Get feature access status
     */
    static getFeatureAccess(trialInfo, feature) {
        if (!trialInfo || !trialInfo.has_subscription) {
            return false;
        }

        if (trialInfo.is_trial) {
            // During trial, all features are available
            return true;
        }

        // For paid subscriptions, check specific features
        switch (feature) {
            case 'home_service':
                return trialInfo.home_service_enabled;
            case 'event_service':
                return trialInfo.event_service_enabled;
            case 'ticker_placement':
                return trialInfo.features_enabled?.includes('ticker_placement');
            default:
                return false;
        }
    }
}

export default TrialService; 