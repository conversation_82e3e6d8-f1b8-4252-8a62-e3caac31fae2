// API Configuration Utility
// Centralized API URL management for production and development

// Get the base API URL from environment variables
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Helper function to build API URLs
export const buildApiUrl = (endpoint) => {
  // Remove leading slash if present to avoid double slashes
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${API_BASE_URL}/${cleanEndpoint}`;
};

// Common API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login/',
    REGISTER: '/api/auth/register/',
    LOGOUT: '/api/auth/logout/',
    REFRESH: '/api/auth/refresh/',
  },
  
  // Salons
  SALONS: {
    SEARCH: '/api/salons/search/',
    DETAIL: (id) => `/api/salons/${id}/`,
    REVIEWS: (id) => `/api/salons/${id}/reviews/`,
  },
  
  // Bookings
  BOOKINGS: {
    CREATE: '/api/bookings/',
    LIST: '/api/bookings/',
    DETAIL: (id) => `/api/bookings/${id}/`,
  },
  
  // Social Features
  SOCIAL: {
    STYLE_POSTS: '/api/style-posts/',
    FRIENDS_ACTIVITY: '/api/friends/activity/',
    LIKES: (postId) => `/api/style-posts/${postId}/like/`,
    COMMENTS: (postId) => `/api/style-posts/${postId}/comments/`,
  },
  
  // AI Features
  AI: {
    HEALTH: '/api/ai/health/',
    GIFT_MESSAGES: '/api/ai/gift-messages/',
    STYLE_RECOMMENDATIONS: '/api/ai/style-recommendations/',
    SMART_SCHEDULING: '/api/ai/smart-scheduling/',
    VIRTUAL_TRYON: '/api/ai/virtual-tryon/',
    TREND_PREDICTIONS: '/api/ai/trend-predictions/',
    ANALYTICS_REPORT: '/api/ai/analytics-report/',
    SALON_MATCHING: '/api/ai/salon-matching/',
    SMART_NOTIFICATIONS: '/api/ai/smart-notifications/',
    VOICE_ASSISTANT: '/api/ai/voice-assistant/',
  },
  
  // Analytics
  ANALYTICS: {
    DASHBOARD: '/api/analytics/dashboard/',
  },
};

// Helper function to get full API URL for an endpoint
export const getApiUrl = (endpoint) => {
  return buildApiUrl(endpoint);
};

// Export configured axios instance (if using axios)
import axios from 'axios';

export const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default {
  API_BASE_URL,
  buildApiUrl,
  getApiUrl,
  API_ENDPOINTS,
  axiosInstance,
};
