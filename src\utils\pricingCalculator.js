// Pricing Calculator Utility for SalonGenz
// Handles all booking cost calculations and breakdowns

export class PricingCalculator {
  constructor() {
    // Platform commission rates (percentage of service price)
    this.platformCommission = 0.05; // 5% platform fee
    this.minimumCommission = 50; // Minimum KSh 50 commission
    this.maximumCommission = 500; // Maximum KSh 500 commission
    
    // Additional fees
    this.bookingFee = 0; // No additional booking fee for now
    this.processingFee = 0; // No processing fee for now
    
    // Currency settings
    this.currency = 'KSh';
    this.currencySymbol = 'KSh';
  }

  /**
   * Calculate total booking cost with breakdown
   * @param {Object} service - Service object with price
   * @param {Object} options - Additional options
   * @returns {Object} Cost breakdown
   */
  calculateBookingCost(service, options = {}) {
    const servicePrice = parseFloat(service.price) || 0;
    
    // Calculate platform commission
    const commission = this.calculateCommission(servicePrice);
    
    // Calculate additional fees
    const additionalFees = this.calculateAdditionalFees(servicePrice, options);
    
    // Calculate subtotal (service price + additional fees)
    const subtotal = servicePrice + additionalFees;
    
    // Calculate total
    const total = subtotal + commission;
    
    return {
      servicePrice,
      commission,
      additionalFees,
      subtotal,
      total,
      breakdown: {
        service: {
          name: service.name,
          price: servicePrice,
          duration: service.duration
        },
        commission: {
          amount: commission,
          percentage: this.platformCommission * 100,
          description: 'Platform service fee'
        },
        additionalFees: {
          amount: additionalFees,
          breakdown: this.getAdditionalFeesBreakdown(servicePrice, options)
        },
        subtotal,
        total
      }
    };
  }

  /**
   * Calculate platform commission
   * @param {number} servicePrice - Service price
   * @returns {number} Commission amount
   */
  calculateCommission(servicePrice) {
    const commission = servicePrice * this.platformCommission;
    
    // Apply minimum and maximum limits
    if (commission < this.minimumCommission) {
      return this.minimumCommission;
    }
    
    if (commission > this.maximumCommission) {
      return this.maximumCommission;
    }
    
    return Math.round(commission);
  }

  /**
   * Calculate additional fees
   * @param {number} servicePrice - Service price
   * @param {Object} options - Additional options
   * @returns {number} Total additional fees
   */
  calculateAdditionalFees(servicePrice, options = {}) {
    let totalFees = 0;
    
    // Booking fee
    if (this.bookingFee > 0) {
      totalFees += this.bookingFee;
    }
    
    // Processing fee
    if (this.processingFee > 0) {
      totalFees += this.processingFee;
    }
    
    // Premium staff fee (if applicable)
    if (options.premiumStaff) {
      totalFees += this.calculatePremiumStaffFee(servicePrice);
    }
    
    // Rush booking fee (if applicable)
    if (options.rushBooking) {
      totalFees += this.calculateRushBookingFee(servicePrice);
    }
    
    return totalFees;
  }

  /**
   * Calculate premium staff fee
   * @param {number} servicePrice - Service price
   * @returns {number} Premium staff fee
   */
  calculatePremiumStaffFee(servicePrice) {
    return Math.round(servicePrice * 0.1); // 10% premium for premium staff
  }

  /**
   * Calculate rush booking fee
   * @param {number} servicePrice - Service price
   * @returns {number} Rush booking fee
   */
  calculateRushBookingFee(servicePrice) {
    return Math.round(servicePrice * 0.05); // 5% rush booking fee
  }

  /**
   * Get detailed breakdown of additional fees
   * @param {number} servicePrice - Service price
   * @param {Object} options - Additional options
   * @returns {Array} Array of fee breakdowns
   */
  getAdditionalFeesBreakdown(servicePrice, options = {}) {
    const breakdown = [];
    
    if (this.bookingFee > 0) {
      breakdown.push({
        name: 'Booking Fee',
        amount: this.bookingFee,
        description: 'Service booking fee'
      });
    }
    
    if (this.processingFee > 0) {
      breakdown.push({
        name: 'Processing Fee',
        amount: this.processingFee,
        description: 'Payment processing fee'
      });
    }
    
    if (options.premiumStaff) {
      const premiumFee = this.calculatePremiumStaffFee(servicePrice);
      breakdown.push({
        name: 'Premium Staff Fee',
        amount: premiumFee,
        description: 'Premium stylist service'
      });
    }
    
    if (options.rushBooking) {
      const rushFee = this.calculateRushBookingFee(servicePrice);
      breakdown.push({
        name: 'Rush Booking Fee',
        amount: rushFee,
        description: 'Same-day booking fee'
      });
    }
    
    return breakdown;
  }

  /**
   * Format currency amount
   * @param {number} amount - Amount to format
   * @returns {string} Formatted currency string
   */
  formatCurrency(amount) {
    return `${this.currencySymbol} ${amount.toLocaleString()}`;
  }

  /**
   * Get commission rate for display
   * @returns {string} Formatted commission rate
   */
  getCommissionRate() {
    return `${(this.platformCommission * 100).toFixed(0)}%`;
  }

  /**
   * Calculate vendor payout (what the salon receives)
   * @param {number} servicePrice - Service price
   * @returns {number} Vendor payout amount
   */
  calculateVendorPayout(servicePrice) {
    const commission = this.calculateCommission(servicePrice);
    return servicePrice - commission;
  }
}

// Export singleton instance
export const pricingCalculator = new PricingCalculator();

// Export utility functions
export const formatCurrency = (amount) => pricingCalculator.formatCurrency(amount);
export const calculateBookingCost = (service, options) => pricingCalculator.calculateBookingCost(service, options);
export const calculateCommission = (servicePrice) => pricingCalculator.calculateCommission(servicePrice);
export const calculateVendorPayout = (servicePrice) => pricingCalculator.calculateVendorPayout(servicePrice); 