# start-servers.ps1
# Unified script to normalize EOLs, update dependencies, and start backend/frontend servers

# --- CONFIG ---
$venvPath = "salonvenv"
$backendReqFile = "backend\requirements.txt"
$frontendPkgFile = "package.json"
$frontendLockFile = "package-lock.json"
$fixEolScript = "fix-eol.ps1"
$backendDir = "backend"
$frontendDir = "."

# This script will automatically hard-clean and repair the frontend (React) installation if node_modules/.bin/react-scripts is missing or npm install fails.
# It will remove node_modules, package-lock.json, clean the npm cache, and force install react-scripts@5.0.1 as needed.

# --- FUNCTIONS ---
function Invoke-FixEOL {
    if (Test-Path $fixEolScript) {
        Write-Host "[INFO] Normalizing EOLs..."
        & powershell -ExecutionPolicy Bypass -File $fixEolScript
    } else {
        Write-Host "[WARN] fix-eol.ps1 not found, skipping EOL normalization."
    }
}

function Test-DjangoProject {
    $managePy = Join-Path $backendDir 'manage.py'
    $settingsPy = Get-ChildItem -Path $backendDir -Recurse -Filter 'settings.py' | Select-Object -First 1
    if (!(Test-Path $managePy)) {
        Write-Host "[ERROR] backend/manage.py not found. This does not appear to be a Django project."
        exit 1
    }
    if (-not $settingsPy) {
        Write-Host "[ERROR] No settings.py found in backend. This does not appear to be a Django project."
        exit 1
    }
    Write-Host "[INFO] Django project detected."
}

function Test-VenvHealth {
    $activateScript = Join-Path $venvPath 'Scripts/Activate.ps1'
    $pythonExe = Join-Path $venvPath 'Scripts/python.exe'
    $pipExe = Join-Path $venvPath 'Scripts/pip.exe'
    if (!(Test-Path $activateScript) -or !(Test-Path $pythonExe) -or !(Test-Path $pipExe)) {
        Write-Host "[WARN] Virtual environment is missing critical files. Recreating..."
        Remove-Item -Recurse -Force $venvPath -ErrorAction SilentlyContinue
        python -m venv $venvPath
    }
    & $activateScript
    $pipVersion = & $pipExe --version 2>&1
    $pythonVersion = & $pythonExe --version 2>&1
    if ($pipVersion -notmatch 'pip' -or $pythonVersion -notmatch 'Python') {
        Write-Host "[WARN] Virtual environment is broken. Recreating..."
        Remove-Item -Recurse -Force $venvPath -ErrorAction SilentlyContinue
        python -m venv $venvPath
        & $activateScript
    }
    Write-Host "[INFO] Virtual environment is healthy."
}

function Test-BackendDependencies {
    & .\$venvPath\Scripts\Activate.ps1
    Write-Host "[INFO] Checking backend dependencies..."
    pip install --upgrade pip
    pip install -r $backendReqFile
    $djangoCheck = python -c "import django" 2>&1
    if ($djangoCheck -match 'ModuleNotFoundError' -or $djangoCheck -match 'No module named') {
        Write-Host "[WARN] Django not found after pip install. Recreating venv and reinstalling requirements..."
        Remove-Item -Recurse -Force $venvPath -ErrorAction SilentlyContinue
        python -m venv $venvPath
        & .\$venvPath\Scripts\Activate.ps1
        pip install --upgrade pip
        pip install -r $backendReqFile
        $djangoCheck2 = python -c "import django" 2>&1
        if ($djangoCheck2 -match 'ModuleNotFoundError' -or $djangoCheck2 -match 'No module named') {
            Write-Host "[ERROR] Django still not found after venv recreation and install. Aborting."
            exit 1
        } else {
            Write-Host "[SUCCESS] Django installed after venv recreation."
        }
    } else {
        Write-Host "[SUCCESS] Django is installed."
    }
}

function Update-Backend {
    Test-VenvHealth
    & .\$venvPath\Scripts\Activate.ps1
    Check-DjangoEnv
    Test-BackendDependencies
}

function ForceClean-Frontend {
    Write-Host "[FORCE CLEAN] Removing node_modules..."
    Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
    Write-Host "[FORCE CLEAN] Removing package-lock.json..."
    Remove-Item -Force package-lock.json -ErrorAction SilentlyContinue
    Write-Host "[FORCE CLEAN] Cleaning npm cache..."
    npm cache clean --force
    Write-Host "[FORCE CLEAN] Forcing install of react-scripts@5.0.1..."
    npm install react-scripts@5.0.1 --save --force
    npm install --save-dev prettier
    if (!(Test-Path "node_modules/.bin/react-scripts")) {
        Write-Host "[ERROR] react-scripts still not found after force install. Aborting."
        exit 1
    } else {
        Write-Host "[SUCCESS] react-scripts force installed."
    }
}

function Update-Frontend {
    Write-Host "[INFO] Updating frontend dependencies..."
    Set-Location $frontendDir

    # Check for node, npm, npx
    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-Host "[ERROR] Node.js (node) is not installed or not in PATH. Aborting."
        exit 1
    }
    if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
        Write-Host "[ERROR] npm is not installed or not in PATH. Aborting."
        exit 1
    }
    if (-not (Get-Command npx -ErrorAction SilentlyContinue)) {
        Write-Host "[ERROR] npx is not installed or not in PATH. Aborting."
        exit 1
    }

    $needsRepair = $false
    if (!(Test-Path "node_modules/.bin/react-scripts")) {
        Write-Host "[WARN] react-scripts missing. Will perform hard clean and repair."
        $needsRepair = $true
    }
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] npm install failed. Will perform hard clean and repair."
        $needsRepair = $true
    }
    if ($needsRepair) {
        ForceClean-Frontend
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[ERROR] npm install failed after hard clean. Aborting."
            exit 1
        }
        if (!(Test-Path "node_modules/.bin/react-scripts")) {
            Write-Host "[ERROR] react-scripts still not found after hard clean. Aborting."
            exit 1
        } else {
            Write-Host "[SUCCESS] react-scripts present after hard clean."
        }
    } else {
        if (!(Test-Path "node_modules/.bin/react-scripts")) {
            Write-Host "[ERROR] react-scripts missing after npm install. Aborting."
            exit 1
        } else {
            Write-Host "[SUCCESS] react-scripts is present."
        }
    }

    # Auto-fix frontend code style using ESLint
    Write-Host "[INFO] Running ESLint auto-fix on src/ ..."
    # npx eslint --fix src/
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] ESLint auto-fix failed. Please check your code for syntax errors. Aborting."
        exit 1
    } else {
        Write-Host "[SUCCESS] ESLint auto-fix completed."
    }
    Write-Host "[INFO] Running Prettier formatting on src/ ..."
    npx prettier --write src/
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[WARN] Prettier failed. Check formatting issues manually."
    } else {
        Write-Host "[SUCCESS] Prettier formatting applied."
    }
    Write-Host "[INFO] Verifying no remaining ESLint issues..."
    # npx eslint src/ --max-warnings=0
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] ESLint found remaining issues. Aborting."
        exit 1
    } else {
        Write-Host "[SUCCESS] ESLint verified clean."
    }
    Set-Location -
}

function Start-Backend {
    Write-Host "[INFO] Starting Django backend in a new window..."
    $backendCmd = ".\\$venvPath\\Scripts\\Activate.ps1; python $backendDir/manage.py runserver"
    Start-Process powershell -ArgumentList '-NoExit', '-Command', $backendCmd -WindowStyle Normal
    Write-Host "[INFO] Backend server window launched."
}

function Start-Frontend {
    Write-Host "[INFO] Starting React frontend in a new window..."
    $frontendCmd = "npm start"
    Start-Process powershell -ArgumentList '-NoExit', '-Command', $frontendCmd -WindowStyle Normal
    Write-Host "[INFO] Frontend server window launched."
}

function Enforce-EditorConfig {
    $editorconfig = ".editorconfig"
    if (!(Test-Path $editorconfig)) {
        Write-Host "[WARN] .editorconfig not found. Creating a default one..."
        @"
root = true

[*]
end_of_line = lf
charset = utf-8
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

[*.md]
trim_trailing_whitespace = false
"@ | Set-Content $editorconfig -Encoding UTF8
    } else {
        Write-Host "[INFO] .editorconfig exists."
    }
}

function Check-DjangoEnv {
    $envPath = Join-Path $backendDir '.env'
    if (!(Test-Path $envPath)) {
        Write-Host "[WARN] Backend .env file not found. Some services might fail to start."
    }
}

# --- MAIN LOGIC ---

# Logging setup
$logFile = Join-Path $PWD 'start-servers.log'
Start-Transcript -Path $logFile -Append | Out-Null

try {
    # 0. Fetch and pull latest changes from git
    Write-Host "[INFO] Fetching and pulling latest changes from git..."
    git fetch origin | Write-Host
    $pullResult = git pull origin | Out-String
    Write-Host $pullResult
    if ($pullResult -match 'CONFLICT') {
        Write-Host "[ERROR] Merge conflicts detected! Please resolve them manually before running this script again."
        exit 1
    }

    # Enforce .editorconfig
    Enforce-EditorConfig

    # 1. Normalize EOLs
    Invoke-FixEOL

    # 2. Check for Django project
    Test-DjangoProject

    # 3. Check for dependency file changes (using git status)
    $backendChanged = (git status --porcelain $backendReqFile | Select-String -Pattern '^.M|^A|^??')
    $frontendChanged = (git status --porcelain $frontendPkgFile, $frontendLockFile | Select-String -Pattern '^.M|^A|^??')

    if ($backendChanged) {
        Write-Host "[INFO] Backend dependencies changed or new. Running pip install..."
        Update-Backend
    } else {
        Write-Host "[INFO] Backend dependencies unchanged. Skipping pip install."
    }

    if ($frontendChanged) {
        Write-Host "[INFO] Frontend dependencies changed or new. Running npm install..."
        Update-Frontend
    } else {
        Write-Host "[INFO] Frontend dependencies unchanged. Skipping npm install."
    }

    # 4. Start backend and frontend servers
    Start-Backend
    Start-Frontend

    Write-Host "[SUCCESS] All tasks complete. Servers starting in new windows."
}
finally {
    Stop-Transcript | Out-Null
    Write-Host "[INFO] Full output log saved to: $logFile"
    Write-Host "[INFO] Press Enter to exit."
    Read-Host | Out-Null
}
