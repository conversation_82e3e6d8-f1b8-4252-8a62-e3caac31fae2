# Repository Sync Script
# Syncs local and remote repositories

Write-Host "=== Repository Sync Script ===" -ForegroundColor Green
Write-Host "Starting repository synchronization..." -ForegroundColor Yellow

try {
    # Check current branch
    Write-Host "`nChecking current branch..." -ForegroundColor Cyan
    $currentBranch = git branch --show-current
    Write-Host "Current branch: $currentBranch" -ForegroundColor White
    
    # Check status
    Write-Host "`nChecking repository status..." -ForegroundColor Cyan
    $status = git status --porcelain
    if ($status) {
        Write-Host "Uncommitted changes found:" -ForegroundColor Yellow
        git status --short
    } else {
        Write-Host "Working directory clean" -ForegroundColor Green
    }
    
    # Fetch from remote
    Write-Host "`nFetching from remote..." -ForegroundColor Cyan
    git fetch origin
    Write-Host "Fetch completed" -ForegroundColor Green
    
    # Check for remote changes
    Write-Host "`nChecking for remote changes..." -ForegroundColor Cyan
    $remoteChanges = git log HEAD..origin/$currentBranch --oneline
    if ($remoteChanges) {
        Write-Host "Remote changes found:" -ForegroundColor Yellow
        Write-Host $remoteChanges -ForegroundColor White
        
        # Pull changes
        Write-Host "`nPulling remote changes..." -ForegroundColor Cyan
        git pull origin $currentBranch
        Write-Host "Pull completed" -ForegroundColor Green
    } else {
        Write-Host "No remote changes to pull" -ForegroundColor Green
    }
    
    # Check for local changes to push
    Write-Host "`nChecking for local changes to push..." -ForegroundColor Cyan
    $localChanges = git log origin/$currentBranch..HEAD --oneline
    if ($localChanges) {
        Write-Host "Local changes found:" -ForegroundColor Yellow
        Write-Host $localChanges -ForegroundColor White
        
        # Push changes
        Write-Host "`nPushing local changes..." -ForegroundColor Cyan
        git push origin $currentBranch
        Write-Host "Push completed" -ForegroundColor Green
    } else {
        Write-Host "No local changes to push" -ForegroundColor Green
    }
    
    # Final status
    Write-Host "`n=== Sync Complete ===" -ForegroundColor Green
    Write-Host "Repository is now synchronized with remote" -ForegroundColor Green
    
    # Show final status
    Write-Host "`nFinal repository status:" -ForegroundColor Cyan
    git status
    
} catch {
    Write-Host "`nError during sync: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check your git configuration and network connection" -ForegroundColor Yellow
    exit 1
}

Write-Host "`nSync script completed successfully!" -ForegroundColor Green
