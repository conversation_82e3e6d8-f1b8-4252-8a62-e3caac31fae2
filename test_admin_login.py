import requests
import json

# Test admin login process
def test_admin_login():
    base_url = "http://127.0.0.1:8000"
    
    print("=== Testing Admin Login Process ===")
    
    # Step 1: Get JWT token
    print("\n1. Getting JWT token...")
    login_data = {
        "username": "admin",
        "password": "!!247urulA"
    }
    
    try:
        response = requests.post(f"{base_url}/api/token/", json=login_data)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access')
            print(f"Access token received: {access_token[:50]}...")
            
            # Step 2: Test user profile endpoint
            print("\n2. Testing user profile endpoint...")
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            profile_response = requests.get(f"{base_url}/api/user/profile/", headers=headers)
            print(f"Profile response status: {profile_response.status_code}")
            
            if profile_response.status_code == 200:
                profile_data = profile_response.json()
                print("Profile data received:")
                print(json.dumps(profile_data, indent=2))
                
                # Check admin status
                is_superuser = profile_data.get('is_superuser', False)
                is_staff = profile_data.get('is_staff', False)
                is_vendor = profile_data.get('is_vendor', False)
                
                print(f"\nAdmin Status:")
                print(f"- is_superuser: {is_superuser}")
                print(f"- is_staff: {is_staff}")
                print(f"- is_vendor: {is_vendor}")
                
                if is_superuser:
                    print("✅ Admin user correctly identified!")
                else:
                    print("❌ Admin user not recognized!")
                    
            else:
                print(f"Profile endpoint failed: {profile_response.text}")
                
        else:
            print(f"Login failed: {response.text}")
            
    except Exception as e:
        print(f"Error during test: {e}")

if __name__ == "__main__":
    test_admin_login() 