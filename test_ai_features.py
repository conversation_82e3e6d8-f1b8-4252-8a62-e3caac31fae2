#!/usr/bin/env python3
"""
Comprehensive AI Features Test Script
Tests all AI features to ensure they work with the AI engine
"""

import sys
import os
import json
import requests
import time
from typing import Dict, Any

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test configuration
BASE_URL = "http://localhost:8000"
AI_ENDPOINTS = {
    "health": "/api/ai/health/",
    "gift_messages": "/api/ai/gift-messages/",
    "style_recommendations": "/api/ai/style-recommendations/",
    "smart_scheduling": "/api/ai/smart-scheduling/",
    "virtual_tryon": "/api/ai/virtual-tryon/",
    "trend_predictions": "/api/ai/trend-predictions/",
    "analytics_report": "/api/ai/analytics-report/",
    "salon_matching": "/api/ai/salon-matching/",
    "smart_notifications": "/api/ai/smart-notifications/",
    "voice_assistant": "/api/ai/voice-assistant/"
}

class AITestSuite:
    def __init__(self):
        self.results = {}
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
    
    def test_health_check(self) -> Dict[str, Any]:
        """Test AI service health"""
        print("🔍 Testing AI Health Check...")
        try:
            response = self.session.get(f"{BASE_URL}{AI_ENDPOINTS['health']}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health Check: {data}")
                return {"status": "pass", "data": data}
            else:
                print(f"❌ Health Check Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Health Check Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_gift_messages(self) -> Dict[str, Any]:
        """Test AI Gift Message Generation"""
        print("🎁 Testing AI Gift Messages...")
        payload = {
            "relationship": "bestie",
            "occasion": "birthday",
            "tone": "fun",
            "recipientName": "Sarah"
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['gift_messages']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Gift Messages: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Gift Messages: {data.get('response', 'Generated successfully')}")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Gift Messages Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Gift Messages Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_style_recommendations(self) -> Dict[str, Any]:
        """Test AI Style Recommendations"""
        print("💇‍♀️ Testing AI Style Recommendations...")
        payload = {
            "userProfile": {
                "hairType": "curly",
                "faceShape": "oval",
                "lifestyle": "active",
                "preferences": ["low-maintenance", "natural"],
                "age": 25,
                "style": "casual"
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['style_recommendations']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('source') == 'service_unavailable':
                    print("⚠️ Style Recommendations: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Style Recommendations: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Style Recommendations Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Style Recommendations Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_smart_scheduling(self) -> Dict[str, Any]:
        """Test AI Smart Scheduling"""
        print("📅 Testing AI Smart Scheduling...")
        payload = {
            "userPreferences": {
                "preferredTime": "morning",
                "preferredDay": "weekend",
                "urgency": "medium",
                "duration": 60
            },
            "salonAvailability": {
                "availableSlots": ["09:00", "10:00", "11:00", "14:00", "15:00"],
                "workingHours": {"start": "09:00", "end": "18:00"},
                "daysAvailable": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
            },
            "serviceDetails": {
                "duration": 60,
                "serviceType": "haircut",
                "complexity": "medium"
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['smart_scheduling']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Smart Scheduling: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Smart Scheduling: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Smart Scheduling Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Smart Scheduling Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_virtual_tryon(self) -> Dict[str, Any]:
        """Test AI Virtual Try-On"""
        print("🪞 Testing AI Virtual Try-On...")
        payload = {
            "userProfile": {
                "hairType": "straight",
                "faceShape": "round",
                "hairLength": "medium",
                "skinTone": "medium",
                "age": 28
            },
            "selectedStyle": {
                "name": "Layered Bob",
                "description": "Modern layered bob with side sweep",
                "difficulty": "medium",
                "maintenance": "low"
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['virtual_tryon']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Virtual Try-On: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Virtual Try-On: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Virtual Try-On Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Virtual Try-On Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_trend_predictions(self) -> Dict[str, Any]:
        """Test AI Trend Predictions"""
        print("📊 Testing AI Trend Predictions...")
        payload = {
            "platformData": {
                "socialMediaTrends": ["balayage", "bob_cuts", "natural_textures"],
                "seasonalData": {"season": "spring", "year": 2024},
                "demographics": {"ageGroup": "18-35", "location": "urban"}
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['trend_predictions']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Trend Predictions: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Trend Predictions: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Trend Predictions Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Trend Predictions Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_analytics_report(self) -> Dict[str, Any]:
        """Test AI Analytics Report"""
        print("📈 Testing AI Analytics Report...")
        payload = {
            "analyticsData": {
                "revenue": {"current": 15000, "previous": 12000},
                "bookings": {"current": 150, "previous": 120},
                "customerSatisfaction": 4.5,
                "popularServices": ["haircut", "color", "styling"]
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['analytics_report']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Analytics Report: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Analytics Report: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Analytics Report Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Analytics Report Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_salon_matching(self) -> Dict[str, Any]:
        """Test AI Salon Matching"""
        print("🏪 Testing AI Salon Matching...")
        payload = {
            "userPreferences": {
                "style": "modern",
                "budget": "mid-range",
                "location": "downtown",
                "services": ["haircut", "color", "styling"]
            },
            "availableSalons": [
                {
                    "name": "Modern Cuts",
                    "rating": 4.5,
                    "services": ["haircut", "color"],
                    "location": "downtown",
                    "priceRange": "mid-range"
                },
                {
                    "name": "Luxury Salon",
                    "rating": 4.8,
                    "services": ["haircut", "color", "styling"],
                    "location": "downtown",
                    "priceRange": "premium"
                }
            ]
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['salon_matching']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Salon Matching: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Salon Matching: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Salon Matching Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Salon Matching Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_smart_notifications(self) -> Dict[str, Any]:
        """Test AI Smart Notifications"""
        print("🧠 Testing AI Smart Notifications...")
        payload = {
            "userContext": {
                "lastVisit": "2024-01-15",
                "preferences": ["reminders", "promotions"],
                "activity": "active"
            },
            "salonData": {
                "services": ["haircut", "color"],
                "promotions": ["20% off color"],
                "availability": "good"
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['smart_notifications']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Smart Notifications: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Smart Notifications: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Smart Notifications Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Smart Notifications Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def test_voice_assistant(self) -> Dict[str, Any]:
        """Test AI Voice Assistant"""
        print("🎤 Testing AI Voice Assistant...")
        payload = {
            "voiceInput": "I want to book a haircut for tomorrow morning",
            "userContext": {
                "timestamp": "2024-01-20T10:00:00Z",
                "platform": "web",
                "location": "downtown"
            }
        }
        
        try:
            response = self.session.post(f"{BASE_URL}{AI_ENDPOINTS['voice_assistant']}", json=payload)
            data = response.json()
            
            if response.status_code == 200:
                if data.get('fallback'):
                    print("⚠️ Voice Assistant: Using fallback (no AI available)")
                    return {"status": "fallback", "data": data}
                else:
                    print(f"✅ Voice Assistant: Generated successfully")
                    return {"status": "pass", "data": data}
            else:
                print(f"❌ Voice Assistant Failed: {response.status_code}")
                return {"status": "fail", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Voice Assistant Error: {e}")
            return {"status": "fail", "error": str(e)}
    
    def run_all_tests(self):
        """Run all AI feature tests"""
        print("🚀 Starting Comprehensive AI Features Test Suite")
        print("=" * 60)
        
        tests = [
            ("health_check", self.test_health_check),
            ("gift_messages", self.test_gift_messages),
            ("style_recommendations", self.test_style_recommendations),
            ("smart_scheduling", self.test_smart_scheduling),
            ("virtual_tryon", self.test_virtual_tryon),
            ("trend_predictions", self.test_trend_predictions),
            ("analytics_report", self.test_analytics_report),
            ("salon_matching", self.test_salon_matching),
            ("smart_notifications", self.test_smart_notifications),
            ("voice_assistant", self.test_voice_assistant)
        ]
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name.upper()} {'='*20}")
            result = test_func()
            self.results[test_name] = result
            time.sleep(1)  # Small delay between tests
        
        self.print_summary()
    
    def print_summary(self):
        """Print test results summary"""
        print("\n" + "=" * 60)
        print("📊 AI FEATURES TEST SUMMARY")
        print("=" * 60)
        
        passed = 0
        failed = 0
        fallback = 0
        
        for test_name, result in self.results.items():
            status = result.get('status', 'unknown')
            if status == 'pass':
                passed += 1
                print(f"✅ {test_name}: PASS")
            elif status == 'fallback':
                fallback += 1
                print(f"⚠️ {test_name}: FALLBACK (No AI available)")
            else:
                failed += 1
                print(f"❌ {test_name}: FAIL - {result.get('error', 'Unknown error')}")
        
        print("\n" + "=" * 60)
        print(f"Total Tests: {len(self.results)}")
        print(f"✅ Passed: {passed}")
        print(f"⚠️ Fallback: {fallback}")
        print(f"❌ Failed: {failed}")
        print("=" * 60)
        
        if fallback > 0:
            print("\n🔧 RECOMMENDATIONS:")
            print("1. Set up AI API keys (GROQ_API_KEY, MISTRAL_API_KEY, OPENAI_API_KEY)")
            print("2. Check AI service configuration")
            print("3. Verify network connectivity to AI providers")
        
        if failed > 0:
            print("\n🚨 ISSUES FOUND:")
            print("Some AI features are not working properly. Check the error messages above.")

if __name__ == "__main__":
    # Check if backend is running
    try:
        response = requests.get(f"{BASE_URL}/api/ai/health/", timeout=5)
        if response.status_code != 200:
            print("❌ Backend is not running or AI service is not available")
            print("Please start the backend server first:")
            print("cd backend && python manage.py runserver 8000")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to backend server")
        print("Please start the backend server first:")
        print("cd backend && python manage.py runserver 8000")
        sys.exit(1)
    
    # Run tests
    test_suite = AITestSuite()
    test_suite.run_all_tests() 