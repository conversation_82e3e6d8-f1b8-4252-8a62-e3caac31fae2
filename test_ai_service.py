#!/usr/bin/env python3
"""
Test script to verify AI service functionality and API key loading
"""
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(project_root / 'backend' / '.env')

print("=== AI SERVICE TEST ===")
print(f"Project root: {project_root}")
print(f"Backend .env path: {project_root / 'backend' / '.env'}")

# Test environment variable loading
print("\n=== ENVIRONMENT VARIABLES ===")
groq_key = os.getenv('GROQ_API_KEY')
mistral_key = os.getenv('MISTRAL_API_KEY')
openai_key = os.getenv('OPENAI_API_KEY')

print(f"GROQ_API_KEY: {'✅ Found' if groq_key else '❌ Not found'}")
if groq_key:
    print(f"  Value: {groq_key[:20]}...")

print(f"MISTRAL_API_KEY: {'✅ Found' if mistral_key else '❌ Not found'}")
if mistral_key:
    print(f"  Value: {mistral_key[:20]}...")

print(f"OPENAI_API_KEY: {'✅ Found' if openai_key else '❌ Not found'}")
if openai_key:
    print(f"  Value: {openai_key}")

# Test AI service initialization
print("\n=== AI SERVICE INITIALIZATION ===")
try:
    from ai_engine.ai_service import AIService
    ai_service = AIService()
    print("✅ AI Service initialized successfully")
    print(f"Available providers: {[p['name'] for p in ai_service.available_providers]}")
    print(f"Total providers: {len(ai_service.available_providers)}")
    
    # Test gift message generation in different languages
    print("\n=== TESTING GIFT MESSAGE GENERATION ===")

    # Test English with full context - NEW EXAMPLE
    print("\n--- English with Full Context ---")
    result_en = ai_service.generate_gift_message(
        relationship="sister",
        occasion="wedding",
        tone="loving",
        recipient_name="Michelle",
        user_profile={
            "location": "Kenya",
            "region": "Nairobi",
            "language": "english",
            "senderName": "Jennifer",
            "salonName": "Elegance Salon",
            "salonLocation": "CBD",
            "serviceName": "bridal hair and makeup",
            "servicePrice": "8000"
        }
    )
    print(f"English: {result_en['response']}")
    print(f"Length: {len(result_en['response'].split())} words")

    # Test Swahili with context - NEW EXAMPLE
    print("\n--- Swahili with Full Context ---")
    result_sw = ai_service.generate_gift_message(
        relationship="rafiki",
        occasion="birthday",
        tone="loving",
        recipient_name="Wanjiku",
        user_profile={
            "location": "Kenya",
            "region": "Nairobi",
            "language": "swahili",
            "senderName": "Grace",
            "salonName": "Salon Mrembo",
            "salonLocation": "Westlands",
            "serviceName": "nywele na rangi",
            "servicePrice": "3000"
        }
    )
    print(f"Swahili: {result_sw['response']}")
    print(f"Length: {len(result_sw['response'].split())} words")

    # Test Sheng with context
    print("\n--- Sheng with Full Context ---")
    result_sh = ai_service.generate_gift_message(
        relationship="friend",
        occasion="promotion",
        tone="excited",
        recipient_name="Mercy",
        user_profile={
            "location": "Kenya",
            "region": "Nairobi",
            "language": "sheng",
            "senderName": "Grace",
            "salonName": "Chic Studio",
            "salonLocation": "Kilimani",
            "serviceName": "hair cut and color",
            "servicePrice": "2800"
        }
    )
    print(f"Sheng: {result_sh['response']}")
    print(f"Length: {len(result_sh['response'].split())} words")
    
except Exception as e:
    print(f"❌ Error initializing AI Service: {e}")
    import traceback
    traceback.print_exc()

print("\n=== TEST COMPLETE ===")
