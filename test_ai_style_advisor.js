// AI Style Advisor Internal Testing Script
// This script tests different user profiles to ensure AI insights work properly

const styleAdvisorService = require('./src/services/styleAdvisorService');

// Test profiles with different metrics
const testProfiles = [
  {
    name: "Young Professional - Curly Hair",
    profile: {
      hairType: 'Curly',
      faceShape: 'Oval',
      age: 25,
      occasion: 'Professional',
      stylePreferences: ['modern', 'low-maintenance'],
      budget: 'Premium',
      maintenanceLevel: 'Low',
      lifestyle: 'busy',
      hairLength: 'medium'
    }
  },
  {
    name: "Gen-Z Student - Straight Hair",
    profile: {
      hairType: 'Straight',
      faceShape: 'Round',
      age: 20,
      occasion: 'Everyday',
      stylePreferences: ['trendy', 'bold', 'colorful'],
      budget: 'Budget',
      maintenanceLevel: 'Medium',
      lifestyle: 'active',
      hairLength: 'long'
    }
  },
  {
    name: "Mature Professional - Wavy Hair",
    profile: {
      hairType: 'Wavy',
      faceShape: 'Square',
      age: 45,
      occasion: 'Professional',
      stylePreferences: ['classic', 'elegant'],
      budget: 'Premium',
      maintenanceLevel: 'High',
      lifestyle: 'professional',
      hairLength: 'short'
    }
  },
  {
    name: "Creative Artist - Mixed Hair",
    profile: {
      hairType: 'Mixed',
      faceShape: 'Heart',
      age: 30,
      occasion: 'Creative',
      stylePreferences: ['artistic', 'unique', 'experimental'],
      budget: 'Mid-range',
      maintenanceLevel: 'Medium',
      lifestyle: 'creative',
      hairLength: 'medium'
    }
  },
  {
    name: "Busy Parent - Fine Hair",
    profile: {
      hairType: 'Fine',
      faceShape: 'Oval',
      age: 35,
      occasion: 'Everyday',
      stylePreferences: ['practical', 'quick-styling'],
      budget: 'Mid-range',
      maintenanceLevel: 'Low',
      lifestyle: 'family',
      hairLength: 'short'
    }
  }
];

// Test function to validate AI responses
async function testAIStyleAdvisor() {
  console.log('🧪 Starting AI Style Advisor Internal Testing...\n');
  
  const service = new styleAdvisorService();
  
  for (let i = 0; i < testProfiles.length; i++) {
    const test = testProfiles[i];
    console.log(`\n📋 Test ${i + 1}: ${test.name}`);
    console.log('Profile:', JSON.stringify(test.profile, null, 2));
    
    try {
      console.log('🔄 Getting AI recommendations...');
      const result = await service.getStyleRecommendations(test.profile);
      
      // Validate response structure
      console.log('\n✅ Response received:');
      console.log('Source:', result.source);
      console.log('Confidence:', result.confidence);
      
      if (result.isServiceUnavailable) {
        console.log('⚠️ Service unavailable - using fallback');
        continue;
      }
      
      // Check if we got structured recommendations
      if (result.response && typeof result.response === 'string') {
        try {
          const parsed = JSON.parse(result.response);
          console.log('\n📊 AI Insights Analysis:');
          
          // Validate recommendations
          if (parsed.recommendations && parsed.recommendations.length > 0) {
            console.log(`✅ Found ${parsed.recommendations.length} recommendations`);
            
            parsed.recommendations.forEach((rec, index) => {
              console.log(`\n🎨 Recommendation ${index + 1}:`);
              console.log(`  Style: ${rec.style}`);
              console.log(`  Suitability Score: ${rec.suitabilityScore}`);
              console.log(`  Maintenance: ${rec.maintenance}`);
              console.log(`  Cost: ${rec.cost}`);
              console.log(`  Styling Time: ${rec.stylingTime}`);
              console.log(`  Why Recommended: ${rec.whyRecommended}`);
            });
          }
          
          // Validate analysis
          if (parsed.analysis) {
            console.log('\n🔍 Professional Analysis:');
            console.log(`  Face Shape: ${parsed.analysis.faceShapeAnalysis}`);
            console.log(`  Hair Type: ${parsed.analysis.hairTypeAnalysis}`);
            console.log(`  Lifestyle Fit: ${parsed.analysis.lifestyleFit}`);
          }
          
          // Validate alternatives
          if (parsed.alternatives && parsed.alternatives.length > 0) {
            console.log('\n🔄 Alternative Options:');
            parsed.alternatives.forEach((alt, index) => {
              console.log(`  ${index + 1}. ${alt.style} (${alt.suitabilityScore} score)`);
              console.log(`     Reason: ${alt.reason}`);
            });
          }
          
          // Metrics validation
          console.log('\n📈 Metrics Validation:');
          const metrics = validateMetrics(test.profile, parsed);
          console.log(metrics);
          
        } catch (parseError) {
          console.log('❌ Failed to parse AI response as JSON');
          console.log('Raw response:', result.response.substring(0, 200) + '...');
        }
      } else if (result.recommendations) {
        console.log('✅ Got structured recommendations directly');
        console.log(`Found ${result.recommendations.length} recommendations`);
      }
      
    } catch (error) {
      console.log('❌ Test failed:', error.message);
    }
    
    console.log('\n' + '='.repeat(80));
  }
  
  console.log('\n🏁 Testing completed!');
}

// Function to validate that AI considers different metrics
function validateMetrics(profile, aiResponse) {
  const metrics = {
    hairTypeConsidered: false,
    faceShapeConsidered: false,
    budgetConsidered: false,
    maintenanceConsidered: false,
    ageConsidered: false,
    lifestyleConsidered: false
  };
  
  const responseText = JSON.stringify(aiResponse).toLowerCase();
  
  // Check if hair type is considered
  if (responseText.includes(profile.hairType.toLowerCase())) {
    metrics.hairTypeConsidered = true;
  }
  
  // Check if face shape is considered
  if (responseText.includes(profile.faceShape.toLowerCase())) {
    metrics.faceShapeConsidered = true;
  }
  
  // Check if budget is considered
  if (responseText.includes(profile.budget.toLowerCase())) {
    metrics.budgetConsidered = true;
  }
  
  // Check if maintenance level is considered
  if (responseText.includes(profile.maintenanceLevel.toLowerCase())) {
    metrics.maintenanceConsidered = true;
  }
  
  // Check if age-appropriate recommendations
  if (profile.age < 25 && (responseText.includes('young') || responseText.includes('trendy'))) {
    metrics.ageConsidered = true;
  } else if (profile.age > 40 && (responseText.includes('mature') || responseText.includes('professional'))) {
    metrics.ageConsidered = true;
  }
  
  // Check if lifestyle is considered
  if (responseText.includes(profile.lifestyle) || responseText.includes('lifestyle')) {
    metrics.lifestyleConsidered = true;
  }
  
  return metrics;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testAIStyleAdvisor, testProfiles, validateMetrics };
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  testAIStyleAdvisor().catch(console.error);
}
