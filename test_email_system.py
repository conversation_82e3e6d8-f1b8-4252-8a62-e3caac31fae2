#!/usr/bin/env python3
"""
Test Email System for SalonGenz
Tests AI message generation with emojis and first names + email sending
"""

import os
import sys
import django
from pathlib import Path

# Setup Django environment
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))
sys.path.insert(0, str(project_root))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

# Now import Django components
from ai_engine.ai_service import AIService
from services.email_service import EmailService

def test_ai_with_emojis_and_first_names():
    """Test AI service with emoji generation and first name extraction"""
    print("=== 🤖 TESTING AI SERVICE WITH EMOJIS & FIRST NAMES ===")
    
    try:
        ai_service = AIService()
        print("✅ AI Service initialized successfully")
        
        # Test with full names to ensure only first names are used
        test_cases = [
            {
                "relationship": "sister",
                "occasion": "birthday",
                "tone": "loving",
                "recipient_name": "<PERSON>",  # Full name
                "user_profile": {
                    "location": "Kenya",
                    "region": "Nairobi",
                    "language": "english",
                    "senderName": "Jennifer Mary Ochieng",  # Full name
                    "salonName": "Glam Studio",
                    "salonLocation": "Westlands",
                    "serviceName": "hair styling and color",
                    "servicePrice": "3500"
                }
            },
            {
                "relationship": "rafiki",
                "occasion": "graduation",
                "tone": "excited",
                "recipient_name": "Amina Hassan Ali",  # Full name
                "user_profile": {
                    "location": "Kenya",
                    "region": "Mombasa",
                    "language": "swahili",
                    "senderName": "Fatuma Khadija Mohamed",  # Full name
                    "salonName": "Beauty Palace",
                    "salonLocation": "Nyali",
                    "serviceName": "manicure na pedicure",
                    "servicePrice": "2000"
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test Case {i}: {test_case['user_profile']['language'].title()} ---")
            
            result = ai_service.generate_gift_message(**test_case)

            # Extract message text from result
            if isinstance(result, dict):
                message_text = result.get('response', str(result))
                # Remove quotes if present
                if message_text.startswith('"') and message_text.endswith('"'):
                    message_text = message_text[1:-1]
            else:
                message_text = str(result)

            print(f"Language: {test_case['user_profile']['language']}")
            print(f"Full Recipient Name: {test_case['recipient_name']}")
            print(f"Full Sender Name: {test_case['user_profile']['senderName']}")
            print(f"Generated Message: {message_text}")
            print(f"Message Length: {len(message_text.split())} words")

            # Check for emojis
            emoji_count = sum(1 for char in message_text if ord(char) > 127)
            print(f"Emoji Characters: {emoji_count}")

            # Check if only first names are used
            recipient_first = test_case['recipient_name'].split()[0]
            sender_first = test_case['user_profile']['senderName'].split()[0]

            if recipient_first in message_text:
                print(f"✅ Recipient first name '{recipient_first}' found in message")
            else:
                print(f"❌ Recipient first name '{recipient_first}' NOT found in message")

            if sender_first in message_text:
                print(f"✅ Sender first name '{sender_first}' found in message")
            else:
                print(f"❌ Sender first name '{sender_first}' NOT found in message")
        
        return True
        
    except Exception as e:
        print(f"❌ AI Service test failed: {e}")
        return False

def test_email_service():
    """Test email service functionality"""
    print("\n=== 📧 TESTING EMAIL SERVICE ===")
    
    try:
        # Test gift notification email
        result = EmailService.send_gift_notification(
            recipient_email="<EMAIL>",
            recipient_name="Grace Wanjiku Mwangi",  # Full name
            gift_message="Hey Grace! 🎉 Happy birthday, sis! I got you a hair styling session at Glam Studio in Westlands. You deserve to be pampered! ✨ Love, Jennifer 💕",
            salon_details={
                "name": "Glam Studio",
                "location": "Westlands"
            },
            booking_details={
                "id": "TEST123",
                "service": "Hair Styling & Color",
                "date": "2024-01-15",
                "time": "2:00 PM"
            },
            sender_name="Jennifer Mary Ochieng"  # Full name
        )
        
        print(f"Email Service Result: {result}")
        
        if result['success']:
            print("✅ Email service test passed")
            return True
        else:
            print(f"❌ Email service test failed: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Email service test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 STARTING SALONGENZ EMAIL SYSTEM TESTS")
    print("=" * 50)
    
    # Test AI service
    ai_success = test_ai_with_emojis_and_first_names()
    
    # Test email service
    email_success = test_email_service()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print(f"🤖 AI Service: {'✅ PASSED' if ai_success else '❌ FAILED'}")
    print(f"📧 Email Service: {'✅ PASSED' if email_success else '❌ FAILED'}")
    
    if ai_success and email_success:
        print("\n🎉 ALL TESTS PASSED! Email system is ready for Phase 1.")
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
