#!/usr/bin/env python3
"""
Test Live Email <NAME_EMAIL>
This will attempt to send a real email using SMTP
"""

import os
import sys
import django
from pathlib import Path

# Setup Django environment
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))
sys.path.insert(0, str(project_root))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salongenz_backend.settings')
django.setup()

# Now import Django components
from ai_engine.ai_service import AIService
from services.email_service import EmailService

def test_email_configuration():
    """Test email configuration"""
    print("🔧 Testing email configuration...")
    
    try:
        from django.conf import settings
        print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
        print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
        print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
        print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
        print(f"EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}")
        print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
        
        # Check if password is set
        if hasattr(settings, 'EMAIL_HOST_PASSWORD') and settings.EMAIL_HOST_PASSWORD:
            if 'your_' in settings.EMAIL_HOST_PASSWORD:
                print("❌ EMAIL_HOST_PASSWORD: Not configured (placeholder detected)")
                return False
            else:
                print("✅ EMAIL_HOST_PASSWORD: Configured")
                return True
        else:
            print("❌ EMAIL_HOST_PASSWORD: Not set")
            return False
            
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def send_live_email():
    """Send live <NAME_EMAIL>"""
    print("📧 Attempting to send live email...")
    
    try:
        # Generate AI message
        print("🤖 Generating AI message...")
        ai_service = AIService()
        
        result = ai_service.generate_gift_message(
            relationship="bestie",
            occasion="testing our new system",
            tone="excited",
            recipient_name="Astella Suite",
            user_profile={
                "location": "Kenya",
                "region": "Nairobi",
                "language": "english",
                "senderName": "SalonGenz Team",
                "salonName": "Glam Paradise",
                "salonLocation": "Westlands",
                "serviceName": "full glam makeover",
                "servicePrice": "5000"
            }
        )
        
        # Extract message
        if isinstance(result, dict):
            message_text = result.get('response', str(result))
            if message_text.startswith('"') and message_text.endswith('"'):
                message_text = message_text[1:-1]
        else:
            message_text = str(result)
        
        print(f"✅ AI Message: {message_text}")
        
        # Send email
        print("📤 Sending email...")
        email_result = EmailService.send_gift_notification(
            recipient_email="<EMAIL>",
            recipient_name="Astella Suite",
            gift_message=message_text,
            salon_details={
                "name": "Glam Paradise",
                "location": "Westlands, Nairobi"
            },
            booking_details={
                "id": "LIVE-TEST-001",
                "service": "Full Glam Makeover",
                "date": "2024-01-25",
                "time": "3:00 PM"
            },
            sender_name="SalonGenz Team"
        )
        
        print(f"📧 Email Result: {email_result}")
        return email_result['success']
        
    except Exception as e:
        print(f"❌ Error sending email: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Provide specific guidance based on error type
        if "authentication" in str(e).lower() or "password" in str(e).lower():
            print("\n🔑 AUTHENTICATION ERROR - SOLUTIONS:")
            print("1. Enable 2-Factor Authentication on Gmail")
            print("2. Generate App Password: https://myaccount.google.com/apppasswords")
            print("3. Use the 16-character app password (not your regular password)")
            print("4. Update EMAIL_HOST_PASSWORD in backend/.env")
            
        elif "connection" in str(e).lower() or "network" in str(e).lower():
            print("\n🌐 CONNECTION ERROR - SOLUTIONS:")
            print("1. Check internet connection")
            print("2. Verify Gmail SMTP settings")
            print("3. Try alternative: SendGrid free tier (100 emails/day)")
            
        return False

def suggest_alternatives():
    """Suggest alternative email services"""
    print("\n🆓 FREE EMAIL SERVICE ALTERNATIVES:")
    print("=" * 50)
    
    print("1. 📧 SENDGRID (Recommended)")
    print("   - 100 emails/day free")
    print("   - Easy setup with API key")
    print("   - Reliable delivery")
    print("   - Sign up: https://sendgrid.com/")
    
    print("\n2. 📧 MAILGUN")
    print("   - 5,000 emails/month free for 3 months")
    print("   - Good for developers")
    print("   - Sign up: https://www.mailgun.com/")
    
    print("\n3. 📧 GMAIL APP PASSWORD")
    print("   - Free with existing Gmail account")
    print("   - Requires 2FA setup")
    print("   - Generate at: https://myaccount.google.com/apppasswords")
    
    print("\n4. 📧 OUTLOOK/HOTMAIL")
    print("   - Free with Microsoft account")
    print("   - SMTP: smtp-mail.outlook.com:587")

def main():
    """Run live email test"""
    print("🚀 LIVE EMAIL <NAME_EMAIL>")
    print("=" * 60)
    
    # Test configuration
    config_ok = test_email_configuration()
    
    if not config_ok:
        print("\n❌ EMAIL CONFIGURATION INCOMPLETE")
        suggest_alternatives()
        return
    
    print("\n✅ Configuration looks good, attempting to send...")
    
    # Attempt to send
    success = send_live_email()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS! Email <NAME_EMAIL>")
        print("📱 Check your inbox for the gift notification!")
        print("✅ Email system is fully operational!")
    else:
        print("❌ EMAIL SENDING FAILED")
        suggest_alternatives()
        print("\n🔧 TO FIX:")
        print("1. Update EMAIL_HOST_PASSWORD in backend/.env")
        print("2. Or switch to SendGrid/Mailgun free tier")
        print("3. Re-run this test script")

if __name__ == "__main__":
    main()
