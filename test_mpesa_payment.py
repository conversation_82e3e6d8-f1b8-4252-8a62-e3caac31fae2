#!/usr/bin/env python3
"""
Test script for M-Pesa payment integration using phone number 700000000
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:8000"
TEST_PHONE = "700000000"
TEST_EMAIL = "<EMAIL>"
TEST_AMOUNT = 1000

def test_mpesa_payment_flow():
    """Test the complete M-Pesa payment flow"""
    print("🧪 Testing M-Pesa Payment Integration")
    print("=" * 50)
    
    # Generate unique reference
    reference = f"SALON_TEST_{int(time.time())}"
    
    # Step 1: Initiate Payment
    print(f"\n1️⃣ Initiating M-Pesa payment...")
    print(f"   Phone: {TEST_PHONE}")
    print(f"   Amount: KSh {TEST_AMOUNT}")
    print(f"   Reference: {reference}")
    
    initiate_payload = {
        "amount": TEST_AMOUNT,
        "phone_number": TEST_PHONE,
        "email": TEST_EMAIL,
        "reference": reference,
        "description": "Test salon service payment",
        "currency": "KES"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/payments/mpesa/initiate/",
            json=initiate_payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            initiate_data = response.json()
            print(f"   ✅ Payment initiated successfully")
            print(f"   Transaction ID: {initiate_data['transaction_id']}")
            print(f"   Status: {initiate_data['status']}")
            
            transaction_id = initiate_data['transaction_id']
        else:
            print(f"   ❌ Failed to initiate payment: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error initiating payment: {e}")
        return False
    
    # Step 2: Simulate STK Push delay
    print(f"\n2️⃣ Simulating STK Push delay...")
    time.sleep(2)
    print(f"   ⏳ Waiting for user to enter M-Pesa PIN...")
    
    # Step 3: Simulate M-Pesa Callback
    print(f"\n3️⃣ Simulating M-Pesa callback...")
    
    callback_payload = {
        "transaction_id": transaction_id,
        "success": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/payments/mpesa/callback/",
            json=callback_payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            callback_data = response.json()
            print(f"   ✅ Callback processed successfully")
            print(f"   Message: {callback_data['message']}")
            print(f"   Payment Status: {callback_data['payment_status']}")
        else:
            print(f"   ❌ Callback failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error processing callback: {e}")
        return False
    
    # Step 4: Verify Payment
    print(f"\n4️⃣ Verifying payment...")
    
    verify_payload = {
        "transaction_id": transaction_id
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/payments/verify/",
            json=verify_payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            verify_data = response.json()
            payment = verify_data['payment']
            
            print(f"   ✅ Payment verification successful")
            print(f"   Transaction ID: {payment['transaction_id']}")
            print(f"   Status: {payment['status']}")
            print(f"   Amount: {payment['amount']} {payment['currency']}")
            print(f"   Phone: {payment['phone_number']}")
            print(f"   Gateway Reference: {payment['gateway_reference']}")
            print(f"   Completed At: {payment['completed_at']}")
            
            if payment['status'] == 'completed':
                print(f"\n🎉 Payment test completed successfully!")
                return True
            else:
                print(f"\n❌ Payment not completed. Status: {payment['status']}")
                return False
                
        else:
            print(f"   ❌ Verification failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error verifying payment: {e}")
        return False

def test_payment_status_endpoint():
    """Test the payment status endpoint"""
    print(f"\n5️⃣ Testing payment status endpoint...")
    
    # Use a test transaction ID
    test_transaction_id = f"SALON_TEST_{int(time.time())}"
    
    try:
        response = requests.get(f"{BASE_URL}/payments/status/{test_transaction_id}/")
        
        if response.status_code == 404:
            print(f"   ✅ Correctly returned 404 for non-existent payment")
            return True
        else:
            print(f"   ❓ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing status endpoint: {e}")
        return False

def main():
    """Run all tests"""
    print(f"🚀 Starting M-Pesa Payment Tests")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Backend URL: {BASE_URL}")
    
    # Test 1: Complete payment flow
    success1 = test_mpesa_payment_flow()
    
    # Test 2: Status endpoint
    success2 = test_payment_status_endpoint()
    
    # Summary
    print(f"\n📊 Test Summary")
    print(f"=" * 30)
    print(f"Payment Flow Test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Status Endpoint Test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print(f"\n🎉 All tests passed! M-Pesa integration is working correctly.")
        print(f"\n💡 Next steps:")
        print(f"   1. Open http://localhost:3000/payment/mpesa?amount=1000")
        print(f"   2. Use phone number: {TEST_PHONE}")
        print(f"   3. Click 'Test M-Pesa Payment (Direct)' button")
        print(f"   4. Watch the payment flow in action!")
    else:
        print(f"\n❌ Some tests failed. Please check the backend server and try again.")

if __name__ == "__main__":
    main()
