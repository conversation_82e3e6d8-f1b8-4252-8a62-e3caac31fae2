<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M-Pesa Payment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #4B0082 0%, #8A2BE2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        button {
            background: #FFD700;
            color: #333;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            background: #FFA500;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
        }
        .success { border-left: 5px solid #4CAF50; }
        .error { border-left: 5px solid #f44336; }
        .info { border-left: 5px solid #2196F3; }
        pre {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 M-Pesa Payment Test (Phone: 700000000)</h1>
        
        <div class="form-group">
            <label for="amount">Amount (KSh):</label>
            <input type="number" id="amount" value="1000" min="1">
        </div>
        
        <div class="form-group">
            <label for="phone">Phone Number:</label>
            <input type="text" id="phone" value="700000000" readonly>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="description">Description:</label>
            <input type="text" id="description" value="Test salon service payment">
        </div>
        
        <button onclick="testPaymentFlow()" id="testBtn">🚀 Test Complete Payment Flow</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000';
        let currentTransactionId = null;

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function logJson(data, title) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'result info';
            div.innerHTML = `<strong>${title}</strong>:<pre>${JSON.stringify(data, null, 2)}</pre>`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function makeRequest(url, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(url, options);
            const responseData = await response.json();
            
            return { response, data: responseData };
        }

        async function testPaymentFlow() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '⏳ Testing...';
            
            try {
                clearResults();
                log('🚀 Starting M-Pesa payment test flow...', 'info');
                
                // Step 1: Initiate Payment
                log('1️⃣ Initiating payment...', 'info');
                const reference = `SALON_TEST_${Date.now()}`;
                currentTransactionId = reference;
                
                const initiateData = {
                    amount: parseFloat(document.getElementById('amount').value),
                    phone_number: document.getElementById('phone').value,
                    email: document.getElementById('email').value,
                    reference: reference,
                    description: document.getElementById('description').value,
                    currency: 'KES'
                };
                
                const { response: initResponse, data: initData } = await makeRequest(
                    `${API_BASE}/payments/mpesa/initiate/`,
                    'POST',
                    initiateData
                );
                
                if (initResponse.ok) {
                    log('✅ Payment initiated successfully!', 'success');
                    logJson(initData, 'Initiate Response');
                } else {
                    throw new Error(`Initiate failed: ${JSON.stringify(initData)}`);
                }
                
                // Step 2: Simulate delay
                log('2️⃣ Simulating STK push delay...', 'info');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Step 3: Simulate callback
                log('3️⃣ Simulating M-Pesa callback...', 'info');
                const { response: callbackResponse, data: callbackData } = await makeRequest(
                    `${API_BASE}/payments/mpesa/callback/`,
                    'POST',
                    {
                        transaction_id: reference,
                        success: true
                    }
                );
                
                if (callbackResponse.ok) {
                    log('✅ Callback processed successfully!', 'success');
                    logJson(callbackData, 'Callback Response');
                } else {
                    throw new Error(`Callback failed: ${JSON.stringify(callbackData)}`);
                }
                
                // Step 4: Verify payment
                log('4️⃣ Verifying payment...', 'info');
                const { response: verifyResponse, data: verifyData } = await makeRequest(
                    `${API_BASE}/payments/verify/`,
                    'POST',
                    {
                        transaction_id: reference
                    }
                );
                
                if (verifyResponse.ok) {
                    log('✅ Payment verified successfully!', 'success');
                    logJson(verifyData, 'Verify Response');
                    
                    if (verifyData.payment.status === 'completed') {
                        log('🎉 PAYMENT TEST COMPLETED SUCCESSFULLY! 🎉', 'success');
                        log(`💰 Amount: KSh ${verifyData.payment.amount}`, 'success');
                        log(`📱 Phone: ${verifyData.payment.phone_number}`, 'success');
                        log(`🔗 Reference: ${verifyData.payment.gateway_reference}`, 'success');
                    } else {
                        log(`⚠️ Payment status: ${verifyData.payment.status}`, 'error');
                    }
                } else {
                    throw new Error(`Verify failed: ${JSON.stringify(verifyData)}`);
                }
                
                // Step 5: Check status endpoint
                log('5️⃣ Testing status endpoint...', 'info');
                const { response: statusResponse, data: statusData } = await makeRequest(
                    `${API_BASE}/payments/status/${reference}/`
                );
                
                if (statusResponse.ok) {
                    log('✅ Status endpoint working!', 'success');
                    logJson(statusData, 'Status Response');
                } else {
                    log('⚠️ Status endpoint failed', 'error');
                }
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                console.error('Test error:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🚀 Test Complete Payment Flow';
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-focus on amount field
        document.getElementById('amount').focus();
    </script>
</body>
</html>
