#!/bin/bash

echo "🧪 Testing M-Pesa Payment API with curl"
echo "======================================"

# Test 1: Initiate Payment
echo ""
echo "1️⃣ Testing payment initiation..."
REFERENCE="SALON_TEST_$(date +%s)"

curl -X POST http://127.0.0.1:8000/payments/mpesa/initiate/ \
  -H "Content-Type: application/json" \
  -d "{
    \"amount\": 1000,
    \"phone_number\": \"700000000\",
    \"email\": \"<EMAIL>\",
    \"reference\": \"$REFERENCE\",
    \"description\": \"Test salon payment\",
    \"currency\": \"KES\"
  }" \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "2️⃣ Testing callback simulation..."
curl -X POST http://127.0.0.1:8000/payments/mpesa/callback/ \
  -H "Content-Type: application/json" \
  -d "{
    \"transaction_id\": \"$REFERENCE\",
    \"success\": true
  }" \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "3️⃣ Testing payment verification..."
curl -X POST http://127.0.0.1:8000/payments/verify/ \
  -H "Content-Type: application/json" \
  -d "{
    \"transaction_id\": \"$REFERENCE\"
  }" \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "4️⃣ Testing payment status..."
curl -X GET "http://127.0.0.1:8000/payments/status/$REFERENCE/" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "✅ Test completed!"
echo "💡 Now test the frontend at: http://localhost:3000/payment/mpesa?amount=1000"
