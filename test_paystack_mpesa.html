<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paystack M-Pesa Test - Phone: 700000000</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #4B0082 0%, #8A2BE2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #FFD700;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        button {
            background: #FFD700;
            color: #333;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            min-width: 200px;
        }
        button:hover {
            background: #FFA500;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
        }
        .success { border-left: 5px solid #4CAF50; }
        .error { border-left: 5px solid #f44336; }
        .info { border-left: 5px solid #2196F3; }
        .warning { border-left: 5px solid #FF9800; }
        pre {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #4CAF50; }
        .status-error { background: #f44336; }
        .status-pending { background: #FF9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Paystack M-Pesa Integration Test</h1>
        <p><strong>Test Phone Number:</strong> 700000000</p>
        
        <div class="test-section">
            <h3>📋 Payment Configuration</h3>
            <div class="form-group">
                <label for="amount">Amount (KSh):</label>
                <input type="number" id="amount" value="1000" min="1">
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number:</label>
                <input type="text" id="phone" value="254" placeholder="254712345678">
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="currency">Currency:</label>
                <select id="currency">
                    <option value="KES">KES (Kenyan Shilling)</option>
                    <option value="NGN">NGN (Nigerian Naira)</option>
                    <option value="GHS">GHS (Ghanaian Cedi)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="paymentMethod">Payment Method:</label>
                <select id="paymentMethod">
                    <option value="mobile_money">Mobile Money (M-Pesa/Airtel)</option>
                    <option value="bank">Bank Transfer</option>
                    <option value="card">Card Payment</option>
                </select>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Test Actions</h3>
            <button onclick="testPaystackMpesa()" id="paystackBtn">
                💳 Test Paystack M-Pesa Payment
            </button>
            <button onclick="checkPaystackLoaded()">
                🔍 Check Paystack Script
            </button>
            <button onclick="clearResults()">
                🗑️ Clear Results
            </button>
        </div>

        <div id="results"></div>
    </div>

    <!-- Paystack Script -->
    <script src="https://js.paystack.co/v1/inline.js"></script>

    <script>
        // Paystack configuration
        const PAYSTACK_PUBLIC_KEY = 'pk_live_22d10798ff283975b275fc96824e6325ca901a0c';
        const API_BASE = 'http://127.0.0.1:8000';

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 'status-pending';
            
            div.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                <strong>${new Date().toLocaleTimeString()}</strong>: ${message}
            `;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function logJson(data, title) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'result info';
            div.innerHTML = `<strong>${title}</strong>:<pre>${JSON.stringify(data, null, 2)}</pre>`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function checkPaystackLoaded() {
            if (typeof PaystackPop !== 'undefined') {
                log('✅ Paystack script loaded successfully!', 'success');
                log(`📦 Paystack version: ${PaystackPop.version || 'Unknown'}`, 'info');
            } else {
                log('❌ Paystack script not loaded!', 'error');
            }
        }

        async function testPaystackMpesa() {
            const btn = document.getElementById('paystackBtn');
            btn.disabled = true;
            btn.textContent = '⏳ Processing...';

            try {
                log('🚀 Starting Paystack M-Pesa test...', 'info');

                const amount = parseFloat(document.getElementById('amount').value);
                const phone = document.getElementById('phone').value;
                const email = document.getElementById('email').value;
                const currency = document.getElementById('currency').value;
                const reference = `SALON_PAYSTACK_${Date.now()}`;

                log(`💰 Amount: ${amount} ${currency}`, 'info');
                log(`📱 Phone: ${phone}`, 'info');
                log(`📧 Email: ${email}`, 'info');
                log(`🔗 Reference: ${reference}`, 'info');

                // Check if using demo keys (skip demo mode for live keys)
                if (PAYSTACK_PUBLIC_KEY.includes('0acab0c42d5b105b842b42004d95c839735b6d0b') || PAYSTACK_PUBLIC_KEY.includes('pk_test_')) {
                    log('🧪 Demo Mode: Simulating Paystack M-Pesa payment...', 'info');

                    // Simulate payment processing
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    if (phone === '700000000') {
                        const mockResponse = {
                            reference: reference,
                            status: 'success',
                            trans: `T_${Date.now()}`,
                            transaction: `T_${Date.now()}`,
                            trxref: reference,
                            message: 'Approved (Demo)',
                            channel: 'mobile_money',
                            currency: currency,
                            amount: amount * 100
                        };

                        log('🎉 Demo Payment Successful!', 'success');
                        logJson(mockResponse, 'Demo Paystack Response');

                        // Verify payment with backend
                        verifyPayment(mockResponse.reference, mockResponse);
                    } else {
                        throw new Error('Demo mode only works with phone number 700000000');
                    }

                } else {
                    // Real Paystack integration
                    if (typeof PaystackPop === 'undefined') {
                        throw new Error('Paystack script not loaded');
                    }

                    const handler = PaystackPop.setup({
                        key: PAYSTACK_PUBLIC_KEY,
                        email: email,
                        amount: amount * 100, // Convert to kobo/cents
                        currency: currency,
                        ref: reference,
                        metadata: {
                            phone: phone,
                            description: 'Salon service payment test',
                            test_mode: true
                        },
                        channels: ['mobile_money'], // Force mobile money (M-Pesa) channel
                        callback: function(response) {
                            log('🎉 Payment successful!', 'success');
                            logJson(response, 'Paystack Response');

                            // Verify payment with backend
                            verifyPayment(response.reference, response);
                        },
                        onClose: function() {
                            log('❌ Payment popup closed by user', 'warning');
                            btn.disabled = false;
                            btn.textContent = '💳 Test Paystack M-Pesa Payment';
                        }
                    });

                    log('📱 Opening Paystack payment popup...', 'info');
                    handler.openIframe();
                }

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                btn.disabled = false;
                btn.textContent = '💳 Test Paystack M-Pesa Payment';
            }
        }

        async function verifyPayment(reference, paystackResponse) {
            try {
                log('🔍 Verifying payment with backend...', 'info');
                
                const response = await fetch(`${API_BASE}/payments/verify/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        transaction_id: reference,
                        paystack_response: paystackResponse
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Payment verified successfully!', 'success');
                    logJson(data, 'Backend Verification Response');
                } else {
                    log(`⚠️ Verification failed: ${data.error}`, 'warning');
                    logJson(data, 'Verification Error');
                }

            } catch (error) {
                log(`❌ Verification error: ${error.message}`, 'error');
            } finally {
                const btn = document.getElementById('paystackBtn');
                btn.disabled = false;
                btn.textContent = '💳 Test Paystack M-Pesa Payment';
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-check Paystack on load
        window.addEventListener('load', function() {
            setTimeout(checkPaystackLoaded, 1000);
        });
    </script>
</body>
</html>
