import requests

# New token from the logs
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxNDk5MjYwLCJpYXQiOjE3NTE0OTgwMjgsImp0aSI6IjcxNDhiY2U3ZWExMDRmNjk4MGFkY2ViOTQ1MjAxNzE1IiwidXNlcl9pZCI6Mzl9.xsyswbKOIveuvsG9gIcmWoLYAq1Ed_pWW52X--NVTQw"

headers = {
    'Authorization': f'Bearer {token}',
    'Content-Type': 'application/json'
}

try:
    response = requests.get('http://127.0.0.1:8000/api/user/profile/', headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"\nUser Profile:")
        print(f"Username: {data.get('username')}")
        print(f"is_superuser: {data.get('is_superuser')}")
        print(f"is_staff: {data.get('is_staff')}")
        print(f"is_vendor: {data.get('is_vendor')}")
        
except Exception as e:
    print(f"Error: {e}") 