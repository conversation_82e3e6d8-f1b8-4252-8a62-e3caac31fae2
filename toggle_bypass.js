#!/usr/bin/env node
/**
 * 🔧 Vendor Payment Bypass Toggle Script
 * 
 * Quick script to enable/disable vendor payment bypass
 * Usage: node toggle_bypass.js [enable|disable|status]
 */

const fs = require('fs');
const path = require('path');

const ENV_FILE = '.env';
const BYPASS_KEY = 'REACT_APP_BYPASS_VENDOR_PAYMENT';

function readEnvFile() {
  try {
    return fs.readFileSync(ENV_FILE, 'utf8');
  } catch (error) {
    console.error('❌ Error reading .env file:', error.message);
    process.exit(1);
  }
}

function writeEnvFile(content) {
  try {
    fs.writeFileSync(ENV_FILE, content);
  } catch (error) {
    console.error('❌ Error writing .env file:', error.message);
    process.exit(1);
  }
}

function getCurrentStatus() {
  const content = readEnvFile();
  const lines = content.split('\n');
  
  for (const line of lines) {
    if (line.startsWith(BYPASS_KEY)) {
      const value = line.split('=')[1];
      return value === 'true';
    }
    if (line.startsWith(`# ${BYPASS_KEY}`)) {
      return false; // Commented out
    }
  }
  
  return false; // Not found
}

function enableBypass() {
  let content = readEnvFile();
  const lines = content.split('\n');
  let found = false;
  
  // Update existing line or uncomment
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith(BYPASS_KEY)) {
      lines[i] = `${BYPASS_KEY}=true`;
      found = true;
      break;
    }
    if (lines[i].startsWith(`# ${BYPASS_KEY}`)) {
      lines[i] = `${BYPASS_KEY}=true`;
      found = true;
      break;
    }
  }
  
  // Add new line if not found
  if (!found) {
    lines.push('');
    lines.push('# Development Settings - Vendor Payment Bypass');
    lines.push(`${BYPASS_KEY}=true`);
  }
  
  writeEnvFile(lines.join('\n'));
  console.log('✅ Vendor payment bypass ENABLED');
  console.log('🔄 Please restart your React server: npm start');
}

function disableBypass() {
  let content = readEnvFile();
  const lines = content.split('\n');
  
  // Comment out or set to false
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith(BYPASS_KEY)) {
      lines[i] = `# ${lines[i]}`;
      break;
    }
  }
  
  writeEnvFile(lines.join('\n'));
  console.log('❌ Vendor payment bypass DISABLED');
  console.log('🔄 Please restart your React server: npm start');
}

function showStatus() {
  const isEnabled = getCurrentStatus();
  console.log('📋 Vendor Payment Bypass Status:');
  console.log(`   Status: ${isEnabled ? '✅ ENABLED' : '❌ DISABLED'}`);
  console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  
  if (isEnabled) {
    console.log('');
    console.log('🚀 Bypass is active - users will see both payment options');
    console.log('   • Real payment button');
    console.log('   • Skip payment button (development mode)');
  } else {
    console.log('');
    console.log('💳 Normal payment mode - users will see only payment button');
  }
}

function showHelp() {
  console.log('🔧 Vendor Payment Bypass Control');
  console.log('');
  console.log('Usage:');
  console.log('  node toggle_bypass.js enable   - Enable bypass');
  console.log('  node toggle_bypass.js disable  - Disable bypass');
  console.log('  node toggle_bypass.js status   - Show current status');
  console.log('  node toggle_bypass.js help     - Show this help');
  console.log('');
  console.log('Examples:');
  console.log('  node toggle_bypass.js enable   # Enable for testing');
  console.log('  node toggle_bypass.js disable  # Disable for production-like testing');
  console.log('  node toggle_bypass.js status   # Check current state');
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'enable':
    enableBypass();
    break;
  case 'disable':
    disableBypass();
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    console.log('❓ Unknown command. Use "help" for usage information.');
    showHelp();
    process.exit(1);
}
